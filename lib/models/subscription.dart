class Subscription {
  final String id;
  final String clientId;
  final String name;
  final String description;
  final double price;
  final String frequency; // 'monthly', 'quarterly', 'yearly'
  final DateTime startDate;
  final DateTime? endDate;
  final bool isActive;
  final int sessionsIncluded;
  final int sessionsUsed;
  final List<String> includedServices;
  final String notes;

  Subscription({
    required this.id,
    required this.clientId,
    required this.name,
    required this.description,
    required this.price,
    required this.frequency,
    required this.startDate,
    this.endDate,
    required this.isActive,
    required this.sessionsIncluded,
    required this.sessionsUsed,
    required this.includedServices,
    this.notes = '',
  });

  // Calculated properties
  int get remainingSessions => sessionsIncluded - sessionsUsed;
  
  double get usagePercentage => 
      sessionsIncluded > 0 ? (sessionsUsed / sessionsIncluded) * 100 : 0;

  bool get isExpired => endDate != null && DateTime.now().isAfter(endDate!);

  String get status {
    if (!isActive) return 'Inactiv';
    if (isExpired) return 'Expirat';
    if (remainingSessions <= 0) return 'Epuizat';
    return 'Activ';
  }

  // Convert from JSON
  factory Subscription.fromJson(Map<String, dynamic> json) {
    return Subscription(
      id: json['id'],
      clientId: json['clientId'],
      name: json['name'],
      description: json['description'],
      price: (json['price']).toDouble(),
      frequency: json['frequency'],
      startDate: DateTime.parse(json['startDate']),
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate']) : null,
      isActive: json['isActive'] ?? true,
      sessionsIncluded: json['sessionsIncluded'],
      sessionsUsed: json['sessionsUsed'],
      includedServices: List<String>.from(json['includedServices'] ?? []),
      notes: json['notes'] ?? '',
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'clientId': clientId,
      'name': name,
      'description': description,
      'price': price,
      'frequency': frequency,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'isActive': isActive,
      'sessionsIncluded': sessionsIncluded,
      'sessionsUsed': sessionsUsed,
      'includedServices': includedServices,
      'notes': notes,
    };
  }
}
