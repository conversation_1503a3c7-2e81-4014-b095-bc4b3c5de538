import 'package:flutter/foundation.dart';
import '../models/api_response.dart';
import '../models/sms_settings.dart';
import '../services/api_service.dart';
import '../config/api_config.dart';
import 'auth/auth_service.dart';

/// Service for managing salon SMS reminder settings
class SmsSettingsService {
  /// Get SMS settings for the current salon
  static Future<ApiResponse<SmsSettings>> getSmsSettings() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<SmsSettings>.error('No salon ID found');
      }

      if (ApiConfig.enableLogging) {
        debugPrint('📱 Getting SMS settings for salon: $salonId');
      }

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/salons/$salonId/sms-reminders',
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final settings = SmsSettings.fromJson(response.data!);
        
        if (ApiConfig.enableLogging) {
          debugPrint('✅ SMS settings retrieved successfully');
        }
        
        return ApiResponse.success(settings);
      }

      return ApiResponse<SmsSettings>.error(response.error ?? 'Failed to get SMS settings');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error getting SMS settings: $e');
      }
      return ApiResponse<SmsSettings>.error('Failed to get SMS settings: $e');
    }
  }

  /// Update SMS settings for the current salon
  /// Only users with CHIEF_GROOMER role can update these settings
  static Future<ApiResponse<SmsSettings>> updateSmsSettings(UpdateSmsSettingsRequest request) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<SmsSettings>.error('No salon ID found');
      }

      if (ApiConfig.enableLogging) {
        debugPrint('📱 Updating SMS settings for salon: $salonId');
        debugPrint('   Appointment confirmations: ${request.appointmentConfirmations}');
        debugPrint('   Day before reminders: ${request.dayBeforeReminders}');
        debugPrint('   Follow-up messages: ${request.followUpMessages}');
        debugPrint('   Selected provider: ${request.selectedProvider}');
      }

      final response = await ApiService.put<Map<String, dynamic>>(
        '/api/salons/$salonId/sms-reminders',
        body: request.toJson(),
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final updatedSettings = SmsSettings.fromJson(response.data!);
        
        if (ApiConfig.enableLogging) {
          debugPrint('✅ SMS settings updated successfully');
        }
        
        return ApiResponse.success(updatedSettings);
      }

      return ApiResponse<SmsSettings>.error(response.error ?? 'Failed to update SMS settings');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error updating SMS settings: $e');
      }
      return ApiResponse<SmsSettings>.error('Failed to update SMS settings: $e');
    }
  }

  /// Get SMS settings for a specific salon (admin only)
  static Future<ApiResponse<SmsSettings>> getSmsSettingsForSalon(String salonId) async {
    try {
      if (ApiConfig.enableLogging) {
        debugPrint('📱 Getting SMS settings for specific salon: $salonId');
      }

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/salons/$salonId/sms-reminders',
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final settings = SmsSettings.fromJson(response.data!);
        
        if (ApiConfig.enableLogging) {
          debugPrint('✅ SMS settings retrieved successfully for salon: $salonId');
        }
        
        return ApiResponse.success(settings);
      }

      return ApiResponse<SmsSettings>.error(response.error ?? 'Failed to get SMS settings');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error getting SMS settings for salon $salonId: $e');
      }
      return ApiResponse<SmsSettings>.error('Failed to get SMS settings: $e');
    }
  }

  /// Reset SMS settings to default values
  /// Only users with CHIEF_GROOMER role can reset settings
  static Future<ApiResponse<SmsSettings>> resetSmsSettings() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<SmsSettings>.error('No salon ID found');
      }

      if (ApiConfig.enableLogging) {
        debugPrint('📱 Resetting SMS settings for salon: $salonId');
      }

      final defaultRequest = const UpdateSmsSettingsRequest(
        enabled: true,
        appointmentConfirmations: true,
        dayBeforeReminders: true,
        followUpMessages: false,
        selectedProvider: null,
      );

      return await updateSmsSettings(defaultRequest);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error resetting SMS settings: $e');
      }
      return ApiResponse<SmsSettings>.error('Failed to reset SMS settings: $e');
    }
  }

  /// Validate SMS settings request
  static String? validateSmsSettingsRequest(UpdateSmsSettingsRequest request) {
    // Basic validation - at least one setting should be enabled if provider is selected
    return null; // Valid request
  }

  /// Check if current user can modify SMS settings
  /// This should be used in UI to show/hide edit controls
  static Future<bool> canModifySmsSettings() async {
    try {
      // This would typically check the user's role in the current salon
      // For now, we'll assume the permission check is done at the UI level
      // using PermissionGuard with requireManagementAccess
      final salonId = await AuthService.getCurrentSalonId();
      return salonId != null;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error checking SMS settings permissions: $e');
      }
      return false;
    }
  }
}
