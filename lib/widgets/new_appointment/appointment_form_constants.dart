import 'package:flutter/cupertino.dart';

/// Constants for appointment form
class AppointmentFormConstants {
  // Pet species options
  static const List<String> petSpecies = [
    'dog',
    'cat',
    'bird',
    'rabbit',
    'hamster',
    'guinea_pig',
    'ferret',
    'other',
  ];

  // Pet species display names (Romanian)
  static const Map<String, String> petSpeciesDisplayNames = {
    'dog': '<PERSON><PERSON><PERSON>',
    'cat': '<PERSON><PERSON><PERSON><PERSON>',
    'bird': '<PERSON><PERSON><PERSON><PERSON>',
    'rabbit': 'Iepure',
    'hamster': '<PERSON>ster',
    'guinea_pig': '<PERSON><PERSON><PERSON>',
    'ferret': 'Di<PERSON>',
    'other': '<PERSON><PERSON>',
  };

  // Time intervals for time picker (in minutes)
  static const int timeInterval = 15;

  // Business hours - these will be fetched from API
  static int businessStartHour = 8;
  static int businessEndHour = 20;

  /// Gets display name for pet species
  static String getSpeciesDisplayName(String species) {
    debugPrint('🔍 getSpeciesDisplayName: $species');
    return petSpeciesDisplayNames[species] ?? species;
  }

  /// Generates time options for dropdown
  static List<DateTime> generateTimeOptions(DateTime date) {
    final List<DateTime> times = [];
    final startTime = DateTime(date.year, date.month, date.day, businessStartHour);
    final endTime = DateTime(date.year, date.month, date.day, businessEndHour);

    DateTime current = startTime;
    while (current.isBefore(endTime) || current.isAtSameMomentAs(endTime)) {
      times.add(current);
      current = current.add(const Duration(minutes: timeInterval));
    }

    return times;
  }

  /// Formats time for display
  static String formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  static const Map<String, String> catBreeds = {
        'Persan\u0103': 'S',
        'Maine Coon': 'S',
        'Siamez\u0103': 'S',
        'British Shorthair': 'S',
        'Ragdoll': 'S',
        'Bengalez\u0103': 'S',
        'Abisinian\u0103': 'S',
        'Birman\u0103': 'S',
        'Oriental Shorthair': 'S',
        'Devon Rex': 'S',
        'Sphynx': 'S',
        'Scottish Fold': 'S',
        'Russian Blue': 'S',
        'Norvegian de P\u0103dure': 'S',
        'Exotic Shorthair': 'S',
        'Cornish Rex': 'S',
        'Selkirk Rex': 'S',
        'Munchkin': 'S',
        'American Shorthair': 'S',
        'Angora Turceasc\u0103': 'S',
        'Birmanez\u0103': 'S',
        'Himalayan\u0103': 'S',
        'Tonkinese': 'S',
        'Somalez\u0103': 'S',
        'Balinez\u0103': 'S',
        'Egyptian Mau': 'S',
        'Manx': 'S',
        'Burmilla': 'S',
        'Chartreux': 'S',
        'Siberian\u0103': 'S',
        'Ocicat': 'S',
        'Metis': 'S'
  };

  static const List<String> otherBreeds = [
    'Metis',
    'Necunoscut'
  ];

  static List<String> forSpecies(String species) {
    switch (species) {
      case 'dog':
        return dogBreeds.keys.toList();
      case 'cat':
        return catBreeds.keys.toList();
      default:
        return otherBreeds;
    }
  }

  static List<String> get all => [
    ...dogBreeds.keys,
    ...catBreeds.keys,
    ...otherBreeds,
  ];


  static const Map<String, String> dogBreeds = {
    'Affenpinscher': 'S',
    'Ainu': 'M',
    'Airedale Terrier': 'L',
    'Akita Inu': 'L',
    'American Eskimo': 'M',
    'American Staffordshire Terrier': 'M',
    'Azawakh': 'M',
    'Amstaff': 'M',
    'Barzoi': 'L',
    'Basenji': 'M',
    'Petit Basset Griffon Vendéen': 'M',
    'Basset Hound': 'M',
    'Beagle': 'M',
    'Bearded Collie': 'M',
    'Beauceron': 'L',
    'Bergamasco': 'L',
    'Bichon Bolognese': 'S',
    'Bichon Frisé': 'S',
    'Bichon Havanez': 'S',
    'Bichon Maltez': 'S',
    'Bloodhound': 'L',
    'Bobtail': 'L',
    'Border Collie': 'M',
    'Border Terrier': 'S',
    'Boston Terrier': 'M',
    'Boxer': 'L',
    'Brac german': 'M',
    'Brac german cu păr aspru': 'M',
    'Brac german cu păr scurt': 'M',
    'Brac italian': 'M',
    'Briard': 'L',
    'Brittany': 'M',
    'Buhund norvegian': 'M',
    'Bulldog': 'M',
    'Bulldog american': 'M',
    'Bulldog englez': 'M',
    'Bulldog francez': 'S',
    'Bulldog spaniol': 'M',
    'Bull terrier': 'M',
    'Bullmastiff': 'L',
    'Câinele belgian de cireadă': 'L',
    'Cairn Terrier': 'S',
    'Canaan': 'M',
    'Cane Corso': 'L',
    'Cavalier Spaniel': 'S',
    'Chesapeake Retriever': 'L',
    'Chihuahua': 'S',
    'Chow chow': 'M',
    'Ciobănesc Anatolian': 'L',
    'Ciobănesc Australian': 'M',
    'Ciobănesc de Berna': 'L',
    'Ciobănesc de Shetland': 'S',
    'Ciobănesc german': 'L',
    'Ciobănesc alb elvețian': 'L',
    'Ciobănesc românesc carpatin': 'L',
    'Ciobănesc românesc de Bucovina': 'L',
    'Ciobănesc românesc mioritic': 'L',
    'Ciobănesc românesc corb': 'L',
    'Cocker Spaniel': 'M',
    'Collie cu păr lung': 'M',
    'Copoi ardelenesc': 'L',
    'Corgi galez Cardigan': 'S',
    'Corgi galez Pembroke': 'S',
    'Dalmațian': 'L',
    'Dandie Dinmont Terrier': 'S',
    'Deerhound': 'L',
    'Dobermann': 'L',
    'Dog argentinian': 'L',
    'Dog de Bordeaux': 'L',
    'Dog german': 'L',
    'English Toy Terrier': 'S',
    'Estrella': 'L',
    'Fox Terrier cu blană întinsă': 'S',
    'Fox Terrier sârmos': 'S',
    'Golden Retriever': 'L',
    'Gordon Setter': 'L',
    'Griffon de Bruxelles': 'S',
    'Husky Siberian': 'M',
    'Irish Terrier': 'M',
    'Jindo': 'M',
    'Jack Russel Terrier': 'S',
    'Labrador Retriever': 'L',
    'Lagotto Romagnolo': 'M',
    'Landseer': 'L',
    'Lapphund finlandez': 'M',
    'Malamut de Alaska': 'L',
    'Malinois': 'M',
    'Marele danez': 'L',
    'Mastiff Napoleon': 'L',
    'Metis': 'M',
    'Ogar afgan': 'L',
    'Ogarul de Ibiza': 'L',
    'Pekinez': 'S',
    'Pit Bull Terrier American': 'M',
    'Pointer englez': 'L',
    'Pug': 'S',
    'Puli unguresc': 'M',
    'Pudel Standard': 'L',
    'Pudel Mini': 'M',
    'Pudel Toy': 'S',
    'Pomeranian': 'S',
    'Rhodesian Ridgeback': 'L',
    'Rottweiler': 'L',
    'Saint-Bernard': 'L',
    'Saluki': 'L',
    'Samoyed': 'M',
    'Schnauzer uriaș': 'L',
    'Scottish Terrier': 'S',
    'Setter englez': 'L',
    'Setter irlandez': 'L',
    'Shar Pei': 'M',
    'Shih-tzu': 'S',
    'Spaniel-ul de câmp': 'M',
    'Spitz german': 'S',
    'Spitz japonez': 'S',
    'Sussex Spaniel': 'M',
    'Terra Nova': 'L',
    'Terrier australian': 'S',
    'Terrier Bedlington': 'S',
    'Terrierul de Boemia': 'S',
    'Terrierul negru rusesc': 'L',
    'Tibatan Spaniel': 'S',
    'Vișlă': 'M',
    'Weimaraner': 'L',
    'Welsh Sprnger': 'M',
    'West highland white terrier': 'S',
    'Whippet': 'M',
  };

  static List<String> get breedList => [
        ...dogBreeds.keys,
        ...catBreeds.keys,
        'Altă rasă'
      ];

  static String getBreedSize(String breed) {
    final size = dogBreeds[breed] ?? catBreeds[breed];

    if (size == null) {
      debugPrint('⚠️ Unknown breed "$breed", defaulting to Medium size');
      return 'M'; // Default to Medium for unknown breeds
    }

    debugPrint('📏 Breed "$breed" mapped to size: $size');
    return size;
  }

  /// Check if a breed has size-based pricing available
  static bool hasBreedSizeMapping(String breed) {
    return dogBreeds.containsKey(breed) || catBreeds.containsKey(breed);
  }

  /// Get all available sizes
  static List<String> get availableSizes => ['S', 'M', 'L'];

  /// Get size description
  static String getSizeDescription(String size) {
    switch (size) {
      case 'S':
        return 'Mic (S)';
      case 'M':
        return 'Mediu (M)';
      case 'L':
        return 'Mare (L)';
      default:
        return 'Necunoscut';
    }
  }

  static String getBreedSpecies(String breed) {
    if (dogBreeds.containsKey(breed)) return 'dog';
    if (catBreeds.containsKey(breed)) return 'cat';
    return 'other';
  }
}
