import 'package:flutter/material.dart';

/// Centralized dimensions and spacing constants for consistent UI
class AppDimensions {
  // Border radius values
  static const double borderRadiusSmall = 8.0;
  static const double borderRadiusMedium = 12.0;
  static const double borderRadiusLarge = 16.0;
  static const double borderRadiusXLarge = 20.0;

  // Padding values
  static const EdgeInsets paddingTiny = EdgeInsets.all(4.0);
  static const EdgeInsets paddingSmall = EdgeInsets.all(8.0);
  static const EdgeInsets paddingStandard = EdgeInsets.all(16.0);
  static const EdgeInsets paddingLarge = EdgeInsets.all(20.0);
  static const EdgeInsets paddingXLarge = EdgeInsets.all(24.0);

  // Horizontal padding
  static const EdgeInsets paddingHorizontalSmall = EdgeInsets.symmetric(horizontal: 8.0);
  static const EdgeInsets paddingHorizontalStandard = EdgeInsets.symmetric(horizontal: 16.0);
  static const EdgeInsets paddingHorizontalLarge = EdgeInsets.symmetric(horizontal: 20.0);

  // Vertical padding
  static const EdgeInsets paddingVerticalSmall = EdgeInsets.symmetric(vertical: 8.0);
  static const EdgeInsets paddingVerticalStandard = EdgeInsets.symmetric(vertical: 16.0);
  static const EdgeInsets paddingVerticalLarge = EdgeInsets.symmetric(vertical: 20.0);

  // Spacing values (for SizedBox)
  static const double spacingTiny = 4.0;
  static const double spacingSmall = 8.0;
  static const double spacingStandard = 16.0;
  static const double spacingLarge = 20.0;
  static const double spacingXLarge = 24.0;
  static const double spacingXXLarge = 32.0;

  // Icon sizes
  static const double iconSizeSmall = 16.0;
  static const double iconSizeStandard = 24.0;
  static const double iconSizeLarge = 32.0;
  static const double iconSizeXLarge = 48.0;

  // Button dimensions
  static const double buttonHeightSmall = 36.0;
  static const double buttonHeightStandard = 48.0;
  static const double buttonHeightLarge = 56.0;

  // Card elevation
  static const double elevationLow = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationHigh = 8.0;

  // Common widget sizes
  static const double appBarHeight = 56.0;
  static const double bottomNavHeight = 60.0;
  static const double fabSize = 56.0;
  static const double avatarSizeSmall = 32.0;
  static const double avatarSizeStandard = 48.0;
  static const double avatarSizeLarge = 64.0;

  // Form field dimensions
  static const double formFieldHeight = 56.0;
  static const double formFieldBorderWidth = 1.0;

  // Divider thickness
  static const double dividerThickness = 1.0;

  // Common BorderRadius objects
  static BorderRadius get borderRadiusSmallAll => BorderRadius.circular(borderRadiusSmall);
  static BorderRadius get borderRadiusMediumAll => BorderRadius.circular(borderRadiusMedium);
  static BorderRadius get borderRadiusLargeAll => BorderRadius.circular(borderRadiusLarge);
  static BorderRadius get borderRadiusXLargeAll => BorderRadius.circular(borderRadiusXLarge);

  // Top-only border radius (for bottom sheets, cards)
  static BorderRadius get borderRadiusTopMedium => const BorderRadius.vertical(
    top: Radius.circular(borderRadiusMedium),
  );
  static BorderRadius get borderRadiusTopLarge => const BorderRadius.vertical(
    top: Radius.circular(borderRadiusLarge),
  );
  static BorderRadius get borderRadiusTopXLarge => const BorderRadius.vertical(
    top: Radius.circular(borderRadiusXLarge),
  );

  // Common SizedBox widgets
  static const Widget verticalSpaceTiny = SizedBox(height: spacingTiny);
  static const Widget verticalSpaceSmall = SizedBox(height: spacingSmall);
  static const Widget verticalSpaceStandard = SizedBox(height: spacingStandard);
  static const Widget verticalSpaceLarge = SizedBox(height: spacingLarge);
  static const Widget verticalSpaceXLarge = SizedBox(height: spacingXLarge);

  static const Widget horizontalSpaceTiny = SizedBox(width: spacingTiny);
  static const Widget horizontalSpaceSmall = SizedBox(width: spacingSmall);
  static const Widget horizontalSpaceStandard = SizedBox(width: spacingStandard);
  static const Widget horizontalSpaceLarge = SizedBox(width: spacingLarge);
  static const Widget horizontalSpaceXLarge = SizedBox(width: spacingXLarge);

  static const int daysAhead = 100;
}

/// Extension methods for easier access to common dimensions
extension AppDimensionsExtension on BuildContext {
  /// Get screen width
  double get screenWidth => MediaQuery.of(this).size.width;
  
  /// Get screen height
  double get screenHeight => MediaQuery.of(this).size.height;
  
  /// Get safe area padding
  EdgeInsets get safeAreaPadding => MediaQuery.of(this).padding;
  
  /// Check if screen is small (width < 600)
  bool get isSmallScreen => screenWidth < 600;
  
  /// Check if screen is tablet size (width >= 600)
  bool get isTablet => screenWidth >= 600;
}
