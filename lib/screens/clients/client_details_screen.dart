import 'package:partykidsapp/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_speed_dial/flutter_speed_dial.dart';
import 'package:provider/provider.dart';
import '../../models/appointment.dart';
import '../../models/client.dart';
import '../../models/pet.dart';
import '../../models/review.dart';
import '../../models/subscription.dart';
import '../../providers/calendar_provider.dart';
import '../../providers/client_provider.dart';
import '../../services/client/client_service.dart';
import '../../config/theme/app_theme.dart';
import '../../widgets/clients/client_header_widget.dart';
import '../../widgets/common/custom_bottom_sheet.dart';
import '../../widgets/common/custom_sliver_app_bar.dart';
import '../../widgets/lists/client_tabs_widget.dart';
import 'add_pet_screen.dart';
import 'edit_client_screen.dart';

class ClientDetailsScreen extends StatefulWidget {
  final Client client;

  const ClientDetailsScreen({
    super.key,
    required this.client,
  });

  @override
  State<ClientDetailsScreen> createState() => _ClientDetailsScreenState();
}

class _ClientDetailsScreenState extends State<ClientDetailsScreen> {
  Client? _currentClient;
  final List<Subscription> _subscriptions = [];
  final List<Review> _reviews = [];
  final List<Appointment> _appointments = [];
  List<Pet> _pets = [];

  bool _isLoadingPets = true;
  bool _isLoadingAppointments = true;

  @override
  void initState() {
    super.initState();
    _currentClient = widget.client;

    // Use post frame callback to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadClientData();
      _refreshClientFromProvider();
    });
  }

  /// Refresh client data from ClientProvider to get latest information
  void _refreshClientFromProvider() {
    debugPrint('🔄 ClientDetailsScreen: Refreshing client data from provider...');
    final clientProvider = context.read<ClientProvider>();
    final updatedClient = clientProvider.getClientById(widget.client.id);

    if (updatedClient != null && mounted) {
      setState(() {
        _currentClient = updatedClient;
      });
      debugPrint('✅ ClientDetailsScreen: Client data refreshed from provider');
    } else {
      debugPrint('⚠️ ClientDetailsScreen: Client not found in provider, using original data');
    }
  }

  Future<void> _loadClientData() async {
    await Future.wait([
      _loadPets(),
      _loadAppointments(),
    ]);
  }

  Future<void> _loadPets() async {
    try {
      debugPrint('🔄 ClientDetailsScreen: Loading pets for client: ${_currentClient?.id}');
      final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
      // Get pets for this client using HTTP service
      final pets = await calendarProvider.calendarService.getPetsForClient(_currentClient?.id ?? widget.client.id);

      if (mounted) {
        setState(() {
          _pets = pets;
          _isLoadingPets = false;
        });
        debugPrint('✅ ClientDetailsScreen: Loaded ${pets.length} pets');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingPets = false;
        });
      }
      debugPrint('❌ ClientDetailsScreen: Error loading client pets: $e');
    }
  }

  Future<void> _loadAppointments() async {
    try {
      debugPrint('🔄 ClientDetailsScreen: Loading appointments for client: ${_currentClient?.id}');
      final response = await ClientService.getClientAppointments(_currentClient?.id ?? widget.client.id);

      if (mounted) {
        setState(() {
          if (response.success && response.data != null) {
            _appointments
              ..clear()
              ..addAll(response.data!);
          } else {
            _appointments.clear();
          }
          _isLoadingAppointments = false;
        });
        debugPrint('✅ ClientDetailsScreen: Loaded ${_appointments.length} appointments');
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoadingAppointments = false;
        });
      }
      debugPrint('❌ ClientDetailsScreen: Error loading client appointments: $e');
    }
  }

  void _showOptionsMenu() {
    CustomBottomSheet.show(
      context: context,
      title: 'Opțiuni Client',
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: Icon(Icons.delete_forever, color: AppTheme.getStatusColor(context, 'error')),
            title: const Text('Șterge Client'),
            subtitle: const Text('Șterge definitiv clientul și toate datele asociate'),
            onTap: () {
              Navigator.pop(context);
              _showDeleteConfirmation();
            },
          ),
          ListTile(
            leading: Icon(Icons.block, color: AppTheme.getStatusColor(context, 'warning')),
            title: const Text('Blochează Client'),
            subtitle: const Text('Împiedică programarea de noi întâlniri'),
            onTap: () {
              Navigator.pop(context);
              _blockClient();
            },
          ),
          ListTile(
            leading: Icon(Icons.favorite, color: Theme.of(context).colorScheme.secondary),
            title: const Text('Adaugă la Favorite'),
            subtitle: const Text('Marchează ca client preferat'),
            onTap: () {
              Navigator.pop(context);
              _favoriteClient();
            },
          ),
        ],
      ),
    );
  }

  void _blockClient() {
    showTopSnackBar(context, 
      SnackBar(
        content: Text('${widget.client.name} a fost blocat'),
        backgroundColor: AppTheme.getStatusColor(context, 'error'),
        action: SnackBarAction(
          label: 'Anulează',
          textColor: Theme.of(context).colorScheme.onError,
          onPressed: () {
            // Undo block action
          },
        ),
      ),
    );
  }

  void _favoriteClient() {
    showTopSnackBar(context, 
      SnackBar(
        content: Text('${widget.client.name} a fost adăugat la favorite'),
        backgroundColor: Theme.of(context).colorScheme.secondary,
        action: SnackBarAction(
          label: 'Anulează',
          textColor: Theme.of(context).colorScheme.onSecondary,
          onPressed: () {
            // Undo favorite action
          },
        ),
      ),
    );
  }

  void _editClient() async {
    debugPrint('🔄 ClientDetailsScreen: Opening edit screen for client: ${_currentClient?.name}');

    final result = await Navigator.of(context).push<Client>(
      MaterialPageRoute(
        builder: (context) => EditClientScreen(client: _currentClient ?? widget.client),
      ),
    );

    // If client was successfully updated, refresh the data from provider
    if (result != null && mounted) {
      debugPrint('✅ ClientDetailsScreen: Client ${result.name} updated successfully');

      // Refresh client provider to get latest data
      final clientProvider = context.read<ClientProvider>();
      await clientProvider.refresh();

      // Update local client data
      _refreshClientFromProvider();

      // Show success message
      if (mounted) {
        showTopSnackBar(context, 
          SnackBar(
            content: Text('Clientul ${result.name} a fost actualizat cu succes!'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }
    }
  }

  /// Show delete confirmation dialog
  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirmare Ștergere'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Ești sigur că vrei să ștergi clientul "${_currentClient?.name ?? widget.client.name}"?'),
              const SizedBox(height: 16),
              const Text(
                'Această acțiune va șterge:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              const Text('• Toate datele clientului'),
              const Text('• Toate animalele asociate'),
              const Text('• Istoricul programărilor'),
              const Text('• Recenziile și abonamentele'),
              const SizedBox(height: 16),
              Text(
                'Această acțiune nu poate fi anulată!',
                style: TextStyle(
                  color: AppTheme.getStatusColor(context, 'error'),
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Anulează'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deleteClient();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.getStatusColor(context, 'error'),
                foregroundColor: Theme.of(context).colorScheme.onError,
              ),
              child: const Text('Șterge'),
            ),
          ],
        );
      },
    );
  }

  /// Delete the client
  Future<void> _deleteClient() async {
    final clientId = _currentClient?.id ?? widget.client.id;
    final clientName = _currentClient?.name ?? widget.client.name;

    debugPrint('🗑️ ClientDetailsScreen: Starting client deletion process...');
    debugPrint('📍 ClientDetailsScreen: Client ID: $clientId');
    debugPrint('📍 ClientDetailsScreen: Client Name: $clientName');

    // Show loading dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Se șterge clientul...'),
            ],
          ),
        );
      },
    );

    try {
      // Delete client using ClientProvider
      final clientProvider = context.read<ClientProvider>();
      final success = await clientProvider.deleteClient(clientId);

      // Close loading dialog
      if (mounted) {
        Navigator.of(context).pop();
      }

      if (success && mounted) {
        debugPrint('✅ ClientDetailsScreen: Client deleted successfully');

        // Show success message
        showTopSnackBar(context, 
          SnackBar(
            content: Text('Clientul "$clientName" a fost șters cu succes!'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );

        // Navigate back to clients list
        Navigator.of(context).pop();
      } else if (mounted) {
        debugPrint('❌ ClientDetailsScreen: Failed to delete client');

        // Show error message
        showTopSnackBar(context, 
          SnackBar(
            content: Text('Eroare la ștergerea clientului "$clientName"'),
            backgroundColor: AppTheme.getStatusColor(context, 'error'),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ ClientDetailsScreen: Exception during client deletion: $e');

      // Close loading dialog
      if (mounted) {
        Navigator.of(context).pop();

        // Show error message
        showTopSnackBar(context, 
          SnackBar(
            content: Text('Eroare la ștergerea clientului: $e'),
            backgroundColor: AppTheme.getStatusColor(context, 'error'),
          ),
        );
      }
    }
  }

  void _showAddPetDialog() async {
    final result = await Navigator.of(context).push<Pet>(
      MaterialPageRoute(
        builder: (context) => AddPetScreen(
          clientId: widget.client.id,
          clientName: widget.client.name,
        ),
      ),
    );

    // If a pet was successfully added, refresh the pets list
    if (result != null) {
      _loadPets();
    }
  }

  @override
  Widget build(BuildContext context) {
    final displayClient = _currentClient ?? widget.client;

    return DetailScreenScaffold(
      headerContent: ClientHeaderWidget(client: displayClient),
      actions: [], // Removed edit button from top
      body: Column(
        children: [
          // Tabs section
          Expanded(
            child: Container(
              margin: const EdgeInsets.fromLTRB(16, 8, 16, 0),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
              ),
              clipBehavior: Clip.antiAlias, // Add this to ensure no overflow
              child: ClientTabsWidget(
                client: displayClient,
                appointments: _appointments,
                subscriptions: _subscriptions,
                reviews: _reviews,
                pets: _pets,
                isLoading: _isLoadingPets || _isLoadingAppointments,
                onAddPet: _showAddPetDialog,
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: SpeedDial(
        animatedIcon: AnimatedIcons.menu_close,
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        activeBackgroundColor: Theme.of(context).colorScheme.secondary,
        spacing: 3,
        childPadding: const EdgeInsets.all(5),
        spaceBetweenChildren: 4,
        tooltip: 'Acțiuni',
        children: [
          SpeedDialChild(
            child: const Icon(Icons.delete_forever),
            backgroundColor: AppTheme.getStatusColor(context, 'error'),
            foregroundColor: Theme.of(context).colorScheme.onError,
            label: 'Șterge Client',
            onTap: _showDeleteConfirmation,
          ),
          SpeedDialChild(
            child: const Icon(Icons.pets),
            backgroundColor: Theme.of(context).colorScheme.surface,
            foregroundColor: Theme.of(context).colorScheme.primary,
            label: 'Adaugă Animal',
            onTap: _showAddPetDialog,
          ),
          SpeedDialChild(
            child: const Icon(Icons.edit),
            backgroundColor: Theme.of(context).colorScheme.surface,
            foregroundColor: Theme.of(context).colorScheme.primary,
            label: 'Editează Client',
            onTap: _editClient,
          ),
          SpeedDialChild(
            child: const Icon(Icons.more_vert),
            backgroundColor: Theme.of(context).colorScheme.surface,
            foregroundColor: Theme.of(context).colorScheme.primary,
            label: 'Opțiuni Client',
            onTap: _showOptionsMenu,
          ),
        ],
      ),
    );
  }
}
