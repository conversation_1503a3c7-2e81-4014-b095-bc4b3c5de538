# Frontend Integration Plan: Min-Max Pricing Implementation

## Overview
Update the appointment booking flow and summary screens to properly handle the new min-max pricing strategy with dynamic price updates based on pet size selection.

## Components That Need Updates

### 1. Service Display Components
- `lib/widgets/new_appointment/multiple_services_widget.dart`
- `lib/widgets/new_appointment/service_time_selection_widget.dart`
- `lib/widgets/calendar_views/appointment_block.dart`
- `lib/widgets/dialogs/appointment_details_dialog.dart`

### 2. Pricing Calculation Components
- `lib/widgets/new_appointment/appointment_form_data.dart`
- `lib/widgets/new_appointment/appointment_footer.dart`
- `lib/services/appointment/appointment_service.dart`

### 3. Pet Selection Components
- `lib/widgets/new_appointment/pet_selection_widget.dart`
- `lib/widgets/new_appointment/appointment_form_logic.dart`

## Implementation Details

### 1. Enhanced Service Model Methods

Add new methods to `lib/models/service.dart`:

```dart
/// Get price for specific pet size
double getPriceForSize(String petSize) {
  if (sizePrices != null && sizePrices!.containsKey(petSize)) {
    return sizePrices![petSize]!;
  }
  return price;
}

/// Get price range for specific pet size
String getPriceRangeForSize(String petSize) {
  if (sizePrices != null && sizePrices!.containsKey(petSize)) {
    final basePrice = sizePrices![petSize]!;
    final minPrice = sizeMinPrices?[petSize];
    final maxPrice = sizeMaxPrices?[petSize];
    
    if (minPrice != null && maxPrice != null) {
      return '${minPrice.toStringAsFixed(2)} - ${maxPrice.toStringAsFixed(2)} RON';
    }
    return '${basePrice.toStringAsFixed(2)} RON';
  }
  
  // Fixed pricing
  if (minPrice != null && maxPrice != null) {
    return '${minPrice!.toStringAsFixed(2)} - ${maxPrice!.toStringAsFixed(2)} RON';
  }
  return '${price.toStringAsFixed(2)} RON';
}

/// Check if service has price ranges
bool get hasPriceRanges {
  return (minPrice != null && maxPrice != null) ||
         (sizeMinPrices != null && sizeMaxPrices != null);
}

/// Get formatted price for specific pet size
String getFormattedPriceForSize(String petSize) {
  return getPriceRangeForSize(petSize);
}
```

### 2. Update AppointmentFormData Pricing Logic

Modify `lib/widgets/new_appointment/appointment_form_data.dart`:

```dart
/// Enhanced price calculation with size-based ranges
double getTotalPriceFromDetailsWithSize(
  Map<String, Map<String, dynamic>> serviceDetails,
  String petSize,
) {
  return services.fold(0.0, (total, serviceName) {
    final details = serviceDetails[serviceName];
    if (details == null) return total + 50.0;
    
    // Create Service object from details
    final service = Service.fromJson(details);
    
    // Use the new size-based pricing method
    return total + service.getPriceForSize(petSize);
  });
}

/// Get price range text for current pet size
String getTotalPriceRangeText(
  Map<String, Map<String, dynamic>> serviceDetails,
) {
  if (services.isEmpty) return '0.00 RON';
  
  double minTotal = 0.0;
  double maxTotal = 0.0;
  bool hasRanges = false;
  
  for (final serviceName in services) {
    final details = serviceDetails[serviceName];
    if (details == null) {
      minTotal += 50.0;
      maxTotal += 50.0;
      continue;
    }
    
    final service = Service.fromJson(details);
    
    if (service.sizePrices != null && service.sizePrices!.containsKey(petSize)) {
      final basePrice = service.sizePrices![petSize]!;
      final minPrice = service.sizeMinPrices?[petSize];
      final maxPrice = service.sizeMaxPrices?[petSize];
      
      if (minPrice != null && maxPrice != null) {
        minTotal += minPrice;
        maxTotal += maxPrice;
        hasRanges = true;
      } else {
        minTotal += basePrice;
        maxTotal += basePrice;
      }
    } else {
      // Fixed pricing
      if (service.minPrice != null && service.maxPrice != null) {
        minTotal += service.minPrice!;
        maxTotal += service.maxPrice!;
        hasRanges = true;
      } else {
        minTotal += service.price;
        maxTotal += service.price;
      }
    }
  }
  
  if (hasRanges && minTotal != maxTotal) {
    return '${minTotal.toStringAsFixed(2)} - ${maxTotal.toStringAsFixed(2)} RON';
  }
  return '${minTotal.toStringAsFixed(2)} RON';
}
```

### 3. Update Service Selection Widget

Modify `lib/widgets/new_appointment/multiple_services_widget.dart`:

```dart
Widget _buildServiceChip(String service) {
  return Consumer<CalendarProvider>(
    builder: (context, calendarProvider, child) {
      final serviceDetails = calendarProvider.getServiceDetails();
      final details = serviceDetails[service];
      final duration = details?['duration'] ?? 60;
      
      // Create Service object to use new pricing methods
      final serviceObj = details != null ? Service.fromJson(details) : null;
      
      String priceText;
      if (serviceObj != null) {
        priceText = serviceObj.getFormattedPriceForSize(widget.formData.petSize);
      } else {
        priceText = '50.00 RON';
      }

      return Container(
        margin: const EdgeInsets.only(bottom: 8),
        child: Chip(
          label: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                service,
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
              Text(
                '${duration}min • $priceText',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
          // ... rest of chip configuration
        ),
      );
    },
  );
}
```

### 4. Update Appointment Footer

Modify `lib/widgets/new_appointment/appointment_footer.dart`:

```dart
@override
Widget build(BuildContext context) {
  return Consumer<CalendarProvider>(
    builder: (context, calendarProvider, child) {
      final serviceDetails = calendarProvider.getServiceDetails();
      final actualDuration = formData.getActualDuration();
      
      // Use new price range calculation
      final priceText = formData.getTotalPriceRangeText(serviceDetails);
      
      // ... rest of build method with priceText instead of fixed price
    },
  );
}
```

### 5. Pet Selection Change Handler

Update `lib/widgets/new_appointment/appointment_form_logic.dart`:

```dart
/// Enhanced pet selection handler with price updates
void handlePetSelection(AppointmentFormData formData, Pet pet) {
  final oldSize = formData.petSize;
  formData.updatePetData(pet);
  
  // If pet size changed, trigger price recalculation
  if (oldSize != formData.petSize) {
    _notifyPriceUpdate();
  }
}

/// Enhanced breed change handler with price updates
void handlePetBreedChange(AppointmentFormData formData, String breed) {
  final oldSize = formData.petSize;
  formData.petBreed = breed;
  formData.petSpecies = AppointmentFormConstants.getBreedSpecies(breed);
  formData.petSize = AppointmentFormConstants.getBreedSize(breed);
  
  // If pet size changed, trigger price recalculation
  if (oldSize != formData.petSize) {
    _notifyPriceUpdate();
  }
}

void _notifyPriceUpdate() {
  // Notify listeners that prices need to be recalculated
  // This will trigger UI updates in components that depend on pricing
}
```

### 6. Service Selection Dialog Updates

Update service selection in `multiple_services_widget.dart`:

```dart
return ListTile(
  title: Text(service),
  subtitle: Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(
        '${duration}min',
        style: TextStyle(
          color: Colors.grey[600],
          fontSize: 12,
        ),
      ),
      if (serviceObj != null) ...[
        Text(
          serviceObj.getFormattedPriceForSize(widget.formData.petSize),
          style: TextStyle(
            color: AppColors.forestGreen,
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    ],
  ),
  trailing: const Icon(Icons.add, color: AppColors.forestGreen),
  onTap: () async {
    Navigator.of(context).pop();
    await widget.onAddService(service);
  },
);
```

## Price Display Logic Summary

### Display Rules Implementation
1. **Single price services**: `"50.00 RON"`
2. **Price range services**: `"40.00 - 60.00 RON"`
3. **Size-based single prices**: `"S: 30.00 RON | M: 50.00 RON | L: 70.00 RON"`
4. **Size-based price ranges**: `"S: 25.00 - 35.00 RON | M: 45.00 - 55.00 RON | L: 65.00 - 75.00 RON"`

### Context-Aware Pricing
- **Service Selection**: Show price for current pet size
- **Appointment Summary**: Show total price range if any service has ranges
- **Calendar View**: Show base price or range indicator
- **Appointment Details**: Show actual price paid (single value)

## Testing Strategy

### Unit Tests
- Test Service model pricing methods
- Test AppointmentFormData price calculations
- Test pet size change handlers

### Widget Tests
- Test service chip price display
- Test appointment footer price updates
- Test service selection dialog pricing

### Integration Tests
- Test complete appointment flow with price ranges
- Test pet size changes triggering price updates
- Test backward compatibility with existing services

## Migration Notes

### Backward Compatibility
- All existing services continue to work without changes
- New pricing features are additive, not breaking
- Default behavior remains unchanged for services without ranges

### Performance Considerations
- Price calculations are lightweight and cached where possible
- UI updates are triggered only when pet size actually changes
- Service details are fetched once and reused throughout the flow
