class Client {
  final String id;
  final String name;
  final String phone;
  final String email;
  final String address;
  final DateTime registrationDate;
  final List<String> petIds; // References to pets
  final String notes;
  final bool isActive; // New field from API
  final int petCount; // New field from API
  final DateTime? updatedAt; // New field from API

  Client({
    required this.id,
    required this.name,
    required this.phone,
    this.email = '',
    this.address = '',
    required this.registrationDate,
    this.petIds = const [],
    this.notes = '',
    this.isActive = true,
    this.petCount = 0,
    this.updatedAt,
  });

  // Convert from JSON
  factory Client.fromJson(Map<String, dynamic> json) {
    return Client(
      id: json['id']?.toString() ?? json['clientId']?.toString() ?? '',
      name: json['name'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'] ?? '',
      address: json['address'] ?? '',
      registrationDate: DateTime.parse(json['registrationDate'] ?? json['createdAt'] ?? DateTime.now().toIso8601String()),
      petIds: List<String>.from(json['petIds'] ?? []),
      notes: json['notes'] ?? '',
      isActive: json['isActive'] ?? true,
      petCount: json['petCount'] ?? 0,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'email': email,
      'address': address,
      'registrationDate': registrationDate.toIso8601String(),
      'petIds': petIds,
      'notes': notes,
      'isActive': isActive,
      'petCount': petCount,
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  // Copy with method for immutable updates
  Client copyWith({
    String? id,
    String? name,
    String? phone,
    String? email,
    String? address,
    DateTime? registrationDate,
    List<String>? petIds,
    String? notes,
    bool? isActive,
    int? petCount,
    DateTime? updatedAt,
  }) {
    return Client(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      registrationDate: registrationDate ?? this.registrationDate,
      petIds: petIds ?? this.petIds,
      notes: notes ?? this.notes,
      isActive: isActive ?? this.isActive,
      petCount: petCount ?? this.petCount,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
