#!/bin/bash
# Test script for automatic deployment setup
# This script validates that all components are properly configured

set -e

echo "🧪 Testing Automatic Deployment Setup"
echo "====================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    print_error "Not in Flutter project root. Please run from flutter-partykids directory."
    exit 1
fi

print_info "Starting validation tests..."
echo ""

# Test 1: Check Flutter installation
echo "🔍 Test 1: Flutter Installation"
if command -v flutter &> /dev/null; then
    FLUTTER_VERSION=$(flutter --version | head -n 1)
    print_success "Flutter is installed: $FLUTTER_VERSION"
else
    print_error "Flutter is not installed or not in PATH"
    exit 1
fi
echo ""

# Test 2: Check project structure
echo "🔍 Test 2: Project Structure"
required_files=(
    "pubspec.yaml"
    "ios/fastlane/Fastfile"
    "ios/fastlane/Appfile"
    "ios/Gemfile"
    "../.github/workflows/auto-deploy-main.yml"
    "scripts/deploy-ios.sh"
)

for file in "${required_files[@]}"; do
    if [ -f "$file" ]; then
        print_success "Found: $file"
    else
        print_error "Missing: $file"
        exit 1
    fi
done
echo ""

# Test 3: Check Fastlane configuration
echo "🔍 Test 3: Fastlane Configuration"
cd ios

if command -v bundle &> /dev/null; then
    print_success "Bundler is installed"
else
    print_error "Bundler is not installed. Run: gem install bundler"
    exit 1
fi

if bundle check &> /dev/null; then
    print_success "Fastlane dependencies are installed"
else
    print_warning "Fastlane dependencies need to be installed"
    print_info "Run: cd ios && bundle install"
fi

# Check if deploy_main lane exists
if bundle exec fastlane lanes 2>/dev/null | grep -q "deploy_main"; then
    print_success "deploy_main lane is configured"
else
    print_error "deploy_main lane not found in Fastfile"
    exit 1
fi

cd ..
echo ""

# Test 4: Check GitHub workflow
echo "🔍 Test 4: GitHub Workflow"
workflow_file="../.github/workflows/auto-deploy-main.yml"

if grep -q "deploy_main" "$workflow_file"; then
    print_success "Workflow uses deploy_main lane"
else
    print_error "Workflow doesn't use deploy_main lane"
    exit 1
fi

if grep -q "APP_STORE_CONNECT_API_KEY" "$workflow_file"; then
    print_success "Workflow configured for App Store Connect API"
else
    print_error "Workflow missing App Store Connect API configuration"
    exit 1
fi
echo ""

# Test 5: Check deployment script
echo "🔍 Test 5: Deployment Script"
deploy_script="scripts/deploy-ios.sh"

if [ -x "$deploy_script" ]; then
    print_success "Deployment script is executable"
else
    print_warning "Deployment script is not executable"
    print_info "Run: chmod +x $deploy_script"
fi

if grep -q "deploy_main" "$deploy_script"; then
    print_success "Deployment script supports automatic mode"
else
    print_error "Deployment script missing automatic mode support"
    exit 1
fi
echo ""

# Test 6: Check environment variables (if in CI)
echo "🔍 Test 6: Environment Variables"
if [ "$CI" = "true" ]; then
    required_vars=(
        "APP_STORE_CONNECT_API_KEY_KEY_ID"
        "APP_STORE_CONNECT_API_KEY_ISSUER_ID"
        "APP_STORE_CONNECT_API_KEY_KEY_FILEPATH"
    )
    
    for var in "${required_vars[@]}"; do
        if [ -n "${!var}" ]; then
            print_success "$var is set"
        else
            print_error "$var is not set"
        fi
    done
else
    print_info "Not in CI environment - skipping environment variable checks"
    print_info "In CI, these variables should be set:"
    print_info "  - APP_STORE_CONNECT_API_KEY_KEY_ID"
    print_info "  - APP_STORE_CONNECT_API_KEY_ISSUER_ID"
    print_info "  - APP_STORE_CONNECT_API_KEY_KEY_FILEPATH"
fi
echo ""

# Test 7: Validate pubspec.yaml
echo "🔍 Test 7: Pubspec Configuration"
if grep -q "version:" pubspec.yaml; then
    VERSION=$(grep "version:" pubspec.yaml | head -n 1)
    print_success "Version found: $VERSION"
    
    if echo "$VERSION" | grep -q "+"; then
        print_success "Build number format is correct"
    else
        print_warning "Build number might not be in correct format (should include +)"
    fi
else
    print_error "No version found in pubspec.yaml"
    exit 1
fi
echo ""

# Test 8: Check iOS configuration
echo "🔍 Test 8: iOS Configuration"
ios_config_files=(
    "ios/Runner.xcodeproj/project.pbxproj"
    "ios/Runner/Info.plist"
    "ios/Podfile"
)

for file in "${ios_config_files[@]}"; do
    if [ -f "$file" ]; then
        print_success "Found: $(basename "$file")"
    else
        print_error "Missing: $file"
        exit 1
    fi
done

# Check bundle identifier
if grep -q "com.partykids.partykidsapp" ios/Runner.xcodeproj/project.pbxproj; then
    print_success "Bundle identifier is configured correctly"
else
    print_warning "Bundle identifier might not be configured correctly"
fi
echo ""

# Summary
echo "📋 Test Summary"
echo "==============="
print_success "All validation tests passed!"
echo ""
print_info "Next steps:"
echo "1. Ensure GitHub secrets are configured:"
echo "   - APP_STORE_CONNECT_KEY_ID"
echo "   - APP_STORE_CONNECT_ISSUER_ID"
echo "   - APP_STORE_CONNECT_API_KEY"
echo ""
echo "2. Test the deployment:"
echo "   - Push a change to main branch"
echo "   - Or run manually: ./scripts/deploy-ios.sh --auto"
echo ""
echo "3. Monitor the deployment:"
echo "   - GitHub Actions: Repository → Actions tab"
echo "   - App Store Connect: https://appstoreconnect.apple.com"
echo ""
print_success "Automatic deployment setup is ready! 🚀"
