import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

/// Utility function to show a SnackBar from the top of the screen.
///
/// This builds a floating SnackBar positioned below the status bar and
/// toolbar so it does not block navigation widgets at the bottom of the
/// screen.
///
/// This function includes safety checks to prevent the "Looking up a deactivated
/// widget's ancestor" error by checking if the context is still mounted and valid.
ScaffoldFeatureController<SnackBar, SnackBarClosedReason>? showTopSnackBar(
  BuildContext context,
  SnackBar snackBar,
) {
  // Safety check: ensure the context is still valid and mounted
  try {
    // Check if the context has a valid widget tree
    final element = context as Element?;
    if (element == null || !element.mounted) {
      debugPrint('⚠️ showTopSnackBar: Context is not mounted, skipping snackbar');
      return null;
    }

    // Check if we can access MediaQuery (indicates valid widget tree)
    final mediaQuery = MediaQuery.maybeOf(context);
    if (mediaQuery == null) {
      debugPrint('⚠️ showTopSnackBar: MediaQuery not available, skipping snackbar');
      return null;
    }

    // Check if ScaffoldMessenger is available
    final scaffoldMessenger = ScaffoldMessenger.maybeOf(context);
    if (scaffoldMessenger == null) {
      debugPrint('⚠️ showTopSnackBar: ScaffoldMessenger not available, skipping snackbar');
      return null;
    }

    final topPadding = mediaQuery.viewPadding.top + kToolbarHeight + 16;
    return scaffoldMessenger.showSnackBar(
      SnackBar(
        content: snackBar.content,
        backgroundColor: snackBar.backgroundColor,
        action: snackBar.action,
        duration: snackBar.duration,
        behavior: SnackBarBehavior.floating,
        margin: EdgeInsets.fromLTRB(16, topPadding, 16, 0),
        shape: snackBar.shape,
      ),
    );
  } catch (e) {
    debugPrint('⚠️ showTopSnackBar error: $e');
    return null;
  }
}

/// Safe version of showTopSnackBar that defers execution to the next frame
/// to avoid issues with disposed contexts during navigation.
void showTopSnackBarSafe(
  BuildContext context,
  SnackBar snackBar,
) {
  SchedulerBinding.instance.addPostFrameCallback((_) {
    showTopSnackBar(context, snackBar);
  });
}
