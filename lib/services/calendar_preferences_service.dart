import 'package:shared_preferences/shared_preferences.dart';

/// Calendar view mode options
enum CalendarHourViewMode {
  businessHours,  // Show only business hours
  fullDay,        // Show all 24 hours
}

/// Service for managing calendar-related user preferences
class CalendarPreferencesService {
  static const String _viewModeKey = 'calendar_view_mode';
  static const String _showFullDayKey = 'calendar_show_full_day';
  static const String _timeSlotHeightKey = 'calendar_time_slot_height';

  /// Get the saved calendar hour view mode
  static Future<CalendarHourViewMode> getHourViewMode() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedMode = prefs.getString(_viewModeKey);
      
      if (savedMode != null) {
        return CalendarHourViewMode.values.firstWhere(
          (mode) => mode.toString() == savedMode,
          orElse: () => CalendarHourViewMode.businessHours,
        );
      }
      
      return CalendarHourViewMode.businessHours;
    } catch (e) {
      return CalendarHourViewMode.businessHours;
    }
  }

  /// Save the calendar hour view mode
  static Future<void> setHourViewMode(CalendarHourViewMode mode) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_viewModeKey, mode.toString());
    } catch (e) {
      // Silently fail - not critical
    }
  }

  /// Get the legacy show full day preference (for migration)
  static Future<bool> getShowFullDay() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool(_showFullDayKey) ?? false;
    } catch (e) {
      return false;
    }
  }

  /// Get the saved time slot height
  static Future<double> getTimeSlotHeight() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getDouble(_timeSlotHeightKey) ?? 80.0; // Updated default to 80px for better visibility
    } catch (e) {
      return 80.0; // Updated default to 80px for better visibility
    }
  }

  /// Save the time slot height
  static Future<void> setTimeSlotHeight(double height) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setDouble(_timeSlotHeightKey, height);
    } catch (e) {
      // Silently fail - not critical
    }
  }

  /// Migrate legacy preference to new system
  static Future<void> migrateLegacyPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final showFullDay = prefs.getBool(_showFullDayKey);

      if (showFullDay != null) {
        // Migrate to new system
        final newMode = showFullDay
            ? CalendarHourViewMode.fullDay
            : CalendarHourViewMode.businessHours;
        await setHourViewMode(newMode);

        // Remove legacy preference
        await prefs.remove(_showFullDayKey);
      }
    } catch (e) {
      // Silently fail - not critical
    }
  }
}
