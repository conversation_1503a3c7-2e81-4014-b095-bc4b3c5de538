/// Centralized string constants for consistent messaging throughout the app
class AppStrings {
  // Common actions
  static const String save = 'Salvează';
  static const String cancel = 'Anulează';
  static const String delete = 'Șterge';
  static const String edit = 'Editează';
  static const String add = 'Adaugă';


  static const String create = 'Creează';
  static const String update = 'Actualizează';
  static const String confirm = 'Confirmă';
  static const String retry = 'Încearcă din nou';
  static const String close = 'Închide';
  static const String back = 'Înapoi';
  static const String next = 'Următorul';
  static const String previous = 'Anterior';
  static const String done = 'Gata';
  static const String ok = 'OK';
  static const String yes = 'Da';
  static const String no = 'Nu';
  // Loading states

  static const String loading = 'Se încarcă...';
  static const String loadingData = 'Se încarcă datele...';
  static const String loadingClients = 'Se încarcă clienții...';
  static const String loadingAppointments = 'Se încarcă programările...';
  static const String loadingServices = 'Se încarcă serviciile...';
  static const String loadingInformation = 'Se încarcă informațiile...';
  static const String processing = 'Se procesează...';
  static const String saving = 'Se salvează...';
  static const String updating = 'Se actualizează...';
  static const String deleting = 'Se șterge...';

  // Empty states
  static const String noData = 'Nu există date';
  static const String noClients = 'Nu există clienți înregistrați';
  static const String noAppointments = 'Nu există programări';
  static const String noServices = 'Nu există servicii';
  static const String noPets = 'Nu există animale înregistrate';
  static const String noSubscriptions = 'Nu există abonamente';
  static const String noReviews = 'Nu există recenzii';
  static const String noNotifications = 'Nu există notificări';
  static const String noResults = 'Nu s-au găsit rezultate';
  static const String noResultsFound = 'Nu s-au găsit rezultate pentru căutarea dvs.';

  // Error messages
  static const String error = 'Eroare';
  static const String errorGeneral = 'A apărut o eroare neașteptată';
  static const String errorNetwork = 'Eroare de conexiune';
  static const String errorServer = 'Eroare de server';
  static const String errorLoadingData = 'Eroare la încărcarea datelor';
  static const String errorSavingData = 'Eroare la salvarea datelor';
  static const String errorUpdatingData = 'Eroare la actualizarea datelor';
  static const String errorDeletingData = 'Eroare la ștergerea datelor';
  static const String errorAuthentication = 'Eroare de autentificare';
  static const String errorPermission = 'Nu aveți permisiunea necesară';
  static const String errorValidation = 'Datele introduse nu sunt valide';
  static const String errorRequired = 'Acest câmp este obligatoriu';
  static const String errorInvalidEmail = 'Adresa de email nu este validă';
  static const String errorInvalidPhone = 'Numărul de telefon nu este valid';

  // Success messages
  static const String success = 'Succes';
  static const String successSaved = 'Salvat cu succes';
  static const String successUpdated = 'Actualizat cu succes';
  static const String successDeleted = 'Șters cu succes';
  static const String successCreated = 'Creat cu succes';

  // Authentication
  static const String login = 'Autentificare';
  static const String logout = 'Deconectare';
  static const String register = 'Înregistrare';
  static const String forgotPassword = 'Ai uitat parola?';
  static const String resetPassword = 'Resetează parola';
  static const String changePassword = 'Schimbă parola';
  static const String currentPassword = 'Parola curentă';
  static const String newPassword = 'Parola nouă';
  static const String confirmPassword = 'Confirmă parola';
  static const String email = 'Email';
  static const String password = 'Parolă';
  static const String phoneNumber = 'Număr de telefon';

  // Navigation
  static const String home = 'Acasă';
  static const String calendar = 'Calendar';
  static const String clients = 'Clienți';
  static const String services = 'Servicii';
  static const String reports = 'Rapoarte';
  static const String settings = 'Setări';
  static const String profile = 'Profil';
  static const String notifications = 'Notificări';
  static const String team = 'Echipa';

  // Client management
  static const String client = 'Client';
  static const String clientName = 'Nume client';
  static const String clientPhone = 'Telefon client';
  static const String clientEmail = 'Email client';
  static const String clientAddress = 'Adresa client';
  static const String clientNotes = 'Notițe client';
  static const String addClient = 'Adaugă client';
  static const String editClient = 'Editează client';
  static const String deleteClient = 'Șterge client';
  static const String clientDetails = 'Detalii client';
  static const String clientHistory = 'Istoricul clientului';

  // Pet management
  static const String pet = 'Animal';
  static const String petName = 'Nume animal';
  static const String petSpecies = 'Specia';
  static const String petBreed = 'Rasa';
  static const String petAge = 'Vârsta';
  static const String petWeight = 'Greutatea';
  static const String petColor = 'Culoarea';
  static const String petNotes = 'Notițe animal';
  static const String addPet = 'Adaugă animal';
  static const String editPet = 'Editează animal';
  static const String deletePet = 'Șterge animal';
  static const String petDetails = 'Detalii animal';

  // Appointment management
  static const String appointment = 'Programare';
  static const String appointments = 'Programări';
  static const String newAppointment = 'Programare nouă';
  static const String editAppointment = 'Editează programarea';
  static const String deleteAppointment = 'Șterge programarea';
  static const String appointmentDate = 'Data programării';
  static const String appointmentTime = 'Ora programării';
  static const String appointmentDuration = 'Durata';
  static const String appointmentService = 'Serviciu';
  static const String appointmentNotes = 'Notițe programare';
  static const String appointmentStatus = 'Status programare';

  // Service management
  static const String service = 'Serviciu';
  static const String serviceName = 'Nume serviciu';
  static const String serviceDescription = 'Descriere serviciu';
  static const String servicePrice = 'Preț serviciu';
  static const String serviceDuration = 'Durata serviciului';
  static const String serviceCategory = 'Categoria serviciului';
  static const String addService = 'Adaugă serviciu';
  static const String editService = 'Editează serviciu';
  static const String deleteService = 'Șterge serviciu';

  // Time and date
  static const String today = 'Astăzi';
  static const String tomorrow = 'Mâine';
  static const String yesterday = 'Ieri';
  static const String thisWeek = 'Săptămâna aceasta';
  static const String nextWeek = 'Săptămâna viitoare';
  static const String thisMonth = 'Luna aceasta';
  static const String nextMonth = 'Luna viitoare';

  // Status labels
  static const String active = 'Activ';
  static const String inactive = 'Inactiv';
  static const String pending = 'În așteptare';
  static const String completed = 'Finalizat';
  static const String cancelled = 'Anulat';
  static const String confirmed = 'Confirmat';
  static const String scheduled = 'Programat';

  // Search and filter
  static const String search = 'Căutare';
  static const String searchHint = 'Căutați...';
  static const String filter = 'Filtrare';
  static const String filterBy = 'Filtrează după';
  static const String sortBy = 'Sortează după';
  static const String clearFilters = 'Șterge filtrele';
  static const String noFiltersApplied = 'Nu sunt aplicate filtre';

  // Validation messages
  static const String fieldRequired = 'Acest câmp este obligatoriu';
  static const String invalidEmailFormat = 'Formatul email-ului nu este valid';
  static const String invalidPhoneFormat = 'Formatul numărului de telefon nu este valid';
  static const String passwordTooShort = 'Parola trebuie să aibă cel puțin 6 caractere';
  static const String passwordsDoNotMatch = 'Parolele nu se potrivesc';

  // Staff schedule validation
  static const String scheduleValidationTitle = 'Conflict cu programul salonului';
  static const String scheduleValidationMessage = 'Programul nu respectă restricțiile salonului';
  static const String businessHoursTitle = 'Program salon';
  static const String staffCannotWorkWhenClosed = 'Nu puteți lucra când salonul este închis';
  static const String staffEndTimeAfterBusiness = 'Ora de sfârșit depășește programul salonului';
  static const String staffStartTimeBeforeBusiness = 'Ora de început este înainte de programul salonului';
  static const String invalidTimeFormat = 'Format oră invalid. Folosiți formatul HH:MM';
  static const String startTimeBeforeEndTime = 'Ora de început trebuie să fie înainte de ora de sfârșit';
  static const String breakTimeWithinWorkingHours = 'Pauza trebuie să fie în intervalul orelor de lucru';
  static const String closed = 'Închis';

  // Confirmation dialogs
  static const String confirmDelete = 'Confirmați ștergerea';
  static const String confirmDeleteMessage = 'Sunteți sigur că doriți să ștergeți acest element?';
  static const String confirmCancel = 'Confirmați anularea';
  static const String confirmCancelMessage = 'Sunteți sigur că doriți să anulați? Modificările nesalvate se vor pierde.';

  // Units and formatting
  static const String minutes = 'minute';
  static const String hours = 'ore';
  static const String days = 'zile';
  static const String currency = 'RON';
  static const String phonePrefix = '+40';

  // Day names
  static const String monday = 'Luni';
  static const String tuesday = 'Marți';
  static const String wednesday = 'Miercuri';
  static const String thursday = 'Joi';
  static const String friday = 'Vineri';
  static const String saturday = 'Sâmbătă';
  static const String sunday = 'Duminică';

  // Accessibility
  static const String accessibilityBack = 'Înapoi';
  static const String accessibilityMenu = 'Meniu';
  static const String accessibilityClose = 'Închide';
  static const String accessibilitySearch = 'Căutare';
  static const String accessibilityFilter = 'Filtrare';

  // Suggestions for grooming services
  static const List<String> groomingServiceSuggestions = [
    'Tuns complet',
    'Tuns igienic',
    'Spălat simplu',
    'Spălat și uscat',
    'Spălat cu șampon special',
    'Pachet complet',
    'Pachet puppy',
    'Pachet senior',
    'Tăiat gheruțe',
    'Descalcit blană',
    'Periaj intensiv',
    'Curățare urechi',
    'Curățare ochi',
    'Tratament blană albă',
    'Carding',
    'Tratament antiparazitar',
    'Coafat special',
    'Trimming păr facial',
    'Detartraj dentar',
    'Parfum special',
  ];


  // Dynamic messages
  static String launchError(String action) => 'Nu s-a putut deschide $action';

  // Scheduling conflicts
  static const String schedulingConflict = 'Intervalul solicitat nu este disponibil';
}
