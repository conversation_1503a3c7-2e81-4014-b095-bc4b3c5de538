import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/promotional_package.dart';
import '../../models/service.dart';
import '../../services/promotional_package_service.dart';
import '../../services/service_management_service.dart';
import '../../config/theme/app_theme.dart';

class PromotionalPackageFormDialog extends StatefulWidget {
  final PromotionalPackage? package; // null for create, non-null for edit
  final void Function(PromotionalPackage) onPackageSaved;

  const PromotionalPackageFormDialog({
    super.key,
    this.package,
    required this.onPackageSaved,
  });

  @override
  State<PromotionalPackageFormDialog> createState() => _PromotionalPackageFormDialogState();
}

class _PromotionalPackageFormDialogState extends State<PromotionalPackageFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _originalPriceController = TextEditingController();
  final _displayOrderController = TextEditingController();

  final _nameFocus = FocusNode();
  final _descriptionFocus = FocusNode();
  final _priceFocus = FocusNode();
  final _originalPriceFocus = FocusNode();
  final _displayOrderFocus = FocusNode();

  List<Service> availableServices = [];
  List<String> selectedServiceIds = [];
  bool _isActive = true;
  bool _isLoading = false;
  bool _isLoadingServices = true;
  String? _servicesError;

  @override
  void initState() {
    super.initState();
    _initializeForm();
    _loadServices();
  }

  void _initializeForm() {
    if (widget.package != null) {
      final package = widget.package!;
      _nameController.text = package.name;
      _descriptionController.text = package.description;
      _priceController.text = package.price.toStringAsFixed(2);
      if (package.originalPrice != null) {
        _originalPriceController.text = package.originalPrice!.toStringAsFixed(2);
      }
      _displayOrderController.text = package.displayOrder.toString();
      selectedServiceIds = List.from(package.serviceIds);
      _isActive = package.isActive;
    } else {
      _displayOrderController.text = '0';
    }
  }

  Future<void> _loadServices() async {
    setState(() {
      _isLoadingServices = true;
      _servicesError = null;
    });

    try {
      final response = await ServiceManagementService.getServices(isActive: true);
      if (response.success && response.data != null) {
        setState(() {
          availableServices = response.data!;
          _isLoadingServices = false;
        });
      } else {
        setState(() {
          _servicesError = response.error ?? 'Failed to load services';
          _isLoadingServices = false;
        });
      }
    } catch (e) {
      setState(() {
        _servicesError = 'Error loading services: $e';
        _isLoadingServices = false;
      });
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _originalPriceController.dispose();
    _displayOrderController.dispose();
    _nameFocus.dispose();
    _descriptionFocus.dispose();
    _priceFocus.dispose();
    _originalPriceFocus.dispose();
    _displayOrderFocus.dispose();
    super.dispose();
  }

  Future<void> _savePackage() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (selectedServiceIds.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Selectează cel puțin un serviciu pentru pachet'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _isLoading = true);

    try {
      final package = PromotionalPackage(
        id: widget.package?.id ?? '',
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        serviceIds: selectedServiceIds,
        price: double.parse(_priceController.text),
        originalPrice: _originalPriceController.text.isNotEmpty 
            ? double.parse(_originalPriceController.text)
            : null,
        isActive: _isActive,
        displayOrder: int.parse(_displayOrderController.text),
        createdAt: widget.package?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final response = widget.package == null
          ? await PromotionalPackageService.createPackage(package)
          : await PromotionalPackageService.updatePackage(widget.package!.id, package);

      if (response.success && response.data != null) {
        widget.onPackageSaved(response.data!);
        if (mounted) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(widget.package == null 
                  ? 'Pachet creat cu succes!' 
                  : 'Pachet actualizat cu succes!'),
              backgroundColor: AppColors.forestGreen,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(response.error ?? 'Eroare la salvarea pachetului'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Eroare: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      widget.package == null ? 'Pachet Nou' : 'Editează Pachetul',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onPrimary,
                      ),
                    ),
                  ),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                ],
              ),
            ),
            // Form content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Package Name
                      TextFormField(
                        controller: _nameController,
                        focusNode: _nameFocus,
                        decoration: InputDecoration(
                          labelText: 'Nume Pachet *',
                          hintText: 'ex. Pachet Complet Grooming',
                          prefixIcon: const Icon(Icons.card_giftcard),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Numele pachetului este obligatoriu';
                          }
                          if (value.trim().length < 3) {
                            return 'Numele trebuie să aibă cel puțin 3 caractere';
                          }
                          return null;
                        },
                        onFieldSubmitted: (_) => _descriptionFocus.requestFocus(),
                      ),
                      const SizedBox(height: 16),

                      // Description
                      TextFormField(
                        controller: _descriptionController,
                        focusNode: _descriptionFocus,
                        maxLines: 3,
                        decoration: InputDecoration(
                          labelText: 'Descriere',
                          hintText: 'Descrierea pachetului promoțional',
                          prefixIcon: const Icon(Icons.description),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        onFieldSubmitted: (_) => _priceFocus.requestFocus(),
                      ),
                      const SizedBox(height: 16),

                      // Price
                      TextFormField(
                        controller: _priceController,
                        focusNode: _priceFocus,
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                        ],
                        decoration: InputDecoration(
                          labelText: 'Preț Pachet (RON) *',
                          hintText: '0.00',
                          prefixIcon: const Icon(Icons.attach_money),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Prețul este obligatoriu';
                          }
                          final price = double.tryParse(value);
                          if (price == null || price <= 0) {
                            return 'Prețul trebuie să fie un număr pozitiv';
                          }
                          return null;
                        },
                        onFieldSubmitted: (_) => _originalPriceFocus.requestFocus(),
                      ),
                      const SizedBox(height: 16),

                      // Original Price (optional)
                      TextFormField(
                        controller: _originalPriceController,
                        focusNode: _originalPriceFocus,
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                        ],
                        decoration: InputDecoration(
                          labelText: 'Preț Original (RON)',
                          hintText: 'Pentru afișarea reducerii',
                          prefixIcon: const Icon(Icons.local_offer),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        validator: (value) {
                          if (value != null && value.trim().isNotEmpty) {
                            final originalPrice = double.tryParse(value);
                            if (originalPrice == null || originalPrice <= 0) {
                              return 'Prețul original trebuie să fie un număr pozitiv';
                            }
                            final currentPrice = double.tryParse(_priceController.text);
                            if (currentPrice != null && originalPrice <= currentPrice) {
                              return 'Prețul original trebuie să fie mai mare decât prețul pachetului';
                            }
                          }
                          return null;
                        },
                        onFieldSubmitted: (_) => _displayOrderFocus.requestFocus(),
                      ),
                      const SizedBox(height: 16),

                      // Display Order
                      TextFormField(
                        controller: _displayOrderController,
                        focusNode: _displayOrderFocus,
                        keyboardType: TextInputType.number,
                        inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                        decoration: InputDecoration(
                          labelText: 'Ordine Afișare',
                          hintText: '0 = primul, 1 = al doilea, etc.',
                          prefixIcon: const Icon(Icons.sort),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        onFieldSubmitted: (_) => _savePackage(),
                      ),
                      const SizedBox(height: 20),

                      // Services Selection
                      _buildServicesSection(),
                      const SizedBox(height: 20),

                      // Active Status
                      _buildActiveStatusSection(),
                    ],
                  ),
                ),
              ),
            ),
            // Bottom actions
            _buildBottomActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildServicesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Servicii Incluse *',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        if (_isLoadingServices)
          const Center(child: CircularProgressIndicator())
        else if (_servicesError != null)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red.shade200),
            ),
            child: Column(
              children: [
                Text(
                  'Eroare la încărcarea serviciilor',
                  style: TextStyle(
                    color: Colors.red.shade700,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _servicesError!,
                  style: TextStyle(color: Colors.red.shade600),
                ),
                const SizedBox(height: 8),
                ElevatedButton(
                  onPressed: _loadServices,
                  child: const Text('Încearcă din nou'),
                ),
              ],
            ),
          )
        else if (availableServices.isEmpty)
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange.shade200),
            ),
            child: Text(
              'Nu ai servicii active. Creează servicii înainte de a crea pachete.',
              style: TextStyle(color: Colors.orange.shade700),
            ),
          )
        else
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey.shade300),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                // Select All / Deselect All
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade50,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          '${selectedServiceIds.length} din ${availableServices.length} servicii selectate',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.grey.shade700,
                          ),
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          setState(() {
                            if (selectedServiceIds.length == availableServices.length) {
                              selectedServiceIds.clear();
                            } else {
                              selectedServiceIds = availableServices.map((s) => s.id).toList();
                            }
                          });
                        },
                        child: Text(
                          selectedServiceIds.length == availableServices.length
                              ? 'Deselectează tot'
                              : 'Selectează tot',
                        ),
                      ),
                    ],
                  ),
                ),
                // Services list
                SizedBox(
                  height: 200,
                  child: ListView.builder(
                    itemCount: availableServices.length,
                    itemBuilder: (context, index) {
                      final service = availableServices[index];
                      final isSelected = selectedServiceIds.contains(service.id);

                      return CheckboxListTile(
                        value: isSelected,
                        onChanged: (bool? value) {
                          setState(() {
                            if (value == true) {
                              selectedServiceIds.add(service.id);
                            } else {
                              selectedServiceIds.remove(service.id);
                            }
                          });
                        },
                        title: Text(service.name),
                        subtitle: Text(
                          '${service.formattedPrice} • ${service.formattedDuration}',
                          style: TextStyle(color: Colors.grey.shade600),
                        ),
                        dense: true,
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildActiveStatusSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.visibility,
            color: Colors.grey.shade600,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Status Pachet',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
                Text(
                  _isActive
                      ? 'Pachetul este activ și vizibil clienților'
                      : 'Pachetul este inactiv și nu este vizibil clienților',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _isActive,
            onChanged: (value) => setState(() => _isActive = value),
            activeColor: AppColors.forestGreen,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(12),
          bottomRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(
              onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
              child: const Text('Anulează'),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: ElevatedButton(
              onPressed: _isLoading ? null : _savePackage,
              child: _isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : Text(widget.package == null ? 'Creează' : 'Salvează'),
            ),
          ),
        ],
      ),
    );
  }
}
