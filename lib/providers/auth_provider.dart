import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
// import 'package:flutter_facebook_auth/flutter_facebook_auth.dart'; // Removed Facebook auth
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:google_sign_in/google_sign_in.dart';
import '../services/auth/auth_service.dart';
import '../services/auth/auth_service_adapter.dart';
import '../services/firebase_config.dart';
import '../services/auth/phone_auth_service.dart';
import '../services/notification_handler.dart';
import '../config/environment.dart';
import '../utils/formatters/phone_number_utils.dart';
import 'role_provider.dart';

enum AuthStatus {
  initial,
  authenticated,
  unauthenticated,
  authenticating,
  error,
}

class AuthProvider extends ChangeNotifier {
  final AuthServiceAdapter _authService;
  AuthStatus _status = AuthStatus.initial;
  Map<String, dynamic>? _user;
  String? _error;

  // Phone authentication state
  String? _verificationId;
  bool _isCodeSent = false;
  PhoneAuthService? _phoneAuthService;

  // Role provider reference for triggering role initialization
  RoleProvider? _roleProvider;

  AuthProvider({AuthServiceAdapter? authService})
      : _authService = authService ?? const DefaultAuthServiceAdapter();

  AuthStatus get status => _status;
  bool get isAuthenticated => _status == AuthStatus.authenticated;
  String? get error => _error;
  Map<String, dynamic>? get user => _user;
  bool get isCodeSent => _isCodeSent;

  // Initialize auth state
  Future<void> initialize() async {
    _status = AuthStatus.authenticating;
    notifyListeners();

    try {
      debugPrint('🔍 AuthProvider: Checking stored authentication...');

      if (AuthService.isAuthenticated()) {
        debugPrint('🔍 AuthProvider: Found stored tokens, checking with server...');

        // Since AuthService.initializeAuth() was already called in main.dart,
        // we can trust that the token validation was already done there.
        // Just get user profile to populate user data.
        final response = await AuthService.getCurrentUser();
        if (response.success && response.data != null) {
          _user = response.data;
          _status = AuthStatus.authenticated;
          debugPrint('✅ AuthProvider: Authentication validated successfully');
        } else {
          debugPrint('❌ AuthProvider: User profile not found, account may be deleted');
          await AuthService.logout();
          _status = AuthStatus.unauthenticated;
          _error = 'Account not found. Please sign in again.';
        }
      } else {
        debugPrint('🔍 AuthProvider: No stored authentication found');
        _status = AuthStatus.unauthenticated;
      }
    } catch (e) {
      debugPrint('❌ AuthProvider: Error during initialization: $e');
      // Clear any potentially corrupted auth data
      await AuthService.logout();
      _status = AuthStatus.unauthenticated;
      _error = 'Authentication error. Please sign in again.';
    }

    notifyListeners();
  }

  // Login with email and password
  Future<bool> login(String email, String password) async {
    _status = AuthStatus.authenticating;
    _error = null;
    notifyListeners();

    try {
      final response =
          await _authService.login(email: email, password: password);

        if (response.success) {
          _user = response.data;
          _status = AuthStatus.authenticated;
          notifyListeners();

          // Trigger role initialization after successful authentication
          await _initializeRoles();

          // Automatically register FCM token now that the user is logged in
          try {
            await NotificationHandler.registerTokenAfterLogin();
          } catch (_) {
            // Ignore FCM errors during login
          }

          return true;
        } else {
        _status = AuthStatus.error;
        _error = response.error ?? 'Login failed';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _status = AuthStatus.error;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Register new user
  Future<bool> register({
    required String name,
    required String email,
    required String password,
  }) async {
    _status = AuthStatus.authenticating;
    _error = null;
    notifyListeners();

    try {
      final response = await _authService.register(
        name: name,
        email: email,
        password: password,
      );

      if (response.success) {
        _user = response.data;
        _status = AuthStatus.authenticated;
        notifyListeners();

        // Trigger role initialization after successful authentication
        await _initializeRoles();

        return true;
      } else {
        _status = AuthStatus.error;
        _error = response.error ?? 'Registration failed';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _status = AuthStatus.error;
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Forgot password
  Future<bool> forgotPassword(String email) async {
    try {
      final response = await AuthService.forgotPassword(email);

      if (!response.success) {
        _error = response.error ?? 'Failed to send reset email';
        notifyListeners();
      }

      return response.success;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Change password
  Future<bool> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final response = await AuthService.changePassword(
        currentPassword: currentPassword,
        newPassword: newPassword,
      );

      if (!response.success) {
        _error = response.error ?? 'Failed to change password';
        notifyListeners();
      }

      return response.success;
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Update profile
  Future<bool> updateProfile({
    String? name,
    String? email,
    String? phone,
  }) async {
    try {
      final response = await AuthService.updateProfile(
        name: name,
        email: email,
        phone: phone,
      );

      if (response.success) {
        _user = response.data;
        notifyListeners();
        return true;
      } else {
        _error = response.error ?? 'Failed to update profile';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = e.toString();
      notifyListeners();
      return false;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await AuthService.logout();
    } catch (e) {
      debugPrint('Error during logout: $e');
    } finally {
      _status = AuthStatus.unauthenticated;
      _user = null;
      _error = null;
      notifyListeners();
    }
  }

  // Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  // Clear all cached data (for testing/debugging)
  Future<void> clearAllData() async {
    debugPrint('🧹 AuthProvider: Clearing all cached data...');
    try {
      await AuthService.logout();
      _status = AuthStatus.unauthenticated;
      _user = null;
      _error = null;
      _isCodeSent = false;
      _verificationId = null;
      _phoneAuthService = null;
      notifyListeners();
      debugPrint('✅ AuthProvider: All data cleared');
    } catch (e) {
      debugPrint('❌ AuthProvider: Error clearing data: $e');
    }
  }

  // Refresh token
  Future<bool> refreshToken() async {
    try {
      final response = await AuthService.refreshToken();

      if (response.success) {
        _user = response.data;
        notifyListeners();
        return true;
      } else {
        // Token refresh failed, sign out
        await signOut();
        return false;
      }
    } catch (e) {
      await signOut();
      return false;
    }
  }

  // Phone number verification
  Future<bool> verifyPhoneNumber(String phoneNumber) async {
    _status = AuthStatus.authenticating;
    _error = null;
    _isCodeSent = false;
    notifyListeners();

    try {
      // Normalize phone number to Romanian standard format
      final normalizedPhoneNumber = PhoneNumberUtils.formatToRomanianStandard(phoneNumber);
      debugPrint('🔍 Starting phone verification for: $phoneNumber');
      debugPrint('🔍 Normalized phone number: $normalizedPhoneNumber');

      // Check if running on iOS Simulator
      if (defaultTargetPlatform == TargetPlatform.iOS && kDebugMode) {
        // Phone authentication doesn't work reliably on iOS Simulators
        // This is a known limitation of Firebase Auth
        debugPrint('⚠️ Phone authentication on iOS may require a physical device');
        debugPrint('If you get internal-error, try testing on a real iPhone/iPad');
      }

      // Firebase should already be initialized in main.dart
      // No need to re-initialize here

      // Initialize phone auth service if not provided (for testing)
      _phoneAuthService ??= PhoneAuthService(FirebaseAuth.instance);

      // Start phone verification with normalized number
      await _phoneAuthService!.verifyPhoneNumber(
        normalizedPhoneNumber,
        verificationCompleted: (PhoneAuthCredential credential) async {
          // Auto-verification completed (Android only)
          try {
            final userCredential = await FirebaseAuth.instance.signInWithCredential(credential);
            final firebaseToken = await userCredential.user?.getIdToken();

            if (firebaseToken != null) {
              final response =
                  await _authService.firebaseLogin(firebaseToken);
              if (response.success) {
                _user = response.data;
                _status = AuthStatus.authenticated;
                notifyListeners();

                // Trigger role initialization after successful authentication
                await _initializeRoles();
              } else {
                _status = AuthStatus.error;
                _error = response.error ?? 'Phone authentication failed';
                notifyListeners();
              }
            }
          } catch (e) {
            _status = AuthStatus.error;
            _error = 'Auto-verification failed: $e';
            notifyListeners();
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          _status = AuthStatus.error;
          _error = 'Phone verification failed: ${e.message}';
          notifyListeners();
        },
        codeSent: (String verificationId, int? resendToken) {
          _verificationId = verificationId;
          _isCodeSent = true;
          _status = AuthStatus.unauthenticated; // Waiting for OTP
          notifyListeners();
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          _verificationId = verificationId;
        },
      );

      return true;
    } catch (e) {
      _status = AuthStatus.error;
      _error = 'Phone verification failed: $e';
      notifyListeners();
      return false;
    }
  }

  // OTP verification
  Future<bool> verifyOTP(String otp) async {
    if (_phoneAuthService == null || _verificationId == null) {
      _error = 'Please request OTP first';
      notifyListeners();
      return false;
    }

    _status = AuthStatus.authenticating;
    _error = null;
    notifyListeners();

    try {
      // Verify OTP with Firebase
      final userCredential = await _phoneAuthService!.verifyOTP(otp);
      final firebaseToken = await userCredential.user?.getIdToken();

      if (firebaseToken != null) {
        // Exchange Firebase token for backend JWT
        final response = await _authService.firebaseLogin(firebaseToken);
        if (response.success) {
          _user = response.data;
          _status = AuthStatus.authenticated;
          _isCodeSent = false;
          notifyListeners();

          // Trigger role initialization after successful authentication
          await _initializeRoles();

          return true;
        } else {
          _status = AuthStatus.error;
          _error = response.error ?? 'OTP verification failed';
          notifyListeners();
          return false;
        }
      } else {
        _status = AuthStatus.error;
        _error = 'Failed to get Firebase token';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _status = AuthStatus.error;
      _error = 'OTP verification failed: $e';
      notifyListeners();
      return false;
    }
  }

  // Social login methods
  Future<bool> signInWithGoogle() async {
    _status = AuthStatus.authenticating;
    _error = null;
    notifyListeners();

    try {
      debugPrint('🔍 Starting Google Sign In flow...');

      // Use SocialAuthService for proper platform-specific handling
      final userCredential = await SocialAuthService.signInWithGoogle();

      if (userCredential == null) {
        debugPrint('🔍 Google Sign In cancelled or failed');
        _status = AuthStatus.unauthenticated;
        notifyListeners();
        return false;
      }

      debugPrint('🔍 Google Sign In successful: ${userCredential.user?.email}');

      final firebaseToken = await userCredential.user?.getIdToken();
      debugPrint('🔍 Firebase token obtained: ${firebaseToken != null}');

      if (firebaseToken != null) {
        // Exchange Firebase token for backend JWT
        debugPrint('🔍 Exchanging Firebase token for backend JWT...');
        final response = await AuthService.firebaseLogin(firebaseToken);
        if (response.success) {
          debugPrint('✅ Backend authentication successful');
          _user = response.data;
          _status = AuthStatus.authenticated;
          notifyListeners();

          // Trigger role initialization after successful authentication
          await _initializeRoles();

          return true;
        } else {
          debugPrint('❌ Backend authentication failed: ${response.error}');
          _status = AuthStatus.error;
          _error = response.error ?? 'Google sign-in failed';
          notifyListeners();
          return false;
        }
      } else {
        debugPrint('❌ Failed to get Firebase token');
        _status = AuthStatus.error;
        _error = 'Failed to get Firebase token';
        notifyListeners();
        return false;
      }
    } catch (e) {
      debugPrint('❌ Google sign-in exception: $e');
      _status = AuthStatus.error;
      _error = 'Google sign-in failed: $e';
      notifyListeners();
      return false;
    }
  }

  Future<bool> signInWithFacebook() async {
    _status = AuthStatus.error;
    _error = 'Facebook authentication has been disabled';
    notifyListeners();
    return false;

    /*
    _status = AuthStatus.authenticating;
    _error = null;
    notifyListeners();

    try {
      // Firebase should already be initialized in main.dart
      // No need to re-initialize here

      // Trigger the Facebook sign-in flow
      final LoginResult result = await FacebookAuth.instance.login();

      if (result.status == LoginStatus.success) {
        // Create a credential from the access token
        final OAuthCredential facebookAuthCredential =
            FacebookAuthProvider.credential(result.accessToken!.tokenString);

        // Sign in to Firebase with the Facebook credential
        final userCredential = await FirebaseAuth.instance.signInWithCredential(facebookAuthCredential);
        final firebaseToken = await userCredential.user?.getIdToken();

        if (firebaseToken != null) {
          // Exchange Firebase token for backend JWT
          final response = await AuthService.firebaseLogin(firebaseToken);
          if (response.success) {
            _user = response.data;
            _status = AuthStatus.authenticated;
            notifyListeners();

            // Trigger role initialization after successful authentication
            await _initializeRoles();

            return true;
          } else {
            _status = AuthStatus.error;
            _error = response.error ?? 'Facebook sign-in failed';
            notifyListeners();
            return false;
          }
        } else {
          _status = AuthStatus.error;
          _error = 'Failed to get Firebase token';
          notifyListeners();
          return false;
        }
      } else {
        _status = AuthStatus.unauthenticated;
        _error = 'Facebook sign-in was cancelled or failed';
        notifyListeners();
        return false;
      }
    } catch (e) {
      _status = AuthStatus.error;
      _error = 'Facebook sign-in failed: $e';
      notifyListeners();
      return false;
    }
    */
  }

  Future<bool> signInWithApple() async {
    _status = AuthStatus.authenticating;
    _error = null;
    notifyListeners();

    try {
      debugPrint('🍎 Starting Apple Sign-In process...');

      // Check if Apple Sign In is available
      debugPrint('🍎 Checking Apple Sign-In availability...');
      final isAvailable = await SignInWithApple.isAvailable();
      debugPrint('🍎 Apple Sign-In available: $isAvailable');

      if (!isAvailable) {
        debugPrint('❌ Apple Sign-In not available on this device');
        _status = AuthStatus.error;
        _error = 'Apple Sign In is not available on this device';
        notifyListeners();
        return false;
      }

      // Trigger the Apple sign-in flow
      debugPrint('🍎 Requesting Apple ID credential...');
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );
      debugPrint('🍎 Apple ID credential received: ${credential.userIdentifier}');

      // Create a Firebase credential from the Apple credential
      debugPrint('🍎 Creating Firebase credential...');
      final oauthCredential = OAuthProvider("apple.com").credential(
        idToken: credential.identityToken,
        accessToken: credential.authorizationCode,
      );
      debugPrint('🍎 Firebase credential created');

      // Sign in to Firebase with the Apple credential
      debugPrint('🍎 Signing in to Firebase...');
      final userCredential = await FirebaseAuth.instance.signInWithCredential(oauthCredential);
      debugPrint('🍎 Firebase sign-in successful: ${userCredential.user?.email}');

      final firebaseToken = await userCredential.user?.getIdToken();
      debugPrint('🍎 Firebase token obtained: ${firebaseToken != null}');

      if (firebaseToken != null) {
        // Exchange Firebase token for backend JWT
        debugPrint('🍎 Exchanging Firebase token for backend JWT...');
        final response = await AuthService.firebaseLogin(firebaseToken);
        if (response.success) {
          debugPrint('✅ Apple Sign-In successful!');
          _user = response.data;
          _status = AuthStatus.authenticated;

          notifyListeners();

          // Trigger role initialization after successful authentication
          await _initializeRoles();

          return true;
        } else {
          debugPrint('❌ Backend authentication failed: ${response.error}');
          _status = AuthStatus.error;
          _error = response.error ?? 'Apple sign-in failed';
          notifyListeners();
          return false;
        }
      } else {
        debugPrint('❌ Failed to get Firebase token');
        _status = AuthStatus.error;
        _error = 'Failed to get Firebase token';
        notifyListeners();
        return false;
      }
    } catch (e) {
      debugPrint('❌ Apple Sign-In exception: $e');
      _status = AuthStatus.error;
      _error = 'Apple sign-in failed: $e';
      notifyListeners();
      return false;
    }
  }

  /// Set role provider reference for triggering role initialization
  void setRoleProvider(RoleProvider roleProvider) {
    _roleProvider = roleProvider;
  }

  /// Set phone auth service (primarily for testing)
  void setPhoneAuthService(PhoneAuthService service) {
    _phoneAuthService = service;
  }

  /// Initialize roles after successful authentication
  Future<void> _initializeRoles() async {
    if (_roleProvider != null) {
      await _roleProvider!.initialize();
    }
  }
}
