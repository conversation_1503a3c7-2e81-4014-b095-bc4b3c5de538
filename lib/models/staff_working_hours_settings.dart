import 'working_hours_settings.dart';

/// Working hours settings for a staff member/groomer
class StaffWorkingHoursSettings {
  final String staffId;
  final String salonId;
  final Map<String, DaySchedule> weeklySchedule;
  final List<Holiday> holidays;
  final List<CustomClosure> customClosures;
  final DateTime updatedAt;

  const StaffWorkingHoursSettings({
    required this.staffId,
    required this.salonId,
    required this.weeklySchedule,
    required this.holidays,
    required this.customClosures,
    required this.updatedAt,
  });

  /// Create StaffWorkingHoursSettings from JSON
  factory StaffWorkingHoursSettings.fromJson(Map<String, dynamic> json) {
    final weeklyScheduleMap = <String, DaySchedule>{};
    final weeklyScheduleJson = json['weeklySchedule'] as Map<String, dynamic>? ?? {};

    for (final entry in weeklyScheduleJson.entries) {
      weeklyScheduleMap[entry.key] = DaySchedule.fromJson(entry.value as Map<String, dynamic>);
    }

    return StaffWorkingHoursSettings(
      staffId: json['staffId'] as String,
      salonId: json['salonId'] as String,
      weeklySchedule: weeklyScheduleMap,
      holidays: (json['holidays'] as List<dynamic>? ?? [])
          .map((h) => Holiday.fromJson(h as Map<String, dynamic>))
          .toList(),
      customClosures: (json['customClosures'] as List<dynamic>? ?? [])
          .map((c) => CustomClosure.fromJson(c as Map<String, dynamic>))
          .toList(),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Convert StaffWorkingHoursSettings to JSON
  Map<String, dynamic> toJson() {
    final weeklyScheduleJson = <String, dynamic>{};
    for (final entry in weeklySchedule.entries) {
      weeklyScheduleJson[entry.key] = entry.value.toJson();
    }

    return {
      'staffId': staffId,
      'salonId': salonId,
      'weeklySchedule': weeklyScheduleJson,
      'holidays': holidays.map((h) => h.toJson()).toList(),
      'customClosures': customClosures.map((c) => c.toJson()).toList(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated values
  StaffWorkingHoursSettings copyWith({
    String? staffId,
    String? salonId,
    Map<String, DaySchedule>? weeklySchedule,
    List<Holiday>? holidays,
    List<CustomClosure>? customClosures,
    DateTime? updatedAt,
  }) {
    return StaffWorkingHoursSettings(
      staffId: staffId ?? this.staffId,
      salonId: salonId ?? this.salonId,
      weeklySchedule: weeklySchedule ?? this.weeklySchedule,
      holidays: holidays ?? this.holidays,
      customClosures: customClosures ?? this.customClosures,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get schedule for a specific day
  DaySchedule? getScheduleForDay(String dayOfWeek) {
    return weeklySchedule[dayOfWeek.toLowerCase()];
  }

  /// Check if staff member is available on a specific date
  bool isAvailableOnDate(DateTime date) {
    final dayOfWeek = _getDayOfWeekString(date);
    final daySchedule = getScheduleForDay(dayOfWeek);

    if (daySchedule == null || !daySchedule.isWorkingDay) {
      return false;
    }

    // Check for holidays
    if (holidays.any((h) => h.date.year == date.year &&
                           h.date.month == date.month &&
                           h.date.day == date.day &&
                           !h.isWorkingDay)) {
      return false;
    }

    // Check for custom closures
    if (customClosures.any((c) =>
        !date.isBefore(c.startDate) && !date.isAfter(c.endDate))) {
      return false;
    }

    return true;
  }

  /// Get working hours for a specific date
  ({String? startTime, String? endTime, String? breakStart, String? breakEnd})? getWorkingHoursForDate(DateTime date) {
    if (!isAvailableOnDate(date)) {
      return null;
    }

    final dayOfWeek = _getDayOfWeekString(date);
    final daySchedule = getScheduleForDay(dayOfWeek);

    if (daySchedule == null) {
      return null;
    }

    return (
      startTime: daySchedule.startTime,
      endTime: daySchedule.endTime,
      breakStart: daySchedule.breakStart,
      breakEnd: daySchedule.breakEnd,
    );
  }

  /// Check if staff member is available at a specific time
  bool isAvailableAtTime(DateTime dateTime) {
    if (!isAvailableOnDate(dateTime)) {
      return false;
    }

    final workingHours = getWorkingHoursForDate(dateTime);
    if (workingHours == null) {
      return false;
    }

    final timeString = '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    
    // Check if time is within working hours
    if (workingHours.startTime != null && timeString.compareTo(workingHours.startTime!) < 0) {
      return false;
    }
    if (workingHours.endTime != null && timeString.compareTo(workingHours.endTime!) > 0) {
      return false;
    }

    // Check if time is during break
    if (workingHours.breakStart != null && workingHours.breakEnd != null) {
      if (timeString.compareTo(workingHours.breakStart!) >= 0 && 
          timeString.compareTo(workingHours.breakEnd!) < 0) {
        return false;
      }
    }

    return true;
  }

  /// Get day of week string from DateTime
  String _getDayOfWeekString(DateTime date) {
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    return days[date.weekday - 1];
  }

  /// Check if this schedule has any working days
  bool get hasWorkingDays {
    return weeklySchedule.values.any((schedule) => schedule.isWorkingDay);
  }

  /// Get total working hours per week
  double get totalWeeklyHours {
    double total = 0.0;
    for (final schedule in weeklySchedule.values) {
      if (schedule.isWorkingDay && schedule.startTime != null && schedule.endTime != null) {
        final start = _parseTime(schedule.startTime!);
        final end = _parseTime(schedule.endTime!);
        if (start != null && end != null) {
          double dayHours = end.difference(start).inMinutes / 60.0;
          
          // Subtract break time if present
          if (schedule.breakStart != null && schedule.breakEnd != null) {
            final breakStart = _parseTime(schedule.breakStart!);
            final breakEnd = _parseTime(schedule.breakEnd!);
            if (breakStart != null && breakEnd != null) {
              dayHours -= breakEnd.difference(breakStart).inMinutes / 60.0;
            }
          }
          
          total += dayHours;
        }
      }
    }
    return total;
  }

  /// Parse time string to DateTime (today's date with specified time)
  DateTime? _parseTime(String timeString) {
    try {
      final parts = timeString.split(':');
      if (parts.length != 2) return null;
      
      final hour = int.parse(parts[0]);
      final minute = int.parse(parts[1]);
      
      final now = DateTime.now();
      return DateTime(now.year, now.month, now.day, hour, minute);
    } catch (e) {
      return null;
    }
  }
}

/// Request model for updating staff working hours
class UpdateStaffWorkingHoursRequest {
  final Map<String, DaySchedule> weeklySchedule;
  final List<Holiday> holidays;
  final List<CustomClosure> customClosures;

  const UpdateStaffWorkingHoursRequest({
    required this.weeklySchedule,
    required this.holidays,
    required this.customClosures,
  });

  /// Convert to JSON for API request
  Map<String, dynamic> toJson() {
    final weeklyScheduleJson = <String, dynamic>{};
    for (final entry in weeklySchedule.entries) {
      weeklyScheduleJson[entry.key] = entry.value.toJson();
    }

    return {
      'weeklySchedule': weeklyScheduleJson,
      'holidays': holidays.map((h) => h.toJson()).toList(),
      'customClosures': customClosures.map((c) => c.toJson()).toList(),
    };
  }

  /// Create from StaffWorkingHoursSettings
  factory UpdateStaffWorkingHoursRequest.fromStaffWorkingHoursSettings(StaffWorkingHoursSettings settings) {
    return UpdateStaffWorkingHoursRequest(
      weeklySchedule: settings.weeklySchedule,
      holidays: settings.holidays,
      customClosures: settings.customClosures,
    );
  }
}
