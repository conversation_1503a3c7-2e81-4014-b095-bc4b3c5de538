import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/appointment.dart';
import '../../config/theme/app_theme.dart';
import 'appointment_block.dart';

/// Data class for drag-and-drop operations
class AppointmentDragData {
  final Appointment appointment;
  final DateTime originalDate;
  final String originalStaffId;

  const AppointmentDragData({
    required this.appointment,
    required this.originalDate,
    required this.originalStaffId,
  });
}

/// Enhanced appointment block with drag-and-drop functionality
class DraggableAppointmentBlock extends StatefulWidget {
  final Appointment appointment;
  final double height;
  final VoidCallback? onTap;
  final bool isCompact;
  final bool isDragEnabled;
  final Function(AppointmentDragData)? onDragStarted;
  final Function(AppointmentDragData)? onDragCompleted;

  const DraggableAppointmentBlock({
    super.key,
    required this.appointment,
    required this.height,
    this.onTap,
    this.isCompact = false,
    this.isDragEnabled = true,
    this.onDragStarted,
    this.onDragCompleted,
  });

  @override
  State<DraggableAppointmentBlock> createState() => _DraggableAppointmentBlockState();
}

class _DraggableAppointmentBlockState extends State<DraggableAppointmentBlock>
    with TickerProviderStateMixin {
  bool _isDragging = false;
  bool _isHovered = false;
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  void _triggerHapticFeedback() {
    HapticFeedback.lightImpact();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isDragEnabled) {
      return AppointmentBlock(
        appointment: widget.appointment,
        height: widget.height,
        onTap: widget.onTap,
        isCompact: widget.isCompact,
      );
    }

    final dragData = AppointmentDragData(
      appointment: widget.appointment,
      originalDate: widget.appointment.startTime,
      originalStaffId: widget.appointment.groomerId ?? '',
    );

    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: LongPressDraggable<AppointmentDragData>(
              data: dragData,
              dragAnchorStrategy: pointerDragAnchorStrategy,
              onDragStarted: () {
                setState(() => _isDragging = true);
                _scaleController.forward();
                _triggerHapticFeedback();
                widget.onDragStarted?.call(dragData);
              },
              onDragEnd: (details) {
                setState(() => _isDragging = false);
                _scaleController.reverse();
                widget.onDragCompleted?.call(dragData);
              },
              onDraggableCanceled: (velocity, offset) {
                setState(() => _isDragging = false);
                _scaleController.reverse();
              },
              feedback: _buildDragFeedback(),
              childWhenDragging: _buildChildWhenDragging(),
              child: _buildNormalChild(),
            ),
          );
        },
      ),
    );
  }

  Widget _buildNormalChild() {
    return ClipRect( // Add ClipRect to prevent visual overflow
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
        ),
        child: AppointmentBlock(
          appointment: widget.appointment,
          height: widget.height,
          onTap: widget.onTap,
          isCompact: widget.isCompact,
        ),
      ),
    );
  }

  Widget _buildChildWhenDragging() {
    return Container(
      height: widget.height,
      margin: const EdgeInsets.symmetric(horizontal: 2, vertical: 1),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: Colors.grey.withValues(alpha: 0.5),
          width: 1,
          style: BorderStyle.solid,
        ),
      ),
      child: Center(
        child: Icon(
          Icons.drag_indicator,
          color: Colors.grey.shade600,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildDragFeedback() {
    return Material(
      elevation: 0,
      borderRadius: BorderRadius.circular(4),
      child: Container(
        width: 200, // Fixed width for feedback
        height: widget.height.clamp(60, 120), // Constrain height
        decoration: BoxDecoration(
          color: AppTheme.getStaffColor(widget.appointment.groomerId ?? ''),
          borderRadius: BorderRadius.circular(4),
          border: Border.all(
            color: Colors.white,
            width: 2,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(8),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                widget.appointment.timeRange,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2),
              Text(
                widget.appointment.clientName,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              if (widget.height > 60) ...[
                const SizedBox(height: 2),
                Text(
                  widget.appointment.service,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 10,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// Drop zone indicator for valid drop targets
class DropZoneIndicator extends StatefulWidget {
  final bool isActive;
  final bool isValid;
  final Widget child;

  const DropZoneIndicator({
    super.key,
    required this.isActive,
    required this.isValid,
    required this.child,
  });

  @override
  State<DropZoneIndicator> createState() => _DropZoneIndicatorState();
}

class _DropZoneIndicatorState extends State<DropZoneIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (widget.isActive) {
      _animationController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(DropZoneIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isActive && !oldWidget.isActive) {
      _animationController.repeat(reverse: true);
    } else if (!widget.isActive && oldWidget.isActive) {
      _animationController.stop();
      _animationController.reset();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isActive) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              border: Border.all(
                color: widget.isValid
                    ? AppColors.forestGreen.withValues(alpha: 0.8)
                    : Colors.red.withValues(alpha: 0.8),
                width: 2,
              ),
              borderRadius: BorderRadius.circular(4),
              color: widget.isValid
                  ? AppColors.forestGreen.withValues(alpha: 0.1)
                  : Colors.red.withValues(alpha: 0.1),
            ),
            child: widget.child,
          ),
        );
      },
    );
  }
}
