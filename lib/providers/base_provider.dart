import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../core/constants/app_strings.dart';

/// Base provider class with common state management patterns
abstract class BaseProvider extends ChangeNotifier {
  bool _isLoading = false;
  String? _error;
  bool _isInitialized = false;

  /// Common getters
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isInitialized => _isInitialized;
  bool get hasError => _error != null;

  /// Set loading state
  @protected
  void setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// Set error state
  @protected
  void setError(String? error) {
    if (_error != error) {
      _error = error;
      notifyListeners();
    }
  }

  /// Clear error state
  @protected
  void clearError() {
    setError(null);
  }

  /// Set initialized state
  @protected
  void setInitialized(bool initialized) {
    if (_isInitialized != initialized) {
      _isInitialized = initialized;
      notifyListeners();
    }
  }

  /// Execute an async operation with automatic loading and error handling
  @protected
  Future<T?> executeAsync<T>(
    Future<T> Function() operation, {
    String? errorMessage,
    bool showLoading = true,
    bool clearErrorFirst = true,
  }) async {
    if (clearErrorFirst) clearError();
    if (showLoading) setLoading(true);

    try {
      final result = await operation();
      clearError();
      return result;
    } catch (e) {
      final message = errorMessage ?? _getErrorMessage(e);
      setError(message);
      debugPrint('❌ $runtimeType: $message - $e');
      return null;
    } finally {
      if (showLoading) setLoading(false);
    }
  }

  /// Execute an async operation that returns a boolean result
  @protected
  Future<bool> executeBoolAsync(
    Future<bool> Function() operation, {
    String? errorMessage,
    bool showLoading = true,
    bool clearErrorFirst = true,
  }) async {
    final result = await executeAsync<bool>(
      operation,
      errorMessage: errorMessage,
      showLoading: showLoading,
      clearErrorFirst: clearErrorFirst,
    );
    return result ?? false;
  }

  /// Execute an async operation without return value
  @protected
  Future<void> executeVoidAsync(
    Future<void> Function() operation, {
    String? errorMessage,
    bool showLoading = true,
    bool clearErrorFirst = true,
  }) async {
    await executeAsync<void>(
      operation,
      errorMessage: errorMessage,
      showLoading: showLoading,
      clearErrorFirst: clearErrorFirst,
    );
  }

  /// Initialize the provider (to be implemented by subclasses)
  Future<void> initialize();

  /// Refresh the provider data (to be implemented by subclasses)
  Future<void> refresh() async {
    await initialize();
  }

  /// Clear all provider data (to be implemented by subclasses)
  void clear() {
    _isLoading = false;
    _error = null;
    _isInitialized = false;
    notifyListeners();
  }

  /// Get user-friendly error message from exception
  String _getErrorMessage(dynamic error) {
    if (error is String) return error;
    
    final errorString = error.toString().toLowerCase();
    
    if (errorString.contains('network') || errorString.contains('connection')) {
      return AppStrings.errorNetwork;
    } else if (errorString.contains('server')) {
      return AppStrings.errorServer;
    } else if (errorString.contains('auth')) {
      return AppStrings.errorAuthentication;
    } else if (errorString.contains('permission')) {
      return AppStrings.errorPermission;
    } else {
      return AppStrings.errorGeneral;
    }
  }

  /// Retry the last failed operation (to be implemented by subclasses if needed)
  Future<void> retry() async {
    await refresh();
  }

  /// Check if provider is in a valid state (not loading, no error, initialized)
  bool get isReady => !_isLoading && !hasError && _isInitialized;

  /// Get status message for UI display
  String? get statusMessage {
    if (_isLoading) return AppStrings.loading;
    if (hasError) return _error;
    if (!_isInitialized) return AppStrings.loadingData;
    return null;
  }
}

/// Mixin for providers that handle paginated data
mixin PaginationMixin<T> on BaseProvider {
  final List<T> _items = [];
  int _currentPage = 1;
  int _totalPages = 1;
  bool _hasMoreData = true;
  bool _isLoadingMore = false;

  /// Getters for pagination
  List<T> get items => _items;
  int get currentPage => _currentPage;
  int get totalPages => _totalPages;
  bool get hasMoreData => _hasMoreData;
  bool get isLoadingMore => _isLoadingMore;
  bool get canLoadMore => _hasMoreData && !_isLoadingMore && !isLoading;

  /// Load first page
  @protected
  Future<void> loadFirstPage() async {
    _currentPage = 1;
    _hasMoreData = true;
    _items.clear();
    await loadNextPage();
  }

  /// Load next page
  @protected
  Future<void> loadNextPage() async {
    if (!canLoadMore) return;

    _isLoadingMore = true;
    notifyListeners();

    try {
      final newItems = await fetchPage(_currentPage);
      if (newItems.isNotEmpty) {
        _items.addAll(newItems);
        _currentPage++;
        
        // Check if we have more data (implement this logic based on your API)
        _hasMoreData = newItems.length >= getPageSize();
      } else {
        _hasMoreData = false;
      }
      clearError();
    } catch (e) {
      setError(_getErrorMessage(e));
    } finally {
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  /// Fetch a specific page (to be implemented by subclasses)
  @protected
  Future<List<T>> fetchPage(int page);

  /// Get page size (to be implemented by subclasses)
  @protected
  int getPageSize() => 20;

  /// Clear pagination data
  @protected
  void clearPagination() {
    _items.clear();
    _currentPage = 1;
    _totalPages = 1;
    _hasMoreData = true;
    _isLoadingMore = false;
  }
}

/// Mixin for providers that handle search functionality
mixin SearchMixin on BaseProvider {
  String _searchQuery = '';
  bool _isSearching = false;

  /// Getters for search
  String get searchQuery => _searchQuery;
  bool get isSearching => _isSearching;
  bool get hasSearchQuery => _searchQuery.isNotEmpty;

  /// Set search query
  void setSearchQuery(String query) {
    if (_searchQuery != query) {
      _searchQuery = query;
      notifyListeners();
      _performSearch();
    }
  }

  /// Clear search
  void clearSearch() {
    setSearchQuery('');
  }

  /// Perform search (to be implemented by subclasses)
  @protected
  Future<void> performSearch(String query);

  /// Internal search handler with debouncing
  void _performSearch() async {
    if (_searchQuery.isEmpty) {
      await refresh();
      return;
    }

    _isSearching = true;
    notifyListeners();

    try {
      await performSearch(_searchQuery);
      clearError();
    } catch (e) {
      setError(_getErrorMessage(e));
    } finally {
      _isSearching = false;
      notifyListeners();
    }
  }
}
