# Block Time API Contract

## Overview
API contract for the block time feature in the Partykids grooming salon management system. This feature allows staff members to block time slots in the calendar when they are unavailable for appointments.

## Base URL
```
https://api.partykids-programari.ro/api/v1
```

## Authentication
All endpoints require Bearer token authentication:
```
Authorization: Bearer <jwt_token>
```

## Endpoints

### 1. Create Block Time

**POST** `/salons/{salonId}/block-time`

Creates a new blocked time slot for specified staff members.

#### Path Parameters
- `salonId` (string, required): The salon ID

#### Request Headers
```
Content-Type: application/json
Authorization: Bearer <jwt_token>
```

#### Request Body
```json
{
  "startTime": "2024-06-15T09:00:00Z",
  "endTime": "2024-06-15T10:00:00Z",
  "reason": "Pauză",
  "customReason": "Întâ<PERSON><PERSON> cu furnizorul",
  "staffIds": ["staff-123", "staff-456"],
  "isRecurring": false,
  "recurrencePattern": {
    "type": "WEEKLY",
    "interval": 1,
    "daysOfWeek": ["MONDAY", "WEDNESDAY", "FRIDAY"],
    "endDate": "2024-12-31T23:59:59Z"
  },
  "notes": "Bloc de timp pentru training echipă"
}
```

#### Request Body Schema
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| `startTime` | string (ISO 8601) | Yes | Start time of the blocked period |
| `endTime` | string (ISO 8601) | Yes | End time of the blocked period |
| `reason` | string | Yes | Predefined reason category |
| `customReason` | string | No | Custom reason when reason is "Altele" |
| `staffIds` | array[string] | Yes | List of staff member IDs to block time for |
| `isRecurring` | boolean | No | Whether this is a recurring block (default: false) |
| `recurrencePattern` | object | No | Recurrence configuration (required if isRecurring is true) |
| `notes` | string | No | Additional notes about the block |

#### Reason Categories
- `"Pauză"` - Break time
- `"Întâlnire"` - Meeting
- `"Concediu"` - Vacation/Leave
- `"Personal"` - Personal time
- `"Training"` - Training session
- `"Altele"` - Other (requires customReason)

#### Response (201 Created)
```json
{
  "success": true,
  "data": {
    "blockId": "block-789",
    "salonId": "salon-123",
    "startTime": "2024-06-15T09:00:00Z",
    "endTime": "2024-06-15T10:00:00Z",
    "reason": "Pauză",
    "customReason": null,
    "staffIds": ["staff-123", "staff-456"],
    "createdBy": "user-456",
    "createdAt": "2024-06-14T15:30:00Z",
    "isRecurring": false,
    "recurrencePattern": null,
    "notes": null,
    "status": "ACTIVE",
    "affectedAppointments": [
      {
        "appointmentId": "appt-123",
        "clientName": "Ana Popescu",
        "conflictType": "OVERLAP",
        "suggestedAction": "RESCHEDULE"
      }
    ]
  },
  "message": "Timpul a fost blocat cu succes pentru 2 membri ai echipei"
}
```

#### Error Responses

**400 Bad Request** - Validation errors
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Datele introduse nu sunt valide",
    "details": [
      {
        "field": "endTime",
        "message": "Ora de sfârșit trebuie să fie după ora de început"
      },
      {
        "field": "staffIds",
        "message": "Trebuie să selectați cel puțin un membru al echipei"
      }
    ]
  }
}
```

**409 Conflict** - Scheduling conflicts
```json
{
  "success": false,
  "error": {
    "code": "SCHEDULING_CONFLICT",
    "message": "Există programări în intervalul selectat",
    "details": {
      "conflictingAppointments": [
        {
          "appointmentId": "appt-123",
          "staffId": "staff-123",
          "clientName": "Ana Popescu",
          "startTime": "2024-06-15T09:30:00Z",
          "endTime": "2024-06-15T10:30:00Z",
          "service": "Tuns complet"
        }
      ],
      "suggestedActions": [
        "FORCE_BLOCK",
        "RESCHEDULE_APPOINTMENTS",
        "ADJUST_TIME_RANGE"
      ]
    }
  }
}
```

### 2. Get Block Time List

**GET** `/salons/{salonId}/block-time`

Retrieves all blocked time slots for a salon with optional filtering.

#### Path Parameters
- `salonId` (string, required): The salon ID

#### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `startDate` | string (YYYY-MM-DD) | No | Filter blocks from this date |
| `endDate` | string (YYYY-MM-DD) | No | Filter blocks until this date |
| `staffId` | string | No | Filter blocks for specific staff member |
| `reason` | string | No | Filter by reason category |
| `status` | string | No | Filter by status (ACTIVE, CANCELLED, EXPIRED) |
| `page` | integer | No | Page number (default: 1) |
| `limit` | integer | No | Items per page (default: 50, max: 100) |

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "blocks": [
      {
        "blockId": "block-789",
        "salonId": "salon-123",
        "startTime": "2024-06-15T09:00:00Z",
        "endTime": "2024-06-15T10:00:00Z",
        "reason": "Pauză",
        "customReason": null,
        "staffIds": ["staff-123"],
        "staffNames": ["Maria Popescu"],
        "createdBy": "user-456",
        "createdByName": "Ion Georgescu",
        "createdAt": "2024-06-14T15:30:00Z",
        "updatedAt": "2024-06-14T15:30:00Z",
        "isRecurring": false,
        "recurrencePattern": null,
        "notes": null,
        "status": "ACTIVE"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 3,
      "totalItems": 25,
      "itemsPerPage": 10,
      "hasNextPage": true,
      "hasPreviousPage": false
    },
    "summary": {
      "totalActiveBlocks": 23,
      "totalHoursBlocked": 45.5,
      "mostCommonReason": "Pauză",
      "staffWithMostBlocks": {
        "staffId": "staff-123",
        "staffName": "Maria Popescu",
        "blockCount": 8
      }
    }
  }
}
```

### 3. Get Block Time Details

**GET** `/salons/{salonId}/block-time/{blockId}`

Retrieves detailed information about a specific blocked time slot.

#### Path Parameters
- `salonId` (string, required): The salon ID
- `blockId` (string, required): The block time ID

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "blockId": "block-789",
    "salonId": "salon-123",
    "startTime": "2024-06-15T09:00:00Z",
    "endTime": "2024-06-15T10:00:00Z",
    "duration": 60,
    "reason": "Pauză",
    "customReason": null,
    "staffIds": ["staff-123", "staff-456"],
    "staffDetails": [
      {
        "staffId": "staff-123",
        "name": "Maria Popescu",
        "nickname": "msakK",
        "role": "GROOMER"
      },
      {
        "staffId": "staff-456",
        "name": "Ion Georgescu",
        "nickname": null,
        "role": "CHIEF_GROOMER"
      }
    ],
    "createdBy": "user-456",
    "createdByName": "Ion Georgescu",
    "createdAt": "2024-06-14T15:30:00Z",
    "updatedAt": "2024-06-14T15:30:00Z",
    "isRecurring": false,
    "recurrencePattern": null,
    "notes": null,
    "status": "ACTIVE",
    "affectedAppointments": [],
    "history": [
      {
        "action": "CREATED",
        "performedBy": "user-456",
        "performedByName": "Ion Georgescu",
        "timestamp": "2024-06-14T15:30:00Z",
        "details": "Bloc de timp creat pentru 2 membri ai echipei"
      }
    ]
  }
}
```

### 4. Update Block Time

**PUT** `/salons/{salonId}/block-time/{blockId}`

Updates an existing blocked time slot.

#### Path Parameters
- `salonId` (string, required): The salon ID
- `blockId` (string, required): The block time ID

#### Request Body
```json
{
  "startTime": "2024-06-15T09:30:00Z",
  "endTime": "2024-06-15T10:30:00Z",
  "reason": "Întâlnire",
  "customReason": "Întâlnire cu clientul VIP",
  "staffIds": ["staff-123"],
  "notes": "Actualizat pentru întâlnire importantă"
}
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "blockId": "block-789",
    "salonId": "salon-123",
    "startTime": "2024-06-15T09:30:00Z",
    "endTime": "2024-06-15T10:30:00Z",
    "reason": "Întâlnire",
    "customReason": "Întâlnire cu clientul VIP",
    "staffIds": ["staff-123"],
    "updatedBy": "user-456",
    "updatedAt": "2024-06-14T16:00:00Z",
    "status": "ACTIVE",
    "changes": [
      {
        "field": "startTime",
        "oldValue": "2024-06-15T09:00:00Z",
        "newValue": "2024-06-15T09:30:00Z"
      },
      {
        "field": "reason",
        "oldValue": "Pauză",
        "newValue": "Întâlnire"
      }
    ]
  },
  "message": "Blocul de timp a fost actualizat cu succes"
}
```

### 5. Delete Block Time

**DELETE** `/salons/{salonId}/block-time/{blockId}`

Deletes (cancels) a blocked time slot.

#### Path Parameters
- `salonId` (string, required): The salon ID
- `blockId` (string, required): The block time ID

#### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `reason` | string | No | Reason for cancellation |
| `notifyStaff` | boolean | No | Whether to notify affected staff (default: true) |

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "blockId": "block-789",
    "status": "CANCELLED",
    "cancelledBy": "user-456",
    "cancelledAt": "2024-06-14T16:30:00Z",
    "cancellationReason": "Programare anulată",
    "affectedStaff": [
      {
        "staffId": "staff-123",
        "staffName": "Maria Popescu",
        "notified": true
      }
    ]
  },
  "message": "Blocul de timp a fost anulat cu succes"
}
```

### 6. Bulk Block Time Operations

**POST** `/salons/{salonId}/block-time/bulk`

Performs bulk operations on multiple block time slots.

#### Request Body
```json
{
  "operation": "CREATE",
  "blocks": [
    {
      "startTime": "2024-06-15T09:00:00Z",
      "endTime": "2024-06-15T10:00:00Z",
      "reason": "Pauză",
      "staffIds": ["staff-123"]
    },
    {
      "startTime": "2024-06-15T14:00:00Z",
      "endTime": "2024-06-15T15:00:00Z",
      "reason": "Pauză",
      "staffIds": ["staff-456"]
    }
  ]
}
```

#### Operations
- `CREATE` - Create multiple blocks
- `DELETE` - Delete multiple blocks
- `UPDATE` - Update multiple blocks

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "operation": "CREATE",
    "totalRequested": 2,
    "successful": 2,
    "failed": 0,
    "results": [
      {
        "index": 0,
        "success": true,
        "blockId": "block-790",
        "message": "Bloc creat cu succes"
      },
      {
        "index": 1,
        "success": true,
        "blockId": "block-791",
        "message": "Bloc creat cu succes"
      }
    ]
  },
  "message": "2 din 2 blocuri au fost procesate cu succes"
}
```

### 7. Check Time Availability

**POST** `/salons/{salonId}/block-time/check-availability`

Checks if a time slot is available for blocking (no conflicts with existing appointments).

#### Request Body
```json
{
  "startTime": "2024-06-15T09:00:00Z",
  "endTime": "2024-06-15T10:00:00Z",
  "staffIds": ["staff-123", "staff-456"]
}
```

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "available": false,
    "conflicts": [
      {
        "staffId": "staff-123",
        "staffName": "Maria Popescu",
        "conflictType": "APPOINTMENT",
        "conflictDetails": {
          "appointmentId": "appt-123",
          "clientName": "Ana Popescu",
          "startTime": "2024-06-15T09:30:00Z",
          "endTime": "2024-06-15T10:30:00Z",
          "service": "Tuns complet"
        }
      }
    ],
    "availableStaff": [
      {
        "staffId": "staff-456",
        "staffName": "Ion Georgescu",
        "available": true
      }
    ],
    "suggestions": [
      {
        "type": "ADJUST_TIME",
        "description": "Ajustați intervalul la 08:00-09:00",
        "startTime": "2024-06-15T08:00:00Z",
        "endTime": "2024-06-15T09:00:00Z"
      },
      {
        "type": "EXCLUDE_STAFF",
        "description": "Excludeți Maria Popescu din blocaj",
        "staffIds": ["staff-456"]
      }
    ]
  }
}
```

### 8. Get Block Time Statistics

**GET** `/salons/{salonId}/block-time/statistics`

Retrieves statistics about blocked time usage.

#### Query Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `startDate` | string (YYYY-MM-DD) | No | Statistics from this date |
| `endDate` | string (YYYY-MM-DD) | No | Statistics until this date |
| `staffId` | string | No | Statistics for specific staff member |

#### Response (200 OK)
```json
{
  "success": true,
  "data": {
    "period": {
      "startDate": "2024-06-01",
      "endDate": "2024-06-30",
      "totalDays": 30
    },
    "summary": {
      "totalBlocks": 45,
      "totalHoursBlocked": 67.5,
      "averageBlockDuration": 90,
      "mostActiveDay": "2024-06-15",
      "leastActiveDay": "2024-06-02"
    },
    "byReason": [
      {
        "reason": "Pauză",
        "count": 20,
        "totalHours": 30.0,
        "percentage": 44.4
      },
      {
        "reason": "Întâlnire",
        "count": 15,
        "totalHours": 22.5,
        "percentage": 33.3
      },
      {
        "reason": "Concediu",
        "count": 8,
        "totalHours": 12.0,
        "percentage": 17.8
      },
      {
        "reason": "Personal",
        "count": 2,
        "totalHours": 3.0,
        "percentage": 4.4
      }
    ],
    "byStaff": [
      {
        "staffId": "staff-123",
        "staffName": "Maria Popescu",
        "totalBlocks": 18,
        "totalHours": 27.0,
        "averageDuration": 90,
        "mostCommonReason": "Pauză"
      },
      {
        "staffId": "staff-456",
        "staffName": "Ion Georgescu",
        "totalBlocks": 15,
        "totalHours": 22.5,
        "averageDuration": 90,
        "mostCommonReason": "Întâlnire"
      }
    ],
    "trends": {
      "weeklyPattern": [
        {"day": "MONDAY", "averageBlocks": 2.3, "averageHours": 3.5},
        {"day": "TUESDAY", "averageBlocks": 1.8, "averageHours": 2.7},
        {"day": "WEDNESDAY", "averageBlocks": 2.1, "averageHours": 3.2},
        {"day": "THURSDAY", "averageBlocks": 1.9, "averageHours": 2.9},
        {"day": "FRIDAY", "averageBlocks": 2.5, "averageHours": 3.8},
        {"day": "SATURDAY", "averageBlocks": 1.2, "averageHours": 1.8},
        {"day": "SUNDAY", "averageBlocks": 0.8, "averageHours": 1.2}
      ],
      "hourlyPattern": [
        {"hour": 9, "averageBlocks": 0.8},
        {"hour": 10, "averageBlocks": 0.6},
        {"hour": 11, "averageBlocks": 0.4},
        {"hour": 12, "averageBlocks": 1.2},
        {"hour": 13, "averageBlocks": 1.5},
        {"hour": 14, "averageBlocks": 0.9},
        {"hour": 15, "averageBlocks": 0.7},
        {"hour": 16, "averageBlocks": 0.5},
        {"hour": 17, "averageBlocks": 0.3}
      ]
    }
  }
}
```

## Data Models

### BlockTime Model
```json
{
  "blockId": "string",
  "salonId": "string",
  "startTime": "string (ISO 8601)",
  "endTime": "string (ISO 8601)",
  "duration": "integer (minutes)",
  "reason": "string",
  "customReason": "string|null",
  "staffIds": ["string"],
  "createdBy": "string",
  "createdAt": "string (ISO 8601)",
  "updatedBy": "string|null",
  "updatedAt": "string (ISO 8601)",
  "isRecurring": "boolean",
  "recurrencePattern": "RecurrencePattern|null",
  "notes": "string|null",
  "status": "string (ACTIVE|CANCELLED|EXPIRED)"
}
```

### RecurrencePattern Model
```json
{
  "type": "string (DAILY|WEEKLY|MONTHLY)",
  "interval": "integer",
  "daysOfWeek": ["string (MONDAY|TUESDAY|...)|null"],
  "dayOfMonth": "integer|null",
  "endDate": "string (ISO 8601)|null",
  "occurrences": "integer|null"
}
```

### Conflict Model
```json
{
  "staffId": "string",
  "staffName": "string",
  "conflictType": "string (APPOINTMENT|BLOCK|SCHEDULE)",
  "conflictDetails": {
    "id": "string",
    "startTime": "string (ISO 8601)",
    "endTime": "string (ISO 8601)",
    "description": "string"
  }
}
```

## Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `VALIDATION_ERROR` | 400 | Request validation failed |
| `UNAUTHORIZED` | 401 | Authentication required |
| `FORBIDDEN` | 403 | Insufficient permissions |
| `SALON_NOT_FOUND` | 404 | Salon not found |
| `BLOCK_NOT_FOUND` | 404 | Block time not found |
| `STAFF_NOT_FOUND` | 404 | Staff member not found |
| `SCHEDULING_CONFLICT` | 409 | Time slot conflicts with existing data |
| `INVALID_TIME_RANGE` | 422 | Invalid time range specified |
| `PAST_TIME_BLOCK` | 422 | Cannot block time in the past |
| `DURATION_TOO_LONG` | 422 | Block duration exceeds maximum allowed |
| `INTERNAL_ERROR` | 500 | Internal server error |

## Business Rules

### Time Validation
- Block start time must be in the future (minimum 5 minutes from now)
- Block end time must be after start time
- Maximum block duration: 12 hours
- Minimum block duration: 15 minutes
- Time must align with salon's operating hours

### Staff Permissions
- **CHIEF_GROOMER**: Can block time for any staff member
- **GROOMER**: Can only block time for themselves
- **ADMIN**: Can block time for any staff member and manage all blocks

### Conflict Resolution
- **SOFT_CONFLICT**: Warning shown, user can proceed
- **HARD_CONFLICT**: Block creation prevented, must resolve first
- **AUTO_RESOLVE**: System suggests alternative times

### Recurring Blocks
- Maximum 365 occurrences per recurring pattern
- Recurring blocks can be modified individually or as a series
- Deleting a recurring block offers options: "This occurrence" or "All future"

## Notifications

### Staff Notifications
- Block time created affecting their schedule
- Block time updated/cancelled
- Appointment conflicts detected

### Client Notifications
- Appointment rescheduled due to block time
- Alternative time slots suggested

## Integration Points

### Calendar Service
- Real-time calendar updates
- Conflict detection with appointments
- Availability calculation

### Notification Service
- Push notifications to mobile apps
- Email notifications for important changes
- SMS notifications for urgent conflicts

### Audit Service
- All block time operations logged
- Change history tracking
- Compliance reporting

## Rate Limiting
- 100 requests per minute per user
- 1000 requests per hour per salon
- Bulk operations count as single request

## Caching Strategy
- Block time data cached for 5 minutes
- Availability checks cached for 1 minute
- Statistics cached for 1 hour
- Cache invalidation on data changes

## Implementation Notes

### Database Schema
```sql
CREATE TABLE block_times (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    salon_id UUID NOT NULL REFERENCES salons(id),
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    reason VARCHAR(50) NOT NULL,
    custom_reason TEXT,
    staff_ids UUID[] NOT NULL,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES users(id),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_pattern JSONB,
    notes TEXT,
    status VARCHAR(20) DEFAULT 'ACTIVE',
    CONSTRAINT valid_time_range CHECK (end_time > start_time),
    CONSTRAINT valid_status CHECK (status IN ('ACTIVE', 'CANCELLED', 'EXPIRED'))
);

CREATE INDEX idx_block_times_salon_time ON block_times(salon_id, start_time, end_time);
CREATE INDEX idx_block_times_staff ON block_times USING GIN(staff_ids);
CREATE INDEX idx_block_times_status ON block_times(status) WHERE status = 'ACTIVE';
```

### Performance Considerations
- Use database indexes for time-based queries
- Implement pagination for large result sets
- Cache frequently accessed data
- Use database constraints for data integrity
- Implement soft deletes for audit trail

### Security Considerations
- Validate all input data
- Implement proper authorization checks
- Log all sensitive operations
- Use parameterized queries to prevent SQL injection
- Implement rate limiting to prevent abuse
```
