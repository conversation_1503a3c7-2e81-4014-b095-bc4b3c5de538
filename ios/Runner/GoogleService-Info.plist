<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CLIENT_ID</key>
	<string>159713565362-l0tqek5gvgdsleq2ur4ijiqtl7i812o6.apps.googleusercontent.com</string>
	<key>REVERSED_CLIENT_ID</key>
	<string>com.googleusercontent.apps.159713565362-l0tqek5gvgdsleq2ur4ijiqtl7i812o6</string>
	<key>API_KEY</key>
	<string>AIzaSyCO8pf-O0ptpalZEjpuKmP0S4rRkLkOdQE</string>
	<key>GCM_SENDER_ID</key>
	<string>159713565362</string>
	<key>PLIST_VERSION</key>
	<string>1</string>
	<key>BUNDLE_ID</key>
	<string>ro.partykidsprogramari.partykids.dev</string>
	<key>PROJECT_ID</key>
	<string>partykidsapp</string>
	<key>STORAGE_BUCKET</key>
	<string>partykidsapp.appspot.com</string>
	<key>IS_ADS_ENABLED</key>
	<false/>
	<key>IS_ANALYTICS_ENABLED</key>
	<true/>
	<key>IS_APPINVITE_ENABLED</key>
	<true/>
	<key>IS_GCM_ENABLED</key>
	<true/>
	<key>IS_SIGNIN_ENABLED</key>
	<true/>
	<key>GOOGLE_APP_ID</key>
	<string>1:159713565362:ios:371de7c613640d521ec7ab</string>
	<key>DATABASE_URL</key>
	<string>https://partykidsapp-default-rtdb.firebaseio.com</string>
</dict>
</plist>
