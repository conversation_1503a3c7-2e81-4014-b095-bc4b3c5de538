import 'package:flutter/foundation.dart';

class PhoneVerificationInfo {
  final String message;
  final String phoneNumber;
  final int expiresIn;
  final int canResendAfter;
  final int remainingAttempts;

  PhoneVerificationInfo({
    required this.message,
    required this.phoneNumber,
    required this.expiresIn,
    required this.canResendAfter,
    required this.remainingAttempts,
  });

  factory PhoneVerificationInfo.fromJson(Map<String, dynamic> json) {
    debugPrint('🔍 === PHONE VERIFICATION INFO PARSING START ===');
    debugPrint('📄 Raw JSON: $json');
    debugPrint('📄 JSON keys: ${json.keys.toList()}');
    debugPrint('📄 JSON values: ${json.values.toList()}');

    final result = PhoneVerificationInfo(
      message: json['message'] ?? '',
      phoneNumber: json['phoneNumber'] ?? '',
      expiresIn: json['expiresIn'] ?? 0,
      canResendAfter: json['canResendAfter'] ?? 0,
      remainingAttempts: json['remainingAttempts'] ?? 0,
    );

    debugPrint('✅ Parsed PhoneVerificationInfo:');
    debugPrint('   - message: ${result.message}');
    debugPrint('   - phoneNumber: ${result.phoneNumber}');
    debugPrint('   - expiresIn: ${result.expiresIn}');
    debugPrint('   - canResendAfter: ${result.canResendAfter}');
    debugPrint('   - remainingAttempts: ${result.remainingAttempts}');
    debugPrint('🔍 === PHONE VERIFICATION INFO PARSING END ===');

    return result;
  }

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'phoneNumber': phoneNumber,
      'expiresIn': expiresIn,
      'canResendAfter': canResendAfter,
      'remainingAttempts': remainingAttempts,
    };
  }
}
