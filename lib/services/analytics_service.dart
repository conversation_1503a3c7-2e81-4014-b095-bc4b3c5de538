import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import '../config/api_config.dart';
import '../config/environment.dart';

class AnalyticsService {
  static final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;
  static bool _isInitialized = false;

  /// Initialize analytics
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Only enable analytics in production
      await _analytics.setAnalyticsCollectionEnabled(EnvironmentConfig.isProduction);

      if (ApiConfig.enableLogging) {
        debugPrint('📊 Analytics initialized (enabled: ${EnvironmentConfig.isProduction})');
      }

      _isInitialized = true;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to initialize analytics: $e');
      }
    }
  }

  /// Set user properties
  static Future<void> setUserProperties({
    String? userId,
    String? userRole,
    String? salonId,
    String? salonName,
  }) async {
    if (!_isInitialized || !EnvironmentConfig.isProduction) return;

    try {
      if (userId != null) {
        await _analytics.setUserId(id: userId);
      }

      if (userRole != null) {
        await _analytics.setUserProperty(name: 'user_role', value: userRole);
      }

      if (salonId != null) {
        await _analytics.setUserProperty(name: 'salon_id', value: salonId);
      }

      if (salonName != null) {
        await _analytics.setUserProperty(name: 'salon_name', value: salonName);
      }

      if (ApiConfig.enableLogging) {
        debugPrint('📊 User properties set');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to set user properties: $e');
      }
    }
  }

  /// Track screen views
  static Future<void> trackScreenView(String screenName) async {
    if (!_isInitialized || !EnvironmentConfig.isProduction) return;

    try {
      await _analytics.logScreenView(screenName: screenName);

      if (ApiConfig.enableLogging) {
        debugPrint('📊 Screen view tracked: $screenName');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to track screen view: $e');
      }
    }
  }

  /// Track user login
  static Future<void> trackLogin(String method) async {
    if (!_isInitialized || !EnvironmentConfig.isProduction) return;

    try {
      await _analytics.logLogin(loginMethod: method);

      if (ApiConfig.enableLogging) {
        debugPrint('📊 Login tracked: $method');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to track login: $e');
      }
    }
  }

  /// Track appointment creation
  static Future<void> trackAppointmentCreated({
    String? appointmentId,
    String? clientId,
    String? groomerId,
    List<String>? services,
    double? totalPrice,
    int? duration,
  }) async {
    if (!_isInitialized || !EnvironmentConfig.isProduction) return;

    try {
      await _analytics.logEvent(
        name: 'appointment_created',
        parameters: {
          if (appointmentId != null) 'appointment_id': appointmentId,
          if (clientId != null) 'client_id': clientId,
          if (groomerId != null) 'groomer_id': groomerId,
          if (services != null) 'services_count': services.length,
          if (totalPrice != null) 'total_price': totalPrice,
          if (duration != null) 'duration_minutes': duration,
        },
      );

      if (ApiConfig.enableLogging) {
        debugPrint('📊 Appointment creation tracked');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to track appointment creation: $e');
      }
    }
  }

  /// Track client creation
  static Future<void> trackClientCreated({
    String? clientId,
    bool? hasPets,
  }) async {
    if (!_isInitialized || !EnvironmentConfig.isProduction) return;

    try {
      await _analytics.logEvent(
        name: 'client_created',
        parameters: {
          if (clientId != null) 'client_id': clientId,
          if (hasPets != null) 'has_pets': hasPets,
        },
      );

      if (ApiConfig.enableLogging) {
        debugPrint('📊 Client creation tracked');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to track client creation: $e');
      }
    }
  }

  /// Track pet creation
  static Future<void> trackPetCreated({
    String? petId,
    String? petType,
    String? breed,
  }) async {
    if (!_isInitialized || !EnvironmentConfig.isProduction) return;

    try {
      await _analytics.logEvent(
        name: 'pet_created',
        parameters: {
          if (petId != null) 'pet_id': petId,
          if (petType != null) 'pet_type': petType,
          if (breed != null) 'breed': breed,
        },
      );

      if (ApiConfig.enableLogging) {
        debugPrint('📊 Pet creation tracked');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to track pet creation: $e');
      }
    }
  }

  /// Track service usage
  static Future<void> trackServiceUsed({
    String? serviceId,
    String? serviceName,
    String? category,
    double? price,
  }) async {
    if (!_isInitialized || !EnvironmentConfig.isProduction) return;

    try {
      await _analytics.logEvent(
        name: 'service_used',
        parameters: {
          if (serviceId != null) 'service_id': serviceId,
          if (serviceName != null) 'service_name': serviceName,
          if (category != null) 'category': category,
          if (price != null) 'price': price,
        },
      );

      if (ApiConfig.enableLogging) {
        debugPrint('📊 Service usage tracked');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to track service usage: $e');
      }
    }
  }

  /// Track search actions
  static Future<void> trackSearch({
    required String searchType,
    String? query,
    int? resultsCount,
  }) async {
    if (!_isInitialized || !EnvironmentConfig.isProduction) return;

    try {
      await _analytics.logSearch(
        searchTerm: query ?? '',
        parameters: {
          'search_type': searchType,
          if (resultsCount != null) 'results_count': resultsCount,
        },
      );

      if (ApiConfig.enableLogging) {
        debugPrint('📊 Search tracked: $searchType');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to track search: $e');
      }
    }
  }

  /// Track errors
  static Future<void> trackError({
    required String errorType,
    String? errorMessage,
    String? screenName,
  }) async {
    if (!_isInitialized || !EnvironmentConfig.isProduction) return;

    try {
      await _analytics.logEvent(
        name: 'app_error',
        parameters: {
          'error_type': errorType,
          if (errorMessage != null) 'error_message': errorMessage,
          if (screenName != null) 'screen_name': screenName,
        },
      );

      if (ApiConfig.enableLogging) {
        debugPrint('📊 Error tracked: $errorType');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to track error: $e');
      }
    }
  }

  /// Track feature usage
  static Future<void> trackFeatureUsed({
    required String featureName,
    Map<String, dynamic>? parameters,
  }) async {
    if (!_isInitialized || !EnvironmentConfig.isProduction) return;

    try {
      await _analytics.logEvent(
        name: 'feature_used',
        parameters: {
          'feature_name': featureName,
          ...?parameters,
        },
      );

      if (ApiConfig.enableLogging) {
        debugPrint('📊 Feature usage tracked: $featureName');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to track feature usage: $e');
      }
    }
  }

  /// Track performance metrics
  static Future<void> trackPerformance({
    required String metricName,
    required int durationMs,
    Map<String, dynamic>? parameters,
  }) async {
    if (!_isInitialized || !EnvironmentConfig.isProduction) return;

    try {
      await _analytics.logEvent(
        name: 'performance_metric',
        parameters: {
          'metric_name': metricName,
          'duration_ms': durationMs,
          ...?parameters,
        },
      );

      if (ApiConfig.enableLogging) {
        debugPrint('📊 Performance tracked: $metricName (${durationMs}ms)');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to track performance: $e');
      }
    }
  }

  // ========== USER FLOW ANALYTICS ==========

  /// Track user journey steps
  static Future<void> trackUserJourney({
    required String journeyName,
    required String stepName,
    int? stepNumber,
    Map<String, dynamic>? stepData,
  }) async {
    if (!_isInitialized || !EnvironmentConfig.isProduction) return;

    try {
      await _analytics.logEvent(
        name: 'user_journey',
        parameters: {
          'journey_name': journeyName,
          'step_name': stepName,
          if (stepNumber != null) 'step_number': stepNumber,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          ...?stepData,
        },
      );

      if (ApiConfig.enableLogging) {
        debugPrint('📊 User journey tracked: $journeyName -> $stepName');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to track user journey: $e');
      }
    }
  }

  /// Track conversion funnel events
  static Future<void> trackFunnelEvent({
    required String funnelName,
    required String eventName,
    int? stepIndex,
    bool? completed,
    Map<String, dynamic>? eventData,
  }) async {
    if (!_isInitialized || !EnvironmentConfig.isProduction) return;

    try {
      await _analytics.logEvent(
        name: 'funnel_event',
        parameters: {
          'funnel_name': funnelName,
          'event_name': eventName,
          if (stepIndex != null) 'step_index': stepIndex,
          if (completed != null) 'completed': completed,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          ...?eventData,
        },
      );

      if (ApiConfig.enableLogging) {
        debugPrint('📊 Funnel event tracked: $funnelName -> $eventName');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to track funnel event: $e');
      }
    }
  }

  // ========== USER INTERACTION ANALYTICS ==========

  /// Track button clicks and UI interactions
  static Future<void> trackInteraction({
    required String interactionType,
    required String elementId,
    String? screenName,
    Map<String, dynamic>? interactionData,
  }) async {
    if (!_isInitialized || !EnvironmentConfig.isProduction) return;

    try {
      await _analytics.logEvent(
        name: 'user_interaction',
        parameters: {
          'interaction_type': interactionType, // 'button_click', 'form_submit', 'swipe', etc.
          'element_id': elementId,
          if (screenName != null) 'screen_name': screenName,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          ...?interactionData,
        },
      );

      if (ApiConfig.enableLogging) {
        debugPrint('📊 Interaction tracked: $interactionType -> $elementId');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to track interaction: $e');
      }
    }
  }

  /// Track form interactions and completion
  static Future<void> trackFormEvent({
    required String formName,
    required String eventType, // 'started', 'field_completed', 'submitted', 'abandoned'
    String? fieldName,
    String? screenName,
    Map<String, dynamic>? formData,
  }) async {
    if (!_isInitialized || !EnvironmentConfig.isProduction) return;

    try {
      await _analytics.logEvent(
        name: 'form_event',
        parameters: {
          'form_name': formName,
          'event_type': eventType,
          if (fieldName != null) 'field_name': fieldName,
          if (screenName != null) 'screen_name': screenName,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          ...?formData,
        },
      );

      if (ApiConfig.enableLogging) {
        debugPrint('📊 Form event tracked: $formName -> $eventType');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to track form event: $e');
      }
    }
  }

  // ========== BUSINESS-SPECIFIC ANALYTICS ==========

  /// Track salon switching behavior
  static Future<void> trackSalonSwitch({
    required String fromSalonId,
    required String toSalonId,
    String? fromSalonName,
    String? toSalonName,
    String? switchReason,
  }) async {
    if (!_isInitialized || !EnvironmentConfig.isProduction) return;

    try {
      await _analytics.logEvent(
        name: 'salon_switch',
        parameters: {
          'from_salon_id': fromSalonId,
          'to_salon_id': toSalonId,
          if (fromSalonName != null) 'from_salon_name': fromSalonName,
          if (toSalonName != null) 'to_salon_name': toSalonName,
          if (switchReason != null) 'switch_reason': switchReason,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );

      if (ApiConfig.enableLogging) {
        debugPrint('📊 Salon switch tracked: $fromSalonId -> $toSalonId');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to track salon switch: $e');
      }
    }
  }

  /// Track appointment workflow events
  static Future<void> trackAppointmentWorkflow({
    required String workflowStep, // 'started', 'client_selected', 'service_added', 'time_selected', 'completed', 'cancelled'
    String? appointmentId,
    String? clientId,
    String? serviceId,
    Map<String, dynamic>? workflowData,
  }) async {
    if (!_isInitialized || !EnvironmentConfig.isProduction) return;

    try {
      await _analytics.logEvent(
        name: 'appointment_workflow',
        parameters: {
          'workflow_step': workflowStep,
          if (appointmentId != null) 'appointment_id': appointmentId,
          if (clientId != null) 'client_id': clientId,
          if (serviceId != null) 'service_id': serviceId,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
          ...?workflowData,
        },
      );

      if (ApiConfig.enableLogging) {
        debugPrint('📊 Appointment workflow tracked: $workflowStep');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to track appointment workflow: $e');
      }
    }
  }

  /// Get analytics instance for custom tracking
  static FirebaseAnalytics get instance => _analytics;
}
