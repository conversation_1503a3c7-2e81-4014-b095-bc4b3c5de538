# Push Notification System - Backend API Contract

## Overview
Pet grooming salon app requiring push notifications to staff when appointment events occur. Frontend expects specific API endpoints and automated notification triggers.

**Provider**: Firebase Cloud Messaging (FCM)
**Note**: SMS functionality already handled separately

---

## Required API Endpoints

### FCM Token Management
```
POST /api/users/fcm-token
Body: { userId, token, platform }
Response: 200 OK

DELETE /api/users/fcm-token
Body: { token }
Response: 200 OK
```

### Notification Settings (per salon)
```
GET /api/salons/{salonId}/notification-settings
Response: {
  pushNotificationsEnabled: boolean,
  soundPreference: string,
  vibrationEnabled: boolean,
  doNotDisturb: { enabled, startTime, endTime, allowCritical },
  notificationRules: {
    newAppointments, appointmentCancellations, paymentConfirmations,
    teamMemberUpdates, systemMaintenanceAlerts, defaultPriority
  }
}

PUT /api/salons/{salonId}/notification-settings
Body: (same structure as GET response)
```

### Notification History
```
GET /api/notifications
Query: unreadOnly, type, limit
Response: [{ id, title, message, type, read, timestamp, metadata }]

PATCH /api/notifications/{id}/read
Response: 200 OK

DELETE /api/notifications/{id}
Response: 200 OK

GET /api/notifications/unread-count
Response: { data: number }
```

### Manual Sending
```
POST /api/notifications/push
Body: { title, message, type, data }

POST /api/notifications/schedule
Body: { title, message, type, scheduledTime, metadata }
```

### Analytics
```
GET /api/salons/{salonId}/notifications/stats
Query: startDate, endDate
Response: {
  totalSent, pushDelivered, failureRate,
  byType: { appointment_created, appointment_cancelled, ... },
  deliveryRates: { push }
}
```

---

## Automated Triggers (Critical)

**These must happen automatically when appointment endpoints are called:**

| Event | Trigger Endpoint | Action |
|-------|------------------|--------|
| Appointment Created | `POST /salons/{id}/appointments` | Send push + store history |
| Appointment Cancelled | `DELETE /salons/{id}/appointments/{id}` | Send push + store history |
| Appointment Completed | `PATCH /salons/{id}/appointments/{id}/status` | Send push + store history |
| Appointment Rescheduled | `PUT /salons/{id}/appointments/{id}` | Send push + store history |
| Payment Received | `POST /salons/{id}/payments` | Send push + store history |

**Requirements:**
- No frontend calls needed - triggers automatically
- Respect user notification settings
- Don't block appointment operations on notification failures
- Store all notifications in history

---

## Push Notification Format

### Romanian Content Templates
```
New appointment: "Programare nouă pentru {petName} ({clientName}) pe {date} la {time}"
Cancelled: "Programarea pentru {petName} ({clientName}) pe {date} a fost anulată"
Completed: "Programarea pentru {petName} ({clientName}) a fost finalizată"
Rescheduled: "Programarea pentru {petName} a fost mutată pe {newDate} la {newTime}"
Payment: "Plată de {amount} RON de la {clientName} confirmată"
```

### FCM Payload Structure
```json
{
  "notification": { "title": "...", "body": "..." },
  "data": {
    "type": "new_appointment|appointment_cancelled|appointment_completed|appointment_rescheduled|payment_confirmation",
    "appointmentId": "string",
    "clientName": "string",
    "petName": "string",
    "appointmentTime": "ISO8601",
    "salonId": "string"
  },
  "android": { "priority": "high", "notification": { "sound": "default" } },
  "apns": { "payload": { "aps": { "sound": "default", "badge": 1 } } }
}
```

---

## Data Storage Requirements

**Store:**
1. **FCM Tokens**: userId, token, platform, timestamps
2. **Notification Settings**: per salon preferences and rules
3. **Notification History**: id, title, message, type, read status, metadata, timestamp
4. **Delivery Tracking**: attempts, success/failure, errors (optional)

---

## Technical Requirements

### Firebase Integration
- Use Firebase Admin SDK for server-side sending
- Batch sending (up to 500 tokens per request)
- Handle invalid/expired tokens automatically
- Set proper Android notification channels and iOS APNS config

### Error Handling
- Log all notification attempts
- Track delivery success/failure rates
- Clean up invalid FCM tokens
- Don't block appointment operations on notification failures

### Performance
- Index notification history by userId and timestamp
- Consider archiving old notifications
- Rate limit if needed

---

## Firebase Setup
**Environment Variables:**
```
FIREBASE_PROJECT_ID=your-project-id
FIREBASE_PRIVATE_KEY=your-private-key
FIREBASE_CLIENT_EMAIL=your-client-email
```

**Setup Steps:**
1. Enable Cloud Messaging in Firebase Console
2. Generate service account key for Admin SDK
3. Configure iOS/Android apps
4. Set up notification channels

---

## Template Variables
- `{petName}` - Pet's name
- `{clientName}` - Client's name
- `{date}` - DD.MM.YYYY format
- `{time}` - HH:MM format
- `{newDate}` - New appointment date
- `{newTime}` - New appointment time
- `{amount}` - Payment amount

**Priority**: High - Critical for real-time staff communication
