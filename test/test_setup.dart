import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:partykidsapp/services/api_service.dart';

/// Test setup utilities for mocking platform channels and plugins
class TestSetup {
  /// Mock flutter_secure_storage plugin
  static void mockFlutterSecureStorage() {
    TestWidgetsFlutterBinding.ensureInitialized();
    
    const MethodChannel channel = MethodChannel('plugins.it_nomads.com/flutter_secure_storage');
    
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
      switch (methodCall.method) {
        case 'read':
          final String key = methodCall.arguments['key'];
          return _mockStorage[key];
        case 'write':
          final String key = methodCall.arguments['key'];
          final String value = methodCall.arguments['value'];
          _mockStorage[key] = value;
          return null;
        case 'delete':
          final String key = methodCall.arguments['key'];
          _mockStorage.remove(key);
          return null;
        case 'deleteAll':
          _mockStorage.clear();
          return null;
        case 'readAll':
          return Map<String, String>.from(_mockStorage);
        case 'containsKey':
          final String key = methodCall.arguments['key'];
          return _mockStorage.containsKey(key);
        default:
          return null;
      }
    });
  }

  /// Mock Google Maps plugin
  static void mockGoogleMaps() {
    TestWidgetsFlutterBinding.ensureInitialized();
    
    const MethodChannel channel = MethodChannel('plugins.flutter.io/google_maps_flutter');
    
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
      switch (methodCall.method) {
        case 'map#create':
          return {'mapId': 1};
        case 'map#update':
          return null;
        default:
          return null;
      }
    });
  }

  /// Mock device info plugin
  static void mockDeviceInfo() {
    TestWidgetsFlutterBinding.ensureInitialized();
    
    const MethodChannel channel = MethodChannel('plugins.flutter.io/device_info');
    
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
      switch (methodCall.method) {
        case 'getAndroidDeviceInfo':
          return {
            'version': {'sdkInt': 29},
            'brand': 'google',
            'device': 'flame',
            'model': 'Pixel 4',
          };
        case 'getIosDeviceInfo':
          return {
            'name': 'iPhone',
            'systemName': 'iOS',
            'systemVersion': '14.0',
            'model': 'iPhone',
          };
        default:
          return null;
      }
    });
  }

  /// Mock package info plugin
  static void mockPackageInfo() {
    TestWidgetsFlutterBinding.ensureInitialized();
    
    const MethodChannel channel = MethodChannel('plugins.flutter.io/package_info');
    
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
      switch (methodCall.method) {
        case 'getAll':
          return {
            'appName': 'PartykidsProject',
            'packageName': 'com.partykids.partykidsapp',
            'version': '1.0.0',
            'buildNumber': '1',
          };
        default:
          return null;
      }
    });
  }

  /// Mock URL launcher plugin
  static void mockUrlLauncher() {
    TestWidgetsFlutterBinding.ensureInitialized();

    const MethodChannel channel = MethodChannel('plugins.flutter.io/url_launcher');

    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
      switch (methodCall.method) {
        case 'canLaunch':
          return true;
        case 'launch':
          return true;
        default:
          return null;
      }
    });
  }

  /// Mock shared preferences plugin
  static void mockSharedPreferences() {
    TestWidgetsFlutterBinding.ensureInitialized();

    const MethodChannel channel = MethodChannel('plugins.flutter.io/shared_preferences');

    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
      switch (methodCall.method) {
        case 'getAll':
          return <String, dynamic>{};
        case 'setBool':
        case 'setInt':
        case 'setDouble':
        case 'setString':
        case 'setStringList':
          return true;
        case 'remove':
          return true;
        case 'clear':
          return true;
        default:
          return null;
      }
    });
  }

  /// Mock path provider plugin
  static void mockPathProvider() {
    TestWidgetsFlutterBinding.ensureInitialized();

    const MethodChannel channel = MethodChannel('plugins.flutter.io/path_provider');

    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(channel, (MethodCall methodCall) async {
      switch (methodCall.method) {
        case 'getTemporaryDirectory':
          return '/tmp';
        case 'getApplicationDocumentsDirectory':
          return '/tmp/docs';
        case 'getApplicationSupportDirectory':
          return '/tmp/support';
        case 'getLibraryDirectory':
          return '/tmp/library';
        case 'getExternalStorageDirectory':
          return '/tmp/external';
        default:
          return null;
      }
    });
  }

  /// Initialize test environment with all required bindings and mocks
  static void initializeTestEnvironment() {
    // Ensure Flutter bindings are initialized first
    TestWidgetsFlutterBinding.ensureInitialized();

    // Setup all platform channel mocks
    setupAllMocks();
  }

  /// Setup all common mocks for testing
  static void setupAllMocks() {
    mockFlutterSecureStorage();
    mockGoogleMaps();
    mockDeviceInfo();
    mockPackageInfo();
    mockUrlLauncher();
    mockSharedPreferences();
    mockPathProvider();
  }

  /// Clear all mock data
  static void clearMocks() {
    _mockStorage.clear();
  }

  /// Get mock storage data for testing
  static Map<String, String> get mockStorage => Map<String, String>.from(_mockStorage);

  /// Set mock storage data for testing
  static void setMockStorageData(Map<String, String> data) {
    _mockStorage.clear();
    _mockStorage.addAll(data);
  }

  /// Mock working hours settings for tests
  static void mockWorkingHoursSettings() {
    _mockStorage['working_hours_settings'] = '''
    {
      "openTime": 9,
      "closeTime": 17,
      "lunchBreakStart": 12,
      "lunchBreakEnd": 13,
      "workingDays": {
        "monday": true,
        "tuesday": true,
        "wednesday": true,
        "thursday": true,
        "friday": true,
        "saturday": true,
        "sunday": false
      },
      "customClosures": [
        {
          "date": "2024-06-15",
          "reason": "Renovări"
        },
        {
          "date": "2024-07-20",
          "reason": "Concediu personal"
        },
        {
          "date": "2024-08-16",
          "reason": "Test closure"
        }
      ]
    }
    ''';
  }

  /// Mock authentication token for tests
  static void mockAuthToken(String token) {
    _mockStorage['access_token'] = token; // Use the same key as AuthService
    // Also set the token in ApiService for immediate use
    ApiService.setAuthToken(token);
  }

  /// Mock salon ID for tests
  static void mockSalonId(String salonId) {
    _mockStorage['salon_id'] = salonId; // Use the same key as AuthService
  }

  /// Mock user data for tests
  static void mockUserData({
    required String userId,
    required String name,
    required String email,
    String? phone,
  }) {
    _mockStorage['user_data'] = '''
    {
      "id": "$userId",
      "name": "$name",
      "email": "$email",
      "phone": "${phone ?? ''}"
    }
    ''';
  }

  /// Setup complete test environment with all necessary mocks
  static void setupTestEnvironment({
    String? authToken,
    String? salonId,
    String? userId,
    String? userName,
    String? userEmail,
    bool includeWorkingHours = true,
  }) {
    setupAllMocks();
    
    if (authToken != null) {
      mockAuthToken(authToken);
    }
    
    if (salonId != null) {
      mockSalonId(salonId);
    }
    
    if (userId != null && userName != null && userEmail != null) {
      mockUserData(
        userId: userId,
        name: userName,
        email: userEmail,
      );
    }
    
    if (includeWorkingHours) {
      mockWorkingHoursSettings();
    }
  }
}

// Private storage for mocking flutter_secure_storage
final Map<String, String> _mockStorage = <String, String>{};
