import 'package:partykidsapp/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../../../models/working_hours_settings.dart';
import '../../../services/working_hours_service.dart';
import '../../../utils/romanian_holidays.dart';
import '../../../widgets/permission_guard.dart';
import '../../../widgets/reason_selector/smart_reason_selector.dart';
import '../../../widgets/schedule/gesture_day_toggle.dart';
import '../../../widgets/schedule/schedule_templates.dart';
import '../../../widgets/time_pickers/modern_time_picker.dart';
import '../../../providers/calendar_provider.dart';

class WorkingHoursScreen extends StatefulWidget {
  const WorkingHoursScreen({super.key});

  @override
  State<WorkingHoursScreen> createState() => _WorkingHoursScreenState();
}

class _WorkingHoursScreenState extends State<WorkingHoursScreen> {
  // Backend integration state
  WorkingHoursSettings? _currentSettings;
  bool _isLoading = true;
  bool _isSaving = false;
  String? _error;

  // Local state for UI
  final List<String> _weekDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  final Map<String, String> _dayNames = {
    'monday': 'Luni',
    'tuesday': 'Marți',
    'wednesday': 'Miercuri',
    'thursday': 'Joi',
    'friday': 'Vineri',
    'saturday': 'Sâmbătă',
    'sunday': 'Duminică',
  };

  @override
  void initState() {
    super.initState();
    _loadWorkingHours();
  }

  /// Load working hours settings from backend
  Future<void> _loadWorkingHours() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await WorkingHoursService.getWorkingHours();

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = response.error ?? 'Failed to load working hours';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading working hours: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Program de Lucru',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        elevation: 0,
        actions: [
          IconButton(
            icon:  Icon(Icons.refresh),
            onPressed: _loadWorkingHours,
            tooltip: 'Reîncarcă',
          ),
        ],
      ),
      body: PermissionGuard(
        requireManagementAccess: true,
        fallback: _buildNoPermissionView(),
        child: _isLoading
          ?  Center(child: CircularProgressIndicator(color: Theme.of(context).colorScheme.onSurface))
          : _error != null
            ? _buildErrorView()
            : _currentSettings != null
              ? _buildWorkingHoursContent()
              : _buildEmptyState(),
      ),
    );
  }

  /// Build main working hours content
  Widget _buildWorkingHoursContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Current status card
          _buildCurrentStatusCard(),
          SizedBox(height: 24),

          // Schedule templates section
          _buildScheduleTemplatesCard(),
          SizedBox(height: 24),

          // Weekly schedule section
          SizedBox(height: 16),
          _buildWeeklyScheduleCard(),
          SizedBox(height: 24),

          // Holidays section
          SizedBox(height: 16),
          _buildHolidaysCard(),
          SizedBox(height: 24),

          // Custom closures section
          SizedBox(height: 16),
          _buildCustomClosuresCard(),
          SizedBox(height: 80), // Space for floating action button
        ],
      ),
    );
  }

  /// Build current status card showing if salon is open/closed
  Widget _buildCurrentStatusCard() {
    final now = DateTime.now();
    final isOpen = _currentSettings?.isOpenOnDate(now) ?? false;
    final holiday = RomanianHolidays.getHolidayForDate(now);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              isOpen ? Theme.of(context).colorScheme.primary : Theme.of(context).colorScheme.error,
              (isOpen ? Theme.of(context).colorScheme.primary : Theme.of(context).colorScheme.error).withValues(alpha: 0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isOpen ? Icons.store : Icons.store_mall_directory_outlined,
                  color: Theme.of(context).colorScheme.onPrimary,
                  size: 32,
                ),
                SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        holiday != null ? 'Închis - ${holiday.name}' :
                        (isOpen ? 'DESCHIS' : 'ÎNCHIS'),
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onPrimary,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 4),
                      Text(
                        _getCurrentStatusSubtitle(now),
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.9),
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            Text(
              DateFormat('EEEE, dd MMMM yyyy', 'ro').format(now),
              style: TextStyle(
                color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.8),
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }


  /// Build schedule templates card
  Widget _buildScheduleTemplatesCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: ScheduleTemplates(
          onTemplateSelected: _applyScheduleTemplate,
        ),
      ),
    );
  }

  /// Apply schedule template
  Future<void> _applyScheduleTemplate(Map<String, DaySchedule> template) async {
    setState(() {
      _isSaving = true;
    });

    try {
      final request = UpdateWorkingHoursRequest(
        weeklySchedule: template,
        holidays: _currentSettings?.holidays ?? [],
        customClosures: _currentSettings?.customClosures ?? [],
      );

      final response = await WorkingHoursService.updateWorkingHours(request);

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
        });

        // Refresh calendar to reflect new business hours
        if (mounted) {
          final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
          await calendarProvider.refreshCalendarAfterWorkingHoursChange();
        }

        _showSuccessSnackBar('Șablonul a fost aplicat cu succes!');
      } else {
        _showErrorSnackBar(response.error ?? 'Eroare la aplicarea șablonului');
      }
    } catch (e) {
      _showErrorSnackBar('Eroare la aplicarea șablonului: $e');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  Widget _buildWeeklyScheduleCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                 Text(
                  'Program Săptămânal',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const Spacer(),
                if (_isSaving)
                   SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
              ],
            ),
            SizedBox(height: 8),
            Text(
              'Glisează pentru a activa/dezactiva zilele sau apasă pentru a edita',
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            SizedBox(height: 16),
            ..._weekDays.map((day) => _buildDayScheduleRow(day)),
          ],
        ),
      ),
    );
  }

  /// Build modern gesture-based day schedule row
  Widget _buildDayScheduleRow(String dayOfWeek) {
    final schedule = _currentSettings?.getScheduleForDay(dayOfWeek);
    final dayName = _dayNames[dayOfWeek] ?? dayOfWeek;

    return GestureDayToggle(
      dayName: dayName,
      isWorkingDay: schedule?.isWorkingDay ?? false,
      startTime: schedule?.startTime,
      endTime: schedule?.endTime,
      onToggle: (isWorking) => _toggleWorkingDay(dayOfWeek, isWorking),
      onTap: () => _editDaySchedule(dayOfWeek),
    );
  }

  /// Toggle working day status
  Future<void> _toggleWorkingDay(String dayOfWeek, bool isWorking) async {
    final currentSchedule = _currentSettings?.getScheduleForDay(dayOfWeek);

    final newSchedule = DaySchedule(
      startTime: isWorking ? (currentSchedule?.startTime ?? '09:00') : null,
      endTime: isWorking ? (currentSchedule?.endTime ?? '17:00') : null,
      isWorkingDay: isWorking,
      breakStart: isWorking ? currentSchedule?.breakStart : null,
      breakEnd: isWorking ? currentSchedule?.breakEnd : null,
    );

    debugPrint('⏰ Toggling working day for $dayOfWeek to $isWorking');
    debugPrint('⏰ Toggling working day from ${newSchedule.toJson()}');

    await _updateDaySchedule(dayOfWeek, newSchedule);
  }











  /// Get current status subtitle
  String _getCurrentStatusSubtitle(DateTime now) {
    final dayOfWeek = _getDayOfWeekString(now);
    final schedule = _currentSettings?.getScheduleForDay(dayOfWeek);

    if (schedule == null || !schedule.isWorkingDay) {
      return 'Zi nelucrătoare';
    }

    return 'Program: ${schedule.startTime ?? '09:00'} - ${schedule.endTime ?? '17:00'}';
  }

  /// Get day of week string from DateTime
  String _getDayOfWeekString(DateTime date) {
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    return days[date.weekday - 1];
  }

  /// Edit day schedule
  Future<void> _editDaySchedule(String dayOfWeek) async {
    final schedule = _currentSettings?.getScheduleForDay(dayOfWeek);
    final dayName = _dayNames[dayOfWeek] ?? dayOfWeek;

    final result = await showDialog<DaySchedule>(
      context: context,
      builder: (context) => _DayScheduleEditDialog(
        dayName: dayName,
        schedule: schedule,
      ),
    );

    if (result != null) {
      await _updateDaySchedule(dayOfWeek, result);
    }
  }

  /// Update day schedule
  Future<void> _updateDaySchedule(String dayOfWeek, DaySchedule newSchedule) async {
    setState(() {
      _isSaving = true;
    });

    try {
      final response = await WorkingHoursService.updateDaySchedule(
        dayOfWeek: dayOfWeek,
        schedule: newSchedule,
      );

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
        });

        // Refresh calendar to reflect new business hours
        if (mounted) {
          final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
          await calendarProvider.refreshCalendarAfterWorkingHoursChange();
        }

        _showSuccessSnackBar('Programul pentru $dayOfWeek a fost actualizat cu succes!');
      } else {
        _showErrorSnackBar(response.error ?? 'Eroare la actualizarea programului');
      }
    } catch (e) {
      _showErrorSnackBar('Eroare la actualizarea programului: $e');
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  /// Build holidays card
  Widget _buildHolidaysCard() {
    final holidays = _currentSettings?.holidays ?? [];
    final upcomingHolidays = RomanianHolidays.getUpcomingHolidays(DateTime.now());

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
             Text(
              'Sărbători și Zile Libere',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 16),
            if (upcomingHolidays.isEmpty)
              Text(
                'Nu sunt sărbători în următoarele 30 de zile',
                style: TextStyle(
                  fontSize: 14,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  fontStyle: FontStyle.italic,
                ),
              )
            else
              ...upcomingHolidays.take(5).map((holiday) => _buildHolidayItem(holiday)),
          ],
        ),
      ),
    );
  }

  /// Build holiday item
  Widget _buildHolidayItem(Holiday holiday) {
    final isToday = DateTime.now().day == holiday.date.day &&
        DateTime.now().month == holiday.date.month &&
        DateTime.now().year == holiday.date.year;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: isToday ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1) : Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
        border: isToday ? Border.all(color: Theme.of(context).colorScheme.onSurface, width: 2) : null,
      ),
      child: Row(
        children: [
          Icon(
            _getHolidayIcon(holiday.type),
            color: isToday ? Theme.of(context).colorScheme.primary : Theme.of(context).colorScheme.onSurfaceVariant,
            size: 20,
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  holiday.name,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                    color: isToday ? Theme.of(context).colorScheme.primary : Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                Text(
                  DateFormat('dd MMMM yyyy', 'ro').format(holiday.date),
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          if (holiday.isWorkingDay)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child:  Text(
                'Lucrăm',
                style: TextStyle(
                  fontSize: 10,
                  color: Theme.of(context).colorScheme.onSurface,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Get holiday icon based on type
  IconData _getHolidayIcon(HolidayType type) {
    switch (type) {
      case HolidayType.religious:
        return Icons.church;
      case HolidayType.national:
        return Icons.flag;
      case HolidayType.legal:
        return Icons.event;
    }
  }

  /// Build custom closures card
  Widget _buildCustomClosuresCard() {
    final customClosures = _currentSettings?.customClosures ?? [];

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    'Închideri Personalizate',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: _addCustomClosure,
                  icon:  Icon(Icons.add),
                  label: Text('Adaugă'),
                  style: ElevatedButton.styleFrom(
                   
                    
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 16),
            if (customClosures.isEmpty)
              Text(
                'Nu sunt închideri personalizate programate',
                style: TextStyle(
                  fontSize: 14,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  fontStyle: FontStyle.italic,
                ),
              )
            else
              ...customClosures.map((closure) => _buildCustomClosureItem(closure)),
          ],
        ),
      ),
    );
  }

  /// Build custom closure item
  Widget _buildCustomClosureItem(CustomClosure closure) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.event_busy,
            color: Colors.orange,
            size: 20,
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  closure.reason,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  _formatItemRange(closure),
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                if (closure.description != null && closure.description!.isNotEmpty)
                  Text(
                    closure.description!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _removeCustomClosure(closure),
            icon: Icon(Icons.delete, color: Theme.of(context).colorScheme.error, size: 20),
            tooltip: 'Șterge închiderea',
          ),
        ],
      ),
    );
  }

  /// Add custom closure
  Future<void> _addCustomClosure() async {
    final result = await showDialog<CustomClosure>(
      context: context,
      builder: (context) => const _CustomClosureAddDialog(),
    );

    if (result != null) {
      setState(() {
        _isSaving = true;
      });

      try {
        final response = await WorkingHoursService.addCustomClosure(result);

        if (response.success && response.data != null) {
          setState(() {
            _currentSettings = response.data!;
          });

          // Trigger calendar update for custom closure changes
          if (mounted) {
            final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
            await calendarProvider.handleScheduleUpdate(
              salonScheduleChanged: false,
              customClosuresChanged: true,
            );
            debugPrint('🔄 Salon custom closure added - calendar updated');
          }

          _showSuccessSnackBar('Închiderea personalizată a fost adăugată cu succes!');
        } else {
          _showErrorSnackBar(response.error ?? 'Eroare la adăugarea închiderii');
        }
      } catch (e) {
        _showErrorSnackBar('Eroare la adăugarea închiderii: $e');
      } finally {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  /// Remove custom closure
  Future<void> _removeCustomClosure(CustomClosure closure) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Confirmare ștergere'),
        content: Text('Sigur doriți să ștergeți închiderea din ${_formatItemRange(closure)}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('Anulează'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Theme.of(context).colorScheme.error),
            child: Text('Șterge', style: TextStyle(color: Theme.of(context).colorScheme.onError)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _isSaving = true;
      });

      try {
        final response = await WorkingHoursService.removeCustomClosure(closure.startDate);

        if (response.success && response.data != null) {
          setState(() {
            _currentSettings = response.data!;
          });

          // Trigger calendar update for custom closure removal
          if (mounted) {
            final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);
            await calendarProvider.handleScheduleUpdate(
              salonScheduleChanged: false,
              customClosuresChanged: true,
            );
            debugPrint('🔄 Salon custom closure removed - calendar updated');
          }

          _showSuccessSnackBar('Închiderea a fost ștearsă cu succes!');
        } else {
          _showErrorSnackBar(response.error ?? 'Eroare la ștergerea închiderii');
        }
      } catch (e) {
        _showErrorSnackBar('Eroare la ștergerea închiderii: $e');
      } finally {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  /// Build error view
  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            SizedBox(height: 16),
            Text(
              'Eroare la încărcarea programului de lucru',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              _error ?? 'Eroare necunoscută',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadWorkingHours,
              style: ElevatedButton.styleFrom(
               
                
              ),
              child: Text('Încearcă din nou'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build no permission view
  Widget _buildNoPermissionView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
             Icon(
              Icons.lock_outline,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            SizedBox(height: 16),
            Text(
              'Acces restricționat',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              'Doar administratorii echipei pot configura programul de lucru pentru aceasta.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
               
                
              ),
              child: Text('Înapoi'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build empty state
  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.schedule,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            SizedBox(height: 16),
            Text(
              'Program de Lucru',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Nu s-au găsit setări pentru programul de lucru.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadWorkingHours,
              style: ElevatedButton.styleFrom(
               
                
              ),
              child: Text('Reîncarcă'),
            ),
          ],
        ),
      ),
    );
  }

  /// Show success message
  void _showSuccessSnackBar(String message) {
    showTopSnackBar(context, 
      SnackBar(
        content: Text(message),
       
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Show error message
  void _showErrorSnackBar(String message) {
    showTopSnackBar(context, 
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}

/// Enhanced dialog for editing day schedule with modern UX
class _DayScheduleEditDialog extends StatefulWidget {
  final String dayName;
  final DaySchedule? schedule;

  const _DayScheduleEditDialog({
    required this.dayName,
    this.schedule,
  });

  @override
  State<_DayScheduleEditDialog> createState() => _DayScheduleEditDialogState();
}

class _DayScheduleEditDialogState extends State<_DayScheduleEditDialog>
    with TickerProviderStateMixin {
  late bool _isWorkingDay;
  String? _startTime;
  String? _endTime;
  String? _breakStart;
  String? _breakEnd;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _isWorkingDay = widget.schedule?.isWorkingDay ?? true;
    _startTime = widget.schedule?.startTime;
    _endTime = widget.schedule?.endTime;
    _breakStart = widget.schedule?.breakStart;
    _breakEnd = widget.schedule?.breakEnd;

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                constraints: const BoxConstraints(maxWidth: 400),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
                        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.onSurface,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.schedule,
                              color: Theme.of(context).colorScheme.onPrimary,
                              size: 20,
                            ),
                          ),
                          SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.dayName,
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  'Configurează programul zilnic',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Content
                    Padding(
                      padding: const EdgeInsets.all(24),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Working day toggle
                          _buildWorkingDayToggle(),

                          if (_isWorkingDay) ...[
                            SizedBox(height: 24),
                            _buildTimeSection(),
                            SizedBox(height: 24),
                            _buildBreakSection(),
                          ],
                        ],
                      ),
                    ),

                    // Actions
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceContainerHighest,
                        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(20)),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(vertical: 12),
                              ),
                              child: Text(
                                'Anulează',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: _saveSchedule,
                              style: ElevatedButton.styleFrom(
                               
                                
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: Text(
                                'Salvează',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildWorkingDayToggle() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _isWorkingDay
            ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
            : Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _isWorkingDay
              ? Theme.of(context).colorScheme.primary
              : Colors.grey.shade300,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _isWorkingDay
                  ? Theme.of(context).colorScheme.primary
                  : Colors.grey.shade300,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _isWorkingDay ? Icons.work : Icons.work_off,
              color: Theme.of(context).colorScheme.onPrimary,
              size: 20,
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _isWorkingDay ? 'Zi lucrătoare' : 'Zi liberă',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: _isWorkingDay
                        ? Theme.of(context).colorScheme.primary
                        : Colors.grey.shade600,
                  ),
                ),
                Text(
                  _isWorkingDay
                      ? 'Salonul este deschis în această zi'
                      : 'Salonul este închis în această zi',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _isWorkingDay,
            onChanged: (value) {
              setState(() {
                _isWorkingDay = value;
                if (!value) {
                  _startTime = null;
                  _endTime = null;
                  _breakStart = null;
                  _breakEnd = null;
                }
              });
            },
            activeColor: Theme.of(context).colorScheme.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildTimeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Ore de funcționare',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: ModernTimePicker(
                label: 'Ora de început',
                initialTime: _startTime ?? '09:00',
                onTimeChanged: (time) {
                  setState(() {
                    _startTime = time;
                  });
                },
              ),
            ),
            SizedBox(width: 16),
            Expanded(
              child: ModernTimePicker(
                label: 'Ora de sfârșit',
                initialTime: _endTime ?? '17:00',
                onTimeChanged: (time) {
                  setState(() {
                    _endTime = time;
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBreakSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Pauză de prânz',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const Spacer(),
            Switch(
              value: _breakStart != null && _breakEnd != null,
              onChanged: (value) {
                setState(() {
                  if (value) {
                    _breakStart = '12:00';
                    _breakEnd = '13:00';
                  } else {
                    _breakStart = null;
                    _breakEnd = null;
                  }
                });
              },
              activeColor: Theme.of(context).colorScheme.primary,
            ),
          ],
        ),
        if (_breakStart != null && _breakEnd != null) ...[
          SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ModernTimePicker(
                  label: 'Început pauză',
                  initialTime: _breakStart ?? '12:00',
                  onTimeChanged: (time) {
                    setState(() {
                      _breakStart = time;
                    });
                  },
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: ModernTimePicker(
                  label: 'Sfârșit pauză',
                  initialTime: _breakEnd ?? '13:00',
                  onTimeChanged: (time) {
                    setState(() {
                      _breakEnd = time;
                    });
                  },
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  void _saveSchedule() {
    // Validate times
    if (_isWorkingDay) {
      print(_startTime);
      print(_endTime);
      if (_startTime == null || _endTime == null ||
          !RomanianHolidays.isValidTimeFormat(_startTime!) ||
          !RomanianHolidays.isValidTimeFormat(_endTime!)) {
          _startTime = '09:00';
          _endTime = '17:00';
      }

      if (_breakStart != null && _breakEnd != null) {
        if (!RomanianHolidays.isValidTimeFormat(_breakStart!) ||
            !RomanianHolidays.isValidTimeFormat(_breakEnd!)) {
          showTopSnackBar(context, 
            SnackBar(
              content: Text('Format oră pauză invalid. Folosiți formatul HH:MM'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
          return;
        }
      }
    }

    final schedule = DaySchedule(
      startTime: _isWorkingDay ? _startTime : null,
      endTime: _isWorkingDay ? _endTime : null,
      isWorkingDay: _isWorkingDay,
      breakStart: _isWorkingDay ? _breakStart : null,
      breakEnd: _isWorkingDay ? _breakEnd : null,
    );

    Navigator.of(context).pop(schedule);
  }
}

/// Enhanced dialog for adding custom closure with smart reason selector
class _CustomClosureAddDialog extends StatefulWidget {
  const _CustomClosureAddDialog();

  @override
  State<_CustomClosureAddDialog> createState() => _CustomClosureAddDialogState();
}

class _CustomClosureAddDialogState extends State<_CustomClosureAddDialog>
    with TickerProviderStateMixin {
  String? _selectedReason;
  final _descriptionController = TextEditingController();
  DateTimeRange _selectedRange =
      DateTimeRange(start: DateTime.now(), end: DateTime.now());

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                constraints: const BoxConstraints(maxWidth: 500, maxHeight: 600),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
                        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.secondary,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.event_busy,
                              color: Theme.of(context).colorScheme.onSecondary,
                              size: 20,
                            ),
                          ),
                          SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Închidere personalizată',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                Text(
                                  'Adaugă o zi de închidere specială',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Content
                    Flexible(
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Smart reason selector
                            SmartReasonSelector(
                              label: 'Motiv închidere',
                              onReasonChanged: (reason) {
                                _selectedReason = reason;
                              },
                            ),

                            SizedBox(height: 24),

                            // Date selector
                            _buildDateRangeSelector(),

                            SizedBox(height: 24),

                            // Description field
                            _buildDescriptionField(),
                          ],
                        ),
                      ),
                    ),

                    // Actions
                    Container(
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surfaceContainerHighest,
                        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(20)),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            child: TextButton(
                              onPressed: () => Navigator.of(context).pop(),
                              style: TextButton.styleFrom(
                                padding: const EdgeInsets.symmetric(vertical: 12),
                                foregroundColor: Theme.of(context).colorScheme.onSurfaceVariant,
                              ),
                              child: const Text(
                                'Anulează',
                                style: TextStyle(
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: _saveClosure,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Theme.of(context).colorScheme.primary,
                                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                                padding: const EdgeInsets.symmetric(horizontal: 12),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: const Text(
                                'Adaugă închiderea',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDateRangeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Perioada închiderii',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 12),
        GestureDetector(
          onTap: _selectDateRange,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Theme.of(context).colorScheme.outline),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.calendar_today,
                    color: Theme.of(context).colorScheme.secondary,
                    size: 20,
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Perioada selectată',
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      SizedBox(height: 2),
                      Text(
                        _formatRange(),
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.keyboard_arrow_down,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDescriptionField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Descriere suplimentară (opțional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 12),
        TextFormField(
          controller: _descriptionController,
          decoration: InputDecoration(
            hintText: 'Adaugă detalii suplimentare...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Theme.of(context).colorScheme.outline),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Theme.of(context).colorScheme.secondary),
            ),
            contentPadding: const EdgeInsets.all(16),
          ),
          maxLines: 3,
          maxLength: 200,
        ),
      ],
    );
  }

  Future<void> _selectDateRange() async {
    final picked = await showDateRangePicker(
      context: context,
      initialDateRange: _selectedRange,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ro'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
              primary: Theme.of(context).colorScheme.secondary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      setState(() {
        _selectedRange = picked;
      });
    }
  }

  String _formatRange() {
    final start = DateFormat('dd MMM yyyy', 'ro').format(_selectedRange.start);
    final end = DateFormat('dd MMM yyyy', 'ro').format(_selectedRange.end);
    return _selectedRange.start == _selectedRange.end ? start : '$start - $end';
  }

  String _formatItemRange(CustomClosure closure) {
    final start = DateFormat('dd MMM yyyy', 'ro').format(closure.startDate);
    final end = DateFormat('dd MMM yyyy', 'ro').format(closure.endDate);
    return closure.startDate == closure.endDate ? start : '$start - $end';
  }

  void _saveClosure() {
    if (_selectedReason == null || _selectedReason!.trim().isEmpty) {
      showTopSnackBar(context, 
        SnackBar(
          content: Text('Te rugăm să selectezi un motiv pentru închidere'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
      return;
    }

    final closure = CustomClosure(
      reason: _selectedReason!.trim(),
      startDate: _selectedRange.start,
      endDate: _selectedRange.end,
      description: _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
    );

    Navigator.of(context).pop(closure);
  }
}