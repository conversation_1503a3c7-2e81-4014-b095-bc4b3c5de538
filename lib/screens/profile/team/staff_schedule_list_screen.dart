import 'package:flutter/material.dart';

import '../../../services/staff_service.dart';
import '../../../widgets/cards/staff_profile_card.dart';

class StaffScheduleListScreen extends StatefulWidget {
  const StaffScheduleListScreen({super.key});

  @override
  State<StaffScheduleListScreen> createState() => _StaffScheduleListScreenState();
}

class _StaffScheduleListScreenState extends State<StaffScheduleListScreen> {
  late Future<StaffListResponse?> _staffFuture;

  @override
  void initState() {
    super.initState();
    _staffFuture = _loadStaff();
  }

  Future<StaffListResponse?> _loadStaff() async {
    final response = await StaffService.getCurrentSalonStaff(activeOnly: true);
    if (response.success && response.data != null) {
      return response.data;
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Program personal'),
        elevation: 0,
      ),
      body: FutureBuilder<StaffListResponse?>(
        future: _staffFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('Eroare: ${snapshot.error}'));
          }

          final staff = snapshot.data?.activeStaff ?? [];
          if (staff.isEmpty) {
            return const Center(child: Text('Niciun membru activ'));
          }

          return ListView.builder(
            padding: const EdgeInsets.only(top: 8, bottom: 8),
            itemCount: staff.length,
            itemBuilder: (context, index) {
              final member = staff[index];
              return StaffProfileCard(staff: member);
            },
          );
        },
      ),
    );
  }
}
