#!/bin/bash
# Debug script for iOS signing issues
# This script helps diagnose and fix common iOS code signing problems

set -e

echo "🔍 iOS Code Signing Debug Script"
echo "================================="

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check required tools
echo "📋 Checking required tools..."
if ! command_exists xcodebuild; then
    echo "❌ xcodebuild not found. Please install Xcode."
    exit 1
fi

if ! command_exists security; then
    echo "❌ security command not found."
    exit 1
fi

echo "✅ Required tools found"

# Check Xcode version
echo ""
echo "📱 Xcode Information:"
xcodebuild -version

# Check available SDKs
echo ""
echo "📱 Available iOS SDKs:"
xcodebuild -showsdks | grep iOS

# Check team information
echo ""
echo "👥 Development Teams:"
if command_exists xcrun; then
    xcrun simctl list devicetypes 2>/dev/null | head -5 || echo "Unable to list device types"
fi

# Check keychain information
echo ""
echo "🔐 Keychain Information:"
security list-keychains
echo ""
echo "🔐 Default keychain:"
security default-keychain

# Check certificates
echo ""
echo "📜 Available certificates:"
security find-identity -v -p codesigning

# Check provisioning profiles
echo ""
echo "📋 Provisioning Profiles:"
PROFILES_DIR="$HOME/Library/MobileDevice/Provisioning Profiles"
if [ -d "$PROFILES_DIR" ]; then
    echo "Profiles directory: $PROFILES_DIR"
    ls -la "$PROFILES_DIR" | head -10
else
    echo "No provisioning profiles directory found"
fi

# Check App Store Connect API key
echo ""
echo "🔑 App Store Connect API Key:"
API_KEY_DIR="$HOME/.appstoreconnect/private_keys"
if [ -d "$API_KEY_DIR" ]; then
    echo "API keys directory: $API_KEY_DIR"
    ls -la "$API_KEY_DIR"
else
    echo "No API keys directory found"
fi

# Check project configuration
echo ""
echo "📱 Project Configuration:"
if [ -f "ios/Runner.xcodeproj/project.pbxproj" ]; then
    echo "✅ Xcode project found"
    
    # Check bundle identifier
    BUNDLE_ID=$(grep -o 'PRODUCT_BUNDLE_IDENTIFIER = [^;]*' ios/Runner.xcodeproj/project.pbxproj | head -1 | cut -d' ' -f3)
    echo "Bundle ID: $BUNDLE_ID"
    
    # Check team ID
    TEAM_ID=$(grep -o 'DEVELOPMENT_TEAM = [^;]*' ios/Runner.xcodeproj/project.pbxproj | head -1 | cut -d' ' -f3)
    echo "Team ID: $TEAM_ID"
    
    # Check code signing style
    SIGNING_STYLE=$(grep -o 'CODE_SIGN_STYLE = [^;]*' ios/Runner.xcodeproj/project.pbxproj | head -1 | cut -d' ' -f3)
    echo "Code signing style: $SIGNING_STYLE"
else
    echo "❌ Xcode project not found"
fi

# Check Flutter configuration
echo ""
echo "🐦 Flutter Configuration:"
if command_exists flutter; then
    flutter --version | head -3
    echo ""
    echo "Flutter doctor (iOS):"
    flutter doctor --verbose | grep -A 10 "iOS toolchain"
else
    echo "❌ Flutter not found"
fi

echo ""
echo "🔍 Debug complete!"
echo ""
echo "💡 Common fixes:"
echo "1. Ensure your Apple Developer account has valid certificates"
echo "2. Check that your bundle ID matches your App Store Connect app"
echo "3. Verify your team ID is correct (6MB735DMZ5)"
echo "4. Make sure App Store Connect API key is properly configured"
echo "5. Try cleaning and rebuilding: flutter clean && flutter pub get"
