# Improved UX for Unavailable Calendar Time Slots

## Problem
Current calendar views indicate unavailable slots using a dense diagonal hatch pattern. While this technically conveys disabled status, it visually overwhelms the interface and reduces readability, especially in dark mode.

## Proposed Solution
1. **Color-coded Overlay**
   - Replace heavy hatch lines with a simple semi-transparent color overlay.
   - Use the existing `surfaceVariant` color with 40‑60% opacity so the slot remains visible but clearly inactive.
2. **Icon Indicator**
   - Display a small lock or blocked icon in the top-right corner of the slot to reinforce that it cannot be booked.
   - Tooltip text can still describe the reason when the user long‑presses or hovers.
3. **Reason Tooltip**
   - Keep the existing tooltip functionality that explains *why* a slot is unavailable (e.g. lunch break, staff not working, salon closed).
4. **Consistent Patterns for Special Cases**
   - Lunch breaks can use a subtle orange tint to match the warning color from the theme.
   - Full-salon closures can use the theme's error color with a slightly stronger opacity.

## Benefits
- Cleaner look without distracting hatch lines.
- Works in both light and dark themes.
- Maintains accessibility by providing textual explanations on hover/tap.
- Icon plus color overlay quickly communicates the slot status at a glance.

## Implementation Notes
- Update `TimeSlot` to conditionally stack an `Icon(Icons.lock)` and a semi-transparent `Container` when `isAvailable` is `false`.
- Remove the `DiagonalLinesPainter` overlay used for general unavailability (it can still be kept for block-time blocks if desired).
- Reuse theme colors (`surfaceVariant`, `darkError`, `darkWarning`) so the design remains consistent with the rest of the application.
