import 'package:flutter/material.dart';
import '../../services/analytics_service.dart';
import '../../services/screen_time_service.dart';

/// Wrapper widget that automatically tracks user interactions
class AnalyticsWrapper extends StatelessWidget {
  final Widget child;
  final String? elementId;
  final String? screenName;
  final Map<String, dynamic>? analyticsData;

  const AnalyticsWrapper({
    super.key,
    required this.child,
    this.elementId,
    this.screenName,
    this.analyticsData,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (elementId != null) {
          ScreenTimeService.recordActivity();
          AnalyticsService.trackInteraction(
            interactionType: 'tap',
            elementId: elementId!,
            screenName: screenName,
            interactionData: analyticsData,
          );
        }
      },
      child: child,
    );
  }
}

/// Enhanced button that automatically tracks clicks
class AnalyticsButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final String buttonId;
  final String? screenName;
  final Map<String, dynamic>? analyticsData;
  final ButtonStyle? style;

  const AnalyticsButton({
    super.key,
    required this.child,
    required this.buttonId,
    this.onPressed,
    this.screenName,
    this.analyticsData,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: style,
      onPressed: onPressed == null ? null : () {
        // Track button click
        ScreenTimeService.recordActivity();
        AnalyticsService.trackInteraction(
          interactionType: 'button_click',
          elementId: buttonId,
          screenName: screenName,
          interactionData: analyticsData,
        );
        
        // Execute original callback
        onPressed!();
      },
      child: child,
    );
  }
}

/// Enhanced text button that automatically tracks clicks
class AnalyticsTextButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final String buttonId;
  final String? screenName;
  final Map<String, dynamic>? analyticsData;
  final ButtonStyle? style;

  const AnalyticsTextButton({
    super.key,
    required this.child,
    required this.buttonId,
    this.onPressed,
    this.screenName,
    this.analyticsData,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    return TextButton(
      style: style,
      onPressed: onPressed == null ? null : () {
        // Track button click
        ScreenTimeService.recordActivity();
        AnalyticsService.trackInteraction(
          interactionType: 'text_button_click',
          elementId: buttonId,
          screenName: screenName,
          interactionData: analyticsData,
        );
        
        // Execute original callback
        onPressed!();
      },
      child: child,
    );
  }
}

/// Enhanced icon button that automatically tracks clicks
class AnalyticsIconButton extends StatelessWidget {
  final Widget icon;
  final VoidCallback? onPressed;
  final String buttonId;
  final String? screenName;
  final Map<String, dynamic>? analyticsData;
  final double? iconSize;
  final Color? color;

  const AnalyticsIconButton({
    super.key,
    required this.icon,
    required this.buttonId,
    this.onPressed,
    this.screenName,
    this.analyticsData,
    this.iconSize,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: icon,
      iconSize: iconSize,
      color: color,
      onPressed: onPressed == null ? null : () {
        // Track button click
        ScreenTimeService.recordActivity();
        AnalyticsService.trackInteraction(
          interactionType: 'icon_button_click',
          elementId: buttonId,
          screenName: screenName,
          interactionData: analyticsData,
        );
        
        // Execute original callback
        onPressed!();
      },
    );
  }
}

/// Enhanced floating action button that automatically tracks clicks
class AnalyticsFloatingActionButton extends StatelessWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final String buttonId;
  final String? screenName;
  final Map<String, dynamic>? analyticsData;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const AnalyticsFloatingActionButton({
    super.key,
    required this.child,
    required this.buttonId,
    this.onPressed,
    this.screenName,
    this.analyticsData,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      onPressed: onPressed == null ? null : () {
        // Track button click
        ScreenTimeService.recordActivity();
        AnalyticsService.trackInteraction(
          interactionType: 'fab_click',
          elementId: buttonId,
          screenName: screenName,
          interactionData: analyticsData,
        );
        
        // Execute original callback
        onPressed!();
      },
      child: child,
    );
  }
}

/// Enhanced list tile that automatically tracks taps
class AnalyticsListTile extends StatelessWidget {
  final Widget? leading;
  final Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final String tileId;
  final String? screenName;
  final Map<String, dynamic>? analyticsData;

  const AnalyticsListTile({
    super.key,
    required this.tileId,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.screenName,
    this.analyticsData,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: leading,
      title: title,
      subtitle: subtitle,
      trailing: trailing,
      onTap: onTap == null ? null : () {
        // Track list tile tap
        ScreenTimeService.recordActivity();
        AnalyticsService.trackInteraction(
          interactionType: 'list_tile_tap',
          elementId: tileId,
          screenName: screenName,
          interactionData: analyticsData,
        );
        
        // Execute original callback
        onTap!();
      },
    );
  }
}

/// Enhanced card that automatically tracks taps
class AnalyticsCard extends StatelessWidget {
  final Widget child;
  final VoidCallback? onTap;
  final String cardId;
  final String? screenName;
  final Map<String, dynamic>? analyticsData;
  final EdgeInsetsGeometry? margin;
  final Color? color;
  final double? elevation;

  const AnalyticsCard({
    super.key,
    required this.child,
    required this.cardId,
    this.onTap,
    this.screenName,
    this.analyticsData,
    this.margin,
    this.color,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: margin,
      color: color,
      elevation: elevation,
      child: InkWell(
        onTap: onTap == null ? null : () {
          // Track card tap
          ScreenTimeService.recordActivity();
          AnalyticsService.trackInteraction(
            interactionType: 'card_tap',
            elementId: cardId,
            screenName: screenName,
            interactionData: analyticsData,
          );
          
          // Execute original callback
          onTap!();
        },
        child: child,
      ),
    );
  }
}

/// Form field wrapper that tracks form interactions
class AnalyticsFormField extends StatefulWidget {
  final Widget child;
  final String fieldId;
  final String formName;
  final String? screenName;
  final Map<String, dynamic>? analyticsData;

  const AnalyticsFormField({
    super.key,
    required this.child,
    required this.fieldId,
    required this.formName,
    this.screenName,
    this.analyticsData,
  });

  @override
  State<AnalyticsFormField> createState() => _AnalyticsFormFieldState();
}

class _AnalyticsFormFieldState extends State<AnalyticsFormField> {
  bool _hasStartedEditing = false;

  @override
  Widget build(BuildContext context) {
    return Focus(
      onFocusChange: (hasFocus) {
        if (hasFocus && !_hasStartedEditing) {
          _hasStartedEditing = true;
          ScreenTimeService.recordActivity();
          AnalyticsService.trackFormEvent(
            formName: widget.formName,
            eventType: 'field_focused',
            fieldName: widget.fieldId,
            screenName: widget.screenName,
            formData: widget.analyticsData,
          );
        } else if (!hasFocus && _hasStartedEditing) {
          AnalyticsService.trackFormEvent(
            formName: widget.formName,
            eventType: 'field_completed',
            fieldName: widget.fieldId,
            screenName: widget.screenName,
            formData: widget.analyticsData,
          );
        }
      },
      child: widget.child,
    );
  }
}
