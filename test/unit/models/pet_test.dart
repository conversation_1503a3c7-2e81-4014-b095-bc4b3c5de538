import 'package:flutter_test/flutter_test.dart';
import 'package:partykidsapp/models/pet.dart';

void main() {
  group('Pet Model Tests', () {
    group('Constructor Tests', () {
      test('should create pet with required fields', () {
        final birthDate = DateTime(2020, 5, 15);
        
        final pet = Pet(
          id: '1',
          name: '<PERSON>',
          species: 'dog',
          breed: 'Golden Retriever',
          gender: 'male',
          birthDate: birthDate,
          weight: 25.5,
          color: 'golden',
          ownerId: 'owner1',
        );

        expect(pet.id, equals('1'));
        expect(pet.name, equals('Buddy'));
        expect(pet.species, equals('dog'));
        expect(pet.breed, equals('Golden Retriever'));
        expect(pet.gender, equals('male'));
        expect(pet.birthDate, equals(birthDate));
        expect(pet.weight, equals(25.5));
        expect(pet.color, equals('golden'));
        expect(pet.ownerId, equals('owner1'));
        expect(pet.microchipNumber, equals(''));
        expect(pet.vaccinations, isEmpty);
        expect(pet.notes, equals(''));
        expect(pet.photoUrl, equals(''));
      });

      test('should create pet with all fields', () {
        final birthDate = DateTime(2019, 3, 10);
        
        final pet = Pet(
          id: '2',
          name: 'Whiskers',
          species: 'cat',
          breed: 'Persian',
          gender: 'female',
          birthDate: birthDate,
          weight: 4.2,
          color: 'white',
          ownerId: 'owner2',
          microchipNumber: 'MC123456789',
          vaccinations: ['Rabies', 'FVRCP'],
          notes: 'Very friendly cat',
          photoUrl: 'https://example.com/whiskers.jpg',
        );

        expect(pet.id, equals('2'));
        expect(pet.name, equals('Whiskers'));
        expect(pet.species, equals('cat'));
        expect(pet.breed, equals('Persian'));
        expect(pet.gender, equals('female'));
        expect(pet.birthDate, equals(birthDate));
        expect(pet.weight, equals(4.2));
        expect(pet.color, equals('white'));
        expect(pet.ownerId, equals('owner2'));
        expect(pet.microchipNumber, equals('MC123456789'));
        expect(pet.vaccinations, equals(['Rabies', 'FVRCP']));
        expect(pet.notes, equals('Very friendly cat'));
        expect(pet.photoUrl, equals('https://example.com/whiskers.jpg'));
      });
    });

    group('Age Calculation Tests', () {
      test('should calculate age correctly for young pet', () {
        final birthDate = DateTime.now().subtract(const Duration(days: 365)); // 1 year ago
        
        final pet = Pet(
          id: '1',
          name: 'Young Pet',
          species: 'dog',
          breed: 'Labrador',
          gender: 'male',
          birthDate: birthDate,
          weight: 20.0,
          color: 'black',
          ownerId: 'owner1',
        );

        final age = pet.ageInYears;
        expect(age, equals(1));
      });

      test('should calculate age correctly for older pet', () {
        final birthDate = DateTime.now().subtract(const Duration(days: 365 * 5 + 100)); // ~5.3 years ago
        
        final pet = Pet(
          id: '1',
          name: 'Old Pet',
          species: 'cat',
          breed: 'Maine Coon',
          gender: 'female',
          birthDate: birthDate,
          weight: 6.5,
          color: 'brown',
          ownerId: 'owner1',
        );

        final age = pet.ageInYears;
        expect(age, equals(5));
      });

      test('should return 0 for very young pet', () {
        final birthDate = DateTime.now().subtract(const Duration(days: 100)); // 100 days ago
        
        final pet = Pet(
          id: '1',
          name: 'Puppy',
          species: 'dog',
          breed: 'Beagle',
          gender: 'male',
          birthDate: birthDate,
          weight: 5.0,
          color: 'brown',
          ownerId: 'owner1',
        );

        final age = pet.ageInYears;
        expect(age, equals(0));
      });
    });

    group('JSON Serialization Tests', () {
      test('should create pet from JSON with all fields', () {
        final json = {
          'id': '1',
          'name': 'Rex',
          'species': 'dog',
          'breed': 'German Shepherd',
          'gender': 'male',
          'birthDate': '2020-06-15T00:00:00.000Z',
          'weight': 30.5,
          'color': 'brown and black',
          'ownerId': 'owner123',
          'microchipNumber': 'MC987654321',
          'vaccinations': ['Rabies', 'DHPP', 'Bordetella'],
          'notes': 'Very active dog, loves to play fetch',
          'photoUrl': 'https://example.com/rex.jpg',
        };

        final pet = Pet.fromJson(json);

        expect(pet.id, equals('1'));
        expect(pet.name, equals('Rex'));
        expect(pet.species, equals('dog'));
        expect(pet.breed, equals('German Shepherd'));
        expect(pet.gender, equals('male'));
        expect(pet.birthDate, equals(DateTime.parse('2020-06-15T00:00:00.000Z')));
        expect(pet.weight, equals(30.5));
        expect(pet.color, equals('brown and black'));
        expect(pet.ownerId, equals('owner123'));
        expect(pet.microchipNumber, equals('MC987654321'));
        expect(pet.vaccinations, equals(['Rabies', 'DHPP', 'Bordetella']));
        expect(pet.notes, equals('Very active dog, loves to play fetch'));
        expect(pet.photoUrl, equals('https://example.com/rex.jpg'));
      });

      test('should create pet from JSON with minimal fields', () {
        final json = {
          'id': '2',
          'name': 'Mittens',
          'species': 'cat',
          'breed': 'Domestic Shorthair',
          'gender': 'female',
          'birthDate': '2021-01-01T00:00:00.000Z',
          'weight': 3.8,
          'color': 'gray',
          'ownerId': 'owner456',
        };

        final pet = Pet.fromJson(json);

        expect(pet.id, equals('2'));
        expect(pet.name, equals('Mittens'));
        expect(pet.species, equals('cat'));
        expect(pet.breed, equals('Domestic Shorthair'));
        expect(pet.gender, equals('female'));
        expect(pet.birthDate, equals(DateTime.parse('2021-01-01T00:00:00.000Z')));
        expect(pet.weight, equals(3.8));
        expect(pet.color, equals('gray'));
        expect(pet.ownerId, equals('owner456'));
        expect(pet.microchipNumber, equals(''));
        expect(pet.vaccinations, isEmpty);
        expect(pet.notes, equals(''));
        expect(pet.photoUrl, equals(''));
      });

      test('should handle null vaccinations in JSON', () {
        final json = {
          'id': '3',
          'name': 'Polly',
          'species': 'bird',
          'breed': 'Cockatiel',
          'gender': 'female',
          'birthDate': '2022-03-15T00:00:00.000Z',
          'weight': 0.1,
          'color': 'yellow and gray',
          'ownerId': 'owner789',
          'vaccinations': null,
        };

        final pet = Pet.fromJson(json);

        expect(pet.vaccinations, isEmpty);
      });

      test('should convert pet to JSON correctly', () {
        final birthDate = DateTime(2020, 6, 15);
        
        final pet = Pet(
          id: '1',
          name: 'Test Pet',
          species: 'dog',
          breed: 'Test Breed',
          gender: 'male',
          birthDate: birthDate,
          weight: 25.0,
          color: 'brown',
          ownerId: 'owner1',
          microchipNumber: 'MC123',
          vaccinations: ['Vaccine1', 'Vaccine2'],
          notes: 'Test notes',
          photoUrl: 'https://example.com/test.jpg',
        );

        final json = pet.toJson();

        expect(json['id'], equals('1'));
        expect(json['name'], equals('Test Pet'));
        expect(json['species'], equals('dog'));
        expect(json['breed'], equals('Test Breed'));
        expect(json['gender'], equals('male'));
        expect(json['birthDate'], equals(birthDate.toIso8601String()));
        expect(json['weight'], equals(25.0));
        expect(json['color'], equals('brown'));
        expect(json['ownerId'], equals('owner1'));
        expect(json['microchipNumber'], equals('MC123'));
        expect(json['vaccinations'], equals(['Vaccine1', 'Vaccine2']));
        expect(json['notes'], equals('Test notes'));
        expect(json['photoUrl'], equals('https://example.com/test.jpg'));
      });
    });

    group('CopyWith Tests', () {
      test('should copy pet with updated fields', () {
        final originalBirthDate = DateTime(2020, 1, 1);
        final newBirthDate = DateTime(2020, 6, 15);
        
        final original = Pet(
          id: '1',
          name: 'Original Name',
          species: 'dog',
          breed: 'Original Breed',
          gender: 'male',
          birthDate: originalBirthDate,
          weight: 20.0,
          color: 'brown',
          ownerId: 'owner1',
          notes: 'Original notes',
        );

        final updated = original.copyWith(
          name: 'Updated Name',
          breed: 'Updated Breed',
          birthDate: newBirthDate,
          weight: 25.0,
          notes: 'Updated notes',
        );

        // Updated fields
        expect(updated.name, equals('Updated Name'));
        expect(updated.breed, equals('Updated Breed'));
        expect(updated.birthDate, equals(newBirthDate));
        expect(updated.weight, equals(25.0));
        expect(updated.notes, equals('Updated notes'));

        // Unchanged fields
        expect(updated.id, equals('1'));
        expect(updated.species, equals('dog'));
        expect(updated.gender, equals('male'));
        expect(updated.color, equals('brown'));
        expect(updated.ownerId, equals('owner1'));
      });

      test('should copy pet without changes when no parameters provided', () {
        final birthDate = DateTime(2020, 1, 1);
        
        final original = Pet(
          id: '1',
          name: 'Test Pet',
          species: 'cat',
          breed: 'Test Breed',
          gender: 'female',
          birthDate: birthDate,
          weight: 4.0,
          color: 'white',
          ownerId: 'owner1',
        );

        final copy = original.copyWith();

        expect(copy.id, equals(original.id));
        expect(copy.name, equals(original.name));
        expect(copy.species, equals(original.species));
        expect(copy.breed, equals(original.breed));
        expect(copy.gender, equals(original.gender));
        expect(copy.birthDate, equals(original.birthDate));
        expect(copy.weight, equals(original.weight));
        expect(copy.color, equals(original.color));
        expect(copy.ownerId, equals(original.ownerId));
        expect(copy.microchipNumber, equals(original.microchipNumber));
        expect(copy.vaccinations, equals(original.vaccinations));
        expect(copy.notes, equals(original.notes));
        expect(copy.photoUrl, equals(original.photoUrl));
      });
    });

    group('Edge Cases', () {
      test('should handle very small weight', () {
        final pet = Pet(
          id: '1',
          name: 'Tiny',
          species: 'hamster',
          breed: 'Syrian',
          gender: 'male',
          birthDate: DateTime(2023, 1, 1),
          weight: 0.05, // 50 grams
          color: 'brown',
          ownerId: 'owner1',
        );

        expect(pet.weight, equals(0.05));
        
        final json = pet.toJson();
        final recreated = Pet.fromJson(json);
        expect(recreated.weight, equals(0.05));
      });

      test('should handle very large weight', () {
        final pet = Pet(
          id: '1',
          name: 'Giant',
          species: 'dog',
          breed: 'Great Dane',
          gender: 'male',
          birthDate: DateTime(2018, 1, 1),
          weight: 90.5,
          color: 'black',
          ownerId: 'owner1',
        );

        expect(pet.weight, equals(90.5));
        
        final json = pet.toJson();
        final recreated = Pet.fromJson(json);
        expect(recreated.weight, equals(90.5));
      });

      test('should handle future birth date', () {
        final futureBirthDate = DateTime.now().add(const Duration(days: 30));
        
        final pet = Pet(
          id: '1',
          name: 'Future Pet',
          species: 'dog',
          breed: 'Test',
          gender: 'male',
          birthDate: futureBirthDate,
          weight: 1.0,
          color: 'brown',
          ownerId: 'owner1',
        );

        // Age should be negative for future birth dates
        expect(pet.ageInYears, lessThan(0));
      });

      test('should handle empty strings in optional fields', () {
        final pet = Pet(
          id: '1',
          name: 'Test Pet',
          species: 'dog',
          breed: 'Test Breed',
          gender: 'male',
          birthDate: DateTime(2020, 1, 1),
          weight: 10.0,
          color: 'brown',
          ownerId: 'owner1',
          microchipNumber: '',
          notes: '',
          photoUrl: '',
        );

        expect(pet.microchipNumber, equals(''));
        expect(pet.notes, equals(''));
        expect(pet.photoUrl, equals(''));
      });
    });
  });
}
