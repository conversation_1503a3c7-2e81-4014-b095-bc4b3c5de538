import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/role_provider.dart';
import '../models/user_role.dart';
import '../config/theme/app_theme.dart';

/// Widget that conditionally shows content based on user permissions
class PermissionGuard extends StatelessWidget {
  final Widget child;
  final String? featureKey;
  final UserRole? requiredRole;
  final bool requireManagementAccess;
  final bool requireAdminAccess;
  final bool requireClientDataAccess;
  final Widget? fallback;
  final bool showFallbackMessage;
  final String? customFallbackMessage;

  const PermissionGuard({
    super.key,
    required this.child,
    this.featureKey,
    this.requiredRole,
    this.requireManagementAccess = false,
    this.requireAdminAccess = false,
    this.requireClientDataAccess = false,
    this.fallback,
    this.showFallbackMessage = false,
    this.customFallbackMessage,
  });

  /// Guard for admin-only features
  const PermissionGuard.admin({
    super.key,
    required this.child,
    this.fallback,
    this.showFallbackMessage = false,
    this.customFallbackMessage,
  })  : featureKey = null,
        requiredRole = null,
        requireManagementAccess = false,
        requireAdminAccess = true,
        requireClientDataAccess = false;

  /// Guard for management-level features
  const PermissionGuard.management({
    super.key,
    required this.child,
    this.fallback,
    this.showFallbackMessage = false,
    this.customFallbackMessage,
  })  : featureKey = null,
        requiredRole = null,
        requireManagementAccess = true,
        requireAdminAccess = false,
        requireClientDataAccess = false;

  /// Guard for features requiring client data access
  const PermissionGuard.clientData({
    super.key,
    required this.child,
    this.fallback,
    this.showFallbackMessage = false,
    this.customFallbackMessage,
  })  : featureKey = null,
        requiredRole = null,
        requireManagementAccess = false,
        requireAdminAccess = false,
        requireClientDataAccess = true;

  /// Guard for specific feature access
  const PermissionGuard.feature({
    super.key,
    required this.child,
    required this.featureKey,
    this.fallback,
    this.showFallbackMessage = false,
    this.customFallbackMessage,
  })  : requiredRole = null,
        requireManagementAccess = false,
        requireAdminAccess = false,
        requireClientDataAccess = false;

  @override
  Widget build(BuildContext context) {
    return Consumer<RoleProvider>(
      builder: (context, roleProvider, _) {
        if (roleProvider.isLoading) {
          return const SizedBox.shrink();
        }

        final hasAccess = _checkAccess(roleProvider);

        if (hasAccess) {
          return child;
        }

        // Return fallback or empty widget
        if (fallback != null) {
          return fallback!;
        }

        if (showFallbackMessage) {
          return _buildFallbackMessage();
        }

        return const SizedBox.shrink();
      },
    );
  }

  bool _checkAccess(RoleProvider roleProvider) {
    // Check specific feature access
    if (featureKey != null) {
      return roleProvider.canAccessFeature(featureKey!);
    }

    // Check role-based access
    if (requiredRole != null) {
      return roleProvider.currentRole == requiredRole;
    }

    // Check admin access
    if (requireAdminAccess) {
      return roleProvider.isAdmin;
    }

    // Check management access
    if (requireManagementAccess) {
      return roleProvider.hasManagementAccess;
    }

    // Check client data access
    if (requireClientDataAccess) {
      return roleProvider.canAccessClientData;
    }

    // Default to allowing access if no restrictions specified
    return true;
  }

  Widget _buildFallbackMessage() {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: AppColors.logoBrown.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.taupe.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.lock_outline,
            color: AppColors.taupe,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              customFallbackMessage ?? 'Nu aveți permisiunea să accesați această funcționalitate.',
              style: TextStyle(
                color: AppColors.taupe,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Widget for conditionally showing navigation items based on permissions
class PermissionAwareNavigationItem extends StatelessWidget {
  final Widget child;
  final String featureKey;
  final VoidCallback? onTap;

  const PermissionAwareNavigationItem({
    super.key,
    required this.child,
    required this.featureKey,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<RoleProvider>(
      builder: (context, roleProvider, _) {
        if (!roleProvider.canAccessFeature(featureKey)) {
          return const SizedBox.shrink();
        }

        if (onTap != null) {
          return GestureDetector(
            onTap: onTap,
            child: child,
          );
        }

        return child;
      },
    );
  }
}

/// Mixin for screens that need permission checking
mixin PermissionAwareScreen<T extends StatefulWidget> on State<T> {
  late RoleProvider _roleProvider;

  @override
  void initState() {
    super.initState();
    _roleProvider = context.read<RoleProvider>();
  }

  /// Check if current user can access this screen
  bool canAccessScreen() => true;

  /// Get required permission for this screen
  String? get requiredFeature => null;

  /// Check if user has required permissions
  bool hasRequiredPermissions() {
    if (requiredFeature != null) {
      return _roleProvider.canAccessFeature(requiredFeature!);
    }
    return canAccessScreen();
  }

  /// Show permission denied dialog
  void showPermissionDeniedDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Acces restricționat'),
        content: const Text('Nu aveți permisiunea să accesați această funcționalitate.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Navigate back if no permissions
  void checkPermissionsAndNavigateBack() {
    if (!hasRequiredPermissions()) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        Navigator.of(context).pop();
        showPermissionDeniedDialog();
      });
    }
  }
}
