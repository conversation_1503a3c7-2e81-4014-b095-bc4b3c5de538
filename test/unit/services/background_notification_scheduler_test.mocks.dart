// Mocks generated by <PERSON>ckito 5.4.6 from annotations
// in partykidsproject/test/unit/services/background_notification_scheduler_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'package:partykidsapp/services/appointment/appointment_service.dart'
    as _i2;
import 'package:partykidsapp/services/auth/auth_service.dart' as _i4;
import 'package:partykidsapp/services/sms_settings_service.dart' as _i3;
import 'package:mockito/mockito.dart' as _i1;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [AppointmentService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAppointmentService extends _i1.Mock
    implements _i2.AppointmentService {
  MockAppointmentService() {
    _i1.throwOnMissingStub(this);
  }
}

/// A class which mocks [SmsSettingsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockSmsSettingsService extends _i1.Mock
    implements _i3.SmsSettingsService {
  MockSmsSettingsService() {
    _i1.throwOnMissingStub(this);
  }
}

/// A class which mocks [AuthService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthService extends _i1.Mock implements _i4.AuthService {
  MockAuthService() {
    _i1.throwOnMissingStub(this);
  }
}
