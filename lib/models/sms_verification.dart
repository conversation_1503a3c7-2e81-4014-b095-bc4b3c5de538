/// Request model for sending SMS verification code
class SendSmsVerificationRequest {
  final String phoneNumber;

  const SendSmsVerificationRequest({
    required this.phoneNumber,
  });

  Map<String, dynamic> toJson() {
    return {
      'phoneNumber': phoneNumber,
    };
  }

  @override
  String toString() {
    return 'SendSmsVerificationRequest(phoneNumber: $phoneNumber)';
  }
}

/// Response model for SMS verification code sending
class SendSmsVerificationResponse {
  final int expiresIn;

  const SendSmsVerificationResponse({
    required this.expiresIn,
  });

  factory SendSmsVerificationResponse.fromJson(Map<String, dynamic> json) {
    return SendSmsVerificationResponse(
      expiresIn: json['expiresIn'] ?? 300,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'expiresIn': expiresIn,
    };
  }

  @override
  String toString() {
    return 'SendSmsVerificationResponse(expiresIn: $expiresIn)';
  }
}

/// Request model for verifying SMS code
class VerifySmsRequest {
  final String phoneNumber;
  final String code;

  const VerifySmsRequest({
    required this.phoneNumber,
    required this.code,
  });

  Map<String, dynamic> toJson() {
    return {
      'phoneNumber': phoneNumber,
      'code': code,
    };
  }

  @override
  String toString() {
    return 'VerifySmsRequest(phoneNumber: $phoneNumber, code: $code)';
  }
}

/// Response model for SMS verification
class VerifySmsResponse {
  final bool verified;

  const VerifySmsResponse({
    required this.verified,
  });

  factory VerifySmsResponse.fromJson(Map<String, dynamic> json) {
    return VerifySmsResponse(
      verified: json['verified'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'verified': verified,
    };
  }

  @override
  String toString() {
    return 'VerifySmsResponse(verified: $verified)';
  }
}

/// SMS verification error types
enum SmsVerificationError {
  invalidCode,
  expiredCode,
  tooManyAttempts,
  noVerificationRequest,
  networkError,
  unknown,
}

/// Extension for SMS verification error handling
extension SmsVerificationErrorExtension on SmsVerificationError {
  String get message {
    switch (this) {
      case SmsVerificationError.invalidCode:
        return 'Codul introdus este incorect';
      case SmsVerificationError.expiredCode:
        return 'Codul a expirat. Solicitați un cod nou';
      case SmsVerificationError.tooManyAttempts:
        return 'Prea multe încercări. Încercați din nou mai târziu';
      case SmsVerificationError.noVerificationRequest:
        return 'Nu există o solicitare de verificare pentru acest număr';
      case SmsVerificationError.networkError:
        return 'Eroare de conexiune. Verificați internetul';
      case SmsVerificationError.unknown:
        return 'A apărut o eroare neașteptată';
    }
  }

  static SmsVerificationError fromStatusCode(int statusCode) {
    switch (statusCode) {
      case 400:
        return SmsVerificationError.invalidCode;
      case 404:
        return SmsVerificationError.noVerificationRequest;
      case 429:
        return SmsVerificationError.tooManyAttempts;
      default:
        return SmsVerificationError.unknown;
    }
  }
}
