import 'package:flutter/material.dart';
import '../../models/pet.dart';
import '../../screens/clients/animal_details_screen.dart';

class PetProfileCard extends StatelessWidget {
  final Pet pet;
  final VoidCallback? onTap;

  const PetProfileCard({
    super.key,
    required this.pet,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: InkWell(
        onTap: onTap ?? () {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => AnimalDetailsScreen(pet: pet),
            ),
          );
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Pet photo/avatar
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
                    width: 2,
                  ),
                ),
                child: pet.photoUrl.isNotEmpty
                    ? ClipOval(
                        child: Image.network(
                          pet.photoUrl,
                          width: 60,
                          height: 60,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return _buildPetAvatar(context);
                          },
                        ),
                      )
                    : _buildPetAvatar(context),
              ),

              SizedBox(width: 16),

              // Pet info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Name and species
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            pet.name,
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(width: 8),
                        _getPetIcon(pet.species, context),
                      ],
                    ),

                    SizedBox(height: 2),

                    // Breed and gender
                    Text(
                      '${pet.breed} • ${_getGenderText(pet.gender)}',
                      style: TextStyle(
                        fontSize: 13,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        fontWeight: FontWeight.w500,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    SizedBox(height: 2),

                    // Age and weight
                    Text(
                      '${_calculateAge(pet.birthDate)} • ${pet.weight.toStringAsFixed(1)} kg',
                      style: TextStyle(
                        fontSize: 11,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    // Special notes if any
                    if (pet.notes.isNotEmpty) ...[
                      SizedBox(height: 2),
                      Text(
                        pet.notes,
                        style: TextStyle(
                          fontSize: 10,
                          color: Theme.of(context).colorScheme.secondary,
                          fontStyle: FontStyle.italic,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),

              // Action indicator
              if (onTap != null)
                Icon(
                  Icons.arrow_forward_ios,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  size: 16,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPetAvatar(BuildContext context) {
    return Icon(
      _getPetIconData(pet.species),
      size: 30,
      color: Theme.of(context).colorScheme.onSurface,
    );
  }

  Widget _getPetIcon(String species, BuildContext context) {
    return Container(
      padding:  EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Icon(
        _getPetIconData(species),
        size: 16,
        color: Theme.of(context).colorScheme.secondary,
      ),
    );
  }

  IconData _getPetIconData(String species) {
    switch (species.toLowerCase()) {
      case 'dog':
        return Icons.pets;
      case 'cat':
        return Icons.pets;
      case 'bird':
        return Icons.flutter_dash;
      case 'rabbit':
        return Icons.cruelty_free;
      default:
        return Icons.pets;
    }
  }

  String _getGenderText(String gender) {
    switch (gender.toLowerCase()) {
      case 'male':
        return 'Mascul';
      case 'female':
        return 'Femelă';
      default:
        return gender;
    }
  }

  String _calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    final age = now.difference(birthDate);

    if (age.inDays < 365) {
      final months = (age.inDays / 30).floor();
      return months > 0 ? '$months luni' : '${age.inDays} zile';
    } else {
      final years = (age.inDays / 365).floor();
      final remainingMonths = ((age.inDays % 365) / 30).floor();

      if (remainingMonths > 0) {
        return '$years ani, $remainingMonths luni';
      } else {
        return '$years ani';
      }
    }
  }
}
