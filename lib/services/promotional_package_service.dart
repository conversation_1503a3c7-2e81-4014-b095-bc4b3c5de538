import '../models/promotional_package.dart';
import '../models/api_response.dart';
import '../models/service.dart';
import 'api_service.dart';
import 'auth/auth_service.dart';

class PromotionalPackageService {
  // Get all promotional packages for the current salon
  static Future<ApiResponse<List<PromotionalPackage>>> getPackages({
    bool? isActive,
    String? search,
    double? minPrice,
    double? maxPrice,
  }) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<List<PromotionalPackage>>.error('No salon selected');
      }

      final queryParams = <String, String>{};
      if (isActive != null) queryParams['isActive'] = isActive.toString();
      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (minPrice != null) queryParams['minPrice'] = minPrice.toString();
      if (maxPrice != null) queryParams['maxPrice'] = maxPrice.toString();

      final response = await ApiService.get<List<PromotionalPackage>>(
        '/api/salons/$salonId/promotional-packages',
        queryParams: queryParams.isNotEmpty ? queryParams : null,
        fromJson: (data) {
          // Handle the backend response format: {"packages":[],"totalCount":0,"activeCount":0,"inactiveCount":0}
          if (data is Map<String, dynamic> && data.containsKey('packages')) {
            final packagesList = data['packages'] as List;
            return packagesList.map((item) => PromotionalPackage.fromJson(item)).toList();
          }
          // Fallback for direct array response
          if (data is List) {
            return data.map((item) => PromotionalPackage.fromJson(item)).toList();
          }
          return <PromotionalPackage>[];
        },
      );

      return response;
    } catch (e) {
      return ApiResponse<List<PromotionalPackage>>.error('Failed to load promotional packages: $e');
    }
  }

  // Get promotional package by ID
  static Future<ApiResponse<PromotionalPackage>> getPackage(String id) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<PromotionalPackage>.error('No salon selected');
      }

      final response = await ApiService.get<PromotionalPackage>(
        '/api/salons/$salonId/promotional-packages/$id',
        fromJson: (data) => PromotionalPackage.fromJson(data),
      );

      return response;
    } catch (e) {
      return ApiResponse<PromotionalPackage>.error('Failed to load promotional package: $e');
    }
  }

  // Create new promotional package
  static Future<ApiResponse<PromotionalPackage>> createPackage(PromotionalPackage package) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<PromotionalPackage>.error('No salon selected');
      }

      final response = await ApiService.post<PromotionalPackage>(
        '/api/salons/$salonId/promotional-packages',
        body: package.toJson(),
        fromJson: (data) => PromotionalPackage.fromJson(data),
      );

      return response;
    } catch (e) {
      return ApiResponse<PromotionalPackage>.error('Failed to create promotional package: $e');
    }
  }

  // Update existing promotional package
  static Future<ApiResponse<PromotionalPackage>> updatePackage(String id, PromotionalPackage package) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<PromotionalPackage>.error('No salon selected');
      }

      final response = await ApiService.put<PromotionalPackage>(
        '/api/salons/$salonId/promotional-packages/$id',
        body: package.toJson(),
        fromJson: (data) => PromotionalPackage.fromJson(data),
      );

      return response;
    } catch (e) {
      return ApiResponse<PromotionalPackage>.error('Failed to update promotional package: $e');
    }
  }

  // Toggle package active status
  static Future<ApiResponse<PromotionalPackage>> togglePackageStatus(String packageId, bool isActive) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<PromotionalPackage>.error('No salon selected');
      }

      final response = await ApiService.patch<PromotionalPackage>(
        '/api/salons/$salonId/promotional-packages/$packageId/toggle-status',
        body: {'isActive': isActive},
        fromJson: (data) => PromotionalPackage.fromJson(data),
      );

      return response;
    } catch (e) {
      return ApiResponse<PromotionalPackage>.error('Failed to toggle package status: $e');
    }
  }

  // Delete promotional package (soft delete)
  static Future<ApiResponse<void>> deletePackage(String id) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<void>.error('No salon selected');
      }

      final response = await ApiService.delete<void>('/api/salons/$salonId/promotional-packages/$id');
      return response;
    } catch (e) {
      return ApiResponse<void>.error('Failed to delete promotional package: $e');
    }
  }

  // Duplicate promotional package
  static Future<ApiResponse<PromotionalPackage>> duplicatePackage(String packageId, {String? newName}) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<PromotionalPackage>.error('No salon selected');
      }

      final body = newName != null ? {'newName': newName} : <String, dynamic>{};

      final response = await ApiService.post<PromotionalPackage>(
        '/api/salons/$salonId/promotional-packages/$packageId/duplicate',
        body: body,
        fromJson: (data) => PromotionalPackage.fromJson(data),
      );

      return response;
    } catch (e) {
      return ApiResponse<PromotionalPackage>.error('Failed to duplicate promotional package: $e');
    }
  }

  // Search promotional packages (convenience method)
  static Future<ApiResponse<List<PromotionalPackage>>> searchPackages(String query) async {
    return getPackages(search: query);
  }

  // Get active promotional packages only (convenience method)
  static Future<ApiResponse<List<PromotionalPackage>>> getActivePackages() async {
    return getPackages(isActive: true);
  }

  // Get inactive promotional packages only (convenience method)
  static Future<ApiResponse<List<PromotionalPackage>>> getInactivePackages() async {
    return getPackages(isActive: false);
  }

  // Get packages in price range (convenience method)
  static Future<ApiResponse<List<PromotionalPackage>>> getPackagesInPriceRange(double minPrice, double maxPrice) async {
    return getPackages(minPrice: minPrice, maxPrice: maxPrice);
  }
}
