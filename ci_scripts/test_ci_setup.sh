#!/bin/bash

# Test script to simulate Xcode Cloud environment locally
# This helps debug CI issues before pushing to the repository

echo "=== Testing CI Setup Locally ==="
echo "Current directory: $(pwd)"

# Save current state
ORIGINAL_DIR=$(pwd)

# Clean up to simulate fresh clone
echo "Cleaning up Flutter and iOS build artifacts..."
flutter clean

# Remove iOS build artifacts
rm -rf ios/build
rm -rf ios/.symlinks
rm -rf ios/Pods
rm -rf ios/Podfile.lock

echo "=== Simulating ci_post_clone.sh ==="
# Run the post-clone script
./ci_scripts/ci_post_clone.sh

echo "=== Simulating ci_pre_xcodebuild.sh ==="
# Run the pre-xcodebuild script
./ci_scripts/ci_pre_xcodebuild.sh

echo "=== Verifying Setup ==="
cd ios

# Check if files exist
if [ -f "Podfile.lock" ]; then
    echo "✅ Podfile.lock exists"
else
    echo "❌ Podfile.lock missing"
fi

if [ -f "Pods/Manifest.lock" ]; then
    echo "✅ Pods/Manifest.lock exists"
else
    echo "❌ Pods/Manifest.lock missing"
fi

# Check if they match
if [ -f "Podfile.lock" ] && [ -f "Pods/Manifest.lock" ]; then
    if cmp -s "Podfile.lock" "Pods/Manifest.lock"; then
        echo "✅ Podfile.lock and Manifest.lock are in sync"
    else
        echo "❌ Podfile.lock and Manifest.lock are out of sync"
        echo "This would cause the 'Check Pods Manifest.lock' build phase to fail"
    fi
fi

# Check if .symlinks directory exists
if [ -d ".symlinks" ]; then
    echo "✅ .symlinks directory exists"
else
    echo "❌ .symlinks directory missing"
fi

cd "$ORIGINAL_DIR"
echo "=== Test Complete ==="
