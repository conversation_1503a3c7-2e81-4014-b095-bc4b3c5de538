import 'package:flutter/cupertino.dart';

import '../models/api_response.dart';
import 'api_service.dart';
import 'auth/auth_service.dart';

/// Base service class with common patterns for salon-specific API operations
abstract class BaseService {
  /// Get current salon ID with error handling
  static Future<ApiResponse<String>> getCurrentSalonId() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<String>.error('No salon selected');
      }
      return ApiResponse<String>.success(salonId);
    } catch (e) {
      return ApiResponse<String>.error('Failed to get salon ID: $e');
    }
  }

  /// Generic salon-specific GET request
  static Future<ApiResponse<T>> getSalonResource<T>(
    String resourcePath, {
    Map<String, String>? queryParams,
    required T Function(dynamic) fromJson,
  }) async {
    try {
      final salonIdResponse = await getCurrentSalonId();
      if (!salonIdResponse.success) {
        return ApiResponse<T>.error(salonIdResponse.error!);
      }

      final salonId = salonIdResponse.data!;
      final endpoint = '/api/salons/$salonId/$resourcePath';

      debugPrint('GET: $endpoint');
      return await ApiService.get<T>(
        endpoint,
        queryParams: queryParams,
        fromJson: fromJson,
      );
    } catch (e) {
      return ApiResponse<T>.error('Failed to fetch $resourcePath: $e');
    }
  }

  /// Generic salon-specific POST request
  static Future<ApiResponse<T>> postSalonResource<T>(
    String resourcePath, {
    Map<String, dynamic>? body,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final salonIdResponse = await getCurrentSalonId();
      if (!salonIdResponse.success) {
        return ApiResponse<T>.error(salonIdResponse.error!);
      }

      final salonId = salonIdResponse.data!;
      final endpoint = '/api/salons/$salonId/$resourcePath';

      return await ApiService.post<T>(
        endpoint,
        body: body,
        fromJson: fromJson,
      );
    } catch (e) {
      return ApiResponse<T>.error('Failed to create $resourcePath: $e');
    }
  }

  static Future<ApiResponse<T>> postResource<T>(
    String resourcePath, {
    Map<String, dynamic>? body,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final endpoint = '/api/$resourcePath';
      return await ApiService.post<T>(
        endpoint,
        body: body,
        fromJson: fromJson,
      );
    } catch (e) {
      return ApiResponse<T>.error('Failed to create $resourcePath: $e');
    }
  }

  /// Generic salon-specific PUT request
  static Future<ApiResponse<T>> putSalonResource<T>(
    String resourcePath, {
    Map<String, dynamic>? body,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final salonIdResponse = await getCurrentSalonId();
      if (!salonIdResponse.success) {
        return ApiResponse<T>.error(salonIdResponse.error!);
      }

      final salonId = salonIdResponse.data!;
      final endpoint = '/api/salons/$salonId/$resourcePath';

      return await ApiService.put<T>(
        endpoint,
        body: body,
        fromJson: fromJson,
      );
    } catch (e) {
      return ApiResponse<T>.error('Failed to update $resourcePath: $e');
    }
  }



  /// Generic salon-specific DELETE request
  static Future<ApiResponse<bool>> deleteSalonResource(String resourcePath) async {
    try {
      final salonIdResponse = await getCurrentSalonId();
      if (!salonIdResponse.success) {
        return ApiResponse<bool>.error(salonIdResponse.error!);
      }

      final salonId = salonIdResponse.data!;
      final endpoint = '/api/salons/$salonId/$resourcePath';

      return await ApiService.delete(endpoint);
    } catch (e) {
      return ApiResponse<bool>.error('Failed to delete $resourcePath: $e');
    }
  }

  /// Generic salon-specific PATCH request
  static Future<ApiResponse<T>> patchSalonResource<T>(
    String resourcePath, {
    Map<String, dynamic>? body,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final salonIdResponse = await getCurrentSalonId();
      if (!salonIdResponse.success) {
        return ApiResponse<T>.error(salonIdResponse.error!);
      }

      final salonId = salonIdResponse.data!;
      final endpoint = '/api/salons/$salonId/$resourcePath';

      return await ApiService.patch<T>(
        endpoint,
        body: body,
        fromJson: fromJson,
      );
    } catch (e) {
      return ApiResponse<T>.error('Failed to patch $resourcePath: $e');
    }
  }

  /// Retry request with exponential backoff
  static Future<ApiResponse<T>> retryRequest<T>(
    Future<ApiResponse<T>> Function() request, {
    int maxRetries = 3,
  }) async {
    return ApiService.retryRequest(request, maxRetries: maxRetries);
  }

  /// Build query parameters from a map, filtering out null values
  static Map<String, String> buildQueryParams(Map<String, dynamic> params) {
    final queryParams = <String, String>{};
    
    for (final entry in params.entries) {
      if (entry.value != null) {
        if (entry.value is DateTime) {
          queryParams[entry.key] = (entry.value as DateTime).toIso8601String();
        } else if (entry.value is bool) {
          queryParams[entry.key] = entry.value.toString();
        } else {
          queryParams[entry.key] = entry.value.toString();
        }
      }
    }
    
    return queryParams;
  }

  /// Format date for API requests (YYYY-MM-DD)
  static String formatDateForApi(DateTime date) {
    return date.toIso8601String().split('T')[0];
  }

  /// Format datetime for API requests (ISO 8601)
  static String formatDateTimeForApi(DateTime dateTime) {
    return dateTime.toIso8601String();
  }

  /// Parse list response with error handling
  static List<T> parseListResponse<T>(
    dynamic data,
    T Function(Map<String, dynamic>) fromJson,
  ) {
    if (data is! List) {
      throw Exception('Expected list but got ${data.runtimeType}');
    }
    
    return data.map((item) {
      if (item is! Map<String, dynamic>) {
        throw Exception('Expected map but got ${item.runtimeType}');
      }
      return fromJson(item);
    }).toList();
  }

  /// Common error messages
  static const String noSalonSelectedError = 'No salon selected';
  static const String authenticationRequiredError = 'Authentication required';
  static const String networkError = 'Network error occurred';
  static const String serverError = 'Server error occurred';
  static const String unknownError = 'An unknown error occurred';

  /// Get user-friendly error message
  static String getUserFriendlyError(String error) {
    if (error.contains('No salon selected')) {
      return 'Vă rugăm să selectați un salon';
    } else if (error.contains('Authentication required')) {
      return 'Autentificare necesară';
    } else if (error.contains('Network')) {
      return 'Eroare de conexiune';
    } else if (error.contains('Server')) {
      return 'Eroare de server';
    } else {
      return 'A apărut o eroare neașteptată';
    }
  }
}
