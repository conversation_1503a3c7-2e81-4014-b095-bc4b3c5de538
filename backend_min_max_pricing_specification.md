# Backend API Specification: Min-Max Pricing for Services

## Overview
Implement min-max pricing functionality for the Service model to support flexible pricing strategies including price ranges for both fixed and size-based pricing.

## Database Schema Changes

### Service Table Modifications
Add the following columns to the `services` table:

```sql
ALTER TABLE services ADD COLUMN min_price DECIMAL(10,2) NULL;
ALTER TABLE services ADD COLUMN max_price DECIMAL(10,2) NULL;
ALTER TABLE services ADD COLUMN size_min_prices JSON NULL;
ALTER TABLE services ADD COLUMN size_max_prices JSON NULL;
```

### Field Descriptions
- `min_price`: Minimum price for fixed pricing services (nullable)
- `max_price`: Maximum price for fixed pricing services (nullable)
- `size_min_prices`: JSON object with min prices by size `{"S": 25.00, "M": 45.00, "L": 65.00}` (nullable)
- `size_max_prices`: JSON object with max prices by size `{"S": 35.00, "M": 55.00, "L": 75.00}` (nullable)

## Service Model Updates

### Java Entity Example
```java
@Entity
@Table(name = "services")
public class Service {
    // Existing fields...
    
    @Column(name = "min_price", precision = 10, scale = 2)
    private BigDecimal minPrice;
    
    @Column(name = "max_price", precision = 10, scale = 2)
    private BigDecimal maxPrice;
    
    @Column(name = "size_min_prices", columnDefinition = "JSON")
    @Convert(converter = JsonMapConverter.class)
    private Map<String, BigDecimal> sizeMinPrices;
    
    @Column(name = "size_max_prices", columnDefinition = "JSON")
    @Convert(converter = JsonMapConverter.class)
    private Map<String, BigDecimal> sizeMaxPrices;
    
    // Getters and setters...
}
```

## API Endpoint Modifications

### Create Service Endpoint
**POST** `/api/salons/{salonId}/services`

#### Request Body Example
```json
{
  "name": "Baie completă",
  "description": "Serviciu complet de baie pentru animale",
  "category": "GROOMING",
  "price": 50.00,
  "minPrice": 40.00,
  "maxPrice": 60.00,
  "duration": 60,
  "sizePrices": {
    "S": 30.00,
    "M": 50.00,
    "L": 70.00
  },
  "sizeMinPrices": {
    "S": 25.00,
    "M": 45.00,
    "L": 65.00
  },
  "sizeMaxPrices": {
    "S": 35.00,
    "M": 55.00,
    "L": 75.00
  },
  "sizeDurations": {
    "S": 45,
    "M": 60,
    "L": 90
  },
  "isActive": true,
  "displayOrder": 1,
  "requirements": ["Vaccinat", "Deparazitat"]
}
```

#### Response Example
```json
{
  "success": true,
  "data": {
    "id": "service-123",
    "name": "Baie completă",
    "description": "Serviciu complet de baie pentru animale",
    "category": "GROOMING",
    "price": 50.00,
    "minPrice": 40.00,
    "maxPrice": 60.00,
    "duration": 60,
    "sizePrices": {
      "S": 30.00,
      "M": 50.00,
      "L": 70.00
    },
    "sizeMinPrices": {
      "S": 25.00,
      "M": 45.00,
      "L": 65.00
    },
    "sizeMaxPrices": {
      "S": 35.00,
      "M": 55.00,
      "L": 75.00
    },
    "sizeDurations": {
      "S": 45,
      "M": 60,
      "L": 90
    },
    "isActive": true,
    "displayOrder": 1,
    "requirements": ["Vaccinat", "Deparazitat"],
    "createdAt": "2024-01-15T10:30:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

### Update Service Endpoint
**PUT** `/api/salons/{salonId}/services/{serviceId}`

Uses the same request/response format as create endpoint.

### Get Services Endpoint
**GET** `/api/salons/{salonId}/services`

Returns array of services with all pricing fields included.

## Validation Rules

### Backend Validation Logic
```java
public class ServiceValidator {
    
    public static ValidationResult validateService(ServiceRequest request) {
        List<String> errors = new ArrayList<>();
        
        // Basic validations
        if (request.getPrice() == null || request.getPrice().compareTo(BigDecimal.valueOf(0.01)) < 0) {
            errors.add("Prețul trebuie să fie cel puțin 0.01 RON");
        }
        
        // Fixed price range validation
        if (request.getMinPrice() != null && request.getMaxPrice() != null) {
            if (request.getMinPrice().compareTo(BigDecimal.valueOf(0.01)) < 0) {
                errors.add("Prețul minim trebuie să fie cel puțin 0.01 RON");
            }
            if (request.getMaxPrice().compareTo(BigDecimal.valueOf(0.01)) < 0) {
                errors.add("Prețul maxim trebuie să fie cel puțin 0.01 RON");
            }
            if (request.getMinPrice().compareTo(request.getMaxPrice()) >= 0) {
                errors.add("Prețul minim trebuie să fie mai mic decât prețul maxim");
            }
        }
        
        // Size-based price range validation
        if (request.getSizeMinPrices() != null && request.getSizeMaxPrices() != null) {
            for (String size : Arrays.asList("S", "M", "L")) {
                BigDecimal minPrice = request.getSizeMinPrices().get(size);
                BigDecimal maxPrice = request.getSizeMaxPrices().get(size);
                
                if (minPrice != null && maxPrice != null) {
                    if (minPrice.compareTo(BigDecimal.valueOf(0.01)) < 0) {
                        errors.add("Prețul minim pentru mărimea " + size + " trebuie să fie cel puțin 0.01 RON");
                    }
                    if (maxPrice.compareTo(BigDecimal.valueOf(0.01)) < 0) {
                        errors.add("Prețul maxim pentru mărimea " + size + " trebuie să fie cel puțin 0.01 RON");
                    }
                    if (minPrice.compareTo(maxPrice) >= 0) {
                        errors.add("Prețul minim trebuie să fie mai mic decât prețul maxim pentru mărimea " + size);
                    }
                }
            }
        }
        
        return new ValidationResult(errors.isEmpty(), errors);
    }
}
```

## Backward Compatibility Requirements

### 1. Existing Services
- All existing services without min-max pricing should continue to work
- `minPrice`, `maxPrice`, `sizeMinPrices`, `sizeMaxPrices` fields should be nullable
- When these fields are null, use the existing `price` and `sizePrices` fields

### 2. API Responses
- Always include all pricing fields in responses (null if not set)
- Frontend can determine pricing strategy based on which fields are populated

### 3. Migration Strategy
- No data migration required - new fields default to null
- Existing appointments and pricing calculations remain unchanged
- New pricing features are opt-in

## Error Handling

### Validation Error Response
```json
{
  "success": false,
  "error": {
    "message": "Prețul minim trebuie să fie mai mic decât prețul maxim",
    "code": "INVALID_PRICE_RANGE",
    "field": "minPrice"
  }
}
```

### Common Error Codes
- `INVALID_PRICE_RANGE`: Min price >= max price
- `INVALID_MIN_PRICE`: Min price < 0.01
- `INVALID_MAX_PRICE`: Max price < 0.01
- `INVALID_SIZE_PRICE_RANGE`: Size-specific min >= max price

## Testing Requirements

### Unit Tests
- Test service creation with all pricing combinations
- Test validation rules for price ranges
- Test backward compatibility with existing services

### Integration Tests
- Test full CRUD operations with min-max pricing
- Test appointment creation with range-priced services
- Test pricing calculations in appointment flow

## Implementation Priority
1. Database schema changes
2. Service model updates
3. Validation logic implementation
4. API endpoint modifications
5. Unit and integration tests
6. Documentation updates
