import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:partykidsapp/widgets/calendar_views/time_slot.dart';
import 'package:partykidsapp/config/theme/app_theme.dart';

/// Visual demonstration of the hour label alignment fix
/// This test creates a side-by-side comparison showing the improvement
void main() {
  group('Hour Label Alignment Visual Demo', () {
    testWidgets('should demonstrate proper hour label alignment', (WidgetTester tester) async {
      // Create a simple test showing a single time label with proper alignment
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Scaffold(
            body: Center(
              child: TimeLabel(
                time: DateTime(2024, 6, 20, 10, 0), // 10:00 AM
                isCurrentHour: false,
              ),
            ),
          ),
        ),
      );

      // Verify the time label renders correctly
      expect(find.text('10:00'), findsOneWidget);
      expect(find.text('AM'), findsOneWidget);

      // Verify the alignment is correct (top-aligned)
      final alignFinder = find.byType(Align);
      expect(alignFinder, findsOneWidget);

      final alignWidget = tester.widget<Align>(alignFinder);
      expect(alignWidget.alignment, equals(Alignment.topCenter));
    });
  });
}

// Simple test to verify the hour label alignment fix works correctly
