class Appointment {
  final String id;
  final String clientId;
  final String clientName;
  final String clientPhone;
  final String petId; // Reference to the pet
  final String petName; // For convenience
  final String petSpecies; // For convenience
  final String service; // Primary service (for backward compatibility)
  final List<String> services; // Multiple services
  final DateTime startTime;
  final DateTime endTime;
  final String status; // 'confirmed', 'canceled', 'pending', 'completed', 'in-progress'
  final bool isPaid;
  final String notes;
  final String assignedGroomer; // Groomer assignment (name)
  final String? groomerId; // Groomer ID for lookup
  final double totalPrice; // Total price for all services
  final int totalDuration; // Total duration in minutes
  final String repetitionFrequency; // 'none', 'daily', 'weekly', 'biweekly', 'monthly'

  Appointment({
    required this.id,
    required this.clientId,
    required this.clientName,
    required this.clientPhone,
    required this.petId,
    required this.petName,
    required this.petSpecies,
    required this.service,
    this.services = const [],
    required this.startTime,
    required this.endTime,
    required this.status,
    required this.isPaid,
    this.notes = '',
    this.assignedGroomer = '',
    this.groomerId,
    this.totalPrice = 0.0,
    this.totalDuration = 60,
    this.repetitionFrequency = 'none',
  });

  /// Calculate duration in minutes between start and end time
  int get durationInMinutes {
    return endTime.difference(startTime).inMinutes;
  }

  // Format time as a string (e.g., "09:00 - 10:30")
  String get timeRange {
    final startHour = startTime.hour.toString().padLeft(2, '0');
    final startMinute = startTime.minute.toString().padLeft(2, '0');
    final endHour = endTime.hour.toString().padLeft(2, '0');
    final endMinute = endTime.minute.toString().padLeft(2, '0');
    return '$startHour:$startMinute - $endHour:$endMinute';
  }

  /// Get formatted display string for appointment
  String get displayInfo {
    final client = clientName.isNotEmpty ? clientName : 'Client necunoscut';
    final pet = petName.isNotEmpty ? petName : 'Animal necunoscut';
    final serviceInfo = service.isNotEmpty ? service : 'Serviciu general';
    return '$client - $pet | $serviceInfo';
  }

  /// Get compact display string for appointment
  String get compactDisplayInfo {
    final client = clientName.isNotEmpty ? clientName : 'Client';
    final pet = petName.isNotEmpty ? petName : 'Pet';
    return '$client - $pet';
  }

  /// Get service display string
  String get serviceDisplayInfo {
    return service.isNotEmpty ? service : 'Serviciu general';
  }

  // Helper method to safely parse DateTime
  static DateTime? _parseDateTime(String? dateTimeStr) {
    if (dateTimeStr == null || dateTimeStr.isEmpty) return null;
    try {
      return DateTime.parse(dateTimeStr);
    } catch (e) {
      return null;
    }
  }

  // Convert from JSON
  factory Appointment.fromJson(Map<String, dynamic> json) {
    try {
      // Handle both old flat structure and new nested backend structure
      // Note: ApiService._handleResponse passes only the 'data' part of the response to this method
      final bool isNestedStructure = json.containsKey('client') && json.containsKey('pet') && json.containsKey('staff');

      if (isNestedStructure) {
        // New backend structure with nested objects (from 'data' part of response)
        final client = json['client'] as Map<String, dynamic>? ?? {};
        final pet = json['pet'] as Map<String, dynamic>? ?? {};
        final staff = json['staff'] as Map<String, dynamic>? ?? {};
        final servicesArray = json['services'] as List<dynamic>? ?? [];

      // Parse date and time fields
      final appointmentDate = json['appointmentDate'] as String?;
      final startTimeStr = json['startTime'] as String?;
      final endTimeStr = json['endTime'] as String?;

      DateTime startTime;
      DateTime endTime;

      if (appointmentDate != null && startTimeStr != null && endTimeStr != null) {
        try {
          // Combine date and time: "2025-06-04" + "09:15:00"
          startTime = DateTime.parse('${appointmentDate}T$startTimeStr');
          endTime = DateTime.parse('${appointmentDate}T$endTimeStr');
        } catch (e) {
          // Fallback to current time if parsing fails
          startTime = DateTime.now();
          endTime = DateTime.now().add(const Duration(hours: 1));
        }
      } else {
        // Fallback to current time if parsing fails
        startTime = DateTime.now();
        endTime = DateTime.now().add(const Duration(hours: 1));
      }

      // Extract service names from services array
      final serviceNames = servicesArray
          .map((service) => service is Map<String, dynamic> ? service['name'] as String? ?? '' : service.toString())
          .where((name) => name.isNotEmpty)
          .toList();

      // Get first service name or default
      final primaryService = serviceNames.isNotEmpty ? serviceNames.first : 'Serviciu general';

      return Appointment(
        id: json['id'] as String? ?? '',
        clientId: client['id'] as String? ?? '',
        clientName: client['name'] as String? ?? '',
        clientPhone: client['phone'] as String? ?? '',
        petId: pet['id'] as String? ?? '',
        petName: pet['name'] as String? ?? '',
        petSpecies: pet['breed'] as String? ?? '', // Backend uses 'breed' field
        service: primaryService,
        services: serviceNames,
        startTime: startTime,
        endTime: endTime,
        status: json['status'] as String? ?? 'SCHEDULED',
        isPaid: false, // Backend doesn't provide isPaid field yet, default to false
        notes: json['notes'] as String? ?? '',
        assignedGroomer: staff['name'] as String? ?? '',
        groomerId: staff['id'] as String?,
        totalPrice: (json['totalPrice'] as num?)?.toDouble() ?? 0.0,
        totalDuration: json['totalDuration'] as int? ?? 60,
        repetitionFrequency: json['repetitionFrequency'] as String? ?? 'none',
      );
    } else {
      // Old flat structure (backward compatibility)
      return Appointment(
        id: json['id'] as String? ?? '',
        clientId: json['clientId'] as String? ?? '',
        clientName: json['clientName'] as String? ?? '',
        clientPhone: json['clientPhone'] as String? ?? '',
        petId: json['petId'] as String? ?? '',
        petName: json['petName'] as String? ?? '',
        petSpecies: json['petSpecies'] as String? ?? '',
        service: json['service'] as String? ?? '',
        services: json['services'] != null
            ? List<String>.from(json['services'])
            : [json['service'] as String? ?? ''],
        startTime: _parseDateTime(json['startTime'] as String?) ?? DateTime.now(),
        endTime: _parseDateTime(json['endTime'] as String?) ?? _parseDateTime(json['startTime'] as String?)?.add(const Duration(hours: 1)) ?? DateTime.now().add(const Duration(hours: 1)),
        status: json['status'] as String? ?? 'SCHEDULED',
        isPaid: json['isPaid'] as bool? ?? false,
        notes: json['notes'] as String? ?? '',
        assignedGroomer: json['assignedGroomer'] as String? ?? json['assignedCoworker'] as String? ?? '',
        groomerId: json['groomerId'] as String? ?? json['assignedGroomerId'] as String?,
        totalPrice: (json['totalPrice'] as num?)?.toDouble() ?? 0.0,
        totalDuration: json['totalDuration'] as int? ?? 60,
        repetitionFrequency: json['repetitionFrequency'] as String? ?? 'none',
      );
    }
    } catch (e) {
      rethrow;
    }
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'clientId': clientId,
      'clientName': clientName,
      'clientPhone': clientPhone,
      'petId': petId,
      'petName': petName,
      'petSpecies': petSpecies,
      'service': service,
      'services': services,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'status': status,
      'isPaid': isPaid,
      'notes': notes,
      'assignedGroomer': assignedGroomer,
      'groomerId': groomerId,
      'assignedCoworker': assignedGroomer, // For backward compatibility
      'totalPrice': totalPrice,
      'totalDuration': totalDuration,
      'repetitionFrequency': repetitionFrequency,
    };
  }

  // Copy with method for immutable updates
  Appointment copyWith({
    String? id,
    String? clientId,
    String? clientName,
    String? clientPhone,
    String? petId,
    String? petName,
    String? petSpecies,
    String? service,
    List<String>? services,
    DateTime? startTime,
    DateTime? endTime,
    String? status,
    bool? isPaid,
    String? notes,
    String? assignedGroomer,
    String? groomerId,
    double? totalPrice,
    int? totalDuration,
    String? repetitionFrequency,
  }) {
    return Appointment(
      id: id ?? this.id,
      clientId: clientId ?? this.clientId,
      clientName: clientName ?? this.clientName,
      clientPhone: clientPhone ?? this.clientPhone,
      petId: petId ?? this.petId,
      petName: petName ?? this.petName,
      petSpecies: petSpecies ?? this.petSpecies,
      service: service ?? this.service,
      services: services ?? this.services,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      status: status ?? this.status,
      isPaid: isPaid ?? this.isPaid,
      notes: notes ?? this.notes,
      assignedGroomer: assignedGroomer ?? this.assignedGroomer,
      groomerId: groomerId ?? this.groomerId,
      totalPrice: totalPrice ?? this.totalPrice,
      totalDuration: totalDuration ?? this.totalDuration,
      repetitionFrequency: repetitionFrequency ?? this.repetitionFrequency,
    );
  }
}
