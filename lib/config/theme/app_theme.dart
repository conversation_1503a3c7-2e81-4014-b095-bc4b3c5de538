import 'package:flutter/material.dart';

// Export all theme-related classes for easy access
export 'app_dimensions.dart';
export '../../core/constants/app_strings.dart';
export '../../widgets/common/standard_form_field.dart';

/// Centralized color definitions for the Partykids app
///
/// === DUAL-BASE COLOR SYSTEM ===
/// This theme system allows independent customization of light and dark themes.
/// Simply update the two base colors below and all variants are computed automatically.
///
/// LIGHT THEME EXAMPLES:
/// • Medical Blue: Color(0xFF1976D2)
/// • Forest Green: Color(0xFF2E7D32)
/// • Elegant Brown: Color(0xFF8D6E63)
/// • Teal: Color(0xFF00796B)
///
/// DARK THEME EXAMPLES:
/// • Bright Blue: Color(0xFF2196F3)
/// • Bright Green: Color(0xFF4CAF50)
/// • Light Brown: Color(0xFFA1887F)
/// • Bright Teal: Color(0xFF26A69A)
///
/// AUTOMATIC COMPUTATIONS:
/// ✓ Container colors (lighter/darker variants)
/// ✓ Hover states and interactions
/// ✓ Accent variations and harmonies
/// ✓ Professional color relationships
///
/// All colors should be referenced from this class to ensure consistency
class AppColors {
  // === DUAL BASE COLOR SYSTEM ===
  // Change these two colors to customize your entire theme
  static const _lightThemePrimaryColor = Color(0xFFFF3131);    // Light theme primary - Medical Blue
  static const _darkThemePrimaryColor = Color(0xFFFF3131);     // Dark theme primary - Bright Blue

  // === COMPUTED LIGHT THEME COLORS ===
  // Primary color variants for light theme
  static Color get lightPrimary => _lightThemePrimaryColor;
  static Color get lightPrimaryContainer => _lightenColor(_lightThemePrimaryColor, 0.85);
  static Color get lightPrimaryLight => _lightenColor(_lightThemePrimaryColor, 0.15);
  static Color get lightPrimaryDark => _darkenColor(_lightThemePrimaryColor, 0.15);

  // === COMPUTED DARK THEME COLORS ===
  // Primary color variants for dark theme
  static Color get darkPrimary => _darkThemePrimaryColor;
  static Color get darkPrimaryContainer => _darkenColor(_darkThemePrimaryColor, 0.6);
  static Color get darkPrimaryLight => _lightenColor(_darkThemePrimaryColor, 0.15);
  static Color get darkPrimaryDark => _darkenColor(_darkThemePrimaryColor, 0.15);

  // === NEUTRAL COLORS ===
  // Light theme neutral colors
  static const pureWhite = Color(0xFFFFFFFF);       // Primary background and cards
  static const salonWhite = Color(0xFFFFFFFE);      // Subtle white variation for surfaces
  static const lightGray = Color(0xFFF8F8F8);       // Input fields and subtle surfaces
  static const lightGrayVariant = Color(0xFFF5F5F5); // Alternative light surface
  static const lightText = Color(0xFF2C2C2C);       // Primary text - sophisticated dark gray
  static const secondaryText = Color(0xFF6D6D6D);   // Secondary text and labels
  static const bodyText = Color(0xFF424242);        // Body text content

  // Dark theme neutral colors
  static const darkBackground = Color(0xFF000000);     // Pure black background
  static const darkSurface = Color(0xFF1C1C1E);        // Cards and elevated surfaces
  static const darkSurfaceVariant = Color(0xFF2C2C2E); // Secondary surfaces
  static const darkSurfaceAlt = Color(0xFF0A0A0A);     // Subtle black variation
  static const darkBorder = Color(0xFF38383A);         // Borders and dividers
  static const darkText = Color(0xFFFFFFFF);           // Primary text - pure white
  static const darkTextSecondary = Color(0xFF8E8E93);  // Secondary text - iOS gray
  static const darkTextTertiary = Color(0xFF636366);   // Tertiary text - darker gray

  // === STATUS COLORS ===
  // Light theme status colors
  static const lightSuccess = Color(0xFF4CAF50);
  static const lightWarning = Color(0xFFFF9800);
  static const lightError = Color(0xFFDC3545);
  static const lightInfo = Color(0xFF2196F3);

  // Dark theme status colors
  static const darkSuccess = Color(0xFF4CAF50);
  static const darkWarning = Color(0xFFFF9800);
  static const darkError = Color(0xFFFF453A);
  static const darkInfo = Color(0xFF007AFF);
  
  // Appointment status colors
  static const appointmentCancelledGrayLight = Color(0xFFB0BEC5);
  static const appointmentCancelledGrayDark = Color(0xFF455A64);

  /// Generate an extra staff color when predefined colors are exhausted
  static Color generateExtraStaffColor(int index) {
    final hue = (index * 37) % 360;
    return HSLColor.fromAHSL(1.0, hue.toDouble(), 0.65, 0.55).toColor();
  }

  // === FEATURE-SPECIFIC COLORS ===
  // Staff identification colors (for calendar and assignments)
  static List<Color> get staffColors => [
    Color(0xFF3A7D44), // Deep Green
    Color(0xFFA7727D), // Earthy Rose
    Color(0xFF3E86F5), // Vibrant Blue
    Color(0xFF9B59B6), // Rich Purple
    Color(0xFFD64550), // Strong Red
    Color(0xFF20B2AA), // Teal
    Color(0xFFF4B400), // Strong Yellow
    Color(0xFFF57C00), // Orange
    Color(0xFF4CAF50), // Bright Green
    Color(0xFF2196F3), // Bright Blue
  ];

  // Pet size indicator colors (used in pet management)
  static const petSizeSmall = Color(0xFF81C784);   // Light green
  static const petSizeMedium = Color(0xFFFFB74D);  // Light orange
  static const petSizeLarge = Color(0xFFF06292);   // Light pink

  // Grooming service category colors (used in service selection)
  static Color get groomingFullService => lightPrimary;    // Premium full grooming
  static const groomingBathDry = Color(0xFF2196F3);        // Bath & dry services
  static const groomingNailCare = Color(0xFFFF9800);       // Nail trimming
  static const groomingStyling = Color(0xFF9C27B0);        // Creative styling
  static const groomingEarCare = Color(0xFF4CAF50);        // Ear cleaning

  // === BACKWARD COMPATIBILITY ALIASES ===
  // Legacy color names that are still referenced in the codebase as constants
  static const forestGreen = _lightThemePrimaryColor;       // Legacy brand color alias
  static const elegantBrown = _lightThemePrimaryColor;      // Legacy elegant brown alias
  static const elegantBrownContainer = Color(0xFFE3F2FD);   // Legacy container alias (computed from medical blue)
  static const elegantBrownLight = Color(0xFF42A5F5);       // Legacy light variant alias
  static const elegantBrownDark = Color(0xFF1565C0);        // Legacy dark variant alias
  static const darkAccent = _darkThemePrimaryColor;         // Legacy dark accent alias
  static const darkAccentVariant = Color(0xFF64B5F6);       // Legacy dark variant alias
  static const darkAccentContainer = Color(0xFF0D47A1);     // Legacy dark container alias
  static const white = pureWhite;                           // Common white alias
  static const logoBrown = _lightThemePrimaryColor;         // Logo color alias
  static const appBackground = pureWhite;                   // App background alias
  static const taupe = secondaryText;                       // Text color alias
  static const softIvory = lightGray;                       // Surface color alias

  // Social login colors (kept for compatibility)
  static const googleRed = Color(0xFFDB4437);
  static const appleBlack = Color(0xFF000000);


  // === COLOR COMPUTATION METHODS ===
  // Private helper methods to compute color variants programmatically
  
  /// Lightens a color by mixing it with white
  static Color _lightenColor(Color color, double amount) {
    assert(amount >= 0 && amount <= 1, 'Amount must be between 0 and 1');
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness + amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  /// Darkens a color by reducing its lightness
  static Color _darkenColor(Color color, double amount) {
    assert(amount >= 0 && amount <= 1, 'Amount must be between 0 and 1');
    final hsl = HSLColor.fromColor(color);
    final lightness = (hsl.lightness - amount).clamp(0.0, 1.0);
    return hsl.withLightness(lightness).toColor();
  }

  /// Adjusts the saturation of a color
  static Color _adjustSaturation(Color color, double amount) {
    assert(amount >= -1 && amount <= 1, 'Amount must be between -1 and 1');
    final hsl = HSLColor.fromColor(color);
    final saturation = (hsl.saturation + amount).clamp(0.0, 1.0);
    return hsl.withSaturation(saturation).toColor();
  }

  /// Creates a harmonious color palette from the primary color
  static List<Color> _generateHarmoniousColors(Color baseColor, int count) {
    final hsl = HSLColor.fromColor(baseColor);
    final colors = <Color>[];
    
    for (int i = 0; i < count; i++) {
      final hueShift = (360.0 / count) * i;
      final newHue = (hsl.hue + hueShift) % 360.0;
      colors.add(hsl.withHue(newHue).toColor());
    }
    
    return colors;
  }
}

/// Centralized theme configuration for the Partykids app
/// Contains both light and dark theme definitions with comprehensive color schemes
class AppTheme {
  /// Internal map for unique staff color assignments
  static Map<String, Color>? _staffColorMap;
  /// Light theme configuration - Clean white with computed primary accents
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primaryColor: AppColors.lightPrimary,
      scaffoldBackgroundColor: AppColors.pureWhite,
      colorScheme: ColorScheme.light(
        // Primary colors
        primary: AppColors.lightPrimary,
        onPrimary: AppColors.pureWhite,
        primaryContainer: AppColors.lightPrimaryContainer,
        onPrimaryContainer: AppColors.lightPrimary,

        // Secondary colors
        secondary: AppColors.lightPrimaryLight,
        onSecondary: AppColors.pureWhite,
        secondaryContainer: AppColors.lightGrayVariant,
        onSecondaryContainer: AppColors.lightText,

        // Surface colors
        surface: AppColors.pureWhite,
        onSurface: AppColors.lightText,
        surfaceContainerHighest: AppColors.appointmentCancelledGrayLight,
        onSurfaceVariant: AppColors.secondaryText,

        // Error colors
        error: AppColors.lightError,
        onError: AppColors.pureWhite,

        // Outline colors
        outline: AppColors.secondaryText,
        outlineVariant: AppColors.lightPrimary.withValues(alpha: 0.3),
      ),
      // App Bar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.lightPrimary,
        foregroundColor: AppColors.pureWhite,
        elevation: 0,
        centerTitle: false,
      ),

      // Card Theme
      cardTheme: CardThemeData(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        color: AppColors.pureWhite,
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.lightGray,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.secondaryText),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.secondaryText),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColors.lightPrimary, width: 2),
        ),
        labelStyle: const TextStyle(color: AppColors.secondaryText),
        hintStyle: const TextStyle(color: AppColors.secondaryText),
      ),

      // Button Themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.lightPrimary,
          foregroundColor: AppColors.pureWhite,
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.lightPrimary,
          side: BorderSide(color: AppColors.lightPrimary),
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.lightPrimary,
        ),
      ),

      // Snackbar Theme
      snackBarTheme: SnackBarThemeData(
        backgroundColor: AppColors.lightPrimary,
        contentTextStyle: const TextStyle(color: AppColors.pureWhite),
        actionTextColor: AppColors.pureWhite,
      ),
    );
  }

  /// Dark theme configuration - Pure black with computed primary accents
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primaryColor: AppColors.darkPrimary,
      scaffoldBackgroundColor: AppColors.darkBackground,
      colorScheme: ColorScheme.dark(
        // Primary colors
        primary: AppColors.darkPrimary,
        onPrimary: AppColors.pureWhite,
        primaryContainer: AppColors.darkPrimaryContainer,
        onPrimaryContainer: AppColors.darkText,

        // Secondary colors
        secondary: AppColors.darkPrimaryLight,
        onSecondary: AppColors.darkBackground,
        secondaryContainer: AppColors.darkSurfaceVariant,
        onSecondaryContainer: AppColors.darkText,

        // Surface colors
        surface: AppColors.darkSurface,
        onSurface: AppColors.darkText,
        surfaceContainerHighest: AppColors.appointmentCancelledGrayDark,
        onSurfaceVariant: AppColors.darkTextSecondary,

        // Error colors
        error: AppColors.darkError,
        onError: AppColors.pureWhite,

        // Outline colors
        outline: AppColors.darkBorder,
        outlineVariant: AppColors.darkBorder.withValues(alpha: 0.3),
      ),

      // App Bar Theme
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.darkSurface,
        foregroundColor: AppColors.darkText,
        elevation: 0,
        centerTitle: false,
        surfaceTintColor: Colors.transparent,
        titleTextStyle: TextStyle(
          color: AppColors.darkText,
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
      ),

      // Card Theme
      cardTheme: CardThemeData(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        color: AppColors.darkSurface,
        surfaceTintColor: Colors.transparent,
      ),

      // Input Decoration Theme
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.darkSurfaceVariant,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.darkBorder),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.darkBorder),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: AppColors.darkPrimary, width: 2),
        ),
        labelStyle: const TextStyle(color: AppColors.darkTextSecondary),
        hintStyle: const TextStyle(color: AppColors.darkTextSecondary),
      ),

      // Button Themes
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.darkPrimary,
          foregroundColor: AppColors.darkBackground,
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.darkPrimary,
          side: BorderSide(color: AppColors.darkPrimary),
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.darkPrimary,
        ),
      ),

      // Snackbar Theme
      snackBarTheme: SnackBarThemeData(
        backgroundColor: AppColors.darkSurfaceVariant,
        contentTextStyle: const TextStyle(color: AppColors.darkText),
        actionTextColor: AppColors.darkPrimary,
      ),
    );
  }

  /// Helper method to get status color based on theme
  static Color getStatusColor(BuildContext context, String status) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    switch (status.toLowerCase()) {
      case 'success':
      case 'confirmed':
      case 'completed':
        return isDark ? AppColors.darkSuccess : AppColors.lightSuccess;
      case 'warning':
      case 'pending':
        return isDark ? AppColors.darkWarning : AppColors.lightWarning;
      case 'error':
      case 'cancelled':
      case 'canceled':
      case 'anulat':
        return isDark ? AppColors.darkError : AppColors.lightError;
      case 'info':
        return isDark ? AppColors.darkInfo : AppColors.lightInfo;
      default:
        return Theme.of(context).colorScheme.onSurfaceVariant;
    }
  }

  /// Helper method to get staff color for identification
  static Color getStaffColor(String staffId) {
    _staffColorMap ??= <String, Color>{};
    if (_staffColorMap!.containsKey(staffId)) {
      return _staffColorMap![staffId]!;
    }

    final usedColors = _staffColorMap!.values.toSet();
    for (final color in AppColors.staffColors) {
      if (!usedColors.contains(color)) {
        _staffColorMap![staffId] = color;
        return color;
      }
    }

    final newColor = AppColors.generateExtraStaffColor(usedColors.length);
    _staffColorMap![staffId] = newColor;
    return newColor;
  }

  /// === PROGRAMMATIC THEME GENERATION ===
  /// Change the entire app theme by updating the base colors

  /// Generates a complete color scheme from a single primary color
  static Map<String, Color> generateColorScheme(Color primaryColor) {
    return {
      'primary': primaryColor,
      'primaryLight': AppColors._lightenColor(primaryColor, 0.15),
      'primaryDark': AppColors._darkenColor(primaryColor, 0.15),
      'primaryContainer': AppColors._lightenColor(primaryColor, 0.85),
      'primaryAccent': AppColors._adjustSaturation(primaryColor, 0.1),
      'darkContainer': AppColors._darkenColor(primaryColor, 0.6),
    };
  }

  /// Preview how the app would look with different primary colors
  static ThemeData previewTheme({
    required Color lightPrimary,
    required Color darkPrimary,
    required bool isDark,
  }) {
    if (isDark) {
      return ThemeData(
        useMaterial3: true,
        brightness: Brightness.dark,
        primaryColor: darkPrimary,
        colorScheme: ColorScheme.dark(
          primary: darkPrimary,
          primaryContainer: AppColors._darkenColor(darkPrimary, 0.6),
          secondary: AppColors._lightenColor(darkPrimary, 0.15),
          surface: AppColors.darkSurface,
          onSurface: AppColors.darkText,
        ),
      );
    } else {
      return ThemeData(
        useMaterial3: true,
        brightness: Brightness.light,
        primaryColor: lightPrimary,
        colorScheme: ColorScheme.light(
          primary: lightPrimary,
          primaryContainer: AppColors._lightenColor(lightPrimary, 0.85),
          secondary: AppColors._lightenColor(lightPrimary, 0.15),
          surface: AppColors.pureWhite,
          onSurface: AppColors.lightText,
        ),
      );
    }
  }
}
