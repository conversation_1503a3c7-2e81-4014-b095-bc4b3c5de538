import 'package:flutter_test/flutter_test.dart';
import 'package:partykidsapp/models/working_hours_settings.dart';

void main() {
  group('WorkingHoursSettings.isTimeRangeWithinWorkingHours', () {
    late WorkingHoursSettings settings;

    setUp(() {
      settings = WorkingHoursSettings(
        salonId: 'salon1',
        weeklySchedule: {
          'monday': const DaySchedule(
            startTime: '09:00',
            endTime: '17:00',
            isWorkingDay: true,
          ),
          'tuesday': const DaySchedule(
            startTime: '09:00',
            endTime: '17:00',
            isWorkingDay: true,
          ),
          'wednesday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
          ),
          'thursday': const DaySchedule(
            startTime: '09:00',
            endTime: '17:00',
            isWorkingDay: true,
          ),
          'friday': const DaySchedule(
            startTime: '09:00',
            endTime: '17:00',
            isWorkingDay: true,
          ),
          'saturday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
          ),
          'sunday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
          ),
        },
        holidays: const [],
        customClosures: const [],
        updatedAt: DateTime(2024, 1, 1),
      );
    });

    test('returns true for valid booking within hours', () {
      final start = DateTime(2024, 1, 8, 10, 0); // Monday
      final end = DateTime(2024, 1, 8, 11, 0);
      expect(settings.isTimeRangeWithinWorkingHours(start, end), isTrue);
    });

    test('returns false for booking before opening', () {
      final start = DateTime(2024, 1, 8, 8, 0);
      final end = DateTime(2024, 1, 8, 9, 0);
      expect(settings.isTimeRangeWithinWorkingHours(start, end), isFalse);
    });

    test('returns false for booking after closing', () {
      final start = DateTime(2024, 1, 8, 17, 30);
      final end = DateTime(2024, 1, 8, 18, 0);
      expect(settings.isTimeRangeWithinWorkingHours(start, end), isFalse);
    });

    test('returns false for booking overlapping non-working hours', () {
      final start = DateTime(2024, 1, 8, 16, 30);
      final end = DateTime(2024, 1, 8, 17, 30);
      expect(settings.isTimeRangeWithinWorkingHours(start, end), isFalse);
    });
  });
}
