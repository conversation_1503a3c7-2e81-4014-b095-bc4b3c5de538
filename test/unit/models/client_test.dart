import 'package:flutter_test/flutter_test.dart';
import 'package:partykidsapp/models/client.dart';

void main() {
  group('Client Model Tests', () {
    group('Constructor Tests', () {
      test('should create client with required fields', () {
        final registrationDate = DateTime(2024, 1, 15);
        
        final client = Client(
          id: '1',
          name: '<PERSON>',
          phone: '+40728626399',
          registrationDate: registrationDate,
        );

        expect(client.id, equals('1'));
        expect(client.name, equals('<PERSON>'));
        expect(client.phone, equals('+40728626399'));
        expect(client.registrationDate, equals(registrationDate));
        expect(client.email, equals(''));
        expect(client.address, equals(''));
        expect(client.kidIds, isEmpty);
        expect(client.notes, equals(''));
        expect(client.isActive, isTrue);
        expect(client.kidCount, equals(0));
        expect(client.updatedAt, isNull);
      });

      test('should create client with all fields', () {
        final registrationDate = DateTime(2024, 1, 15);
        final updatedAt = DateTime(2024, 2, 1);
        
        final client = Client(
          id: '1',
          name: '<PERSON>',
          phone: '+40728626399',
          email: '<EMAIL>',
          address: 'Str. Exemplu 123, București',
          registrationDate: registrationDate,
          kidIds: ['pet1', 'pet2'],
          notes: 'Client preferat',
          isActive: true,
          kidCount: 2,
          updatedAt: updatedAt,
        );

        expect(client.id, equals('1'));
        expect(client.name, equals('John Doe'));
        expect(client.phone, equals('+40728626399'));
        expect(client.email, equals('<EMAIL>'));
        expect(client.address, equals('Str. Exemplu 123, București'));
        expect(client.registrationDate, equals(registrationDate));
        expect(client.kidIds, equals(['pet1', 'pet2']));
        expect(client.notes, equals('Client preferat'));
        expect(client.isActive, isTrue);
        expect(client.kidCount, equals(2));
        expect(client.updatedAt, equals(updatedAt));
      });
    });

    group('JSON Serialization Tests', () {
      test('should create client from JSON with all fields', () {
        final json = {
          'id': '1',
          'name': 'Maria Popescu',
          'phone': '+40728626399',
          'email': '<EMAIL>',
          'address': 'Str. Libertății 45, Cluj-Napoca',
          'registrationDate': '2024-01-15T10:30:00.000Z',
          'kidIds': ['pet1', 'pet2'],
          'notes': 'Client VIP',
          'isActive': true,
          'kidCount': 2,
          'updatedAt': '2024-02-01T15:45:00.000Z',
        };

        final client = Client.fromJson(json);

        expect(client.id, equals('1'));
        expect(client.name, equals('Maria Popescu'));
        expect(client.phone, equals('+40728626399'));
        expect(client.email, equals('<EMAIL>'));
        expect(client.address, equals('Str. Libertății 45, Cluj-Napoca'));
        expect(client.registrationDate, equals(DateTime.parse('2024-01-15T10:30:00.000Z')));
        expect(client.kidIds, equals(['pet1', 'pet2']));
        expect(client.notes, equals('Client VIP'));
        expect(client.isActive, isTrue);
        expect(client.kidCount, equals(2));
        expect(client.updatedAt, equals(DateTime.parse('2024-02-01T15:45:00.000Z')));
      });

      test('should create client from JSON with minimal fields', () {
        final json = {
          'id': '2',
          'name': 'Ana Ionescu',
          'phone': '+40755123456',
        };

        final client = Client.fromJson(json);

        expect(client.id, equals('2'));
        expect(client.name, equals('Ana Ionescu'));
        expect(client.phone, equals('+40755123456'));
        expect(client.email, equals(''));
        expect(client.address, equals(''));
        expect(client.kidIds, isEmpty);
        expect(client.notes, equals(''));
        expect(client.isActive, isTrue);
        expect(client.kidCount, equals(0));
        expect(client.updatedAt, isNull);
      });

      test('should handle null and missing fields in JSON', () {
        final json = {
          'id': null,
          'name': null,
          'phone': null,
          'email': null,
          'address': null,
          'kidIds': null,
          'notes': null,
          'isActive': null,
          'kidCount': null,
        };

        final client = Client.fromJson(json);

        expect(client.id, equals(''));
        expect(client.name, equals(''));
        expect(client.phone, equals(''));
        expect(client.email, equals(''));
        expect(client.address, equals(''));
        expect(client.kidIds, isEmpty);
        expect(client.notes, equals(''));
        expect(client.isActive, isTrue);
        expect(client.kidCount, equals(0));
      });

      test('should handle createdAt as fallback for registrationDate', () {
        final json = {
          'id': '3',
          'name': 'Test Client',
          'phone': '+40728626399',
          'createdAt': '2024-01-20T12:00:00.000Z',
        };

        final client = Client.fromJson(json);

        expect(client.registrationDate, equals(DateTime.parse('2024-01-20T12:00:00.000Z')));
      });

      test('should convert client to JSON correctly', () {
        final registrationDate = DateTime(2024, 1, 15, 10, 30);
        final updatedAt = DateTime(2024, 2, 1, 15, 45);
        
        final client = Client(
          id: '1',
          name: 'Test Client',
          phone: '+40728626399',
          email: '<EMAIL>',
          address: 'Test Address',
          registrationDate: registrationDate,
          kidIds: ['pet1', 'pet2'],
          notes: 'Test notes',
          isActive: true,
          kidCount: 2,
          updatedAt: updatedAt,
        );

        final json = client.toJson();

        expect(json['id'], equals('1'));
        expect(json['name'], equals('Test Client'));
        expect(json['phone'], equals('+40728626399'));
        expect(json['email'], equals('<EMAIL>'));
        expect(json['address'], equals('Test Address'));
        expect(json['registrationDate'], equals(registrationDate.toIso8601String()));
        expect(json['kidIds'], equals(['pet1', 'pet2']));
        expect(json['notes'], equals('Test notes'));
        expect(json['isActive'], isTrue);
        expect(json['kidCount'], equals(2));
        expect(json['updatedAt'], equals(updatedAt.toIso8601String()));
      });
    });

    group('CopyWith Tests', () {
      test('should copy client with updated fields', () {
        final originalDate = DateTime(2024, 1, 15);
        final updatedDate = DateTime(2024, 2, 1);
        
        final original = Client(
          id: '1',
          name: 'Original Name',
          phone: '+40728626399',
          email: '<EMAIL>',
          registrationDate: originalDate,
          kidCount: 1,
        );

        final updated = original.copyWith(
          name: 'Updated Name',
          email: '<EMAIL>',
          kidCount: 2,
          updatedAt: updatedDate,
        );

        // Updated fields
        expect(updated.name, equals('Updated Name'));
        expect(updated.email, equals('<EMAIL>'));
        expect(updated.kidCount, equals(2));
        expect(updated.updatedAt, equals(updatedDate));

        // Unchanged fields
        expect(updated.id, equals('1'));
        expect(updated.phone, equals('+40728626399'));
        expect(updated.registrationDate, equals(originalDate));
      });

      test('should copy client without changes when no parameters provided', () {
        final registrationDate = DateTime(2024, 1, 15);
        
        final original = Client(
          id: '1',
          name: 'Test Client',
          phone: '+40728626399',
          registrationDate: registrationDate,
        );

        final copy = original.copyWith();

        expect(copy.id, equals(original.id));
        expect(copy.name, equals(original.name));
        expect(copy.phone, equals(original.phone));
        expect(copy.registrationDate, equals(original.registrationDate));
        expect(copy.email, equals(original.email));
        expect(copy.address, equals(original.address));
        expect(copy.kidIds, equals(original.kidIds));
        expect(copy.notes, equals(original.notes));
        expect(copy.isActive, equals(original.isActive));
        expect(copy.kidCount, equals(original.kidCount));
        expect(copy.updatedAt, equals(original.updatedAt));
      });

      test('should copy client with null values', () {
        final registrationDate = DateTime(2024, 1, 15);
        final updatedAt = DateTime(2024, 2, 1);

        final original = Client(
          id: '1',
          name: 'Test Client',
          phone: '+40728626399',
          registrationDate: registrationDate,
          updatedAt: updatedAt,
        );

        // Note: The copyWith method might not support setting null values
        // This test checks if the copyWith preserves the original value when no parameter is provided
        final updated = original.copyWith();

        expect(updated.updatedAt, equals(updatedAt)); // Should preserve original value
        expect(updated.name, equals('Test Client')); // Other fields unchanged
      });
    });

    group('Edge Cases', () {
      test('should handle empty strings in JSON', () {
        final json = {
          'id': '',
          'name': '',
          'phone': '',
          'email': '',
          'address': '',
          'notes': '',
          'registrationDate': DateTime.now().toIso8601String(),
        };

        final client = Client.fromJson(json);

        expect(client.id, equals(''));
        expect(client.name, equals(''));
        expect(client.phone, equals(''));
        expect(client.email, equals(''));
        expect(client.address, equals(''));
        expect(client.notes, equals(''));
      });

      test('should handle invalid date strings gracefully', () {
        final json = {
          'id': '1',
          'name': 'Test Client',
          'phone': '+40728626399',
          'registrationDate': 'invalid-date',
        };

        expect(() => Client.fromJson(json), throwsA(isA<FormatException>()));
      });

      test('should handle large pet counts', () {
        final client = Client(
          id: '1',
          name: 'Test Client',
          phone: '+40728626399',
          registrationDate: DateTime.now(),
          kidCount: 999999,
        );

        expect(client.kidCount, equals(999999));
        
        final json = client.toJson();
        final recreated = Client.fromJson(json);
        expect(recreated.kidCount, equals(999999));
      });
    });
  });
}
