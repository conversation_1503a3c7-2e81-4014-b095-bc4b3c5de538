import 'package:flutter_test/flutter_test.dart';
import 'package:partykidsapp/models/staff_working_hours_settings.dart';
import 'package:partykidsapp/models/working_hours_settings.dart';
import 'package:partykidsapp/widgets/schedule/staff_schedule_templates.dart';
import 'package:partykidsapp/utils/romanian_holidays.dart';

void main() {
  group('StaffWorkingHoursSettings', () {
    late StaffWorkingHoursSettings testSettings;

    setUp(() {
      testSettings = StaffWorkingHoursSettings(
        staffId: 'staff123',
        salonId: 'salon456',
        weeklySchedule: {
          'monday': const DaySchedule(
            startTime: '09:00',
            endTime: '17:00',
            isWorkingDay: true,
            breakStart: '12:00',
            breakEnd: '13:00',
          ),
          'tuesday': const DaySchedule(
            startTime: '09:00',
            endTime: '17:00',
            isWorkingDay: true,
            breakStart: '12:00',
            breakEnd: '13:00',
          ),
          'wednesday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
          'thursday': const DaySchedule(
            startTime: '10:00',
            endTime: '18:00',
            isWorkingDay: true,
            breakStart: '13:00',
            breakEnd: '14:00',
          ),
          'friday': const DaySchedule(
            startTime: '09:00',
            endTime: '17:00',
            isWorkingDay: true,
            breakStart: '12:00',
            breakEnd: '13:00',
          ),
          'saturday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
          'sunday': const DaySchedule(
            startTime: null,
            endTime: null,
            isWorkingDay: false,
            breakStart: null,
            breakEnd: null,
          ),
        },
        holidays: [
          Holiday(
            name: 'Anul Nou',
            date: DateTime(2024, 1, 1),
            isWorkingDay: false,
            type: HolidayType.legal,
          ),
        ],
        customClosures: [
          CustomClosure(
            reason: 'Concediu medical',
            date: DateTime(2024, 6, 15),
            description: 'Recuperare după intervenție',
          ),
        ],
        updatedAt: DateTime(2024, 1, 1),
      );
    });

    test('should serialize to and from JSON correctly', () {
      // Test serialization
      final json = testSettings.toJson();
      expect(json['staffId'], equals('staff123'));
      expect(json['salonId'], equals('salon456'));
      expect(json['weeklySchedule'], isA<Map<String, dynamic>>());
      expect(json['holidays'], isA<List>());
      expect(json['customClosures'], isA<List>());

      // Test deserialization
      final restored = StaffWorkingHoursSettings.fromJson(json);
      expect(restored.staffId, equals(testSettings.staffId));
      expect(restored.salonId, equals(testSettings.salonId));
      expect(restored.weeklySchedule.length, equals(testSettings.weeklySchedule.length));
      expect(restored.holidays.length, equals(testSettings.holidays.length));
      expect(restored.customClosures.length, equals(testSettings.customClosures.length));
    });

    test('should get schedule for specific day', () {
      final mondaySchedule = testSettings.getScheduleForDay('monday');
      expect(mondaySchedule, isNotNull);
      expect(mondaySchedule!.isWorkingDay, isTrue);
      expect(mondaySchedule.startTime, equals('09:00'));
      expect(mondaySchedule.endTime, equals('17:00'));

      final wednesdaySchedule = testSettings.getScheduleForDay('wednesday');
      expect(wednesdaySchedule, isNotNull);
      expect(wednesdaySchedule!.isWorkingDay, isFalse);
    });

    test('should check availability on specific date', () {
      // Monday (working day)
      final monday = DateTime(2024, 1, 8); // A Monday
      expect(testSettings.isAvailableOnDate(monday), isTrue);

      // Wednesday (day off)
      final wednesday = DateTime(2024, 1, 10); // A Wednesday
      expect(testSettings.isAvailableOnDate(wednesday), isFalse);

      // Holiday (New Year)
      final newYear = DateTime(2024, 1, 1);
      expect(testSettings.isAvailableOnDate(newYear), isFalse);

      // Custom closure
      final customClosure = DateTime(2024, 6, 15);
      expect(testSettings.isAvailableOnDate(customClosure), isFalse);
    });

    test('should check availability at specific time', () {
      final monday9AM = DateTime(2024, 1, 8, 9, 0); // Monday 9:00 AM
      expect(testSettings.isAvailableAtTime(monday9AM), isTrue);

      final monday12PM = DateTime(2024, 1, 8, 12, 30); // Monday 12:30 PM (break time)
      expect(testSettings.isAvailableAtTime(monday12PM), isFalse);

      final monday8AM = DateTime(2024, 1, 8, 8, 0); // Monday 8:00 AM (before work)
      expect(testSettings.isAvailableAtTime(monday8AM), isFalse);

      final wednesday10AM = DateTime(2024, 1, 10, 10, 0); // Wednesday (day off)
      expect(testSettings.isAvailableAtTime(wednesday10AM), isFalse);
    });

    test('should calculate total weekly hours correctly', () {
      final totalHours = testSettings.totalWeeklyHours;
      // Monday: 8 hours - 1 hour break = 7 hours
      // Tuesday: 8 hours - 1 hour break = 7 hours
      // Wednesday: 0 hours (day off)
      // Thursday: 8 hours - 1 hour break = 7 hours
      // Friday: 8 hours - 1 hour break = 7 hours
      // Saturday: 0 hours (day off)
      // Sunday: 0 hours (day off)
      // Total: 28 hours
      expect(totalHours, equals(28.0));
    });

    test('should check if has working days', () {
      expect(testSettings.hasWorkingDays, isTrue);

      // Test with no working days
      final noWorkSettings = testSettings.copyWith(
        weeklySchedule: {
          'monday': const DaySchedule(isWorkingDay: false, startTime: null, endTime: null, breakStart: null, breakEnd: null),
          'tuesday': const DaySchedule(isWorkingDay: false, startTime: null, endTime: null, breakStart: null, breakEnd: null),
          'wednesday': const DaySchedule(isWorkingDay: false, startTime: null, endTime: null, breakStart: null, breakEnd: null),
          'thursday': const DaySchedule(isWorkingDay: false, startTime: null, endTime: null, breakStart: null, breakEnd: null),
          'friday': const DaySchedule(isWorkingDay: false, startTime: null, endTime: null, breakStart: null, breakEnd: null),
          'saturday': const DaySchedule(isWorkingDay: false, startTime: null, endTime: null, breakStart: null, breakEnd: null),
          'sunday': const DaySchedule(isWorkingDay: false, startTime: null, endTime: null, breakStart: null, breakEnd: null),
        },
      );
      expect(noWorkSettings.hasWorkingDays, isFalse);
    });

    test('should create copy with updated values', () {
      final updatedSettings = testSettings.copyWith(
        staffId: 'newStaff789',
        salonId: 'newSalon123',
      );

      expect(updatedSettings.staffId, equals('newStaff789'));
      expect(updatedSettings.salonId, equals('newSalon123'));
      expect(updatedSettings.weeklySchedule, equals(testSettings.weeklySchedule));
      expect(updatedSettings.holidays, equals(testSettings.holidays));
      expect(updatedSettings.customClosures, equals(testSettings.customClosures));
    });
  });

  group('UpdateStaffWorkingHoursRequest', () {
    test('should create from StaffWorkingHoursSettings', () {
      final settings = RomanianHolidays.getDefaultStaffWorkingHours('staff123', 'salon456');
      final request = UpdateStaffWorkingHoursRequest.fromStaffWorkingHoursSettings(settings);

      expect(request.weeklySchedule, equals(settings.weeklySchedule));
      expect(request.holidays, equals(settings.holidays));
      expect(request.customClosures, equals(settings.customClosures));
    });

    test('should serialize to JSON correctly', () {
      final settings = RomanianHolidays.getDefaultStaffWorkingHours('staff123', 'salon456');
      final request = UpdateStaffWorkingHoursRequest.fromStaffWorkingHoursSettings(settings);
      final json = request.toJson();

      expect(json['weeklySchedule'], isA<Map<String, dynamic>>());
      expect(json['holidays'], isA<List>());
      expect(json['customClosures'], isA<List>());
    });
  });

  group('StaffScheduleTemplates', () {
    test('should return all templates', () {
      final templates = StaffScheduleTemplates.allTemplates;
      expect(templates.length, greaterThan(0));
      expect(templates.every((t) => t.name.isNotEmpty), isTrue);
      expect(templates.every((t) => t.description.isNotEmpty), isTrue);
      expect(templates.every((t) => t.schedule.isNotEmpty), isTrue);
    });

    test('should get template by name', () {
      final template = StaffScheduleTemplates.getTemplateByName('Program standard groomer');
      expect(template, isNotNull);
      expect(template!.name, equals('Program standard groomer'));

      final nonExistent = StaffScheduleTemplates.getTemplateByName('Non-existent template');
      expect(nonExistent, isNull);
    });

    test('should calculate template statistics correctly', () {
      final template = StaffScheduleTemplates.getTemplateByName('Program standard groomer');
      expect(template, isNotNull);
      
      expect(template!.totalWeeklyHours, greaterThan(0));
      expect(template.workingDaysCount, greaterThan(0));
      expect(template.workingDaysCount, lessThanOrEqualTo(7));
    });
  });

  group('RomanianHolidays - Staff Integration', () {
    test('should create default staff working hours', () {
      final settings = RomanianHolidays.getDefaultStaffWorkingHours('staff123', 'salon456');

      expect(settings.staffId, equals('staff123'));
      expect(settings.salonId, equals('salon456'));
      expect(settings.weeklySchedule.length, equals(7));
      expect(settings.holidays.isNotEmpty, isTrue);
      expect(settings.customClosures.isEmpty, isTrue);
      expect(settings.hasWorkingDays, isTrue);
    });

    test('should include Romanian holidays in default settings', () {
      final settings = RomanianHolidays.getDefaultStaffWorkingHours('staff123', 'salon456');
      final currentYear = DateTime.now().year;
      
      // Should include holidays for current and next year
      final newYearHolidays = settings.holidays.where((h) => 
        h.name.contains('Anul Nou') && 
        (h.date.year == currentYear || h.date.year == currentYear + 1)
      ).toList();
      
      expect(newYearHolidays.length, greaterThanOrEqualTo(1));
    });
  });
}
