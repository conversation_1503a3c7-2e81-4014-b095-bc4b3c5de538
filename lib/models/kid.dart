class Kid {
  final String id;
  final String name;
  final DateTime birthDate;
  final String favoriteCharacter;
  final String notes;
  final String parentId;

  Kid({
    required this.id,
    required this.name,
    required this.birthDate,
    required this.parentId,
    this.favoriteCharacter = '',
    this.notes = '',
  });

  int get ageInYears {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    if (now.month < birthDate.month ||
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    return age;
  }

  factory Kid.fromJson(Map<String, dynamic> json) {
    return Kid(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      birthDate: json['birthDate'] != null
          ? DateTime.parse(json['birthDate'])
          : DateTime.now(),
      parentId: json['parentId'] ?? json['clientId'] ?? '',
      favoriteCharacter: json['favoriteCharacter'] ?? '',
      notes: json['notes'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'birthDate': birthDate.toIso8601String(),
      'parentId': parentId,
      'favoriteCharacter': favoriteCharacter,
      'notes': notes,
    };
  }

  Kid copyWith({
    String? id,
    String? name,
    DateTime? birthDate,
    String? parentId,
    String? favoriteCharacter,
    String? notes,
  }) {
    return Kid(
      id: id ?? this.id,
      name: name ?? this.name,
      birthDate: birthDate ?? this.birthDate,
      parentId: parentId ?? this.parentId,
      favoriteCharacter: favoriteCharacter ?? this.favoriteCharacter,
      notes: notes ?? this.notes,
    );
  }
}
