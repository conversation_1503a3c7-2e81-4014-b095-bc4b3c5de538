import 'environment.dart';

class ApiConfig {
  // API version
  static const String apiVersion = 'v1';

  // Base URL for the API server (environment-based)
  static String get baseUrl => EnvironmentConfig.apiBaseUrl;

  // API endpoints
  static String get authEndpoint => '$baseUrl/auth';
  static String get clientsEndpoint => '$baseUrl/clients';
  static String get appointmentsEndpoint => '$baseUrl/appointments';
  static String get servicesEndpoint => '$baseUrl/services';
  static String get groomersEndpoint => '$baseUrl/groomers';
  static String get settingsEndpoint => '$baseUrl/settings';
  static String get notificationsEndpoint => '$baseUrl/notifications';
  static String get smsEndpoint => '$baseUrl/sms';
  static String get reportsEndpoint => '$baseUrl/reports';

  // Authentication endpoints
  static String get firebaseLoginEndpoint => '$authEndpoint/firebase-login';
  static String get refreshTokenEndpoint => '$authEndpoint/refresh-token';
  static String get logoutEndpoint => '$authEndpoint/logout';

  // Request configuration (environment-based)
  static Duration get timeout => EnvironmentConfig.apiTimeout;
  static const Duration shortTimeout = Duration(seconds: 10);
  static const Duration longTimeout = Duration(seconds: 60);

  // Development flags
  static bool get enableLogging => EnvironmentConfig.isDebugMode;
  static const bool enableRetry = true;
  static const int maxRetries = 3;

  // Headers
  static Map<String, String> get defaultHeaders => {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-API-Version': apiVersion,
    'X-App-Environment': EnvironmentConfig.currentEnvironment.name,
  };

  // Headers with authentication
  static Map<String, String> getAuthHeaders(String token) => {
    ...defaultHeaders,
    'Authorization': 'Bearer $token',
  };

  // Helper method to validate server configuration
  static bool isValidServerConfig() {
    try {
      final uri = Uri.parse(baseUrl);
      return uri.hasScheme && uri.hasAuthority;
    } catch (e) {
      return false;
    }
  }

  // Get configuration info for debugging
  static Map<String, dynamic> getConfigInfo() {
    return {
      'baseUrl': baseUrl,
      'environment': EnvironmentConfig.currentEnvironment.name,
      'apiVersion': apiVersion,
      'isValidConfig': isValidServerConfig(),
      'enableLogging': enableLogging,
      'timeout': timeout.inSeconds,
      'isDebugMode': EnvironmentConfig.isDebugMode,
    };
  }

}