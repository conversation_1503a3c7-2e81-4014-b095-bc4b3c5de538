import 'package:partykidsapp/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';
import '../../models/pet.dart';
import '../../config/theme/app_theme.dart';
import '../../widgets/animals/animal_header_widget.dart';
import '../../widgets/animals/animal_info_widget.dart';
import '../../widgets/common/custom_bottom_sheet.dart';
import '../../widgets/common/custom_sliver_app_bar.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

class AnimalDetailsScreen extends StatefulWidget {
  final Pet pet;

  const AnimalDetailsScreen({
    super.key,
    required this.pet,
  });

  @override
  State<AnimalDetailsScreen> createState() => _AnimalDetailsScreenState();
}

class _AnimalDetailsScreenState extends State<AnimalDetailsScreen> {
  bool _isLoading = false;
  File? _selectedImage;
  final ImagePicker _imagePicker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _loadAnimalData();
  }

  Future<void> _loadAnimalData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Additional data loading can be done here in the future
    } catch (e) {
      debugPrint('Error loading animal data: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }


  void _pickImage() {
    CustomBottomSheet.show(
      context: context,
      title: 'Selectează sursa imaginii',
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: Icon(Icons.camera_alt, color: AppColors.forestGreen),
            title: const Text('Camera'),
            onTap: () {
              Navigator.pop(context);
              _getImageFromSource(ImageSource.camera);
            },
          ),
          ListTile(
            leading: Icon(Icons.photo_library, color: AppColors.forestGreen),
            title: const Text('Galerie'),
            onTap: () {
              Navigator.pop(context);
              _getImageFromSource(ImageSource.gallery);
            },
          ),
          if (_selectedImage != null)
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('Șterge imaginea'),
              onTap: () {
                Navigator.pop(context);
                setState(() {
                  _selectedImage = null;
                });
              },
            ),
        ],
      ),
    );
  }

  Future<void> _getImageFromSource(ImageSource source) async {
    try {
      final XFile? image = await _imagePicker.pickImage(
        source: source,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (image != null && mounted) {
        setState(() {
          _selectedImage = File(image.path);
        });
      }
    } catch (e) {
      if (mounted) {
        showTopSnackBar(context, 
          SnackBar(
            content: Text('Eroare la selectarea imaginii: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return DetailScreenScaffold(
      headerContent: AnimalHeaderWidget(
        pet: widget.pet,
        photoFile: _selectedImage,
        onPhotoTap: _pickImage,
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.share, color: Colors.white),
          onPressed: () {
            showTopSnackBar(context, 
              const SnackBar(
                content: Text('Partajare - în curând!'),
              ),
            );
          },
        ),
        IconButton(
          icon: const Icon(Icons.photo_camera, color: Colors.white),
          onPressed: _pickImage,
        ),
      ],
      body: _isLoading
          ? const LoadingWidget(message: 'Se încarcă informațiile...')
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Basic Information
                  AnimalInfoWidget(pet: widget.pet),

                  const SizedBox(height: 16),

                  // Medical and grooming information removed
                ],
              ),
            ),
      floatingActionButton: null,
    );
  }
}
