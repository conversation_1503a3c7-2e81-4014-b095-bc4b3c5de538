import 'package:flutter_test/flutter_test.dart';
import 'package:partykidsapp/models/service.dart';

void main() {
  group('Service Pricing Form Tests', () {
    
    group('Safe Parsing Functions', () {
      test('should safely parse valid double values', () {
        // Helper function to safely parse double values
        double safeParseDouble(String value, {double defaultValue = 0.0}) {
          final trimmed = value.trim();
          if (trimmed.isEmpty) return defaultValue;
          return double.tryParse(trimmed) ?? defaultValue;
        }

        expect(safeParseDouble('10'), equals(10.0));
        expect(safeParseDouble('20.5'), equals(20.5));
        expect(safeParseDouble('30.0'), equals(30.0));
      });

      test('should handle empty values with defaults', () {
        // Helper function to safely parse double values
        double safeParseDouble(String value, {double defaultValue = 0.0}) {
          final trimmed = value.trim();
          if (trimmed.isEmpty) return defaultValue;
          return double.tryParse(trimmed) ?? defaultValue;
        }

        expect(safeParseDouble(''), equals(0.0));
        expect(safeParseDouble('   '), equals(0.0));
        expect(safeParseDouble('', defaultValue: 50.0), equals(50.0));
      });

      test('should handle invalid values with defaults', () {
        // Helper function to safely parse double values
        double safeParseDouble(String value, {double defaultValue = 0.0}) {
          final trimmed = value.trim();
          if (trimmed.isEmpty) return defaultValue;
          return double.tryParse(trimmed) ?? defaultValue;
        }

        expect(safeParseDouble('invalid'), equals(0.0));
        expect(safeParseDouble('abc123'), equals(0.0));
        expect(safeParseDouble('invalid', defaultValue: 25.0), equals(25.0));
      });

      test('should safely parse valid int values', () {
        // Helper function to safely parse int values
        int safeParseInt(String value, {int defaultValue = 0}) {
          final trimmed = value.trim();
          if (trimmed.isEmpty) return defaultValue;
          return int.tryParse(trimmed) ?? defaultValue;
        }

        expect(safeParseInt('60'), equals(60));
        expect(safeParseInt('90'), equals(90));
        expect(safeParseInt('120'), equals(120));
      });

      test('should handle empty int values with defaults', () {
        // Helper function to safely parse int values
        int safeParseInt(String value, {int defaultValue = 0}) {
          final trimmed = value.trim();
          if (trimmed.isEmpty) return defaultValue;
          return int.tryParse(trimmed) ?? defaultValue;
        }

        expect(safeParseInt(''), equals(0));
        expect(safeParseInt('   '), equals(0));
        expect(safeParseInt('', defaultValue: 60), equals(60));
      });
    });

    group('Service Creation with Both Pricing Strategies', () {
      test('should create service with variable pricing and price ranges', () {
        // Simulate the scenario that was causing the FormatException
        final service = Service(
          id: 'test-service',
          name: 'Test Service',
          description: 'Test Description',
          price: 50.0, // S size price
          sizePrices: {
            'S': 50.0,
            'M': 60.0,
            'L': 70.0,
          },
          sizeMinPrices: {
            'S': 40.0,
            'M': 50.0,
            'L': 60.0,
          },
          sizeMaxPrices: {
            'S': 60.0,
            'M': 70.0,
            'L': 80.0,
          },
          duration: 60,
          sizeDurations: {
            'S': 60,
            'M': 90,
            'L': 120,
          },
          category: 'test-category',
          createdAt: DateTime.now(),
        );

        expect(service.name, equals('Test Service'));
        expect(service.price, equals(50.0));
        expect(service.sizePrices!['S'], equals(50.0));
        expect(service.sizePrices!['M'], equals(60.0));
        expect(service.sizePrices!['L'], equals(70.0));
        expect(service.sizeMinPrices!['S'], equals(40.0));
        expect(service.sizeMaxPrices!['L'], equals(80.0));
      });

      test('should handle the exact input values from the error case', () {
        // Helper functions (same as in the fixed form)
        double safeParseDouble(String value, {double defaultValue = 0.0}) {
          final trimmed = value.trim();
          if (trimmed.isEmpty) return defaultValue;
          return double.tryParse(trimmed) ?? defaultValue;
        }

        int safeParseInt(String value, {int defaultValue = 0}) {
          final trimmed = value.trim();
          if (trimmed.isEmpty) return defaultValue;
          return int.tryParse(trimmed) ?? defaultValue;
        }

        // Simulate the exact input from the error case:
        // S: 10 20 20
        // M: 20 30 30
        final sPrices = '10 20 20'.split(' ');
        final mPrices = '20 30 30'.split(' ');

        // This should not throw FormatException anymore
        final service = Service(
          id: 'test-service',
          name: 'Test Service',
          description: 'Test Description',
          price: safeParseDouble(sPrices[0], defaultValue: 50.0), // 10
          sizePrices: {
            'S': safeParseDouble(sPrices[0], defaultValue: 50.0), // 10
            'M': safeParseDouble(mPrices[0], defaultValue: 60.0), // 20
            'L': safeParseDouble('', defaultValue: 70.0), // empty -> 70
          },
          sizeMinPrices: {
            'S': safeParseDouble(sPrices[1], defaultValue: 40.0), // 20
            'M': safeParseDouble(mPrices[1], defaultValue: 50.0), // 30
            'L': safeParseDouble('', defaultValue: 60.0), // empty -> 60
          },
          sizeMaxPrices: {
            'S': safeParseDouble(sPrices[2], defaultValue: 60.0), // 20
            'M': safeParseDouble(mPrices[2], defaultValue: 70.0), // 30
            'L': safeParseDouble('', defaultValue: 80.0), // empty -> 80
          },
          duration: 60,
          category: 'test-category',
          createdAt: DateTime.now(),
        );

        expect(service.sizePrices!['S'], equals(10.0));
        expect(service.sizePrices!['M'], equals(20.0));
        expect(service.sizeMinPrices!['S'], equals(20.0));
        expect(service.sizeMinPrices!['M'], equals(30.0));
        expect(service.sizeMaxPrices!['S'], equals(20.0));
        expect(service.sizeMaxPrices!['M'], equals(30.0));
      });
    });
  });
}
