# Notification System Usage Guide

## 🎯 Overview

The Partykids Project uses a **backend-driven notification system** where the frontend simply makes normal API calls and the backend automatically handles all notification logic. This guide shows how to use the system from both frontend and backend perspectives.

---

## 📱 Frontend Usage

### 🔧 Initialization

The notification system is automatically initialized in `main.dart`:

```dart
void main() async {
  // ... other initialization
  
  // Initialize simple notification handler (backend handles all logic)
  await NotificationHandler.initialize();
  
  runApp(MyApp());
}
```

### 📅 Creating Appointments (with automatic notifications)

```dart
// Just call the normal API - backend handles all notifications automatically!
final response = await AppointmentService.createAppointment(appointment);

if (response.success) {
  // Backend automatically:
  // ✅ Sends SMS confirmation to client (if enabled)
  // ✅ Sends push notification to assigned groomer
  // ✅ Sends push notification to all salon staff
  // ✅ Schedules day-before reminder (if enabled)
  
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(content: Text('✅ Programarea a fost creată cu succes!')),
  );
}
```

### ❌ Cancelling Appointments

```dart
final response = await AppointmentService.cancelAppointment(
  appointmentId, 
  reason: 'Client request'
);

if (response.success) {
  // Backend automatically:
  // ✅ Sends SMS cancellation to client
  // ✅ Sends push notification to staff
  // ✅ Cancels any scheduled reminders
}
```

### ✅ Completing Appointments

```dart
final response = await AppointmentService.completeAppointment(
  appointmentId,
  notes: 'Grooming completed successfully'
);

if (response.success) {
  // Backend automatically:
  // ✅ Sends SMS completion message to client
  // ✅ Sends push notification to staff
  // ✅ Schedules follow-up message (2 hours later, if enabled)
}
```

### 🎛️ Managing SMS Settings (Chief Groomer Only)

```dart
// Get current settings
final settingsResponse = await SmsSettingsService.getSmsSettings();
if (settingsResponse.success) {
  final settings = settingsResponse.data!;
  print('Confirmations: ${settings.appointmentConfirmations}');
  print('Reminders: ${settings.dayBeforeReminders}');
  print('Follow-ups: ${settings.followUpMessages}');
}

// Update settings
final updateResponse = await SmsSettingsService.updateSmsSettings(
  appointmentConfirmations: true,
  dayBeforeReminders: false,
  followUpMessages: true,
);
```

### 🔔 Handling Incoming Notifications

The `NotificationHandler` automatically handles incoming notifications:

```dart
// Notifications are automatically displayed and handled
// No additional code needed in your screens!

// When user taps notification, they're automatically navigated to:
// - Appointment details (for appointment notifications)
// - Calendar view (for reminders)
// - Client details (for client messages)
// - Dashboard (for unknown types)
```

---

## 🌐 Backend API Contract

### 📱 FCM Token Management

#### Register FCM Token
```http
POST /api/users/fcm-token
Content-Type: application/json
Authorization: Bearer {token}

{
  "userId": "user-123",
  "token": "fcm-token-string",
  "platform": "android"
}

Response 200:
{
  "success": true,
  "message": "FCM token registered successfully"
}
```

#### Remove FCM Token
```http
DELETE /api/users/fcm-token
Content-Type: application/json
Authorization: Bearer {token}

{
  "token": "fcm-token-string"
}
```

### 🔔 SMS Settings Management

#### Get SMS Settings
```http
GET /api/salons/{salonId}/sms-settings
Authorization: Bearer {token}

Response 200:
{
  "salonId": "salon-123",
  "appointmentConfirmations": true,
  "dayBeforeReminders": true,
  "followUpMessages": false,
  "updatedAt": "2024-01-15T10:00:00Z"
}
```

#### Update SMS Settings (Chief Groomer Only)
```http
PUT /api/salons/{salonId}/sms-settings
Content-Type: application/json
Authorization: Bearer {token}

{
  "appointmentConfirmations": true,
  "dayBeforeReminders": false,
  "followUpMessages": true
}
```

### 📊 Notification Statistics
```http
GET /api/salons/{salonId}/notifications/stats?startDate=2024-01-01&endDate=2024-01-31
Authorization: Bearer {token}

Response 200:
{
  "totalSent": 1250,
  "smsDelivered": 1180,
  "pushDelivered": 1200,
  "failureRate": 0.05,
  "byType": {
    "appointment_created": 400,
    "appointment_cancelled": 50,
    "appointment_reminder": 600,
    "appointment_completed": 200
  }
}
```

---

## 🔄 Automatic Notification Triggers

### When Backend Should Send Notifications:

| **Event** | **SMS to Client** | **Push to Staff** | **Scheduled Tasks** |
|-----------|-------------------|-------------------|-------------------|
| **Appointment Created** | ✅ Confirmation (if enabled) | ✅ New appointment alert | ⏰ Schedule day-before reminder |
| **Appointment Cancelled** | ✅ Cancellation notice | ✅ Cancellation alert | ❌ Cancel scheduled reminders |
| **Appointment Completed** | ✅ Thank you message | ✅ Completion alert | ⏰ Schedule follow-up (2h later) |
| **Appointment Rescheduled** | ✅ New time/date | ✅ Reschedule alert | ⏰ Update reminder schedule |
| **Day Before Reminder** | ✅ Reminder SMS (if enabled) | ✅ Staff reminder | - |
| **Follow-up Message** | ✅ Feedback request (if enabled) | - | - |

---

## 🇷🇴 Romanian Message Examples

### SMS Templates the Backend Should Use:

```java
// Appointment confirmation
"Bună ziua! Programarea pentru {petName} a fost confirmată pe {date} la ora {time}. Vă așteptăm la Partykids Grooming!"

// Day-before reminder  
"Vă reamintim că mâine, {date} la ora {time}, aveți programare pentru {petName} la Partykids Grooming."

// Appointment completion
"Mulțumim că ați ales Partykids Grooming pentru {petName}! Sperăm că sunteți mulțumiți de serviciile noastre."

// Cancellation notice
"Programarea pentru {petName} pe {date} la ora {time} a fost anulată. Motiv: {reason}. Ne cerem scuze pentru inconveniență."

// Follow-up message
"Mulțumim că ați ales Partykids Grooming pentru {petName}! Cum a fost experiența? Vă așteptăm din nou!"
```

---

## 🛡️ Error Handling

### Frontend Error Handling:
```dart
// Notifications are non-critical - never fail appointment operations
final response = await AppointmentService.createAppointment(appointment);

if (response.success) {
  // Appointment created successfully
  // Notifications are handled automatically by backend
  // Even if notifications fail, appointment creation succeeds
} else {
  // Handle appointment creation failure
  // (Not related to notifications)
}
```

### Backend Error Handling:
```java
@PostMapping("/api/appointments")
public ResponseEntity<Appointment> createAppointment(@RequestBody CreateAppointmentRequest request) {
    try {
        // 1. Create appointment (critical operation)
        Appointment appointment = appointmentService.create(request);
        
        // 2. Trigger notifications (non-critical - async)
        try {
            eventPublisher.publishEvent(new AppointmentCreatedEvent(appointment));
        } catch (Exception e) {
            // Log notification error but don't fail the appointment creation
            log.warn("Notification failed for appointment {}: {}", appointment.getId(), e.getMessage());
        }
        
        return ResponseEntity.ok(appointment);
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }
}
```

---

## 🔧 Testing

### Frontend Testing:
```dart
// Test normal appointment operations
// Notifications are handled by backend, so no additional frontend testing needed

testWidgets('should create appointment successfully', (tester) async {
  // Mock the API response
  when(mockAppointmentService.createAppointment(any))
      .thenAnswer((_) async => ApiResponse.success(mockAppointment));
  
  // Test appointment creation
  // Backend handles notifications automatically
});
```

### Backend Testing:
```java
@Test
public void shouldSendNotificationsWhenAppointmentCreated() {
    // Given
    Appointment appointment = createTestAppointment();
    
    // When
    appointmentController.createAppointment("salon-123", createRequest);
    
    // Then
    verify(eventPublisher).publishEvent(any(AppointmentCreatedEvent.class));
    verify(smsService).sendConfirmationSms(appointment);
    verify(pushNotificationService).notifyStaff(appointment);
}
```

---

## 📋 Quick Reference

### ✅ **What Frontend Does:**
- Makes normal API calls to AppointmentService
- Displays incoming push notifications
- Manages SMS settings (Chief Groomer only)
- Registers FCM tokens automatically

### ✅ **What Backend Does:**
- Detects appointment events automatically
- Sends SMS messages via provider (Twilio, etc.)
- Sends push notifications via Firebase FCM
- Schedules and processes reminder notifications
- Tracks notification delivery and statistics
- Handles all notification logic and timing

### 🎯 **Key Benefits:**
- **Simple Frontend** - No complex notification logic
- **Reliable Backend** - Server-side processing is dependable
- **Automatic Operation** - No manual notification triggering needed
- **Graceful Degradation** - Appointment operations never fail due to notifications
- **Romanian Language** - All client messages in Romanian
- **Role-Based Access** - SMS settings restricted to Chief Groomers

This system provides robust, automated notifications while keeping the frontend simple and maintainable! 🚀
