import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:partykidsapp/widgets/calendar_views/time_slot.dart';

void main() {
  group('Calendar Hour Label Alignment Tests', () {
    testWidgets('should position hour labels at top of time slots', (WidgetTester tester) async {
      // Arrange
      final testTime = DateTime(2024, 6, 20, 10, 0); // 10:00 AM

      // Build the TimeLabel widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TimeLabel(
              time: testTime,
              isCurrentHour: false,
            ),
          ),
        ),
      );

      // Find the TimeLabel container
      final containerFinder = find.byType(Container);
      expect(containerFinder, findsOneWidget);

      // Find the Align widget that should position content at top
      final alignFinder = find.byType(Align);
      expect(alignFinder, findsOneWidget);

      // Verify the alignment is set to topCenter
      final alignWidget = tester.widget<Align>(alignFinder);
      expect(alignWidget.alignment, equals(Alignment.topCenter));

      // Find the hour text
      expect(find.text('10:00'), findsOneWidget);
      expect(find.text('AM'), findsOneWidget);
    });

    testWidgets('should highlight current hour correctly with top alignment', (WidgetTester tester) async {
      // Arrange
      final currentTime = DateTime.now();
      final testTime = DateTime(currentTime.year, currentTime.month, currentTime.day, currentTime.hour, 0);

      // Build the TimeLabel widget for current hour
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TimeLabel(
              time: testTime,
              isCurrentHour: true,
            ),
          ),
        ),
      );

      // Find the TimeLabel container
      final containerFinder = find.byType(Container);
      expect(containerFinder, findsOneWidget);

      // Verify the container has background color for current hour
      final containerWidget = tester.widget<Container>(containerFinder);
      expect(containerWidget.decoration, isA<BoxDecoration>());
      
      final decoration = containerWidget.decoration as BoxDecoration;
      expect(decoration.color, isNotNull); // Should have background color for current hour

      // Verify alignment is still topCenter even for current hour
      final alignFinder = find.byType(Align);
      expect(alignFinder, findsOneWidget);
      
      final alignWidget = tester.widget<Align>(alignFinder);
      expect(alignWidget.alignment, equals(Alignment.topCenter));
    });

    testWidgets('should display 12-hour format correctly with top alignment', (WidgetTester tester) async {
      // Test various hours to ensure 12-hour format works with new alignment
      final testCases = [
        {'hour': 0, 'expectedHour': '12', 'expectedPeriod': 'AM'}, // Midnight
        {'hour': 1, 'expectedHour': '1', 'expectedPeriod': 'AM'},
        {'hour': 12, 'expectedHour': '12', 'expectedPeriod': 'PM'}, // Noon
        {'hour': 13, 'expectedHour': '1', 'expectedPeriod': 'PM'},
        {'hour': 23, 'expectedHour': '11', 'expectedPeriod': 'PM'},
      ];

      for (final testCase in testCases) {
        final testTime = DateTime(2024, 6, 20, testCase['hour'] as int, 0);

        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: TimeLabel(
                time: testTime,
                isCurrentHour: false,
              ),
            ),
          ),
        );

        // Verify the hour and period are displayed correctly
        expect(find.text('${testCase['expectedHour']}:00'), findsOneWidget);
        expect(find.text(testCase['expectedPeriod'] as String), findsOneWidget);

        // Verify alignment is correct
        final alignFinder = find.byType(Align);
        expect(alignFinder, findsOneWidget);
        
        final alignWidget = tester.widget<Align>(alignFinder);
        expect(alignWidget.alignment, equals(Alignment.topCenter));
      }
    });

    testWidgets('should have proper padding from top edge', (WidgetTester tester) async {
      // Arrange
      final testTime = DateTime(2024, 6, 20, 14, 0); // 2:00 PM

      // Build the TimeLabel widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TimeLabel(
              time: testTime,
              isCurrentHour: false,
            ),
          ),
        ),
      );

      // Find the specific Padding widget with top padding
      final paddingWidgets = tester.widgetList<Padding>(find.byType(Padding));
      final topPaddingWidget = paddingWidgets.firstWhere(
        (widget) => widget.padding == const EdgeInsets.only(top: 4.0),
      );

      // Verify the padding is set correctly
      expect(topPaddingWidget.padding, equals(const EdgeInsets.only(top: 4.0)));

      // Find the Column widget
      final columnFinder = find.byType(Column);
      expect(columnFinder, findsOneWidget);

      // Verify the Column uses minimum size
      final columnWidget = tester.widget<Column>(columnFinder);
      expect(columnWidget.mainAxisSize, equals(MainAxisSize.min));
    });

    testWidgets('should maintain proper visual hierarchy with borders', (WidgetTester tester) async {
      // Arrange
      final testTime = DateTime(2024, 6, 20, 9, 0); // 9:00 AM

      // Build the TimeLabel widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TimeLabel(
              time: testTime,
              isCurrentHour: false,
            ),
          ),
        ),
      );

      // Find the TimeLabel container
      final containerFinder = find.byType(Container);
      expect(containerFinder, findsOneWidget);

      // Verify the container has proper dimensions
      final containerWidget = tester.widget<Container>(containerFinder);
      final constraints = containerWidget.constraints;
      // Note: Container dimensions are set via constraints, not direct properties

      // Verify the border decoration
      expect(containerWidget.decoration, isA<BoxDecoration>());
      final decoration = containerWidget.decoration as BoxDecoration;
      expect(decoration.border, isA<Border>());
      
      final border = decoration.border as Border;
      expect(border.right.width, equals(1)); // Right border for separation
      expect(border.top.width, equals(0.5)); // Top border for grid lines
    });

    testWidgets('should work correctly in a list of time labels', (WidgetTester tester) async {
      // Simulate a column of time labels like in the actual calendar
      final timeLabels = List.generate(9, (index) {
        final hour = 9 + index; // 9 AM to 5 PM
        final time = DateTime(2024, 6, 20, hour, 0);
        return TimeLabel(
          time: time,
          isCurrentHour: hour == 12, // Make noon the current hour
        );
      });

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: timeLabels,
            ),
          ),
        ),
      );

      // Verify all time labels are rendered
      expect(find.byType(TimeLabel), findsNWidgets(9));

      // Verify specific hours are displayed
      expect(find.text('9:00'), findsOneWidget);
      expect(find.text('12:00'), findsOneWidget);
      expect(find.text('5:00'), findsOneWidget);

      // Verify all use top alignment
      final alignFinders = find.byType(Align);
      expect(alignFinders, findsNWidgets(9));

      // Check that all alignments are topCenter
      for (int i = 0; i < 9; i++) {
        final alignWidget = tester.widget<Align>(alignFinders.at(i));
        expect(alignWidget.alignment, equals(Alignment.topCenter));
      }
    });

    testWidgets('should maintain consistent spacing between hour and period', (WidgetTester tester) async {
      // Arrange
      final testTime = DateTime(2024, 6, 20, 15, 0); // 3:00 PM

      // Build the TimeLabel widget
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TimeLabel(
              time: testTime,
              isCurrentHour: false,
            ),
          ),
        ),
      );

      // Find both text widgets
      final hourText = find.text('3:00');
      final periodText = find.text('PM');
      
      expect(hourText, findsOneWidget);
      expect(periodText, findsOneWidget);

      // Get the positions of both text widgets
      final hourRect = tester.getRect(hourText);
      final periodRect = tester.getRect(periodText);

      // Verify that the period text is below or adjacent to the hour text
      expect(periodRect.top, greaterThanOrEqualTo(hourRect.bottom));

      // Verify both are horizontally centered
      expect(hourRect.center.dx, closeTo(periodRect.center.dx, 1.0));
    });
  });
}
