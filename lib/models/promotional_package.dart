import 'service.dart';

class PromotionalPackage {
  final String id;
  final String name;
  final String description;
  final List<String> serviceIds; // List of service IDs included in the package
  final List<Service>? services; // Populated services (for display purposes)
  final double price;
  final double? originalPrice; // Optional: to show discount
  final bool isActive;
  final int displayOrder;
  final DateTime createdAt;
  final DateTime? updatedAt;

  PromotionalPackage({
    required this.id,
    required this.name,
    required this.description,
    required this.serviceIds,
    this.services,
    required this.price,
    this.originalPrice,
    this.isActive = true,
    this.displayOrder = 0,
    required this.createdAt,
    this.updatedAt,
  });

  // Computed properties for formatted display
  String get formattedPrice {
    return '${price.toStringAsFixed(2)} RON';
  }

  String get formattedOriginalPrice {
    if (originalPrice != null) {
      return '${originalPrice!.toStringAsFixed(2)} RON';
    }
    return '';
  }

  /// Calculate discount percentage if original price is available
  double? get discountPercentage {
    if (originalPrice != null && originalPrice! > price) {
      return ((originalPrice! - price) / originalPrice!) * 100;
    }
    return null;
  }

  /// Get formatted discount percentage
  String get formattedDiscount {
    final discount = discountPercentage;
    if (discount != null) {
      return '-${discount.toStringAsFixed(0)}%';
    }
    return '';
  }

  /// Check if package has discount
  bool get hasDiscount {
    return originalPrice != null && originalPrice! > price;
  }

  /// Get total duration of all services in the package
  int get totalDuration {
    if (services != null) {
      return services!.fold(0, (sum, service) => sum + service.duration);
    }
    return 0;
  }

  /// Get formatted total duration
  String get formattedDuration {
    final total = totalDuration;
    if (total == 0) return 'Durată necunoscută';
    
    final hours = total ~/ 60;
    final minutes = total % 60;
    
    if (hours > 0 && minutes > 0) {
      return '${hours}h ${minutes}min';
    } else if (hours > 0) {
      return '${hours}h';
    } else {
      return '${minutes}min';
    }
  }

  /// Get number of services in package
  int get serviceCount {
    return serviceIds.length;
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'serviceIds': serviceIds,
      'price': price,
      if (originalPrice != null) 'originalPrice': originalPrice,
      'isActive': isActive,
      'displayOrder': displayOrder,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory PromotionalPackage.fromJson(Map<String, dynamic> json) {
    try {
      return PromotionalPackage(
        id: json['id']?.toString() ?? '',
        name: json['name'] ?? '',
        description: json['description'] ?? '',
        serviceIds: List<String>.from(json['serviceIds'] ?? []),
        services: json['services'] != null 
            ? List<Service>.from((json['services'] as List).map((x) => Service.fromJson(x)))
            : null,
        price: (json['price'] as num?)?.toDouble() ?? 0.0,
        originalPrice: (json['originalPrice'] as num?)?.toDouble(),
        isActive: json['isActive'] ?? true,
        displayOrder: json['displayOrder'] ?? 0,
        createdAt: DateTime.tryParse(json['createdAt'] ?? '') ?? DateTime.now(),
        updatedAt: json['updatedAt'] != null 
            ? DateTime.tryParse(json['updatedAt']) 
            : null,
      );
    } catch (e) {
      throw Exception('Failed to parse PromotionalPackage from JSON: $e');
    }
  }

  PromotionalPackage copyWith({
    String? id,
    String? name,
    String? description,
    List<String>? serviceIds,
    List<Service>? services,
    double? price,
    double? originalPrice,
    bool? isActive,
    int? displayOrder,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PromotionalPackage(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      serviceIds: serviceIds ?? this.serviceIds,
      services: services ?? this.services,
      price: price ?? this.price,
      originalPrice: originalPrice ?? this.originalPrice,
      isActive: isActive ?? this.isActive,
      displayOrder: displayOrder ?? this.displayOrder,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PromotionalPackage && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PromotionalPackage(id: $id, name: $name, price: $price, serviceCount: $serviceCount, isActive: $isActive)';
  }
}
