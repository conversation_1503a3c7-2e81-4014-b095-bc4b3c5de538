import 'package:flutter/cupertino.dart';

import '../../models/api_response.dart';
import '../../models/pet.dart';
import '../api_service.dart';
import '../auth/auth_service.dart';

class PetService {
  // Get all pets
  static Future<ApiResponse<List<Pet>>> getPets({
    String? ownerId,
    String? species,
    bool? isActive,
  }) async {
    final queryParams = <String, String>{};

    if (ownerId != null) queryParams['ownerId'] = ownerId;
    if (species != null) queryParams['species'] = species;
    if (isActive != null) queryParams['isActive'] = isActive.toString();

    final response = await ApiService.get<List<Pet>>(
      '/api/pets',
      queryParams: queryParams.isNotEmpty ? queryParams : null,
      fromJson: (data) => (data as List).map((item) => Pet.fromJson(item)).toList(),
    );

    return response;
  }

  // Get pet by ID
  static Future<ApiResponse<Pet>> getPet(String id) async {
    final response = await ApiService.get<Pet>(
      '/api/pets/$id',
      fromJson: (data) => Pet.fromJson(data),
    );

    return response;
  }

  // Create new pet
  static Future<ApiResponse<Pet>> createPet(Pet pet) async {
    final response = await ApiService.post<Pet>(
      '/api/pets',
      body: pet.toJson(),
      fromJson: (data) => Pet.fromJson(data),
    );

    return response;
  }

  // Update existing pet
  static Future<ApiResponse<Pet>> updatePet(String id, Pet pet) async {
    final response = await ApiService.put<Pet>(
      '/api/pets/$id',
      body: pet.toJson(),
      fromJson: (data) => Pet.fromJson(data),
    );

    return response;
  }

  // Delete pet
  static Future<ApiResponse<void>> deletePet(String id) async {
    final response = await ApiService.delete<void>('/api/pets/$id');
    return response;
  }

  // Get pets for a specific client
  static Future<ApiResponse<List<Pet>>> getPetsForClient(String clientId) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        debugPrint('❌ PetService: No salon ID available for getPetsForClient');
        return ApiResponse<List<Pet>>.error('No salon selected');
      }

      debugPrint('🔄 PetService: Getting pets for client $clientId in salon $salonId');
      debugPrint('📡 PetService: API endpoint: /api/salons/$salonId/clients/$clientId/pets');

      final response = await ApiService.get<List<Pet>>(
        '/api/salons/$salonId/clients/$clientId/pets',
        fromJson: (data) => (data as List).map((item) => Pet.fromJson(item)).toList(),
      );

      if (response.success && response.data != null) {
        debugPrint('✅ PetService: Successfully loaded ${response.data!.length} pets for client $clientId');
        for (final pet in response.data!) {
          debugPrint('   - Pet: ${pet.name} (${pet.breed}) - ID: ${pet.id} - Owner: ${pet.ownerId}');
        }
      } else {
        debugPrint('❌ PetService: Failed to load pets for client $clientId: ${response.error}');
      }

      return response;
    } catch (e) {
      debugPrint('❌ PetService: Exception loading pets for client $clientId: $e');
      return ApiResponse<List<Pet>>.error('Failed to load client pets: $e');
    }
  }

  // Add pet to client
  static Future<ApiResponse<Pet>> addPetToClient(String clientId, Pet pet) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Pet>.error('No salon selected');
      }

      final response = await ApiService.post<Pet>(
        '/api/salons/$salonId/clients/$clientId/pets',
        body: pet.toJson(),
        fromJson: (data) => Pet.fromJson(data),
      );

      return response;
    } catch (e) {
      return ApiResponse<Pet>.error('Failed to add pet to client: $e');
    }
  }

  // Update pet for client
  static Future<ApiResponse<Pet>> updateClientPet(String clientId, String petId, Pet pet) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Pet>.error('No salon selected');
      }

      final response = await ApiService.put<Pet>(
        '/api/salons/$salonId/clients/$clientId/pets/$petId',
        body: pet.toJson(),
        fromJson: (data) => Pet.fromJson(data),
      );

      return response;
    } catch (e) {
      return ApiResponse<Pet>.error('Failed to update client pet: $e');
    }
  }

  // Remove pet from client
  static Future<ApiResponse<void>> removePetFromClient(String clientId, String petId) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<void>.error('No salon selected');
      }

      final response = await ApiService.delete<void>('/api/salons/$salonId/clients/$clientId/pets/$petId');
      return response;
    } catch (e) {
      return ApiResponse<void>.error('Failed to remove pet from client: $e');
    }
  }

  // Get pet medical history
  static Future<ApiResponse<List<Map<String, dynamic>>>> getPetMedicalHistory(String petId) async {
    final response = await ApiService.get<List<Map<String, dynamic>>>(
      '/api/pets/$petId/medical-history',
      fromJson: (data) => List<Map<String, dynamic>>.from(data),
    );

    return response;
  }

  // Add medical record to pet
  static Future<ApiResponse<Map<String, dynamic>>> addMedicalRecord(
    String petId,
    Map<String, dynamic> record,
  ) async {
    final response = await ApiService.post<Map<String, dynamic>>(
      '/api/pets/$petId/medical-history',
      body: record,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Get pet vaccinations
  static Future<ApiResponse<List<String>>> getPetVaccinations(String petId) async {
    final response = await ApiService.get<List<String>>(
      '/api/pets/$petId/vaccinations',
      fromJson: (data) => List<String>.from(data),
    );

    return response;
  }

  // Update pet vaccinations
  static Future<ApiResponse<List<String>>> updatePetVaccinations(
    String petId,
    List<String> vaccinations,
  ) async {
    final response = await ApiService.put<List<String>>(
      '/api/pets/$petId/vaccinations',
      body: {'vaccinations': vaccinations},
      fromJson: (data) => List<String>.from(data),
    );

    return response;
  }

  // Search pets
  static Future<ApiResponse<List<Pet>>> searchPets(String query) async {
    final response = await ApiService.get<List<Pet>>(
      '/api/pets',
      queryParams: {'search': query},
      fromJson: (data) => (data as List).map((item) => Pet.fromJson(item)).toList(),
    );

    return response;
  }

  // Get pets by species
  static Future<ApiResponse<List<Pet>>> getPetsBySpecies(String species) async {
    final response = await ApiService.get<List<Pet>>(
      '/api/pets',
      queryParams: {'species': species},
      fromJson: (data) => (data as List).map((item) => Pet.fromJson(item)).toList(),
    );

    return response;
  }

  // Get pets by breed
  static Future<ApiResponse<List<Pet>>> getPetsByBreed(String breed) async {
    final response = await ApiService.get<List<Pet>>(
      '/api/pets',
      queryParams: {'breed': breed},
      fromJson: (data) => (data as List).map((item) => Pet.fromJson(item)).toList(),
    );

    return response;
  }

  // Get available species
  static Future<ApiResponse<List<String>>> getAvailableSpecies() async {
    final response = await ApiService.get<List<String>>(
      '/api/pets/species',
      fromJson: (data) => List<String>.from(data),
    );

    return response;
  }

  // Get available breeds for species
  static Future<ApiResponse<List<String>>> getAvailableBreeds(String species) async {
    final response = await ApiService.get<List<String>>(
      '/api/pets/breeds',
      queryParams: {'species': species},
      fromJson: (data) => List<String>.from(data),
    );

    return response;
  }

  // Validate pet data before sending to server
  static String? validatePet(Pet pet) {
    if (pet.name.trim().isEmpty) {
      return 'Numele animalului este obligatoriu';
    }

    if (pet.species.trim().isEmpty) {
      return 'Specia animalului este obligatorie';
    }

    if (pet.weight <= 0) {
      return 'Greutatea trebuie să fie mai mare decât 0';
    }

    if (pet.ownerId.trim().isEmpty) {
      return 'ID-ul proprietarului este obligatoriu';
    }

    return null; // No validation errors
  }

  // Retry failed requests with exponential backoff
  static Future<ApiResponse<T>> retryPetRequest<T>(
    Future<ApiResponse<T>> Function() request,
  ) async {
    return ApiService.retryRequest(request, maxRetries: 3);
  }

  // Bulk operations
  static Future<ApiResponse<List<Pet>>> createMultiplePets(List<Pet> pets) async {
    final response = await ApiService.post<List<Pet>>(
      '/api/pets/bulk',
      body: {'pets': pets.map((p) => p.toJson()).toList()},
      fromJson: (data) => (data as List).map((item) => Pet.fromJson(item)).toList(),
    );

    return response;
  }

  // Export pets data
  static Future<ApiResponse<String>> exportPets({
    String format = 'csv',
    List<String>? petIds,
    String? ownerId,
  }) async {
    final body = <String, dynamic>{
      'format': format,
      if (petIds != null) 'petIds': petIds,
      if (ownerId != null) 'ownerId': ownerId,
    };

    final response = await ApiService.post<String>(
      '/api/pets/export',
      body: body,
      fromJson: (data) => data.toString(),
    );

    return response;
  }

  // Get pet statistics
  static Future<ApiResponse<Map<String, dynamic>>> getPetStats() async {
    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/pets/stats',
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }
}
