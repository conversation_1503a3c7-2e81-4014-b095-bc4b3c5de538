import 'package:flutter_test/flutter_test.dart';
import 'package:partykidsapp/utils/formatters/phone_number_utils.dart';

void main() {
  group('Simple Phone Normalization Tests', () {
    test('should handle both 9 and 10 character inputs by trimming leading zero', () {
      print('📱 Simple Phone Normalization Fix:');
      print('  • Handle 9 characters: 731446895 → +40731446895');
      print('  • Handle 10 characters: 0731446895 → +40731446895 (trim leading 0)');
      
      // Test 9 character input (without leading 0)
      const input9chars = '731446895';
      final result9 = PhoneNumberUtils.formatToRomanianStandard(input9chars);
      expect(result9, equals('+40731446895'));
      print('  ✅ 9 chars: "$input9chars" → "$result9"');
      
      // Test 10 character input (with leading 0)
      const input10chars = '0731446895';
      final result10 = PhoneNumberUtils.formatToRomanianStandard(input10chars);
      expect(result10, equals('+40731446895'));
      print('  ✅ 10 chars: "$input10chars" → "$result10"');
      
      // Both should result in the same normalized format
      expect(result9, equals(result10));
      print('  ✅ Both inputs normalize to same result: $result9');
    });

    test('should handle various Romanian mobile prefixes', () {
      print('📋 Romanian Mobile Prefixes Test:');
      
      const testCases = [
        // 9 character inputs
        '721123456',  // Vodafone
        '731123456',  // Orange
        '741123456',  // Telekom
        '751123456',  // Digi
        
        // 10 character inputs (with leading 0)
        '0721123456', // Vodafone
        '0731123456', // Orange  
        '0741123456', // Telekom
        '0751123456', // Digi
      ];
      
      for (final testCase in testCases) {
        final result = PhoneNumberUtils.formatToRomanianStandard(testCase);
        final expectedLength = '+40'.length + 9; // +40 + 9 digits
        
        expect(result.length, equals(expectedLength));
        expect(result, startsWith('+40'));
        
        print('  ✅ "$testCase" → "$result"');
      }
    });

    test('should maintain existing functionality for other formats', () {
      print('🔄 Existing Functionality Test:');
      
      const existingFormats = {
        '+40731446895': '+40731446895',     // Already correct
        '40731446895': '+40731446895',      // Without + prefix
        '+40 731 446 895': '+40731446895',  // With spaces
        '0040731446895': '+40731446895',    // Double country code
      };
      
      existingFormats.forEach((input, expected) {
        final result = PhoneNumberUtils.formatToRomanianStandard(input);
        expect(result, equals(expected));
        print('  ✅ "$input" → "$result"');
      });
    });
  });
}
