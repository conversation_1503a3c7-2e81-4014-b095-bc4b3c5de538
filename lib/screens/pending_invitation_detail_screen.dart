import 'package:partykidsapp/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';
import '../services/staff_service.dart';
import '../config/theme/app_theme.dart';

class PendingInvitationDetailScreen extends StatefulWidget {
  final PendingStaffInvitation invitation;

  const PendingInvitationDetailScreen({
    super.key,
    required this.invitation,
  });

  @override
  State<PendingInvitationDetailScreen> createState() => _PendingInvitationDetailScreenState();
}

class _PendingInvitationDetailScreenState extends State<PendingInvitationDetailScreen> {
  late PendingStaffInvitation _currentInvitation;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _currentInvitation = widget.invitation;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: 200,
            floating: false,
            pinned: true,
            backgroundColor: AppColors.lightWarning,
            iconTheme: const IconThemeData(color: Colors.white),
            flexibleSpace: FlexibleSpaceBar(
              title: Text(
                _currentInvitation.formattedPhoneNumber,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              background: Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      AppColors.lightWarning,
                      Color.fromRGBO(255, 152, 0, 0.8), // AppColors.lightWarning with 0.8 opacity
                    ],
                  ),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(height: 40), // Account for status bar
                      Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 3),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.schedule,
                            color: Colors.white,
                            size: 32,
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          _currentInvitation.status,
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            actions: [
              PopupMenuButton<String>(
                onSelected: (value) {
                  if (value == 'resend') {
                    _resendInvitation();
                  } else if (value == 'cancel') {
                    _showCancelConfirmation();
                  }
                },
                itemBuilder: (context) => [
                  if (_currentInvitation.isValid) ...[
                    const PopupMenuItem(
                      value: 'resend',
                      child: Row(
                        children: [
                          Icon(Icons.send, color: Colors.blue),
                          SizedBox(width: 8),
                          Text('Retrimite invitația'),
                        ],
                      ),
                    ),
                  ],
                  const PopupMenuItem(
                    value: 'cancel',
                    child: Row(
                      children: [
                        Icon(Icons.cancel, color: Colors.red),
                        SizedBox(width: 8),
                        Text('Anulează invitația'),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildInvitationInfoSection(),
                  const SizedBox(height: 24),
                  _buildRoleSection(),
                  const SizedBox(height: 24),
                  _buildPermissionsSection(),
                  const SizedBox(height: 24),
                  _buildStatusSection(),
                  const SizedBox(height: 24),
                  _buildTimingSection(),
                  const SizedBox(height: 24),
                  _buildActionsSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInvitationInfoSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Informații invitație',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(Icons.phone, 'Număr telefon', _currentInvitation.formattedPhoneNumber),
            const SizedBox(height: 12),
            _buildInfoRow(Icons.person, 'Invitat de', _currentInvitation.invitedBy),
            const SizedBox(height: 12),
            _buildInfoRow(
              _currentInvitation.isValid ? Icons.check_circle : Icons.error_outline,
              'Status validitate',
              _currentInvitation.isValid ? 'Valid' : 'Expirat',
              valueColor: _currentInvitation.isValid ? Colors.green : Colors.red,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Rol și responsabilități',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              Icons.work,
              'Rol în echipă',
              _currentInvitation.groomerRole.displayName,
            ),
            const SizedBox(height: 8),
            Text(
              _currentInvitation.groomerRole.description,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Permisiuni acces date',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              Icons.security,
              'Nivel acces',
              _currentInvitation.clientDataPermission.displayName,
            ),
            const SizedBox(height: 8),
            Text(
              _currentInvitation.clientDataPermission.description,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Status invitație',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _currentInvitation.isValid
                    ? Colors.orange.withValues(alpha: 0.1)
                    : Colors.red.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _currentInvitation.isValid
                      ? Colors.orange.withValues(alpha: 0.3)
                      : Colors.red.withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _currentInvitation.isValid ? Icons.schedule : Icons.error_outline,
                        color: _currentInvitation.isValid ? Colors.orange : Colors.red,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _currentInvitation.status,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: _currentInvitation.isValid ? Colors.orange : Colors.red,
                        ),
                      ),
                    ],
                  ),
                  if (_currentInvitation.message != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      _currentInvitation.message!,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimingSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Cronologie invitație',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              Icons.calendar_today,
              'Data invitației',
              _formatDate(_currentInvitation.invitedAt),
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.schedule,
              'Data expirării',
              _formatDate(_currentInvitation.expiresAt),
              valueColor: _currentInvitation.isValid ? Colors.orange : Colors.red,
            ),
            const SizedBox(height: 12),
            _buildInfoRow(
              Icons.timer,
              'Timp rămas',
              _getTimeRemaining(),
              valueColor: _currentInvitation.isValid ? Colors.green : Colors.red,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionsSection() {
    if (!_currentInvitation.isValid) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Acțiuni disponibile',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.orange,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _resendInvitation,
                    icon: const Icon(Icons.send),
                    label: const Text('Retrimite'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _showCancelConfirmation,
                    icon: const Icon(Icons.cancel),
                    label: const Text('Anulează'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.red,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value, {Color? valueColor}) {
    return Row(
      children: [
        Icon(icon, size: 20, color: Colors.orange),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: valueColor ?? Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    final months = [
      'ianuarie', 'februarie', 'martie', 'aprilie', 'mai', 'iunie',
      'iulie', 'august', 'septembrie', 'octombrie', 'noiembrie', 'decembrie'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  String _getTimeRemaining() {
    if (!_currentInvitation.isValid) {
      return 'Expirat';
    }

    final now = DateTime.now();
    final difference = _currentInvitation.expiresAt.difference(now);

    if (difference.isNegative) {
      return 'Expirat';
    }

    final days = difference.inDays;
    final hours = difference.inHours % 24;

    if (days > 0) {
      return '$days zile, $hours ore';
    } else if (hours > 0) {
      return '$hours ore';
    } else {
      final minutes = difference.inMinutes;
      return '$minutes minute';
    }
  }

  Future<void> _resendInvitation() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final response = await StaffService.resendInvitationInCurrentSalon(_currentInvitation.invitationId);

      if (response.success) {
        showTopSnackBar(context, 
          SnackBar(
            content: Text('Invitația pentru ${_currentInvitation.phoneNumber} a fost retrimisă cu succes'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        showTopSnackBar(context, 
          SnackBar(
            content: Text(response.error ?? 'Nu s-a putut retrimite invitația'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      showTopSnackBar(context, 
        SnackBar(
          content: Text('Eroare: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showCancelConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Anulează Invitația'),
        content: Text(
          'Ești sigur că vrei să anulezi invitația pentru ${_currentInvitation.phoneNumber}?\n\n'
          'Această acțiune nu poate fi anulată.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Nu'),
          ),
          ElevatedButton(
            onPressed: () => _cancelInvitation(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('Da, anulează'),
          ),
        ],
      ),
    );
  }

  Future<void> _cancelInvitation() async {
    Navigator.pop(context); // Close confirmation dialog

    setState(() {
      _isLoading = true;
    });

    try {
      final response = await StaffService.cancelInvitationInCurrentSalon(_currentInvitation.invitationId);

      if (response.success) {
        showTopSnackBar(context, 
          SnackBar(
            content: Text('Invitația pentru ${_currentInvitation.phoneNumber} a fost anulată cu succes'),
            backgroundColor: Colors.green,
          ),
        );
        // Navigate back to team management screen
        Navigator.pop(context);
      } else {
        showTopSnackBar(context, 
          SnackBar(
            content: Text(response.error ?? 'Nu s-a putut anula invitația'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      showTopSnackBar(context, 
        SnackBar(
          content: Text('Eroare: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
