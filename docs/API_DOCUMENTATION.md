# Partykids Project - Backend API Documentation

## Overview

This document provides a comprehensive reference for all backend API endpoints that the Partykids Project Flutter frontend application consumes. The API follows RESTful principles and uses JSON for data exchange.

**Base URL**: `https://api.partykids-programari.ro` (Production) | `http://*************:8080` (Development)

**API Version**: v1

**Authentication**: Bearer token (JWT) required for most endpoints

## Table of Contents

1. [Authentication & User Management](#authentication--user-management)
2. [Salon Operations](#salon-operations)
3. [Client & Pet Management](#client--pet-management)
4. [Appointment Scheduling](#appointment-scheduling)
5. [Service Management](#service-management)
6. [Staff Management](#staff-management)
7. [Staff Working Hours](#staff-working-hours)
8. [Settings & Configuration](#settings--configuration)
9. [Reports & Analytics](#reports--analytics)
10. [Notifications & SMS](#notifications--sms)
11. [Automated Notification System](#automated-notification-system-new)
12. [Feature Toggle System](#feature-toggle-system-new)
13. [SMS Verification System](#sms-verification-system-new)
14. [Common Response Formats](#common-response-formats)

---

## Authentication & User Management

### Firebase Authentication

#### POST `/auth/firebase-login`
**Purpose**: Exchange Firebase ID token for backend JWT token
**Authentication**: None required
**Request Body**:
```json
{
  "firebaseToken": "string",
  "platform": "ios",
  "appVersion": "1.0.0"
}
```
**Response**:
```json
{
  "success": true,
  "data": {
    "accessToken": "string",
    "userId": "string",
    "userPhone": "string",
    "userRole": "GROOMER",
    "salonId": "string"
  }
}
```
**Error Codes**: 400 (Invalid token), 401 (Authentication failed)

#### POST `/auth/refresh-token`
**Purpose**: Refresh expired access token
**Authentication**: Bearer token (can be expired)
**Request Body**:
```json
{
  "refreshToken": "string"
}
```
**Response**: Same as firebase-login

#### POST `/auth/logout`
**Purpose**: Invalidate current session
**Authentication**: Bearer token required
**Request Body**: Empty
**Response**: 200 OK

### User Profile Management

#### GET `/auth/profile`
**Purpose**: Get current user profile information
**Authentication**: Bearer token required
**Response**:
```json
{
  "success": true,
  "data": {
    "id": "string",
    "name": "string",
    "email": "string",
    "phone": "string",
    "role": "GROOMER",
    "salonId": "string",
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```

#### PATCH `/auth/profile/name`
**Purpose**: Update user's display name
**Authentication**: Bearer token required
**Request Body**:
```json
{
  "name": "string"
}
```
**Response**: Updated user profile object

#### PATCH `/auth/profile/phone`
**Purpose**: Update user's phone number with SMS verification
**Authentication**: Bearer token required
**Request Body**:
```json
{
  "phoneNumber": "+40XXXXXXXXX",
  "confirmationCode": "123456"
}
```
**Response**: Updated user profile object

#### POST `/auth/phone/resend-code`
**Purpose**: Resend SMS confirmation code for phone update
**Authentication**: Bearer token required
**Request Body**:
```json
{
  "phoneNumber": "+40XXXXXXXXX"
}
```
**Response**: 200 OK

---

## Salon Operations

### Salon Management

#### POST `/salons`
**Purpose**: Create a new salon
**Authentication**: Bearer token required
**Request Body**:
```json
{
  "name": "string",
  "address": "string",
  "city": "string",
  "phone": "+40XXXXXXXXX",
  "email": "string"
}
```
**Response**:
```json
{
  "success": true,
  "data": {
    "salonId": "string",
    "name": "string",
    "address": "string",
    "city": "string",
    "phone": "string",
    "email": "string",
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```

#### GET `/user/{userId}/salons`
**Purpose**: Get all salons associated with a user
**Authentication**: Bearer token required
**Response**:
```json
{
  "success": true,
  "data": {
    "activeSalon": {
      "salonId": "string",
      "name": "string",
      "role": "CHIEF_GROOMER"
    },
    "allSalons": [
      {
        "salonId": "string",
        "name": "string",
        "role": "REGULAR_GROOMER",
        "isActive": true
      }
    ],
    "pendingInvitations": [
      {
        "invitationId": "string",
        "salonName": "string",
        "role": "REGULAR_GROOMER",
        "expiresAt": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

#### POST `/user/{userId}/switch-salon`
**Purpose**: Switch user's active salon
**Authentication**: Bearer token required
**Request Body**:
```json
{
  "salonId": "string"
}
```
**Response**: 200 OK

### Salon Settings

#### GET `/salons/{salonId}/settings`
**Purpose**: Get salon configuration settings
**Authentication**: Bearer token required
**Response**:
```json
{
  "success": true,
  "data": {
    "id": "string",
    "name": "string",
    "address": "string",
    "phone": "string",
    "email": "string",
    "website": "string",
    "businessHours": {
      "monday": {
        "isWorkingDay": true,
        "openTime": "09:00",
        "closeTime": "18:00",
        "breakStart": "13:00",
        "breakEnd": "14:00"
      }
    },
    "holidays": ["2024-01-01", "2024-12-25"],
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

#### PUT `/salons/{salonId}/settings`
**Purpose**: Update salon settings
**Authentication**: Bearer token required (CHIEF_GROOMER role)
**Request Body**: Same structure as GET response
**Response**: Updated salon settings object

---

## Client & Pet Management

### Client Operations

#### GET `/salons/{salonId}/clients`
**Purpose**: Get all clients for a salon
**Authentication**: Bearer token required
**Query Parameters**:
- `search` (string, optional): Search term for client name/phone
- `active` (boolean, optional): Filter by active status
- `limit` (integer, optional): Number of results to return
- `offset` (integer, optional): Pagination offset

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "string",
      "name": "string",
      "phone": "+40XXXXXXXXX",
      "email": "string",
      "address": "string",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00Z",
      "lastVisit": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### GET `/salons/{salonId}/clients/{clientId}`
**Purpose**: Get specific client details
**Authentication**: Bearer token required
**Response**: Single client object with additional details

#### POST `/salons/{salonId}/clients`
**Purpose**: Create new client
**Authentication**: Bearer token required
**Request Body**:
```json
{
  "name": "string",
  "phone": "+40XXXXXXXXX",
  "email": "string",
  "address": "string"
}
```
**Response**: Created client object

#### PUT `/salons/{salonId}/clients/{clientId}`
**Purpose**: Update existing client
**Authentication**: Bearer token required
**Request Body**: Same as POST
**Response**: Updated client object

#### DELETE `/salons/{salonId}/clients/{clientId}`
**Purpose**: Soft delete client (mark as inactive)
**Authentication**: Bearer token required
**Response**: 200 OK

### Pet Operations

#### GET `/salons/{salonId}/clients/{clientId}/pets`
**Purpose**: Get all pets for a specific client
**Authentication**: Bearer token required
**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "string",
      "name": "string",
      "species": "DOG",
      "breed": "string",
      "age": 3,
      "weight": 15.5,
      "specialNotes": "string",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### POST `/salons/{salonId}/clients/{clientId}/pets`
**Purpose**: Add new pet for client
**Authentication**: Bearer token required
**Request Body**:
```json
{
  "name": "string",
  "species": "DOG",
  "breed": "string",
  "age": 3,
  "weight": 15.5,
  "specialNotes": "string"
}
```
**Response**: Created pet object

### Client Statistics

#### GET `/salons/{salonId}/clients/{clientId}/stats`
**Purpose**: Get client statistics and history
**Authentication**: Bearer token required
**Response**:
```json
{
  "success": true,
  "data": {
    "totalAppointments": 25,
    "completedAppointments": 23,
    "cancelledAppointments": 2,
    "totalRevenue": 1250.00,
    "averageAppointmentValue": 50.00,
    "lastVisitDate": "2024-01-01T00:00:00Z",
    "loyaltyScore": 85.5
  }
}
```

---

## Appointment Scheduling

### Appointment Operations

#### GET `/salons/{salonId}/appointments`
**Purpose**: Get appointments for salon
**Authentication**: Bearer token required
**Query Parameters**:
- `date` (string, optional): Specific date (YYYY-MM-DD)
- `startDate` (string, optional): Date range start
- `endDate` (string, optional): Date range end
- `status` (string, optional): Filter by status
- `clientId` (string, optional): Filter by client
- `groomerId` (string, optional): Filter by groomer

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "string",
      "clientId": "string",
      "clientName": "string",
      "petId": "string",
      "petName": "string",
      "groomerId": "string",
      "groomerName": "string",
      "appointmentDate": "2024-01-01",
      "startTime": "10:00",
      "endTime": "11:30",
      "status": "SCHEDULED",
      "services": [
        {
          "serviceId": "string",
          "serviceName": "string",
          "price": 50.00,
          "duration": 90
        }
      ],
      "totalPrice": 50.00,
      "totalDuration": 90,
      "notes": "string",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### POST `/salons/{salonId}/appointments`
**Purpose**: Create new appointment
**Authentication**: Bearer token required
**Request Body**:
```json
{
  "clientId": "string",
  "petId": "string",
  "groomerId": "string",
  "appointmentDate": "2024-01-01",
  "startTime": "10:00",
  "endTime": "11:30",
  "serviceIds": ["string1", "string2"],
  "notes": "string",
  "repetitionFrequency": "NONE"
}
```
**Response**: Created appointment object

#### PUT `/salons/{salonId}/appointments/{appointmentId}`
**Purpose**: Update existing appointment
**Authentication**: Bearer token required
**Request Body**: Same as POST
**Response**: Updated appointment object

#### PATCH `/salons/{salonId}/appointments/{appointmentId}/status`
**Purpose**: Update appointment status only
**Authentication**: Bearer token required
**Request Body**:
```json
{
  "status": "COMPLETED",
  "notes": "string"
}
```
**Response**: Updated appointment object

#### DELETE `/salons/{salonId}/appointments/{appointmentId}`
**Purpose**: Cancel appointment
**Authentication**: Bearer token required
**Response**: 200 OK

---

## Service Management

### Service Operations

#### GET `/salons/{salonId}/services`
**Purpose**: Get all services for salon
**Authentication**: Bearer token required
**Query Parameters**:
- `isActive` (boolean, optional): Filter by active status
- `category` (string, optional): Filter by service category
- `search` (string, optional): Search term
- `minPrice` (number, optional): Minimum price filter
- `maxPrice` (number, optional): Maximum price filter

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "string",
      "name": "string",
      "description": "string",
      "category": "GROOMING",
      "price": 50.00,
      "duration": 90,
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### POST `/salons/{salonId}/services`
**Purpose**: Create new service
**Authentication**: Bearer token required
**Request Body**:
```json
{
  "name": "string",
  "description": "string",
  "category": "GROOMING",
  "price": 50.00,
  "duration": 90
}
```
**Response**: Created service object

#### PUT `/salons/{salonId}/services/{serviceId}`
**Purpose**: Update existing service
**Authentication**: Bearer token required
**Request Body**: Same as POST
**Response**: Updated service object

#### POST `/salons/{salonId}/services/{serviceId}/duplicate`
**Purpose**: Create duplicate of existing service
**Authentication**: Bearer token required
**Request Body**:
```json
{
  "name": "string"
}
```
**Response**: Created service object

#### PATCH `/salons/{salonId}/services/{serviceId}/toggle`
**Purpose**: Toggle service active status
**Authentication**: Bearer token required
**Response**: Updated service object

#### DELETE `/salons/{salonId}/services/{serviceId}`
**Purpose**: Delete service
**Authentication**: Bearer token required
**Response**: 200 OK

---

## Staff Management

### Staff Operations

#### GET `/salons/{salonId}/staff`
**Purpose**: Get all staff members for salon
**Authentication**: Bearer token required
**Query Parameters**:
- `activeOnly` (boolean, optional): Filter active staff only
- `search` (string, optional): Search term

**Response**:
```json
{
  "success": true,
  "data": {
    "activeStaff": [
      {
        "id": "string",
        "name": "string",
        "phone": "+40XXXXXXXXX",
        "email": "string",
        "groomerRole": "CHIEF_GROOMER",
        "clientDataPermission": "FULL_ACCESS",
        "isActive": true,
        "joinedAt": "2024-01-01T00:00:00Z",
        "workingHours": {},
        "specialties": ["string"],
        "experience": 5
      }
    ],
    "pendingStaff": [
      {
        "invitationId": "string",
        "phone": "+40XXXXXXXXX",
        "groomerRole": "REGULAR_GROOMER",
        "status": "PENDING",
        "expiresAt": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

#### POST `/salons/{salonId}/staff`
**Purpose**: Add new staff member (send invitation)
**Authentication**: Bearer token required (CHIEF_GROOMER role)
**Request Body**:
```json
{
  "phoneNumber": "+40XXXXXXXXX",
  "groomerRole": "REGULAR_GROOMER",
  "clientDataPermission": "READ_ONLY",
  "notes": "string"
}
```
**Response**: Created staff invitation object

#### PUT `/salons/{salonId}/staff/{staffId}`
**Purpose**: Update staff member details
**Authentication**: Bearer token required (CHIEF_GROOMER role)
**Request Body**:
```json
{
  "groomerRole": "REGULAR_GROOMER",
  "clientDataPermission": "READ_ONLY",
  "workingHours": {},
  "specialties": ["string"],
  "notes": "string"
}
```
**Response**: Updated staff object

#### DELETE `/salons/{salonId}/staff/{staffId}`
**Purpose**: Remove staff member from salon
**Authentication**: Bearer token required (CHIEF_GROOMER role)
**Response**: 200 OK

#### POST `/salons/{salonId}/staff/invitations/{invitationId}/resend`
**Purpose**: Resend staff invitation
**Authentication**: Bearer token required (CHIEF_GROOMER role)
**Response**: 200 OK

#### DELETE `/salons/{salonId}/staff/invitations/{invitationId}`
**Purpose**: Cancel pending staff invitation
**Authentication**: Bearer token required (CHIEF_GROOMER role)
**Response**: 200 OK

---

## Staff Working Hours

### Staff Schedule Management

#### GET `/salons/{salonId}/staff/{staffId}/working-hours`
**Purpose**: Get working hours configuration for a specific staff member
**Authentication**: Bearer token required
**Response**:
```json
{
  "success": true,
  "data": {
    "staffId": "string",
    "salonId": "string",
    "weeklySchedule": {
      "monday": {
        "isWorkingDay": true,
        "startTime": "09:00",
        "endTime": "17:00",
        "breakStart": "12:00",
        "breakEnd": "13:00"
      },
      "tuesday": {
        "isWorkingDay": true,
        "startTime": "09:00",
        "endTime": "17:00",
        "breakStart": "12:00",
        "breakEnd": "13:00"
      },
      "wednesday": {
        "isWorkingDay": false,
        "startTime": null,
        "endTime": null,
        "breakStart": null,
        "breakEnd": null
      }
    },
    "holidays": [
      {
        "name": "Anul Nou",
        "date": "2024-01-01",
        "isWorkingDay": false,
        "type": "LEGAL"
      }
    ],
    "customClosures": [
      {
        "reason": "Concediu medical",
        "date": "2024-06-15",
        "description": "Recuperare după intervenție"
      }
    ],
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

#### PUT `/salons/{salonId}/staff/{staffId}/working-hours`
**Purpose**: Update working hours configuration for a specific staff member
**Authentication**: Bearer token required (CHIEF_GROOMER role or staff member themselves)
**Request Body**:
```json
{
  "weeklySchedule": {
    "monday": {
      "isWorkingDay": true,
      "startTime": "09:00",
      "endTime": "17:00",
      "breakStart": "12:00",
      "breakEnd": "13:00"
    },
    "tuesday": {
      "isWorkingDay": false,
      "startTime": null,
      "endTime": null,
      "breakStart": null,
      "breakEnd": null
    }
  },
  "holidays": [
    {
      "name": "Anul Nou",
      "date": "2024-01-01",
      "isWorkingDay": false,
      "type": "LEGAL"
    }
  ],
  "customClosures": [
    {
      "reason": "Concediu medical",
      "date": "2024-06-15",
      "description": "Recuperare după intervenție"
    }
  ]
}
```
**Response**: Updated staff working hours object

#### GET `/salons/{salonId}/staff/availability`
**Purpose**: Get availability status for all staff members on a specific date
**Authentication**: Bearer token required
**Query Parameters**:
- `date` (string, required): Date to check availability (YYYY-MM-DD)
- `startTime` (string, optional): Start time to check (HH:MM)
- `endTime` (string, optional): End time to check (HH:MM)

**Response**:
```json
{
  "success": true,
  "data": {
    "date": "2024-01-15",
    "staffAvailability": [
      {
        "staffId": "string",
        "staffName": "string",
        "isAvailable": true,
        "workingHours": {
          "startTime": "09:00",
          "endTime": "17:00",
          "breakStart": "12:00",
          "breakEnd": "13:00"
        },
        "conflictingAppointments": [],
        "reason": null
      },
      {
        "staffId": "string",
        "staffName": "string",
        "isAvailable": false,
        "workingHours": null,
        "conflictingAppointments": [],
        "reason": "Day off"
      }
    ]
  }
}
```

#### POST `/salons/{salonId}/staff/{staffId}/working-hours/reset`
**Purpose**: Reset staff working hours to default salon schedule
**Authentication**: Bearer token required (CHIEF_GROOMER role)
**Request Body**: Empty
**Response**: Reset staff working hours object

### Staff Schedule Templates

The system supports predefined schedule templates for different staff roles:

- **Standard Groomer**: Monday-Friday 9:00-17:00, Saturday 10:00-15:00
- **Part-time**: Monday, Wednesday, Friday 10:00-16:00
- **Weekend Specialist**: Saturday-Sunday 9:00-18:00
- **Flexible Schedule**: Tuesday-Saturday 10:00-19:00
- **Assistant Schedule**: Monday-Friday 8:00-16:00 (support hours)

### Business Rules

1. **Permission Levels**:
   - CHIEF_GROOMER: Can modify any staff member's schedule
   - Staff members: Can only modify their own schedule
   - Other roles: Read-only access

2. **Validation Rules**:
   - Working day must have both start and end times
   - End time must be after start time
   - Break times must be within working hours
   - Break end time must be after break start time
   - Times must follow HH:MM format (24-hour)

3. **Holiday Management**:
   - Romanian national holidays are automatically included
   - Custom closures can be added for personal time off
   - Holidays override regular working schedules

4. **Availability Calculation**:
   - Considers working days, holidays, and custom closures
   - Excludes break times from available periods
   - Integrates with appointment scheduling system

---

## Settings & Configuration

### SMS Settings

#### GET `/salons/{salonId}/sms-reminders`
**Purpose**: Get SMS reminder settings for salon
**Authentication**: Bearer token required
**Response**:
```json
{
  "success": true,
  "data": {
    "salonId": "string",
    "appointmentConfirmations": true,
    "dayBeforeReminders": true,
    "followUpMessages": false,
    "selectedProvider": "string",
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

#### PUT `/salons/{salonId}/sms-reminders`
**Purpose**: Update SMS reminder settings
**Authentication**: Bearer token required (CHIEF_GROOMER role)
**Request Body**:
```json
{
  "appointmentConfirmations": true,
  "dayBeforeReminders": true,
  "followUpMessages": false,
  "selectedProvider": "string"
}
```
**Response**: Updated SMS settings object

### Notification Settings

#### GET `/salons/{salonId}/notification-settings`
**Purpose**: Get notification settings for salon
**Authentication**: Bearer token required
**Response**:
```json
{
  "success": true,
  "data": {
    "salonId": "string",
    "pushNotificationsEnabled": true,
    "soundPreference": "default",
    "vibrationEnabled": true,
    "doNotDisturb": {
      "enabled": false,
      "startTime": "22:00",
      "endTime": "08:00",
      "allowCritical": true
    },
    "notificationRules": {
      "newAppointments": true,
      "appointmentCancellations": true,
      "paymentConfirmations": true,
      "teamMemberUpdates": true,
      "systemMaintenanceAlerts": true,
      "defaultPriority": "NORMAL"
    },
    "updatedAt": "2024-01-01T00:00:00Z"
  }
}
```

#### PUT `/salons/{salonId}/notification-settings`
**Purpose**: Update notification settings
**Authentication**: Bearer token required (CHIEF_GROOMER role)
**Request Body**: Same structure as GET response
**Response**: Updated notification settings object

---

## Reports & Analytics

### Dashboard & Statistics

#### GET `/reports/dashboard`
**Purpose**: Get dashboard statistics for current salon
**Authentication**: Bearer token required
**Response**:
```json
{
  "success": true,
  "data": {
    "totalClients": 150,
    "totalPets": 200,
    "todayAppointments": 8,
    "weekAppointments": 45,
    "weekRevenue": 2250.00,
    "monthRevenue": 9500.00,
    "newClientsThisMonth": 12,
    "averageRating": 4.8,
    "totalReviews": 89,
    "serviceStats": {
      "Grooming": 25,
      "Bathing": 15
    },
    "groomerStats": {
      "groomer1": 1250.00,
      "groomer2": 980.00
    }
  }
}
```

#### GET `/reports/revenue`
**Purpose**: Get revenue statistics
**Authentication**: Bearer token required
**Query Parameters**:
- `period` (string): daily, weekly, monthly, yearly
- `startDate` (string, optional): Start date (YYYY-MM-DD)
- `endDate` (string, optional): End date (YYYY-MM-DD)

**Response**:
```json
{
  "success": true,
  "data": {
    "totalRevenue": 9500.00,
    "periodRevenue": [
      {
        "period": "2024-01",
        "revenue": 9500.00,
        "appointments": 45
      }
    ],
    "topServices": [
      {
        "serviceName": "Full Grooming",
        "revenue": 3500.00,
        "count": 35
      }
    ]
  }
}
```

#### GET `/reports/appointments`
**Purpose**: Get appointment statistics
**Authentication**: Bearer token required
**Query Parameters**: Same as revenue endpoint
**Response**: Appointment statistics with trends

#### GET `/reports/clients`
**Purpose**: Get client statistics
**Authentication**: Bearer token required
**Response**: Client acquisition and retention metrics

#### GET `/reports/services`
**Purpose**: Get service performance statistics
**Authentication**: Bearer token required
**Response**: Service usage and revenue metrics

#### GET `/reports/groomers`
**Purpose**: Get groomer performance statistics
**Authentication**: Bearer token required
**Response**: Individual groomer performance metrics

---

## Notifications & SMS

### Notification Management

#### GET `/notifications`
**Purpose**: Get user notifications
**Authentication**: Bearer token required
**Query Parameters**:
- `unreadOnly` (boolean, optional): Filter unread notifications
- `type` (string, optional): Filter by notification type
- `limit` (integer, optional): Number of results

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "string",
      "title": "string",
      "message": "string",
      "type": "APPOINTMENT",
      "isRead": false,
      "createdAt": "2024-01-01T00:00:00Z",
      "metadata": {}
    }
  ]
}
```

#### PATCH `/notifications/{notificationId}/read`
**Purpose**: Mark notification as read
**Authentication**: Bearer token required
**Response**: 200 OK

#### DELETE `/notifications/{notificationId}`
**Purpose**: Delete notification
**Authentication**: Bearer token required
**Response**: 200 OK

#### GET `/notifications/unread-count`
**Purpose**: Get count of unread notifications
**Authentication**: Bearer token required
**Response**:
```json
{
  "success": true,
  "data": 5
}
```

### Automated Notification System (New)

The backend automatically handles all appointment-related notifications. Frontend only needs to register FCM tokens and manage SMS settings.

#### POST `/api/users/fcm-token`
**Purpose**: Register FCM token for push notifications
**Authentication**: Bearer token required
**Request Body**:
```json
{
  "userId": "string",
  "token": "fcm-token-string",
  "platform": "android"
}
```
**Response**:
```json
{
  "success": true,
  "message": "FCM token registered successfully"
}
```

#### DELETE `/api/users/fcm-token`
**Purpose**: Remove FCM token
**Authentication**: Bearer token required
**Request Body**:
```json
{
  "token": "fcm-token-string"
}
```
**Response**: 200 OK

#### GET `/api/salons/{salonId}/sms-settings`
**Purpose**: Get SMS notification settings
**Authentication**: Bearer token required
**Response**:
```json
{
  "success": true,
  "data": {
    "salonId": "string",
    "appointmentConfirmations": true,
    "dayBeforeReminders": true,
    "followUpMessages": false,
    "updatedAt": "2024-01-15T10:00:00Z"
  }
}
```

#### PUT `/api/salons/{salonId}/sms-settings`
**Purpose**: Update SMS notification settings
**Authentication**: Bearer token required (CHIEF_GROOMER role only)
**Request Body**:
```json
{
  "appointmentConfirmations": true,
  "dayBeforeReminders": false,
  "followUpMessages": true
}
```
**Response**: Updated SMS settings object

#### GET `/api/salons/{salonId}/notifications/stats`
**Purpose**: Get notification delivery statistics
**Authentication**: Bearer token required
**Query Parameters**:
- `startDate` (string, optional): Date range start (YYYY-MM-DD)
- `endDate` (string, optional): Date range end (YYYY-MM-DD)

**Response**:
```json
{
  "success": true,
  "data": {
    "totalSent": 1250,
    "smsDelivered": 1180,
    "pushDelivered": 1200,
    "failureRate": 0.05,
    "byType": {
      "appointment_created": 400,
      "appointment_cancelled": 50,
      "appointment_reminder": 600,
      "appointment_completed": 200,
      "appointment_follow_up": 100
    },
    "deliveryRates": {
      "sms": 0.94,
      "push": 0.96
    }
  }
}
```

### Automatic Notification Triggers

The backend automatically sends notifications for these events:

| **Event** | **Endpoint** | **SMS to Client** | **Push to Staff** | **Scheduled Tasks** |
|-----------|--------------|-------------------|-------------------|-------------------|
| **Appointment Created** | `POST /salons/{id}/appointments` | ✅ Confirmation (if enabled) | ✅ New appointment alert | ⏰ Schedule day-before reminder |
| **Appointment Cancelled** | `PUT /salons/{id}/appointments/{id}/cancel` | ✅ Cancellation notice | ✅ Cancellation alert | ❌ Cancel scheduled reminders |
| **Appointment Completed** | `PUT /salons/{id}/appointments/{id}/complete` | ✅ Thank you message | ✅ Completion alert | ⏰ Schedule follow-up (2h later) |
| **Appointment Rescheduled** | `PUT /salons/{id}/appointments/{id}/reschedule` | ✅ New time/date | ✅ Reschedule alert | ⏰ Update reminder schedule |

### Romanian SMS Message Templates

The backend uses these Romanian message templates:

```javascript
// Appointment confirmation
"Bună ziua! Programarea pentru {petName} a fost confirmată pe {date} la ora {time}. Vă așteptăm la Partykids Grooming!"

// Day-before reminder
"Vă reamintim că mâine, {date} la ora {time}, aveți programare pentru {petName} la Partykids Grooming."

// Appointment completion
"Mulțumim că ați ales Partykids Grooming pentru {petName}! Sperăm că sunteți mulțumiți de serviciile noastre."

// Cancellation notice
"Programarea pentru {petName} pe {date} la ora {time} a fost anulată. Motiv: {reason}. Ne cerem scuze pentru inconveniență."

// Follow-up message
"Mulțumim că ați ales Partykids Grooming pentru {petName}! Cum a fost experiența? Vă așteptăm din nou!"
```

### Push Notification Data Format

Push notifications sent to staff include this data structure:
```json
{
  "type": "appointment_created",
  "appointmentId": "string",
  "clientId": "string",
  "petName": "string",
  "appointmentTime": "2024-01-15T10:00:00Z",
  "salonId": "string"
}
```

**Notification Types**:
- `appointment_created` - New appointment created
- `appointment_cancelled` - Appointment cancelled
- `appointment_completed` - Appointment marked complete
- `appointment_reminder` - Day-before reminder
- `appointment_follow_up` - Follow-up message
- `client_message` - General client communication

---

## Common Response Formats

### Success Response
```json
{
  "success": true,
  "data": {},
  "message": "string (optional)"
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": {}
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `409` - Conflict (duplicate data)
- `422` - Unprocessable Entity (business logic errors)
- `500` - Internal Server Error

### Authentication Headers
All authenticated endpoints require:
```
Authorization: Bearer {jwt_token}
Content-Type: application/json
Accept: application/json
X-API-Version: v1
X-App-Environment: production
```

### Romanian Business Context
- All monetary values are in RON (Romanian Lei)
- Phone numbers follow Romanian format: `+40XXXXXXXXX`
- Dates use ISO 8601 format: `YYYY-MM-DD`
- Times use 24-hour format: `HH:MM`
- Business hours respect Romanian working patterns
- Holiday management includes Romanian national holidays

### Service Categories (Backend Enum Values)
- `GROOMING` - Grooming services
- `BATHING` - Bathing and cleaning
- `STYLING` - Styling and trimming
- `NAIL_CARE` - Nail trimming and care
- `DENTAL_CARE` - Dental hygiene
- `SPECIALTY` - Specialized treatments
- `PACKAGE` - Service packages

### User Roles
- `USER` - Basic user (converted to GROOMER in app)
- `GROOMER` - Salon staff member
- `ADMIN` - System administrator

### Groomer Roles (Salon-specific)
- `CHIEF_GROOMER` - Salon owner/manager
- `REGULAR_GROOMER` - Regular staff member

### Client Data Permissions
- `NO_ACCESS` - Cannot view client data
- `READ_ONLY` - Can view but not edit
- `FULL_ACCESS` - Can view and edit

---

## Future/Planned Endpoints

### Working Hours Management

#### GET `/salons/{salonId}/working-hours`
**Purpose**: Get salon working hours configuration
**Authentication**: Bearer token required
**Response**:
```json
{
  "success": true,
  "data": {
    "salonId": "string",
    "workingHours": {
      "monday": {
        "isWorkingDay": true,
        "openTime": "09:00",
        "closeTime": "18:00",
        "breakStart": "13:00",
        "breakEnd": "14:00"
      },
      "tuesday": { "isWorkingDay": false }
    },
    "holidays": [
      {
        "date": "2024-01-01",
        "name": "Anul Nou",
        "isRecurring": true
      }
    ],
    "customClosures": [
      {
        "startDate": "2024-06-15",
        "endDate": "2024-06-20",
        "reason": "Vacanță"
      }
    ]
  }
}
```

#### PUT `/salons/{salonId}/working-hours`
**Purpose**: Update salon working hours
**Authentication**: Bearer token required (CHIEF_GROOMER role)
**Request Body**: Same structure as GET response
**Response**: Updated working hours object

### Review & Rating System

#### GET `/salons/{salonId}/reviews`
**Purpose**: Get reviews for salon
**Authentication**: Bearer token required
**Query Parameters**:
- `rating` (integer, optional): Filter by rating (1-5)
- `limit` (integer, optional): Number of results
- `offset` (integer, optional): Pagination offset

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "string",
      "clientId": "string",
      "clientName": "string",
      "appointmentId": "string",
      "rating": 5,
      "comment": "string",
      "createdAt": "2024-01-01T00:00:00Z",
      "response": "string",
      "respondedAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### POST `/salons/{salonId}/reviews/{reviewId}/response`
**Purpose**: Respond to a client review
**Authentication**: Bearer token required (CHIEF_GROOMER role)
**Request Body**:
```json
{
  "response": "string"
}
```
**Response**: Updated review object

### Subscription Management

#### GET `/salons/{salonId}/clients/{clientId}/subscriptions`
**Purpose**: Get client subscription plans
**Authentication**: Bearer token required
**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "string",
      "planName": "string",
      "serviceIds": ["string"],
      "frequency": "MONTHLY",
      "price": 200.00,
      "startDate": "2024-01-01",
      "endDate": "2024-12-31",
      "status": "ACTIVE",
      "nextAppointmentDate": "2024-02-01"
    }
  ]
}
```

#### POST `/salons/{salonId}/clients/{clientId}/subscriptions`
**Purpose**: Create subscription plan for client
**Authentication**: Bearer token required
**Request Body**:
```json
{
  "planName": "string",
  "serviceIds": ["string"],
  "frequency": "MONTHLY",
  "price": 200.00,
  "startDate": "2024-01-01",
  "duration": 12
}
```
**Response**: Created subscription object

### Payment Management

#### GET `/salons/{salonId}/payments`
**Purpose**: Get payment history
**Authentication**: Bearer token required
**Query Parameters**:
- `startDate` (string, optional): Date range start
- `endDate` (string, optional): Date range end
- `status` (string, optional): Payment status filter
- `clientId` (string, optional): Filter by client

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "string",
      "appointmentId": "string",
      "clientId": "string",
      "amount": 50.00,
      "currency": "RON",
      "method": "CASH",
      "status": "COMPLETED",
      "transactionId": "string",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### POST `/salons/{salonId}/payments`
**Purpose**: Record payment for appointment
**Authentication**: Bearer token required
**Request Body**:
```json
{
  "appointmentId": "string",
  "amount": 50.00,
  "method": "CASH",
  "notes": "string"
}
```
**Response**: Created payment object

### Inventory Management (Future)

#### GET `/salons/{salonId}/inventory`
**Purpose**: Get salon inventory items
**Authentication**: Bearer token required
**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "string",
      "name": "string",
      "category": "SHAMPOO",
      "currentStock": 10,
      "minStock": 5,
      "unit": "bottles",
      "cost": 25.00,
      "supplier": "string",
      "lastRestocked": "2024-01-01T00:00:00Z"
    }
  ]
}
```

#### POST `/salons/{salonId}/inventory`
**Purpose**: Add inventory item
**Authentication**: Bearer token required (CHIEF_GROOMER role)
**Request Body**:
```json
{
  "name": "string",
  "category": "SHAMPOO",
  "currentStock": 10,
  "minStock": 5,
  "unit": "bottles",
  "cost": 25.00,
  "supplier": "string"
}
```
**Response**: Created inventory item

### Marketing & Promotions (Future)

#### GET `/salons/{salonId}/promotions`
**Purpose**: Get active promotions
**Authentication**: Bearer token required
**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "string",
      "name": "string",
      "description": "string",
      "type": "PERCENTAGE",
      "value": 20.0,
      "startDate": "2024-01-01",
      "endDate": "2024-01-31",
      "applicableServices": ["string"],
      "minAmount": 100.00,
      "usageLimit": 50,
      "usageCount": 15,
      "isActive": true
    }
  ]
}
```

#### POST `/salons/{salonId}/promotions`
**Purpose**: Create new promotion
**Authentication**: Bearer token required (CHIEF_GROOMER role)
**Request Body**: Same structure as response data
**Response**: Created promotion object

### System Health & Monitoring

#### GET `/health`
**Purpose**: System health check
**Authentication**: None required
**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "version": "1.0.0",
  "services": {
    "database": "healthy",
    "redis": "healthy",
    "sms": "healthy"
  }
}
```

#### GET `/version`
**Purpose**: Get API version information
**Authentication**: None required
**Response**:
```json
{
  "version": "1.0.0",
  "buildDate": "2024-01-01T00:00:00Z",
  "environment": "production"
}
```

---

## Error Handling & Validation

### Validation Error Response
```json
{
  "success": false,
  "error": "Validation failed",
  "code": "VALIDATION_ERROR",
  "details": {
    "field": "phone",
    "message": "Numărul de telefon nu este valid",
    "value": "invalid_phone"
  }
}
```

### Business Logic Error Response
```json
{
  "success": false,
  "error": "Appointment conflict",
  "code": "APPOINTMENT_CONFLICT",
  "details": {
    "conflictingAppointmentId": "string",
    "suggestedTimes": ["10:30", "11:00", "14:00"]
  }
}
```

### Rate Limiting
- **Rate Limit**: 1000 requests per hour per user
- **Headers**:
  - `X-RateLimit-Limit`: 1000
  - `X-RateLimit-Remaining`: 999
  - `X-RateLimit-Reset`: **********

### Pagination
For endpoints that return lists, pagination follows this pattern:
```json
{
  "success": true,
  "data": [],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8,
    "hasNext": true,
    "hasPrev": false
  }
}
```

---

## Feature Toggle System (NEW)

### Get Feature Toggles
**GET** `/api/feature-toggles`

Returns all feature flags for the application.

**Response:**
```json
{
  "success": true,
  "data": {
    "monthly_view_enabled": false,
    "theme_selection_enabled": false,
    "translations_enabled": false
  }
}
```

**Feature Flags:**
- `monthly_view_enabled`: Controls visibility of monthly calendar view
- `theme_selection_enabled`: Controls visibility of theme selection in settings
- `translations_enabled`: Controls visibility of language/translation options
- `sell_screen`: Controls access to the "Vânzări" screen
- `reviews`: Controls visibility of customer reviews features

---

## SMS Verification System (NEW)

### Send SMS Verification Code
**POST** `/api/auth/send-sms-verification`

Sends a 6-digit OTP code to the specified phone number.

**Request Body:**
```json
{
  "phoneNumber": "+40721123456"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Verification code sent successfully",
  "data": {
    "expiresIn": 300
  }
}
```

**Rate Limiting:** Maximum 3 attempts per 10 minutes per phone number.

### Verify SMS Code
**POST** `/api/auth/verify-sms`

Verifies the OTP code for phone number verification.

**Request Body:**
```json
{
  "phoneNumber": "+40721123456",
  "code": "123456"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Phone number verified successfully",
  "data": {
    "verified": true
  }
}
```

**Error Responses:**
- `400`: Invalid or expired code
- `429`: Too many verification attempts
- `404`: No verification request found for phone number

---

## Integration Notes

### Frontend Service Mapping
- **AuthService** → `/auth/*` endpoints
- **ClientService** → `/salons/{salonId}/clients/*` endpoints
- **AppointmentService** → `/salons/{salonId}/appointments/*` endpoints (auto-triggers notifications)
- **ServiceManagementService** → `/salons/{salonId}/services/*` endpoints
- **StaffService** → `/salons/{salonId}/staff/*` endpoints
- **SalonService** → `/salons/*` endpoints
- **SettingsService** → `/salons/{salonId}/settings/*` endpoints
- **SmsSettingsService** → `/salons/{salonId}/sms-settings/*` endpoints
- **NotificationHandler** → `/api/users/fcm-token` endpoints
- **NotificationService** → `/notifications/*` endpoints
- **DashboardService** → `/reports/*` endpoints
- **FeatureToggleService** → `/api/feature-toggles` endpoints
- **SmsVerificationService** → `/api/auth/send-sms-verification`, `/api/auth/verify-sms` endpoints

### Environment Configuration
```dart
// Development
static const String baseUrl = 'http://*************:8080';

// Production
static const String baseUrl = 'https://api.partykids-partykidsapp.ro';
```

### Romanian Localization Requirements
- All user-facing messages in Romanian
- Date formats: DD.MM.YYYY
- Time formats: HH:MM (24-hour)
- Currency: RON with 2 decimal places
- Phone validation: Romanian mobile format (+40XXXXXXXXX)
- Address validation: Romanian postal codes and cities

This documentation serves as the contract between frontend and backend development teams, ensuring consistent API implementation and integration.
