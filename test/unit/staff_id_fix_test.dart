import 'package:flutter_test/flutter_test.dart';
import 'package:partykidsapp/services/staff_service.dart';
import 'package:partykidsapp/models/user_role.dart';

void main() {
  group('StaffResponse ID Mapping Fix', () {
    test('should prioritize staffId over userId for id field', () {
      // Test data with both staffId and userId
      final jsonWithBothIds = {
        'staffId': 'staff-123',
        'userId': 'user-456',
        'userName': '<PERSON>',
        'role': 'GROOMER',
        'isActive': true,
        'hiredAt': '2024-01-01T00:00:00Z',
      };

      final staff = StaffResponse.fromJson(jsonWithBothIds);

      // The id field should contain staffId, not userId
      expect(staff.id, equals('staff-123'));
      expect(staff.userId, equals('user-456'));
    });

    test('should use staffId when only staffId is present', () {
      final jsonWithStaffIdOnly = {
        'staffId': 'staff-789',
        'userName': '<PERSON>',
        'role': 'GROOMER',
        'isActive': true,
        'hiredAt': '2024-01-01T00:00:00Z',
      };

      final staff = StaffResponse.fromJson(jsonWithStaffIdOnly);

      expect(staff.id, equals('staff-789'));
      expect(staff.userId, isNull);
    });

    test('should fallback to id field when staffId is not present', () {
      final jsonWithIdOnly = {
        'id': 'fallback-id-123',
        'userName': 'Bob Wilson',
        'role': 'GROOMER',
        'isActive': true,
        'hiredAt': '2024-01-01T00:00:00Z',
      };

      final staff = StaffResponse.fromJson(jsonWithIdOnly);

      expect(staff.id, equals('fallback-id-123'));
      expect(staff.userId, isNull);
    });

    test('should include both staffId and userId in toJson output', () {
      final staff = StaffResponse(
        id: 'staff-123',
        userId: 'user-456',
        name: 'Test Staff',
        groomerRole: GroomerRole.groomer,
        clientDataPermission: ClientDataPermission.fullAccess,
        isActive: true,
        joinedAt: DateTime.parse('2024-01-01T00:00:00Z'),
      );

      final json = staff.toJson();

      expect(json['staffId'], equals('staff-123'));
      expect(json['userId'], equals('user-456'));
    });

    test('should handle copyWith for both staffId and userId', () {
      final originalStaff = StaffResponse(
        id: 'staff-123',
        userId: 'user-456',
        name: 'Original Name',
        groomerRole: GroomerRole.groomer,
        clientDataPermission: ClientDataPermission.fullAccess,
        isActive: true,
        joinedAt: DateTime.parse('2024-01-01T00:00:00Z'),
      );

      final updatedStaff = originalStaff.copyWith(
        staffId: 'new-staff-789',
        userId: 'new-user-101',
        name: 'Updated Name',
      );

      expect(updatedStaff.id, equals('new-staff-789'));
      expect(updatedStaff.userId, equals('new-user-101'));
      expect(updatedStaff.name, equals('Updated Name'));
    });
  });
}
