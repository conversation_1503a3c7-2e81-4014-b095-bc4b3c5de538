#!/bin/sh

echo "Xcode Cloud Post-Clone Script"
echo "Current directory: $(pwd)"

# Detect and set environment based on Git branch
echo "Detecting environment from Git branch..."
BRANCH_NAME=$(git branch --show-current 2>/dev/null || git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
echo "Current branch: $BRANCH_NAME"

# Set environment based on branch
case "$BRANCH_NAME" in
    "main"|"master")
        FLUTTER_ENV="production"
        echo "🚀 Setting PRODUCTION environment for main branch"
        ;;
    "staging"|"stage"|"develop")
        FLUTTER_ENV="staging"
        echo "🔧 Setting STAGING environment"
        ;;
    *)
        FLUTTER_ENV="development"
        echo "🛠️ Setting DEVELOPMENT environment"
        ;;
esac

# Export environment variable for Flutter build
export FLUTTER_ENV=$FLUTTER_ENV
echo "Environment set to: $FLUTTER_ENV"

# Install Flutter dependencies first
echo "Running flutter pub get..."
flutter pub get

# Change to iOS directory and install pods
echo "Changing to iOS directory and installing pods..."
cd ios
pod install

echo "Post-clone script completed successfully"
echo "Environment: $FLUTTER_ENV"
