import '../../services/ui_notification_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/service.dart';
import '../../services/service_management_service.dart';
import '../../config/theme/app_theme.dart';

class ServiceFormDialog extends StatefulWidget {
  final Service? service; // null for create, non-null for edit
  final void Function(Service) onServiceSaved;
  final bool showCreateAnotherOption; // Whether to show "Create Another" workflow

  const ServiceFormDialog({
    super.key,
    this.service,
    required this.onServiceSaved,
    this.showCreateAnotherOption = true,
  });

  @override
  State<ServiceFormDialog> createState() => _ServiceFormDialogState();
}

class _ServiceFormDialogState extends State<ServiceFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _minPriceController = TextEditingController();
  final _maxPriceController = TextEditingController();
  final _priceSmallController = TextEditingController();
  final _minPriceSmallController = TextEditingController();
  final _maxPriceSmallController = TextEditingController();
  final _priceMediumController = TextEditingController();
  final _minPriceMediumController = TextEditingController();
  final _maxPriceMediumController = TextEditingController();
  final _priceLargeController = TextEditingController();
  final _minPriceLargeController = TextEditingController();
  final _maxPriceLargeController = TextEditingController();
  final _durationController = TextEditingController();
  final _durationSmallController = TextEditingController();
  final _durationMediumController = TextEditingController();
  final _durationLargeController = TextEditingController();
  final _displayOrderController = TextEditingController();

  final _nameFocus = FocusNode();
  final _descriptionFocus = FocusNode();
  final _priceFocus = FocusNode();
  final _durationFocus = FocusNode();
  final _displayOrderFocus = FocusNode();

  String _selectedCategory = '';
  List<String> _selectedRequirements = [];
  bool _isActive = true;
  bool _isLoading = false;
  bool _showOptionalFields = false; // New: Toggle for optional fields
  bool _variablePricing = false; // Toggle for size-based pricing
  bool _priceRange = false; // Toggle for min-max pricing
  bool _showCreateAnother = false; // Show create another button after successful creation

  final List<String> _categories = ServiceManagementService.getServiceCategories();
  final List<String> _availableRequirements = ServiceManagementService.getCommonRequirements();
  final List<String> _serviceNameSuggestions = AppStrings.groomingServiceSuggestions;

  @override
  void initState() {
    super.initState();
    _initializeForm();
  }

  void _initializeForm() {
    if (widget.service != null) {
      final service = widget.service!;
      _nameController.text = service.name;
      _descriptionController.text = service.description;
      _priceController.text = service.price.toString();

      // Initialize price range fields
      if (service.minPrice != null && service.maxPrice != null) {
        _priceRange = true;
        _minPriceController.text = service.minPrice!.toString();
        _maxPriceController.text = service.maxPrice!.toString();
      }

      if (service.sizePrices != null) {
        _variablePricing = true;
        _priceSmallController.text = service.sizePrices!['S']?.toString() ?? '';
        _priceMediumController.text = service.sizePrices!['M']?.toString() ?? '';
        _priceLargeController.text = service.sizePrices!['L']?.toString() ?? '';

        // Initialize size-based price ranges
        if (service.sizeMinPrices != null && service.sizeMaxPrices != null) {
          _minPriceSmallController.text = service.sizeMinPrices!['S']?.toString() ?? '';
          _maxPriceSmallController.text = service.sizeMaxPrices!['S']?.toString() ?? '';
          _minPriceMediumController.text = service.sizeMinPrices!['M']?.toString() ?? '';
          _maxPriceMediumController.text = service.sizeMaxPrices!['M']?.toString() ?? '';
          _minPriceLargeController.text = service.sizeMinPrices!['L']?.toString() ?? '';
          _maxPriceLargeController.text = service.sizeMaxPrices!['L']?.toString() ?? '';
        }
      }

      _durationController.text = service.duration.toString();
      if (service.sizeDurations != null) {
        _durationSmallController.text = service.sizeDurations!['S']?.toString() ?? '';
        _durationMediumController.text = service.sizeDurations!['M']?.toString() ?? '';
        _durationLargeController.text = service.sizeDurations!['L']?.toString() ?? '';
      }
      _displayOrderController.text = service.displayOrder.toString();
      _selectedCategory = service.category; // This is already the display name from the service
      _selectedRequirements = List.from(service.requirements);
      _isActive = service.isActive;
    } else {
      _selectedCategory = _categories.first; // Default to first Romanian category
      _displayOrderController.text = '0';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _minPriceController.dispose();
    _maxPriceController.dispose();
    _priceSmallController.dispose();
    _minPriceSmallController.dispose();
    _maxPriceSmallController.dispose();
    _priceMediumController.dispose();
    _minPriceMediumController.dispose();
    _maxPriceMediumController.dispose();
    _priceLargeController.dispose();
    _minPriceLargeController.dispose();
    _maxPriceLargeController.dispose();
    _durationController.dispose();
    _durationSmallController.dispose();
    _durationMediumController.dispose();
    _durationLargeController.dispose();
    _displayOrderController.dispose();
    _nameFocus.dispose();
    _descriptionFocus.dispose();
    _priceFocus.dispose();
    _durationFocus.dispose();
    _displayOrderFocus.dispose();
    super.dispose();
  }

  Future<void> _saveService() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Debug logging for pricing strategy
      debugPrint('💰 Service form save - Variable pricing: $_variablePricing, Price range: $_priceRange');

      // Helper function to safely parse double values
      double safeParseDouble(String value, {double defaultValue = 0.0}) {
        final trimmed = value.trim();
        if (trimmed.isEmpty) {
          debugPrint('⚠️ Empty value, using default: $defaultValue');
          return defaultValue;
        }
        final parsed = double.tryParse(trimmed);
        if (parsed == null) {
          debugPrint('❌ Failed to parse "$trimmed", using default: $defaultValue');
          return defaultValue;
        }
        debugPrint('✅ Parsed "$trimmed" as $parsed');
        return parsed;
      }

      // Helper function to safely parse int values
      int safeParseInt(String value, {int defaultValue = 0}) {
        final trimmed = value.trim();
        if (trimmed.isEmpty) {
          debugPrint('⚠️ Empty duration value, using default: $defaultValue');
          return defaultValue;
        }
        final parsed = int.tryParse(trimmed);
        if (parsed == null) {
          debugPrint('❌ Failed to parse duration "$trimmed", using default: $defaultValue');
          return defaultValue;
        }
        debugPrint('✅ Parsed duration "$trimmed" as $parsed');
        return parsed;
      }

      // Debug current controller values
      if (_variablePricing) {
        debugPrint('📊 Size pricing values:');
        debugPrint('   S: "${_priceSmallController.text}"');
        debugPrint('   M: "${_priceMediumController.text}"');
        debugPrint('   L: "${_priceLargeController.text}"');

        if (_priceRange) {
          debugPrint('📊 Size min pricing values:');
          debugPrint('   S min: "${_minPriceSmallController.text}"');
          debugPrint('   M min: "${_minPriceMediumController.text}"');
          debugPrint('   L min: "${_minPriceLargeController.text}"');
          debugPrint('📊 Size max pricing values:');
          debugPrint('   S max: "${_maxPriceSmallController.text}"');
          debugPrint('   M max: "${_maxPriceMediumController.text}"');
          debugPrint('   L max: "${_maxPriceLargeController.text}"');
        }
      }

      final service = Service(
        id: widget.service?.id ?? '',
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        price: _variablePricing
            ? safeParseDouble(_priceSmallController.text, defaultValue: 50.0)
            : safeParseDouble(_priceController.text, defaultValue: 50.0),
        minPrice: _priceRange && !_variablePricing
            ? double.tryParse(_minPriceController.text.trim())
            : null,
        maxPrice: _priceRange && !_variablePricing
            ? double.tryParse(_maxPriceController.text.trim())
            : null,
        sizePrices: _variablePricing
            ? {
                'S': safeParseDouble(_priceSmallController.text, defaultValue: 50.0),
                'M': safeParseDouble(_priceMediumController.text, defaultValue: 60.0),
                'L': safeParseDouble(_priceLargeController.text, defaultValue: 70.0),
              }
            : null,
        sizeMinPrices: _variablePricing && _priceRange
            ? {
                'S': safeParseDouble(_minPriceSmallController.text, defaultValue: 40.0),
                'M': safeParseDouble(_minPriceMediumController.text, defaultValue: 50.0),
                'L': safeParseDouble(_minPriceLargeController.text, defaultValue: 60.0),
              }
            : null,
        sizeMaxPrices: _variablePricing && _priceRange
            ? {
                'S': safeParseDouble(_maxPriceSmallController.text, defaultValue: 60.0),
                'M': safeParseDouble(_maxPriceMediumController.text, defaultValue: 70.0),
                'L': safeParseDouble(_maxPriceLargeController.text, defaultValue: 80.0),
              }
            : null,
        duration: _variablePricing
            ? safeParseInt(_durationSmallController.text, defaultValue: 60)
            : safeParseInt(_durationController.text, defaultValue: 60),
        sizeDurations: _variablePricing
            ? {
                'S': safeParseInt(_durationSmallController.text, defaultValue: 60),
                'M': safeParseInt(_durationMediumController.text, defaultValue: 90),
                'L': safeParseInt(_durationLargeController.text, defaultValue: 120),
              }
            : null,
        category: _selectedCategory,
        displayOrder: int.tryParse(_displayOrderController.text.trim()) ?? 0,
        requirements: _selectedRequirements,
        isActive: _isActive,
        createdAt: widget.service?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Validate service
      final validationError = ServiceManagementService.validateService(service);
      if (validationError != null) {
        if (mounted) {
          UINotificationService.showWarning(
            context: context,
            title: 'Date invalide',
            message: validationError,
          );
        }
        return;
      }

      final response = widget.service == null
          ? await ServiceManagementService.createService(service)
          : await ServiceManagementService.updateService(widget.service!.id, service);

      if (mounted) {
        if (response.success && response.data != null) {
          widget.onServiceSaved(response.data!);

          if (widget.service == null && widget.showCreateAnotherOption) {
            // For new services, show create another option only if enabled
            setState(() {
              _showCreateAnother = true;
            });
          } else {
            // For edits or when create another is disabled, just close the dialog
            Navigator.of(context).pop();
          }

          UINotificationService.showSuccess(
            context: context,
            title: 'Succes',
            message: widget.service == null
                ? 'Serviciul "${service.name}" a fost creat cu succes!'
                : 'Serviciul "${service.name}" a fost actualizat cu succes!',
          );
        } else {
          UINotificationService.showError(
            context: context,
            title: 'Eroare',
            message: response.error ?? 'Nu s-a putut salva serviciul',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: 'Eroare',
          message: 'Eroare de conexiune: $e',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _resetFormForNewService() {
    setState(() {
      _nameController.clear();
      _descriptionController.clear();
      _priceController.clear();
      _minPriceController.clear();
      _maxPriceController.clear();
      _priceSmallController.clear();
      _minPriceSmallController.clear();
      _maxPriceSmallController.clear();
      _priceMediumController.clear();
      _minPriceMediumController.clear();
      _maxPriceMediumController.clear();
      _priceLargeController.clear();
      _minPriceLargeController.clear();
      _maxPriceLargeController.clear();
      _durationController.clear();
      _durationSmallController.clear();
      _durationMediumController.clear();
      _durationLargeController.clear();
      _displayOrderController.text = '0';
      _selectedRequirements.clear();
      _isActive = true;
      _variablePricing = false;
      _priceRange = false;
      _showOptionalFields = false;
      _showCreateAnother = false;
      // Keep the same category as it's likely the user wants to create similar services
    });

    // Focus on the name field
    Future.microtask(() {
      if (mounted) {
        FocusScope.of(context).requestFocus(_nameFocus);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.service != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          isEditing ? 'Editează Serviciu' : 'Adaugă Serviciu Nou',
          style: TextStyle(
            color: Theme.of(context).colorScheme.onPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: Icon(Icons.close, color: Theme.of(context).colorScheme.onPrimary),
        ),
        actions: [
          if (isEditing)
            Container(
              margin: const EdgeInsets.only(right: 16),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                widget.service!.name,
                style: TextStyle(
                  fontSize: 12,
                  color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.7),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
      body: Column(
        children: [
          // Success message for create another
          if (_showCreateAnother) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
                border: Border(
                  bottom: BorderSide(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.2),
                  ),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.onSurface,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Icon(
                      Icons.check,
                      color: Colors.white,
                      size: 16,
                    ),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      'Serviciu creat cu succes! Poți adăuga încă unul.',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Text(
                      'Închide',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],

          // Form content
          Expanded(
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Name field with suggestions
                    Autocomplete<String>(
                      optionsBuilder: (TextEditingValue textEditingValue) {
                        if (textEditingValue.text.isEmpty) {
                          return _serviceNameSuggestions;
                        }
                        return _serviceNameSuggestions.where(
                          (option) => option
                              .toLowerCase()
                              .contains(textEditingValue.text.toLowerCase()),
                        );
                      },
                      onSelected: (String selection) {
                        _nameController.text = selection;
                      },
                      fieldViewBuilder:
                          (context, controller, focusNode, onEditingComplete) {
                        if (controller.text != _nameController.text) {
                          controller.text = _nameController.text;
                        }
                        return TextFormField(
                          controller: controller,
                          focusNode: focusNode,
                          onEditingComplete: onEditingComplete,
                          onChanged: (value) {
                            _nameController.text = value;
                          },
                          decoration: InputDecoration(
                            labelText: 'Nume serviciu *',
                            hintText: 'ex: Tuns complet, Spălat și uscat',
                            prefixIcon: const Icon(Icons.content_cut),
                            border: const OutlineInputBorder(),
                          ),
                          validator: (value) {
                            if (value == null || value.trim().isEmpty) {
                              return 'Numele serviciului este obligatoriu';
                            }
                            if (value.trim().length > 255) {
                              return 'Numele nu poate depăși 255 de caractere';
                            }
                            return null;
                          },
                          textCapitalization: TextCapitalization.sentences,
                        );
                      },
                      optionsViewBuilder: (context, onSelected, options) {
                        return Align(
                          alignment: Alignment.topLeft,
                          child: Material(
                            elevation: 4.0,
                            borderRadius: BorderRadius.circular(8),
                            child: ConstrainedBox(
                              constraints: const BoxConstraints(maxHeight: 200),
                              child: ListView.builder(
                                padding: EdgeInsets.zero,
                                shrinkWrap: true,
                                itemCount: options.length,
                                itemBuilder: (context, index) {
                                  final option = options.elementAt(index);
                                  return InkWell(
                                    onTap: () => onSelected(option),
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 16, vertical: 12),
                                      child: Text(option),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                    SizedBox(height: 24),

                    // Variable pricing toggle
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: _variablePricing
                              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
                              : Theme.of(context).colorScheme.outline,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.05),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: _variablePricing
                                  ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                                  : Theme.of(context).colorScheme.surfaceContainerHighest,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.pets,
                              color: _variablePricing
                                  ? Theme.of(context).colorScheme.primary
                                  : Theme.of(context).colorScheme.onSurfaceVariant,
                              size: 20,
                            ),
                          ),
                          SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Preț și durată variabile',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Theme.of(context).colorScheme.onSurface,
                                  ),
                                ),
                                SizedBox(height: 2),
                                Text(
                                  _variablePricing
                                      ? 'Prețuri și durate diferite pentru S/M/L'
                                      : 'Preț și durată fixe pentru toate mărimile',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Switch(
                            value: _variablePricing,
                            onChanged: (val) {
                              setState(() {
                                _variablePricing = val;
                              });
                            },
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 16),

                    // Price range toggle
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: _priceRange
                              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
                              : Theme.of(context).colorScheme.outline,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.05),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: _priceRange
                                  ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                                  : Theme.of(context).colorScheme.surfaceContainerHighest,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              Icons.trending_up,
                              color: _priceRange
                                  ? Theme.of(context).colorScheme.primary
                                  : Theme.of(context).colorScheme.onSurfaceVariant,
                              size: 20,
                            ),
                          ),
                          SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Interval de preț',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Theme.of(context).colorScheme.onSurface,
                                  ),
                                ),
                                SizedBox(height: 2),
                                Text(
                                  _priceRange
                                      ? 'Preț minim și maxim pentru serviciu'
                                      : 'Preț fix pentru serviciu',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Switch(
                            value: _priceRange,
                            onChanged: (val) {
                              setState(() {
                                _priceRange = val;
                              });
                            },
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 24),

                    // Pricing and Duration Section
                    _variablePricing
                        ? _buildVariablePricingSection()
                        : _buildFixedPricingSection(),

                    SizedBox(height: 32),
                    // Optional fields toggle
                    _buildSectionHeader(
                      'Opțiuni Avansate',
                      'Configurări suplimentare pentru serviciu',
                      Icons.settings,
                      isOptional: true,
                      isExpanded: _showOptionalFields,
                      onToggle: () {
                        setState(() {
                          _showOptionalFields = !_showOptionalFields;
                        });
                      },
                    ),

                    // Optional fields section
                    if (_showOptionalFields) ...[
                      SizedBox(height: 20),

                      // Description field
                      StandardFormField(
                        controller: _descriptionController,
                        focusNode: _descriptionFocus,
                        labelText: 'Descriere',
                        hintText: 'Descrierea detaliată a serviciului...',
                        prefixIcon: Icons.description,
                        maxLines: 3,
                        validator: (value) {
                          if (value != null && value.trim().length > 1000) {
                            return 'Descrierea nu poate depăși 1000 de caractere';
                          }
                          return null;
                        },
                        onFieldSubmitted: (_) => FocusScope.of(context).requestFocus(_displayOrderFocus),
                      ),
                      SizedBox(height: 20),

                      // Display Order
                      StandardFormField(
                        controller: _displayOrderController,
                        focusNode: _displayOrderFocus,
                        labelText: 'Ordine afișare',
                        hintText: '0 = primul, 1 = al doilea, etc.',
                        prefixIcon: Icons.sort,
                        keyboardType: TextInputType.number,
                        inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                        onFieldSubmitted: (_) => _saveService(),
                      ),
                      SizedBox(height: 20),

                      // Requirements section
                      _buildRequirementsSection(),
                      SizedBox(height: 20),

                      // Active Status
                      _buildActiveStatusSection(),
                    ],

                    SizedBox(height: 100), // Space for bottom buttons
                  ],
                ),
              ),
            ),
          ),

          // Bottom action buttons
          _buildBottomActions(),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(
    String title,
    String subtitle,
    IconData icon, {
    bool isOptional = false,
    bool isExpanded = false,
    VoidCallback? onToggle,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isOptional && isExpanded
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
              : Theme.of(context).colorScheme.outline,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: InkWell(
        onTap: onToggle,
        borderRadius: BorderRadius.circular(8),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: Theme.of(context).colorScheme.onSurface,
                size: 20,
              ),
            ),
            SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            if (isOptional) ...[
              if (onToggle != null)
                Icon(
                  isExpanded ? Icons.expand_less : Icons.expand_more,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              if (!isExpanded)
                Container(
                  margin: const EdgeInsets.only(left: 8),
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Opțional',
                    style: TextStyle(
                      fontSize: 10,
                      color: Theme.of(context).colorScheme.onSurface,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFixedPricingSection() {
    return Column(
      children: [
        // Price section
        if (_priceRange) ...[
          // Min-Max price fields
          Row(
            children: [
              Expanded(
                child: StandardFormField(
                  controller: _minPriceController,
                  labelText: 'Preț minim (RON)',
                  hintText: 'ex: 40.00',
                  prefixIcon: Icons.trending_down,
                  isRequired: true,
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Prețul minim este obligatoriu';
                    }
                    final minPrice = double.tryParse(value.trim());
                    if (minPrice == null || minPrice < 0.01) {
                      return 'Prețul trebuie să fie cel puțin 0.01 RON';
                    }
                    final maxPriceText = _maxPriceController.text.trim();
                    if (maxPriceText.isNotEmpty) {
                      final maxPrice = double.tryParse(maxPriceText);
                      if (maxPrice != null && minPrice >= maxPrice) {
                        return 'Prețul minim trebuie să fie mai mic decât maximul';
                      }
                    }
                    return null;
                  },
                ),
              ),
              SizedBox(width: 16),
              Expanded(
                child: StandardFormField(
                  controller: _maxPriceController,
                  labelText: 'Preț maxim (RON)',
                  hintText: 'ex: 60.00',
                  prefixIcon: Icons.trending_up,
                  isRequired: true,
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Prețul maxim este obligatoriu';
                    }
                    final maxPrice = double.tryParse(value.trim());
                    if (maxPrice == null || maxPrice < 0.01) {
                      return 'Prețul trebuie să fie cel puțin 0.01 RON';
                    }
                    final minPriceText = _minPriceController.text.trim();
                    if (minPriceText.isNotEmpty) {
                      final minPrice = double.tryParse(minPriceText);
                      if (minPrice != null && maxPrice <= minPrice) {
                        return 'Prețul maxim trebuie să fie mai mare decât minimul';
                      }
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
        ] else ...[
          // Single price field
          StandardFormField(
            controller: _priceController,
            focusNode: _priceFocus,
            labelText: 'Preț (RON)',
            hintText: 'ex: 50.00',
            prefixIcon: Icons.attach_money,
            isRequired: true,
            keyboardType: TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Prețul este obligatoriu';
              }
              final price = double.tryParse(value.trim());
              if (price == null || price < 0.01) {
                return 'Prețul trebuie să fie cel puțin 0.01 RON';
              }
              return null;
            },
            onFieldSubmitted: (_) => FocusScope.of(context).requestFocus(_durationFocus),
          ),
        ],
        SizedBox(height: 16),
        // Duration field
        StandardFormField(
          controller: _durationController,
          focusNode: _durationFocus,
          labelText: 'Durată (min)',
          hintText: 'ex: 60',
          prefixIcon: Icons.schedule,
          isRequired: true,
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Durata este obligatorie';
            }
            final duration = int.tryParse(value.trim());
            if (duration == null || duration < 1 || duration > 480) {
              return 'Durata trebuie să fie între 1-480 min';
            }
            return null;
          },
          onFieldSubmitted: (_) => _saveService(),
        ),
      ],
    );
  }

  Widget _buildVariablePricingSection() {
    return Column(
      children: [
        // Small size
        _buildSizeRow('S', 'Mic', _priceSmallController, _durationSmallController),
        SizedBox(height: 16),
        // Medium size
        _buildSizeRow('M', 'Mediu', _priceMediumController, _durationMediumController),
        SizedBox(height: 16),
        // Large size
        _buildSizeRow('L', 'Mare', _priceLargeController, _durationLargeController),
      ],
    );
  }

  Widget _buildSizeRow(String size, String label, TextEditingController priceController, TextEditingController durationController) {
    // Get the corresponding min/max controllers for this size
    TextEditingController? minPriceController;
    TextEditingController? maxPriceController;

    switch (size) {
      case 'S':
        minPriceController = _minPriceSmallController;
        maxPriceController = _maxPriceSmallController;
        break;
      case 'M':
        minPriceController = _minPriceMediumController;
        maxPriceController = _maxPriceMediumController;
        break;
      case 'L':
        minPriceController = _minPriceLargeController;
        maxPriceController = _maxPriceLargeController;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Theme.of(context).colorScheme.outline),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  size,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ),
              SizedBox(width: 12),
              Text(
                'Mărime $label',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),
          SizedBox(height: 16),

          // Price section
          if (_priceRange && minPriceController != null && maxPriceController != null) ...[
            // Min-Max price fields for this size
            Row(
              children: [
                Expanded(
                  child: StandardFormField(
                    controller: minPriceController,
                    labelText: 'Min (RON)',
                    hintText: 'ex: 40.00',
                    prefixIcon: Icons.trending_down,
                    isRequired: true,
                    keyboardType: TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Obligatoriu';
                      }
                      final minPrice = double.tryParse(value.trim());
                      if (minPrice == null || minPrice < 0.01) {
                        return '>=0.01';
                      }
                      final maxPriceText = maxPriceController!.text.trim();
                      if (maxPriceText.isNotEmpty) {
                        final maxPrice = double.tryParse(maxPriceText);
                        if (maxPrice != null && minPrice >= maxPrice) {
                          return 'Min < Max';
                        }
                      }
                      return null;
                    },
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: StandardFormField(
                    controller: maxPriceController,
                    labelText: 'Max (RON)',
                    hintText: 'ex: 60.00',
                    prefixIcon: Icons.trending_up,
                    isRequired: true,
                    keyboardType: TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Obligatoriu';
                      }
                      final maxPrice = double.tryParse(value.trim());
                      if (maxPrice == null || maxPrice < 0.01) {
                        return '>=0.01';
                      }
                      final minPriceText = minPriceController!.text.trim();
                      if (minPriceText.isNotEmpty) {
                        final minPrice = double.tryParse(minPriceText);
                        if (minPrice != null && maxPrice <= minPrice) {
                          return 'Max > Min';
                        }
                      }
                      return null;
                    },
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: StandardFormField(
                    controller: durationController,
                    labelText: 'Durată (min)',
                    hintText: 'ex: 60',
                    prefixIcon: Icons.schedule,
                    isRequired: true,
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Obligatoriu';
                      }
                      final duration = int.tryParse(value.trim());
                      if (duration == null || duration < 1 || duration > 480) {
                        return '1-480 min';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ] else ...[
            // Single price field for this size
            Row(
              children: [
                Expanded(
                  child: StandardFormField(
                    controller: priceController,
                    labelText: 'Preț (RON)',
                    hintText: 'ex: 50.00',
                    prefixIcon: Icons.attach_money,
                    isRequired: true,
                    keyboardType: TextInputType.numberWithOptions(decimal: true),
                    inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}'))],
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Obligatoriu';
                      }
                      final price = double.tryParse(value.trim());
                      if (price == null || price < 0.01) {
                        return '>=0.01';
                      }
                      return null;
                    },
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: StandardFormField(
                    controller: durationController,
                    labelText: 'Durată (min)',
                    hintText: 'ex: 60',
                    prefixIcon: Icons.schedule,
                    isRequired: true,
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Obligatoriu';
                      }
                      final duration = int.tryParse(value.trim());
                      if (duration == null || duration < 1 || duration > 480) {
                        return '1-480 min';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildActiveStatusSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Theme.of(context).colorScheme.outline),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: _isActive
                  ? const Color(0xFF2E7D32).withValues(alpha: 0.1)
                  : Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _isActive ? Icons.visibility : Icons.visibility_off,
              color: _isActive ? const Color(0xFF2E7D32) : Colors.grey,
              size: 20,
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Status serviciu',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                SizedBox(height: 2),
                Text(
                  _isActive
                      ? 'Activ - disponibil pentru programări'
                      : 'Inactiv - ascuns din listă',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _isActive,
            onChanged: (value) {
              setState(() {
                _isActive = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (_showCreateAnother) ...[
              // Create Another Service Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _isLoading ? null : _resetFormForNewService,
                  icon:  Icon(Icons.add, size: 20),
                  label: Text(
                    'Creează Alt Serviciu',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                ),
              ),
              SizedBox(height: 12),
              // Close Button
              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    side: BorderSide(color: Theme.of(context).colorScheme.onSurface),
                  ),
                  child: Text(
                    'Închide',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
              ),
            ] else ...[
              // Normal action buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        side: BorderSide(color: Colors.grey),
                      ),
                      child: Text(
                        'Anulează',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _saveService,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                      ),
                      child: _isLoading
                          ? SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.onPrimary),
                              ),
                            )
                          : Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(widget.service == null ? Icons.add : Icons.save, size: 20),
                                SizedBox(width: 8),
                                Text(
                                  widget.service == null ? 'Creează Serviciu' : 'Salvează Modificări',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRequirementsSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Theme.of(context).colorScheme.outline),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.checklist,
                  color: Theme.of(context).colorScheme.onSurface,
                  size: 20,
                ),
              ),
              SizedBox(width: 12),
              Text(
                'Cerințe serviciu',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _availableRequirements.map((requirement) {
              final isSelected = _selectedRequirements.contains(requirement);
              return FilterChip(
                label: Text(
                  requirement,
                  style: TextStyle(
                    fontSize: 12,
                    color: isSelected ? Theme.of(context).colorScheme.onPrimary : Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                selected: isSelected,
                onSelected: (selected) {
                  setState(() {
                    if (selected) {
                      _selectedRequirements.add(requirement);
                    } else {
                      _selectedRequirements.remove(requirement);
                    }
                  });
                },
                backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                selectedColor: Theme.of(context).colorScheme.primary,
                checkmarkColor: Theme.of(context).colorScheme.onPrimary,
                side: BorderSide(
                  color: isSelected ? Theme.of(context).colorScheme.primary : Theme.of(context).colorScheme.outline,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
