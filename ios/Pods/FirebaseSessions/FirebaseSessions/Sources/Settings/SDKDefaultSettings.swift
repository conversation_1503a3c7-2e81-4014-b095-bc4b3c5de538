//
// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import Foundation

/// Class that manages the local overrides configs related to the library.
class SDKDefaultSettings: SettingsProvider {
  var sessionsEnabled: Bool? {
    // Default is sessions enabled
    return true
  }

  var sessionTimeout: TimeInterval? {
    // Default is 30 minutes
    return 30 * 60
  }

  var samplingRate: Double? {
    // Default is all events are dispatched
    return 1.0
  }
}

typealias SDKDefaultSettingsProvider = SDKDefaultSettings
extension SDKDefaultSettingsProvider {
  func updateSettings() {
    // Nothing to be done since there is nothing to be updated.
  }

  func isSettingsStale() -> Bool {
    // Settings are never stale since all of these are local settings from Plist
    return false
  }
}
