import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../providers/role_provider.dart';
import '../../providers/calendar_provider.dart';
import '../../services/invitation_service.dart';
import '../../services/auth/auth_service.dart';
import '../../models/salon_invitation.dart';
import '../../screens/profile/salon_creation_screen.dart';

/// Modern, clean onboarding widget for users without salon association
class SalonOnboardingWidget extends StatefulWidget {
  const SalonOnboardingWidget({super.key});

  @override
  State<SalonOnboardingWidget> createState() => _SalonOnboardingWidgetState();
}

class _SalonOnboardingWidgetState extends State<SalonOnboardingWidget>
    with TickerProviderStateMixin {
  List<SalonInvitation> _pendingInvitations = [];
  bool _isLoadingInvitations = false;

  // Simplified animation controllers
  late AnimationController _fadeAnimationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _loadPendingInvitations();
  }

  /// Initialize simplified animations for smooth onboarding experience
  void _initializeAnimations() {
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeAnimationController,
      curve: Curves.easeInOut,
    ));

    // Start fade animation
    _fadeAnimationController.forward();
  }

  @override
  void dispose() {
    _fadeAnimationController.dispose();
    super.dispose();
  }

  /// Load pending invitations for the user
  Future<void> _loadPendingInvitations() async {
    setState(() {
      _isLoadingInvitations = true;
    });

    try {
      final response = await InvitationService.getPendingInvitations();
      if (response.success && response.data != null) {
        setState(() {
          _pendingInvitations = response.data!;
        });
      }
    } catch (e) {
      debugPrint('❌ Error loading pending invitations: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingInvitations = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            const SizedBox(height: 20),

            // Welcome Section
            _buildWelcomeSection(),

            const SizedBox(height: 24),

            // Action Buttons Section
            _buildActionButtons(),

            const SizedBox(height: 20),

            // Info Card
            _buildInfoCard(),

            const SizedBox(height: 40),
          ],
        ),
      ),
    );
  }

  /// Build clean welcome section with logo and message
  Widget _buildWelcomeSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // Logo
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Image.asset(
                'assets/images/festro_logo_no_bg.png',
                width: 64,
                height: 64,
                fit: BoxFit.contain,
              ),
            ),

            const SizedBox(height: 20),

            // Welcome Text
            Text(
              'Bine ai venit în festro!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),

            const SizedBox(height: 12),

            Text(
              _pendingInvitations.isNotEmpty
                  ? 'Ai invitații în așteptare! Poți să te alături unui salon existent sau să îți creezi propriul salon.'
                  : 'Pentru a începe să folosești aplicația, trebuie să îți creezi propria echipă de evenimente.',
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// Build action buttons section (invitations + create salon)
  Widget _buildActionButtons() {
    return Column(
      children: [
        // Pending Invitations Section
        if (_isLoadingInvitations)
          _buildLoadingInvitations()
        else if (_pendingInvitations.isNotEmpty)
          _buildPendingInvitations(),

        // Spacing between invitations and create button
        if (_pendingInvitations.isNotEmpty) SizedBox(height: 16),

        // Create Salon Button
        _buildCreateSalonButton(),
      ],
    );
  }

  /// Build loading state for invitations
  Widget _buildLoadingInvitations() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Text(
              'Se încarcă invitațiile...',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build pending invitations list
  Widget _buildPendingInvitations() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Row(
          children: [
            Icon(
              Icons.mail_outline,
              color: Theme.of(context).colorScheme.onSurface,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'Invitații primite (${_pendingInvitations.length})',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // Invitations List
        ...(_pendingInvitations.take(3).map((invitation) =>
          _buildInvitationCard(invitation)
        ).toList()),

        // Show more indicator if there are more than 3 invitations
        if (_pendingInvitations.length > 3)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Text(
              'și încă ${_pendingInvitations.length - 3} invitații...',
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
      ],
    );
  }

  /// Build individual invitation card
  Widget _buildInvitationCard(SalonInvitation invitation) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Salon name and role
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        invitation.salonName,
                        style: TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      SizedBox(height: 2),
                      Text(
                        'Rol: ${invitation.proposedRole.displayName}',
                        style: TextStyle(
                          fontSize: 13,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                // Status badge
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    invitation.status.displayName,
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.secondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 6),

            // Invited by
            Text(
              'Invitat de: ${invitation.invitedByName}',
              style: TextStyle(
                fontSize: 11,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            SizedBox(height: 8),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => _declineInvitation(invitation),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.red,
                      side: BorderSide(color: Colors.red),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text('Refuză'),
                  ),
                ),
                SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _acceptInvitation(invitation),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                        'Acceptă',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onPrimary,
                        )
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build create salon button
  Widget _buildCreateSalonButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () => _navigateToCreateSalon(context),
        icon: Icon(Icons.add_business),
        label: Text(
          _pendingInvitations.isNotEmpty
              ? 'Sau creează propria Echipă'
              : 'Adaugă Echipă',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: _pendingInvitations.isNotEmpty
              ? Theme.of(context).colorScheme.secondary
              : Theme.of(context).colorScheme.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 3,
        ),
      ),
    );
  }

  /// Build clean info card
  Widget _buildInfoCard() {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.lightbulb_outline,
                color: Theme.of(context).colorScheme.onSurface,
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Primul pas important',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'După crearea echipei, vei deveni proprietarul și administratorul echipei, cu acces complet la toate funcționalitățile.',
                    style: TextStyle(
                      fontSize: 13,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      height: 1.3,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Navigate to salon creation screen
  Future<void> _navigateToCreateSalon(BuildContext context) async {
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => const SalonCreationScreen(),
      ),
    );

    if (result == true && context.mounted) {
      // Salon was created successfully, refresh all providers
      final roleProvider = context.read<RoleProvider>();
      final calendarProvider = context.read<CalendarProvider>();

      // Refresh role provider first
      debugPrint('🔄 Refreshing role provider after salon creation...');
      await roleProvider.refresh();

      // Add a longer delay to ensure backend has processed the staff creation
      debugPrint('🔄 Waiting for backend to process staff creation...');
      await Future.delayed(const Duration(seconds: 2));

      // Retry staff loading with multiple attempts
      if (context.mounted) {
        await _retryStaffLoading(calendarProvider);
        await calendarProvider.loadServices();
      }

      // go to calendar screen
      Navigator.of(context).pushNamed('/calendar');

      debugPrint('✅ Salon created successfully - triggering navigation to calendar');

      // The salon creation was successful, and roleProvider.refresh() was called
      // The MainLayout Consumer will automatically detect the salon association change
      // and switch to the calendar tab due to the state tracking logic
      debugPrint('🔄 MainLayout will automatically switch to Calendar tab');
    }
  }

  /// Accept salon invitation
  Future<void> _acceptInvitation(SalonInvitation invitation) async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
      );

      final response = await InvitationService.acceptInvitation(invitation.id);

      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      if (response.success && mounted) {
        // Refresh role provider to update salon association
        final roleProvider = context.read<RoleProvider>();
        final calendarProvider = context.read<CalendarProvider>();

        debugPrint('🔄 Refreshing role provider after accepting invitation...');
        await roleProvider.refresh();

        // Add delay for backend processing
        await Future.delayed(const Duration(seconds: 2));

        // Load calendar data
        if (mounted) {
          await _retryStaffLoading(calendarProvider);
          await calendarProvider.loadServices();
        }

        // Remove invitation from list since it was accepted
        setState(() {
          _pendingInvitations.removeWhere((inv) => inv.id == invitation.id);
        });

        debugPrint('✅ Successfully joined salon: ${invitation.salonName}');

        // The invitation was accepted successfully, and roleProvider.refresh() was called
        // The MainLayout will automatically detect the new salon association and switch to calendar
        debugPrint('🔄 MainLayout will automatically switch to Calendar tab');
      } else {
        debugPrint('❌ Failed to accept invitation: ${response.error}');
      }
    } catch (e) {
      // Close loading dialog if still open
      if (mounted) Navigator.of(context).pop();

      debugPrint('❌ Error accepting invitation: $e');
    }
  }

  /// Decline salon invitation
  Future<void> _declineInvitation(SalonInvitation invitation) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Refuzi invitația?'),
        content: Text(
          'Ești sigur că vrei să refuzi invitația de la ${invitation.salonName}?'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('Anulează'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text('Refuză'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final response = await InvitationService.declineInvitation(invitation.id);

        if (response.success) {
          // Remove invitation from list
          setState(() {
            _pendingInvitations.removeWhere((inv) => inv.id == invitation.id);
          });

          debugPrint('✅ Successfully declined invitation from ${invitation.salonName}');
        } else {
          debugPrint('❌ Failed to decline invitation: ${response.error}');
        }
      } catch (e) {
        debugPrint('❌ Error declining invitation: $e');
      }
    }
  }

  /// Retry staff loading with multiple attempts
  Future<void> _retryStaffLoading(CalendarProvider calendarProvider) async {
    const maxAttempts = 3;
    const delayBetweenAttempts = Duration(seconds: 1);

    // Check current salon ID before attempting to load staff
    final currentSalonId = await AuthService.getCurrentSalonId();
    debugPrint('🔍 Current salon ID before staff loading: $currentSalonId');

    for (int attempt = 1; attempt <= maxAttempts; attempt++) {
      debugPrint('🔄 Staff loading attempt $attempt/$maxAttempts');

      await calendarProvider.loadStaff();

      // Check if staff was loaded successfully
      if (calendarProvider.availableStaff.isNotEmpty) {
        debugPrint('✅ Staff loaded successfully on attempt $attempt: ${calendarProvider.availableStaff.length} staff members');
        return;
      }

      if (attempt < maxAttempts) {
        debugPrint('⏳ No staff found, waiting before retry...');
        await Future.delayed(delayBetweenAttempts);
      }
    }

    debugPrint('❌ Failed to load staff after $maxAttempts attempts');
    debugPrint('❌ Final salon ID check: ${await AuthService.getCurrentSalonId()}');
  }


}
