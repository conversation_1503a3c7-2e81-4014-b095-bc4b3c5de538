import 'package:partykidsapp/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/salon_invitation.dart';
import '../../providers/role_provider.dart';
import '../../services/invitation_service.dart';
import '../../config/theme/app_theme.dart';

/// Dialog for displaying and responding to salon invitations
class InvitationDialog extends StatefulWidget {
  final SalonInvitation invitation;

  const InvitationDialog({
    super.key,
    required this.invitation,
  });

  @override
  State<InvitationDialog> createState() => _InvitationDialogState();
}

class _InvitationDialogState extends State<InvitationDialog> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.group_add,
              color: Theme.of(context).colorScheme.onSurface,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Invitație Salon',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Invitation details
            _buildInvitationDetails(),
            const SizedBox(height: 16),

            // Role information
            _buildRoleInformation(),
            const SizedBox(height: 16),

            // Expiration warning if needed
            if (widget.invitation.timeUntilExpiration.inHours < 24)
              _buildExpirationWarning(),
          ],
        ),
      ),
      actions: [
        if (!_isLoading) ...[
          TextButton(
            onPressed: () => _declineInvitation(),
            child: const Text(
              'Refuză',
              style: TextStyle(color: Colors.red),
            ),
          ),
          ElevatedButton(
            onPressed: () => _acceptInvitation(),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.forestGreen,
              foregroundColor: Colors.white,
            ),
            child: const Text('Acceptă'),
          ),
        ] else
          const CircularProgressIndicator(),
      ],
    );
  }

  Widget _buildInvitationDetails() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.elegantBrownLight.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.forestGreen.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.invitation.description,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 12),
          _buildDetailRow(Icons.business, 'Salon', widget.invitation.salonName),
          const SizedBox(height: 8),
          _buildDetailRow(Icons.person, 'Invitat de', widget.invitation.invitedByName),
          const SizedBox(height: 8),
          _buildDetailRow(Icons.schedule, 'Expiră în', widget.invitation.formattedExpirationTime),
          if (widget.invitation.message != null) ...[
            const SizedBox(height: 12),
            Text(
              'Mesaj:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              widget.invitation.message!,
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: AppColors.forestGreen,
        ),
        const SizedBox(width: 8),
        Text(
          '$label: ',
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            color: AppColors.forestGreen,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              color: Colors.grey.shade700,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRoleInformation() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.forestGreen.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.forestGreen.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Rolul propus:',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: AppColors.forestGreen,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                widget.invitation.proposedRole.hasManagementAccess ? Icons.supervisor_account : Icons.person,
                color: AppColors.forestGreen,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.invitation.proposedRole.displayName,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      widget.invitation.proposedRole.description,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          const Text(
            'Acces date clienți:',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: AppColors.forestGreen,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Icon(
                Icons.visibility,
                color: AppColors.taupe,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                widget.invitation.proposedClientDataPermission.displayName,
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            widget.invitation.proposedClientDataPermission.description,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExpirationWarning() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.warning_amber,
            color: Colors.orange,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Această invitație expiră în ${widget.invitation.formattedExpirationTime}',
              style: const TextStyle(
                color: Colors.orange,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _acceptInvitation() async {
    setState(() => _isLoading = true);

    try {
      final response = await InvitationService.acceptInvitation(widget.invitation.id);

      if (response.success) {
        // Refresh role provider to get new permissions
        if (mounted) {
          await context.read<RoleProvider>().refresh();

          Navigator.of(context).pop(true); // Return true to indicate acceptance

          showTopSnackBar(context, 
            SnackBar(
              content: Text('Te-ai alăturat cu succes echipei ${widget.invitation.salonName}!'),
              backgroundColor: AppColors.forestGreen,
            ),
          );

          // Navigate to profile screen to see the new salon
          _navigateToProfile();
        }
      } else {
        if (mounted) {
          showTopSnackBar(context, 
            SnackBar(
              content: Text(response.error ?? 'Eroare la acceptarea invitației'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        showTopSnackBar(context, 
          SnackBar(
            content: Text('Eroare: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _declineInvitation() async {
    setState(() => _isLoading = true);

    try {
      final response = await InvitationService.declineInvitation(widget.invitation.id);

      if (response.success) {
        if (mounted) {
          Navigator.of(context).pop(false); // Return false to indicate decline

          showTopSnackBar(context, 
            const SnackBar(
              content: Text('Invitația a fost refuzată'),
              backgroundColor: AppColors.taupe,
            ),
          );
        }
      } else {
        if (mounted) {
          showTopSnackBar(context, 
            SnackBar(
              content: Text(response.error ?? 'Eroare la refuzarea invitației'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        showTopSnackBar(context, 
          SnackBar(
            content: Text('Eroare: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// Navigate to main layout with bottom navigation
  void _navigateToProfile() {
    // Navigate to main layout which will automatically show the calendar tab
    // since the user now has salon association after accepting the invitation
    Navigator.of(context).pushNamedAndRemoveUntil(
      '/',
      (route) => false,
    );
  }
}
