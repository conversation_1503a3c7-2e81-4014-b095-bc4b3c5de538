import 'package:partykidsapp/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import '../config/api_config.dart';
import '../config/environment.dart';
import 'analytics_service.dart';

class ErrorHandlingService {
  static bool _isInitialized = false;

  /// Initialize error handling
  static void initialize() {
    if (_isInitialized) return;

    // Set up global error handlers
    FlutterError.onError = _handleFlutterError;
    
    _isInitialized = true;
    
    if (ApiConfig.enableLogging) {
      debugPrint('🛡️ Error handling service initialized');
    }
  }

  /// Handle Flutter framework errors
  static void _handleFlutterError(FlutterErrorDetails details) {
    // Log to console in debug mode
    if (ApiConfig.enableLogging) {
      debugPrint('🚨 Flutter Error: ${details.exception}');
      debugPrint('📍 Stack trace: ${details.stack}');
    }

    // Send to Crashlytics in production
    if (EnvironmentConfig.isProduction) {
      FirebaseCrashlytics.instance.recordFlutterFatalError(details);
    }

    // Track in analytics
    AnalyticsService.trackError(
      errorType: 'flutter_error',
      errorMessage: details.exception.toString(),
      screenName: details.context?.toString(),
    );
  }

  /// Handle API errors
  static void handleApiError({
    required String endpoint,
    required String error,
    int? statusCode,
    String? screenName,
  }) {
    if (ApiConfig.enableLogging) {
      debugPrint('🌐 API Error: $endpoint');
      debugPrint('   Status: $statusCode');
      debugPrint('   Error: $error');
      debugPrint('   Screen: $screenName');
    }

    // Send to Crashlytics in production
    if (EnvironmentConfig.isProduction) {
      FirebaseCrashlytics.instance.recordError(
        'API Error: $endpoint',
        StackTrace.current,
        reason: error,
        information: [
          'Status Code: $statusCode',
          'Screen: $screenName',
          'Endpoint: $endpoint',
        ],
      );
    }

    // Track in analytics
    AnalyticsService.trackError(
      errorType: 'api_error',
      errorMessage: '$endpoint: $error (Status: $statusCode)',
      screenName: screenName,
    );
  }

  /// Handle network errors
  static void handleNetworkError({
    required String operation,
    required String error,
    String? screenName,
  }) {
    if (ApiConfig.enableLogging) {
      debugPrint('📡 Network Error: $operation');
      debugPrint('   Error: $error');
      debugPrint('   Screen: $screenName');
    }

    // Send to Crashlytics in production
    if (EnvironmentConfig.isProduction) {
      FirebaseCrashlytics.instance.recordError(
        'Network Error: $operation',
        StackTrace.current,
        reason: error,
        information: [
          'Operation: $operation',
          'Screen: $screenName',
        ],
      );
    }

    // Track in analytics
    AnalyticsService.trackError(
      errorType: 'network_error',
      errorMessage: '$operation: $error',
      screenName: screenName,
    );
  }

  /// Handle authentication errors
  static void handleAuthError({
    required String operation,
    required String error,
    String? screenName,
  }) {
    if (ApiConfig.enableLogging) {
      debugPrint('🔐 Auth Error: $operation');
      debugPrint('   Error: $error');
      debugPrint('   Screen: $screenName');
    }

    // Send to Crashlytics in production
    if (EnvironmentConfig.isProduction) {
      FirebaseCrashlytics.instance.recordError(
        'Auth Error: $operation',
        StackTrace.current,
        reason: error,
        information: [
          'Operation: $operation',
          'Screen: $screenName',
        ],
      );
    }

    // Track in analytics
    AnalyticsService.trackError(
      errorType: 'auth_error',
      errorMessage: '$operation: $error',
      screenName: screenName,
    );
  }

  /// Handle validation errors
  static void handleValidationError({
    required String field,
    required String error,
    String? screenName,
  }) {
    if (ApiConfig.enableLogging) {
      debugPrint('✅ Validation Error: $field');
      debugPrint('   Error: $error');
      debugPrint('   Screen: $screenName');
    }

    // Track in analytics (don't send validation errors to Crashlytics)
    AnalyticsService.trackError(
      errorType: 'validation_error',
      errorMessage: '$field: $error',
      screenName: screenName,
    );
  }

  /// Handle general application errors
  static void handleAppError({
    required String operation,
    required dynamic error,
    StackTrace? stackTrace,
    String? screenName,
    Map<String, dynamic>? additionalInfo,
  }) {
    if (ApiConfig.enableLogging) {
      debugPrint('🚨 App Error: $operation');
      debugPrint('   Error: $error');
      debugPrint('   Screen: $screenName');
      if (additionalInfo != null) {
        debugPrint('   Additional Info: $additionalInfo');
      }
    }

    // Send to Crashlytics in production
    if (EnvironmentConfig.isProduction) {
      final information = <String>[
        'Operation: $operation',
        'Screen: $screenName',
      ];
      
      if (additionalInfo != null) {
        additionalInfo.forEach((key, value) {
          information.add('$key: $value');
        });
      }

      FirebaseCrashlytics.instance.recordError(
        error,
        stackTrace ?? StackTrace.current,
        reason: operation,
        information: information,
      );
    }

    // Track in analytics
    AnalyticsService.trackError(
      errorType: 'app_error',
      errorMessage: '$operation: $error',
      screenName: screenName,
    );
  }

  /// Get user-friendly error message
  static String getUserFriendlyMessage(dynamic error) {
    final errorString = error.toString();
    if (errorString.contains('SocketException')) {
      return 'Nu se poate conecta la server. Verificați conexiunea la internet.';
    } else if (errorString.contains('HttpException')) {
      return 'Eroare de comunicare cu serverul.';
    } else if (errorString.contains('TimeoutException')) {
      return 'Timpul de așteptare a expirat. Încercați din nou.';
    } else if (errorString.contains('FormatException')) {
      return 'Datele primite de la server sunt invalide.';
    } else if (errorString.contains('401')) {
      return 'Sesiunea a expirat. Vă rugăm să vă autentificați din nou.';
    } else if (errorString.contains('403')) {
      return 'Nu aveți permisiunea să efectuați această acțiune.';
    } else if (errorString.contains('404')) {
      return 'Resursa solicitată nu a fost găsită.';
    } else if (errorString.contains('500')) {
      return 'Eroare internă de server. Încercați din nou mai târziu.';
    } else {
      return 'A apărut o eroare neașteptată. Încercați din nou.';
    }
  }

  /// Show error dialog to user
  static void showErrorDialog({
    required BuildContext context,
    required String title,
    required String message,
    VoidCallback? onRetry,
    VoidCallback? onCancel,
  }) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          title,
          style: const TextStyle(
            color: Colors.red,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(message),
        actions: [
          if (onCancel != null)
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                onCancel();
              },
              child: const Text('Anulează'),
            ),
          if (onRetry != null)
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                onRetry();
              },
              child: const Text('Încearcă din nou'),
            ),
          if (onRetry == null && onCancel == null)
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('OK'),
            ),
        ],
      ),
    );
  }

  /// Show error snackbar
  static void showErrorSnackBar({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 4),
    VoidCallback? onRetry,
  }) {
    showTopSnackBar(context, 
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: duration,
        action: onRetry != null
            ? SnackBarAction(
                label: 'Încearcă din nou',
                textColor: Colors.white,
                onPressed: onRetry,
              )
            : null,
      ),
    );
  }

  /// Log custom event for debugging
  static void logDebugEvent({
    required String event,
    Map<String, dynamic>? data,
  }) {
    if (ApiConfig.enableLogging) {
      debugPrint('🐛 Debug Event: $event');
      if (data != null) {
        debugPrint('   Data: $data');
      }
    }

    // Track in analytics for debugging
    if (EnvironmentConfig.isDebugMode) {
      AnalyticsService.trackFeatureUsed(
        featureName: 'debug_event',
        parameters: {
          'event': event,
          ...?data,
        },
      );
    }
  }

  /// Set custom keys for Crashlytics
  static void setCustomKeys(Map<String, dynamic> keys) {
    if (!EnvironmentConfig.isProduction) return;

    keys.forEach((key, value) {
      FirebaseCrashlytics.instance.setCustomKey(key, value);
    });
  }

  /// Set user identifier for Crashlytics
  static void setUserIdentifier(String userId) {
    if (!EnvironmentConfig.isProduction) return;

    FirebaseCrashlytics.instance.setUserIdentifier(userId);
  }
}
