import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart' show debugPrint, kIsWeb;
// Temporarily commented out for macOS compatibility
// import 'package:google_sign_in/google_sign_in.dart';
// import 'package:flutter_facebook_auth/flutter_facebook_auth.dart'; // Removed Facebook auth

class SocialAuthService {
  final FirebaseAuth _auth;

  // Temporarily commented out for macOS compatibility
  // final GoogleSignIn _googleSignIn = GoogleSignIn();

  SocialAuthService(this._auth);

  // Google Sign In
  Future<UserCredential?> signInWithGoogle() async {
    try {
      debugPrint('Starting Google Sign In process');

      // Create a GoogleAuthProvider credential
      final googleProvider = GoogleAuthProvider();

      // Add scopes (optional)
      googleProvider.addScope('https://www.googleapis.com/auth/userinfo.email');
      googleProvider.addScope(
          'https://www.googleapis.com/auth/userinfo.profile');

      // Set custom parameters for better UX
      googleProvider.setCustomParameters({
        'prompt': 'select_account'
      });

      // Sign in with popup/redirect for web or with Firebase directly for mobile
      if (kIsWeb) {
        // Web implementation
        debugPrint('Using web implementation for Google Sign In');
        try {
          // First try with popup
          final userCredential = await _auth.signInWithPopup(googleProvider);
          debugPrint(
              'Google Sign In successful with popup: ${userCredential.user
                  ?.email}');
          return userCredential;
        } catch (popupError) {
          debugPrint('Popup sign-in failed, trying redirect: $popupError');
          // If popup fails, try with redirect
          await _auth.signInWithRedirect(googleProvider);
          // Note: This will redirect the page, so we won't reach the code below
          // The result will be handled when the page loads back
          return null;
        }
      } else {
        // Mobile/desktop implementation
        debugPrint('Using Firebase Auth directly for Google Sign In');
        final userCredential = await _auth.signInWithProvider(googleProvider);
        debugPrint('Google Sign In successful: ${userCredential.user?.email}');
        return userCredential;
      }
    } on FirebaseAuthException catch (e) {
      debugPrint('FirebaseAuthException during Google Sign In: ${e.code} - ${e
          .message}');
      rethrow;
    } catch (e) {
      debugPrint('Exception during Google Sign In: $e');
      rethrow;
    }
  }

  // Facebook Sign In - DISABLED
  Future<UserCredential?> signInWithFacebook() async {
    throw UnsupportedError('Facebook authentication has been disabled');
  }
}