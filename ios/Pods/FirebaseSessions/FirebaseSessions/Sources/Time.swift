//
// Copyright 2022 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

import Foundation

protocol TimeProvider {
  var timestampUS: Int64 { get }
}

///
/// Time is provides timestamp values in different formats to classes in the Sessions SDK. It mainly
/// exists for testing purposes.
///
class Time: TimeProvider {
  // Returns the current time as a timestamp in microseconds
  var timestampUS: Int64 {
    return Int64(UInt64(Date().timeIntervalSince1970) * USEC_PER_SEC)
  }
}
