import 'package:flutter/foundation.dart';
import '../models/salon_invitation.dart';
import '../models/user_role.dart';
import '../models/api_response.dart';
import '../config/api_config.dart';
import 'api_service.dart';
import 'auth/auth_service.dart';
import 'notification_handler.dart';

/// Service for managing salon invitations
class InvitationService {
  /// Send invitation to join salon
  static Future<ApiResponse<SalonInvitation>> sendInvitation({
    required String phoneNumber,
    required GroomerRole role,
    required ClientDataPermission clientDataPermission,
    String? message,
  }) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<SalonInvitation>.error('No salon ID found');
      }

      final body = {
        'invitedUserPhone': phoneNumber,
        'proposedRole': role.value,
        'proposedClientDataPermission': clientDataPermission.value,
        if (message != null) 'message': message,
      };

      final response = await ApiService.post<Map<String, dynamic>>(
        '/api/salon/$salonId/invitations',
        body: body,
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final invitation = SalonInvitation.fromJson(response.data!);
        return ApiResponse.success(invitation);
      }

      return ApiResponse<SalonInvitation>.error(response.error ?? 'Failed to send invitation');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error sending invitation: $e');
      }
      return ApiResponse<SalonInvitation>.error('Failed to send invitation: $e');
    }
  }

  /// Get pending invitations for current user
  static Future<ApiResponse<List<SalonInvitation>>> getPendingInvitations() async {
    try {
      final userId = await AuthService.getCurrentUserId();
      if (userId == null) {
        return ApiResponse<List<SalonInvitation>>.error('No user ID found');
      }

      final response = await ApiService.get<List<dynamic>>(
        '/api/user/$userId/invitations/pending',
        fromJson: (data) => List<dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final invitations = response.data!
            .map((json) => SalonInvitation.fromJson(Map<String, dynamic>.from(json)))
            .toList();
        return ApiResponse.success(invitations);
      }

      return ApiResponse<List<SalonInvitation>>.error(response.error ?? 'Failed to get invitations');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error getting pending invitations: $e');
      }
      return ApiResponse<List<SalonInvitation>>.error('Failed to get invitations: $e');
    }
  }

  /// Get sent invitations for current salon
  static Future<ApiResponse<List<SalonInvitation>>> getSentInvitations() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<List<SalonInvitation>>.error('No salon ID found');
      }

      final response = await ApiService.get<List<dynamic>>(
        '/api/salon/$salonId/invitations',
        fromJson: (data) => List<dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final invitations = response.data!
            .map((json) => SalonInvitation.fromJson(Map<String, dynamic>.from(json)))
            .toList();
        return ApiResponse.success(invitations);
      }

      return ApiResponse<List<SalonInvitation>>.error(response.error ?? 'Failed to get sent invitations');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error getting sent invitations: $e');
      }
      return ApiResponse<List<SalonInvitation>>.error('Failed to get sent invitations: $e');
    }
  }

  /// Accept salon invitation
  static Future<ApiResponse<GroomerPermissions>> acceptInvitation(String invitationId) async {
    try {
      final response = await ApiService.post<Map<String, dynamic>>(
        '/api/invitations/$invitationId/accept',
        body: {},
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final permissions = GroomerPermissions.fromJson(response.data!);

        // Update stored user data with new role and salon info
        await _updateStoredUserData(permissions);

        // Re-register FCM token for new salon
        try {
          await NotificationHandler.registerTokenAfterSalonChange(reason: 'Invitation acceptance');
          if (ApiConfig.enableLogging) {
            debugPrint('✅ FCM token re-registered after invitation acceptance');
          }
        } catch (e) {
          if (ApiConfig.enableLogging) {
            debugPrint('⚠️ Failed to re-register FCM token: $e');
          }
          // Don't fail invitation acceptance if FCM registration fails
        }

        return ApiResponse.success(permissions);
      }

      return ApiResponse<GroomerPermissions>.error(response.error ?? 'Failed to accept invitation');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error accepting invitation: $e');
      }
      return ApiResponse<GroomerPermissions>.error('Failed to accept invitation: $e');
    }
  }

  /// Decline salon invitation
  static Future<ApiResponse<bool>> declineInvitation(String invitationId) async {
    try {
      final response = await ApiService.post<bool>(
        '/api/invitations/$invitationId/decline',
        body: {},
        fromJson: (data) {
          // Handle both boolean and map responses
          if (data is bool) {
            return data;
          } else if (data is Map<String, dynamic>) {
            return data['success'] ?? true;
          }
          return true; // Default to true if response format is unexpected
        },
      );

      if (response.success) {
        return ApiResponse.success(response.data ?? true);
      }

      return ApiResponse<bool>.error(response.error ?? 'Failed to decline invitation');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error declining invitation: $e');
      }
      return ApiResponse<bool>.error('Failed to decline invitation: $e');
    }
  }

  /// Cancel sent invitation (admin only)
  static Future<ApiResponse<bool>> cancelInvitation(String invitationId) async {
    try {
      final response = await ApiService.delete<bool>(
        '/api/invitations/$invitationId',
        fromJson: (data) {
          // Handle both boolean and map responses
          if (data is bool) {
            return data;
          } else if (data is Map<String, dynamic>) {
            return data['success'] ?? true;
          }
          return true; // Default to true if response format is unexpected
        },
      );

      if (response.success) {
        return ApiResponse.success(response.data ?? true);
      }

      return ApiResponse<bool>.error(response.error ?? 'Failed to cancel invitation');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error canceling invitation: $e');
      }
      return ApiResponse<bool>.error('Failed to cancel invitation: $e');
    }
  }

  /// Resend invitation (admin only)
  static Future<ApiResponse<SalonInvitation>> resendInvitation(String invitationId) async {
    try {
      final response = await ApiService.post<Map<String, dynamic>>(
        '/api/invitations/$invitationId/resend',
        body: {},
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final invitation = SalonInvitation.fromJson(response.data!);
        return ApiResponse.success(invitation);
      }

      return ApiResponse<SalonInvitation>.error(response.error ?? 'Failed to resend invitation');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error resending invitation: $e');
      }
      return ApiResponse<SalonInvitation>.error('Failed to resend invitation: $e');
    }
  }

  /// Update stored user data after accepting invitation
  static Future<void> _updateStoredUserData(GroomerPermissions permissions) async {
    try {
      // Update the current salon ID to the salon from the accepted invitation
      await AuthService.updateCurrentSalonId(permissions.salonId);

      if (ApiConfig.enableLogging) {
        debugPrint('✅ Updated current salon ID to: ${permissions.salonId}');
        debugPrint('✅ Invitation accepted - salon data updated');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error updating stored user data: $e');
      }
    }
  }

  /// Check if user has pending invitations
  static Future<bool> hasPendingInvitations() async {
    try {
      final response = await getPendingInvitations();
      return response.success && (response.data?.isNotEmpty ?? false);
    } catch (e) {
      return false;
    }
  }

  /// Get invitation by ID
  static Future<ApiResponse<SalonInvitation>> getInvitation(String invitationId) async {
    try {
      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/invitations/$invitationId',
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final invitation = SalonInvitation.fromJson(response.data!);
        return ApiResponse.success(invitation);
      }

      return ApiResponse<SalonInvitation>.error(response.error ?? 'Failed to get invitation');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error getting invitation: $e');
      }
      return ApiResponse<SalonInvitation>.error('Failed to get invitation: $e');
    }
  }
}
