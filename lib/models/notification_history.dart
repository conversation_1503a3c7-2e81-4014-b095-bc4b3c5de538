/// Model for notification history entries
class NotificationHistory {
  final String id;
  final String title;
  final String message;
  final String type;
  final bool read;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  NotificationHistory({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    this.read = false,
    required this.timestamp,
    this.metadata,
  });

  /// Create from JSON
  factory NotificationHistory.fromJson(Map<String, dynamic> json) {
    return NotificationHistory(
      id: json['id'],
      title: json['title'],
      message: json['message'],
      type: json['type'],
      read: json['read'] ?? false,
      timestamp: DateTime.parse(json['timestamp']),
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'])
          : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type,
      'read': read,
      'timestamp': timestamp.toIso8601String(),
      'metadata': metadata,
    };
  }

  /// Create a copy with updated values
  NotificationHistory copyWith({
    String? id,
    String? title,
    String? message,
    String? type,
    bool? read,
    DateTime? timestamp,
    Map<String, dynamic>? metadata,
  }) {
    return NotificationHistory(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      read: read ?? this.read,
      timestamp: timestamp ?? this.timestamp,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Check if this notification is unread
  bool get isUnread => !read;

  /// Get formatted timestamp for display
  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Acum';
    } else if (difference.inMinutes < 60) {
      return 'Acum ${difference.inMinutes} min';
    } else if (difference.inHours < 24) {
      return 'Acum ${difference.inHours}h';
    } else if (difference.inDays == 1) {
      return 'Ieri';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} zile în urmă';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  /// Get notification priority from metadata
  String get priority => metadata?['priority'] ?? 'normal';

  /// Get appointment ID if this is an appointment-related notification
  String? get appointmentId => metadata?['appointmentId'];

  /// Get client ID if this is a client-related notification
  String? get clientId => metadata?['clientId'];

  /// Get appointment start time if provided
  DateTime? get appointmentTime {
    final value = metadata?['startTime'];
    if (value is String && value.isNotEmpty) {
      try {
        return DateTime.parse(value);
      } catch (_) {}
    }
    return null;
  }

  /// Check if this notification can be acted upon (e.g., has associated data)
  bool get isActionable {
    return appointmentId != null || clientId != null || metadata?['actionUrl'] != null;
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NotificationHistory &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'NotificationHistory{id: $id, title: $title, type: $type, read: $read, timestamp: $timestamp}';
  }
}
