import 'package:partykidsapp/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../config/theme/app_theme.dart';

class ReviewsScreen extends StatefulWidget {
  const ReviewsScreen({super.key});

  @override
  State<ReviewsScreen> createState() => _ReviewsScreenState();
}

class _ReviewsScreenState extends State<ReviewsScreen> {
  // Mock reviews data - will be replaced with API calls
  final List<Map<String, dynamic>> _reviews = [
    {
      'id': 'review_1',
      'clientName': '<PERSON>',
      'petName': 'Rex',
      'rating': 5,
      'comment': 'Serviciu excelent! Rex arată fantastic după toaletare. Personalul este foarte profesionist și îngrijitor.',
      'date': DateTime.now().subtract(const Duration(days: 2)),
      'service': 'Toaletare completă',
      'groomer': '<PERSON>',
      'response': null,
      'photos': ['https://example.com/rex_before.jpg', 'https://example.com/rex_after.jpg'],
    },
    {
      'id': 'review_2',
      'clientName': '<PERSON>',
      'petName': '<PERSON>',
      'rating': 4,
      'comment': 'Foarte mulțumit de servicii. Bella este mai frumoasă ca niciodată. Recomand cu încredere!',
      'date': DateTime.now().subtract(const Duration(days: 5)),
      'service': 'Baie completă',
      'groomer': 'Maria Ionescu',
      'response': {
        'text': 'Mulțumim pentru feedback-ul pozitiv! Ne bucurăm că Bella și dumneavoastră sunteți mulțumiți.',
        'date': DateTime.now().subtract(const Duration(days: 4)),
        'author': 'Echipa Salon',
      },
      'photos': [],
    },
    {
      'id': 'review_3',
      'clientName': 'Elena Dumitrescu',
      'petName': 'Max',
      'rating': 5,
      'comment': 'Incredibil de profesionali! Max era foarte stresat, dar echipa a fost foarte răbdătoare și îngrijitoare. Rezultatul este perfect!',
      'date': DateTime.now().subtract(const Duration(days: 8)),
      'service': 'Tuns și aranjat',
      'groomer': 'Elena Dumitrescu',
      'response': null,
      'photos': ['https://example.com/max_styled.jpg'],
    },
    {
      'id': 'review_4',
      'clientName': 'Andrei Stoica',
      'petName': 'Luna',
      'rating': 3,
      'comment': 'Serviciul a fost ok, dar aș fi dorit mai multă atenție la detalii. Luna arată bine, dar nu excepțional.',
      'date': DateTime.now().subtract(const Duration(days: 12)),
      'service': 'Baie completă',
      'groomer': 'Ana Popescu',
      'response': {
        'text': 'Îmi pare rău că nu am îndeplinit pe deplin așteptările. Vă invităm să reveniți pentru o ședință gratuită de retușuri.',
        'date': DateTime.now().subtract(const Duration(days: 11)),
        'author': 'Ana Popescu',
      },
      'photos': [],
    },
    {
      'id': 'review_5',
      'clientName': 'Ioana Vasile',
      'petName': 'Fluffy',
      'rating': 5,
      'comment': 'Absolut minunat! Fluffy nu a mai fost niciodată atât de frumoasă. Echipa este fantastică și foarte pricepută.',
      'date': DateTime.now().subtract(const Duration(days: 15)),
      'service': 'Pachet complet premium',
      'groomer': 'Andrei Stoica',
      'response': {
        'text': 'Mulțumim din suflet pentru cuvintele frumoase! Ne bucurăm că Fluffy și dumneavoastră sunteți încântați.',
        'date': DateTime.now().subtract(const Duration(days: 14)),
        'author': 'Andrei Stoica',
      },
      'photos': ['https://example.com/fluffy_premium.jpg'],
    },
  ];

  double get averageRating {
    if (_reviews.isEmpty) return 0.0;
    final total = _reviews.fold<double>(0.0, (sum, review) => sum + review['rating']);
    return total / _reviews.length;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      
      appBar: AppBar(
        title: const Text(
          'Recenzii Clienți',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppColors.forestGreen,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: _showReviewAnalytics,
            tooltip: 'Analiză recenzii',
          ),
        ],
      ),
      body: Column(
        children: [
          _buildReviewsHeader(),
          Expanded(
            child: _buildReviewsList(),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewsHeader() {
    final totalReviews = _reviews.length;
    final ratingDistribution = _calculateRatingDistribution();

    return Container(
      padding: const EdgeInsets.all(16),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          averageRating.toStringAsFixed(1),
                          style: const TextStyle(
                            fontSize: 36,
                            fontWeight: FontWeight.bold,
                            color: AppColors.forestGreen,
                          ),
                        ),
                        _buildStarRating(averageRating),
                        const SizedBox(height: 8),
                        Text(
                          '$totalReviews recenzii',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      children: [
                        for (int i = 5; i >= 1; i--)
                          _buildRatingBar(i, ratingDistribution[i] ?? 0, totalReviews),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStarRating(double rating) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        if (index < rating.floor()) {
          return const Icon(Icons.star, color: Colors.amber, size: 20);
        } else if (index < rating) {
          return const Icon(Icons.star_half, color: Colors.amber, size: 20);
        } else {
          return const Icon(Icons.star_border, color: Colors.amber, size: 20);
        }
      }),
    );
  }

  Widget _buildRatingBar(int stars, int count, int total) {
    final percentage = total > 0 ? count / total : 0.0;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text('$stars', style: const TextStyle(fontSize: 12)),
          const SizedBox(width: 4),
          const Icon(Icons.star, color: Colors.amber, size: 12),
          const SizedBox(width: 8),
          Expanded(
            child: LinearProgressIndicator(
              value: percentage,
              backgroundColor: Colors.grey.shade300,
              valueColor: const AlwaysStoppedAnimation<Color>(AppColors.forestGreen),
            ),
          ),
          const SizedBox(width: 8),
          Text('$count', style: const TextStyle(fontSize: 12)),
        ],
      ),
    );
  }

  Map<int, int> _calculateRatingDistribution() {
    final distribution = <int, int>{};
    for (int i = 1; i <= 5; i++) {
      distribution[i] = 0;
    }

    for (final review in _reviews) {
      final rating = review['rating'] as int;
      distribution[rating] = (distribution[rating] ?? 0) + 1;
    }

    return distribution;
  }

  Widget _buildReviewsList() {
    if (_reviews.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.rate_review, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text(
              'Nu există recenzii încă',
              style: TextStyle(fontSize: 18, color: Colors.grey, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'Recenziile clienților vor apărea aici',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _reviews.length,
      itemBuilder: (context, index) {
        final review = _reviews[index];
        return _buildReviewCard(review, index);
      },
    );
  }

  Widget _buildReviewCard(Map<String, dynamic> review, int index) {
    final hasResponse = review['response'] != null;

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with client info and rating
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: AppColors.forestGreen.withValues(alpha: 0.1),
                  child: Text(
                    review['clientName'][0],
                    style: const TextStyle(
                      color: AppColors.forestGreen,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        review['clientName'],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        '${review['petName']} • ${review['service']}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    _buildStarRating(review['rating'].toDouble()),
                    const SizedBox(height: 4),
                    Text(
                      _formatDate(review['date']),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),

            // Review comment
            Text(
              review['comment'],
              style: const TextStyle(
                fontSize: 15,
                height: 1.4,
              ),
            ),

            // Groomer info
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppColors.forestGreen.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Groomer: ${review['groomer']}',
                style: const TextStyle(
                  fontSize: 12,
                  color: AppColors.forestGreen,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),

            // Photos if available
            if (review['photos'].isNotEmpty) ...[
              const SizedBox(height: 12),
              _buildPhotoGallery(review['photos']),
            ],

            // Response section
            if (hasResponse) ...[
              const SizedBox(height: 16),
              _buildResponseSection(review['response']),
            ],

            // Action buttons
            const SizedBox(height: 12),
            Row(
              children: [
                if (!hasResponse)
                  TextButton.icon(
                    onPressed: () => _showResponseDialog(review, index),
                    icon: const Icon(Icons.reply, size: 18),
                    label: const Text('Răspunde'),
                    style: TextButton.styleFrom(
                      foregroundColor: AppColors.forestGreen,
                    ),
                  ),
                const Spacer(),
                PopupMenuButton<String>(
                  onSelected: (value) => _handleReviewAction(value, review, index),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'share',
                      child: Row(
                        children: [
                          Icon(Icons.share, size: 18),
                          SizedBox(width: 8),
                          Text('Partajează'),
                        ],
                      ),
                    ),
                    if (hasResponse)
                      const PopupMenuItem(
                        value: 'edit_response',
                        child: Row(
                          children: [
                            Icon(Icons.edit, size: 18),
                            SizedBox(width: 8),
                            Text('Editează răspuns'),
                          ],
                        ),
                      ),
                    const PopupMenuItem(
                      value: 'report',
                      child: Row(
                        children: [
                          Icon(Icons.flag, size: 18, color: Colors.orange),
                          SizedBox(width: 8),
                          Text('Raportează'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResponseSection(Map<String, dynamic> response) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.forestGreen.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: AppColors.forestGreen.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(
                Icons.reply,
                size: 16,
                color: AppColors.forestGreen,
              ),
              const SizedBox(width: 8),
              Text(
                'Răspuns de la ${response['author']}',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: AppColors.forestGreen,
                ),
              ),
              const Spacer(),
              Text(
                _formatDate(response['date']),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            response['text'],
            style: const TextStyle(
              fontSize: 14,
              height: 1.3,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoGallery(List<String> photos) {
    return SizedBox(
      height: 80,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: photos.length,
        itemBuilder: (context, index) {
          return Container(
            margin: const EdgeInsets.only(right: 8),
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.grey.shade200,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Container(
                color: AppColors.forestGreen.withValues(alpha: 0.1),
                child: const Icon(
                  Icons.pets,
                  color: AppColors.forestGreen,
                  size: 32,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Astăzi';
    } else if (difference.inDays == 1) {
      return 'Ieri';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} zile în urmă';
    } else {
      return DateFormat('dd MMM yyyy').format(date);
    }
  }

  void _showResponseDialog(Map<String, dynamic> review, int index) {
    final TextEditingController responseController = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Răspunde la recenzie'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Recenzie de la ${review['clientName']}:',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  review['comment'],
                  style: const TextStyle(fontStyle: FontStyle.italic),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: responseController,
                maxLines: 4,
                decoration: const InputDecoration(
                  hintText: 'Scrie răspunsul tău aici...',
                  border: OutlineInputBorder(),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Anulează'),
            ),
            ElevatedButton(
              onPressed: () {
                if (responseController.text.trim().isNotEmpty) {
                  _addResponse(review, index, responseController.text.trim());
                  Navigator.of(context).pop();
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.forestGreen,
                foregroundColor: Colors.white,
              ),
              child: const Text('Trimite răspuns'),
            ),
          ],
        );
      },
    );
  }

  void _addResponse(Map<String, dynamic> review, int index, String responseText) {
    setState(() {
      _reviews[index]['response'] = {
        'text': responseText,
        'date': DateTime.now(),
        'author': 'Echipa Salon',
      };
    });

    showTopSnackBar(context, 
      const SnackBar(
        content: Text('Răspunsul a fost adăugat cu succes'),
        backgroundColor: AppColors.forestGreen,
      ),
    );
  }

  void _handleReviewAction(String action, Map<String, dynamic> review, int index) {
    switch (action) {
      case 'share':
        _shareReview(review);
        break;
      case 'edit_response':
        _editResponse(review, index);
        break;
      case 'report':
        _reportReview(review);
        break;
    }
  }

  void _shareReview(Map<String, dynamic> review) {
    showTopSnackBar(context, 
      const SnackBar(
        content: Text('Funcționalitatea de partajare va fi disponibilă în curând'),
        backgroundColor: AppColors.forestGreen,
      ),
    );
  }

  void _editResponse(Map<String, dynamic> review, int index) {
    final TextEditingController responseController = TextEditingController(
      text: review['response']['text'],
    );

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Editează răspuns'),
          content: TextField(
            controller: responseController,
            maxLines: 4,
            decoration: const InputDecoration(
              hintText: 'Editează răspunsul...',
              border: OutlineInputBorder(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Anulează'),
            ),
            ElevatedButton(
              onPressed: () {
                if (responseController.text.trim().isNotEmpty) {
                  setState(() {
                    _reviews[index]['response']['text'] = responseController.text.trim();
                    _reviews[index]['response']['date'] = DateTime.now();
                  });
                  Navigator.of(context).pop();
                  showTopSnackBar(context, 
                    const SnackBar(
                      content: Text('Răspunsul a fost actualizat'),
                      backgroundColor: AppColors.forestGreen,
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.forestGreen,
                foregroundColor: Colors.white,
              ),
              child: const Text('Actualizează'),
            ),
          ],
        );
      },
    );
  }

  void _reportReview(Map<String, dynamic> review) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Raportează recenzie'),
          content: const Text('Ești sigur că vrei să raportezi această recenzie ca fiind necorespunzătoare?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Anulează'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                showTopSnackBar(context, 
                  const SnackBar(
                    content: Text('Recenzia a fost raportată'),
                    backgroundColor: Colors.orange,
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: const Text('Raportează'),
            ),
          ],
        );
      },
    );
  }

  void _showReviewAnalytics() {
    showTopSnackBar(context, 
      const SnackBar(
        content: Text('Analiza detaliată va fi disponibilă în curând'),
        backgroundColor: AppColors.forestGreen,
      ),
    );
  }
}