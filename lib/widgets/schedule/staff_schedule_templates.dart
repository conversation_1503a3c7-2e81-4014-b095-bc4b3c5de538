import 'package:flutter/material.dart';
import '../../config/theme/app_theme.dart';
import '../../models/working_hours_settings.dart';

/// Staff-specific schedule templates for groomers
class StaffScheduleTemplates {
  /// Get all available staff schedule templates
  static List<StaffScheduleTemplate> get allTemplates => _templates;

  /// Get template by name
  static StaffScheduleTemplate? getTemplateByName(String name) {
    try {
      return _templates.firstWhere((template) => template.name == name);
    } catch (e) {
      return null;
    }
  }

  static final List<StaffScheduleTemplate> _templates = [
    StaffScheduleTemplate(
      name: 'Program standard groomer',
      description: 'Lu<PERSON>-Vineri 9:00-17:00\nSâmbătă 10:00-15:00',
      icon: Icons.person_outline,
      color: AppColors.forestGreen,
      suitableForSenior: true,
      suitableForRegular: true,
      suitableForAssistant: true,
      schedule: {
        'monday': const DaySchedule(
          startTime: '09:00',
          endTime: '17:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'tuesday': const DaySchedule(
          startTime: '09:00',
          endTime: '17:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'wednesday': const DaySchedule(
          startTime: '09:00',
          endTime: '17:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'thursday': const DaySchedule(
          startTime: '09:00',
          endTime: '17:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'friday': const DaySchedule(
          startTime: '09:00',
          endTime: '17:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'saturday': const DaySchedule(
          startTime: '10:00',
          endTime: '15:00',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'sunday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
      },
    ),
    StaffScheduleTemplate(
      name: 'Program part-time',
      description: 'Luni, Miercuri, Vineri\n10:00-16:00',
      icon: Icons.schedule,
      color: Colors.blue,
      suitableForSenior: true,
      suitableForRegular: true,
      suitableForAssistant: true,
      schedule: {
        'monday': const DaySchedule(
          startTime: '10:00',
          endTime: '16:00',
          isWorkingDay: true,
          breakStart: '13:00',
          breakEnd: '14:00',
        ),
        'tuesday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
        'wednesday': const DaySchedule(
          startTime: '10:00',
          endTime: '16:00',
          isWorkingDay: true,
          breakStart: '13:00',
          breakEnd: '14:00',
        ),
        'thursday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
        'friday': const DaySchedule(
          startTime: '10:00',
          endTime: '16:00',
          isWorkingDay: true,
          breakStart: '13:00',
          breakEnd: '14:00',
        ),
        'saturday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
        'sunday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
      },
    ),
    StaffScheduleTemplate(
      name: 'Program weekend',
      description: 'Sâmbătă-Duminică\n9:00-18:00',
      icon: Icons.weekend,
      color: Colors.orange,
      suitableForSenior: true,
      suitableForRegular: true,
      suitableForAssistant: false,
      schedule: {
        'monday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
        'tuesday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
        'wednesday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
        'thursday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
        'friday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
        'saturday': const DaySchedule(
          startTime: '09:00',
          endTime: '18:00',
          isWorkingDay: true,
          breakStart: '13:00',
          breakEnd: '14:00',
        ),
        'sunday': const DaySchedule(
          startTime: '09:00',
          endTime: '18:00',
          isWorkingDay: true,
          breakStart: '13:00',
          breakEnd: '14:00',
        ),
      },
    ),
    StaffScheduleTemplate(
      name: 'Program flexibil',
      description: 'Marți-Sâmbătă\n10:00-19:00',
      icon: Icons.access_time,
      color: Colors.purple,
      suitableForSenior: true,
      suitableForRegular: true,
      suitableForAssistant: false,
      schedule: {
        'monday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
        'tuesday': const DaySchedule(
          startTime: '10:00',
          endTime: '19:00',
          isWorkingDay: true,
          breakStart: '13:00',
          breakEnd: '14:00',
        ),
        'wednesday': const DaySchedule(
          startTime: '10:00',
          endTime: '19:00',
          isWorkingDay: true,
          breakStart: '13:00',
          breakEnd: '14:00',
        ),
        'thursday': const DaySchedule(
          startTime: '10:00',
          endTime: '19:00',
          isWorkingDay: true,
          breakStart: '13:00',
          breakEnd: '14:00',
        ),
        'friday': const DaySchedule(
          startTime: '10:00',
          endTime: '19:00',
          isWorkingDay: true,
          breakStart: '13:00',
          breakEnd: '14:00',
        ),
        'saturday': const DaySchedule(
          startTime: '10:00',
          endTime: '19:00',
          isWorkingDay: true,
          breakStart: '13:00',
          breakEnd: '14:00',
        ),
        'sunday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
      },
    ),
    StaffScheduleTemplate(
      name: 'Program asistent',
      description: 'Luni-Vineri 8:00-16:00\nSuport pentru groomeri',
      icon: Icons.support_agent,
      color: Colors.teal,
      suitableForSenior: false,
      suitableForRegular: false,
      suitableForAssistant: true,
      schedule: {
        'monday': const DaySchedule(
          startTime: '08:00',
          endTime: '16:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'tuesday': const DaySchedule(
          startTime: '08:00',
          endTime: '16:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'wednesday': const DaySchedule(
          startTime: '08:00',
          endTime: '16:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'thursday': const DaySchedule(
          startTime: '08:00',
          endTime: '16:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'friday': const DaySchedule(
          startTime: '08:00',
          endTime: '16:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'saturday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
        'sunday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
      },
    ),

    // Non-stop schedule template
    StaffScheduleTemplate(
      name: 'Program non-stop',
      description: 'Deschis permanent 24/7',
      icon: Icons.timelapse,
      color: Colors.redAccent,
      suitableForSenior: true,
      suitableForRegular: true,
      suitableForAssistant: true,
      schedule: {
        'monday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'tuesday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'wednesday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'thursday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'friday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'saturday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'sunday': const DaySchedule(
          startTime: '00:00',
          endTime: '23:59',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
      },
    ),
  ];
}

/// Model for staff schedule templates
class StaffScheduleTemplate {
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final Map<String, DaySchedule> schedule;
  final bool suitableForSenior;
  final bool suitableForRegular;
  final bool suitableForAssistant;

  const StaffScheduleTemplate({
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.schedule,
    this.suitableForSenior = true,
    this.suitableForRegular = true,
    this.suitableForAssistant = true,
  });

  /// Get total working hours per week for this template
  double get totalWeeklyHours {
    double total = 0.0;
    for (final daySchedule in schedule.values) {
      if (daySchedule.isWorkingDay && daySchedule.startTime != null && daySchedule.endTime != null) {
        final start = _parseTime(daySchedule.startTime!);
        final end = _parseTime(daySchedule.endTime!);
        if (start != null && end != null) {
          double dayHours = end.difference(start).inMinutes / 60.0;
          
          // Subtract break time if present
          if (daySchedule.breakStart != null && daySchedule.breakEnd != null) {
            final breakStart = _parseTime(daySchedule.breakStart!);
            final breakEnd = _parseTime(daySchedule.breakEnd!);
            if (breakStart != null && breakEnd != null) {
              dayHours -= breakEnd.difference(breakStart).inMinutes / 60.0;
            }
          }
          
          total += dayHours;
        }
      }
    }
    return total;
  }

  /// Get working days count
  int get workingDaysCount {
    return schedule.values.where((daySchedule) => daySchedule.isWorkingDay).length;
  }

  /// Parse time string to DateTime (today's date with specified time)
  DateTime? _parseTime(String timeString) {
    try {
      final parts = timeString.split(':');
      if (parts.length != 2) return null;
      
      final hour = int.parse(parts[0]);
      final minute = int.parse(parts[1]);
      
      final now = DateTime.now();
      return DateTime(now.year, now.month, now.day, hour, minute);
    } catch (e) {
      return null;
    }
  }
}
