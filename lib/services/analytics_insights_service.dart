import 'package:flutter/material.dart';
import 'analytics_service.dart';
import 'screen_time_service.dart';
import 'retention_analytics_service.dart';
import '../config/api_config.dart';

/// Service for generating analytics insights and tracking business metrics
class AnalyticsInsightsService {
  
  // ========== USER FLOW TRACKING ==========
  
  /// Track complete onboarding flow
  static Future<void> trackOnboardingFlow(String step, {Map<String, dynamic>? stepData}) async {
    await AnalyticsService.trackUserJourney(
      journeyName: 'onboarding',
      stepName: step,
      stepData: stepData,
    );
    
    // Track funnel progression
    final stepIndex = _getOnboardingStepIndex(step);
    await AnalyticsService.trackFunnelEvent(
      funnelName: 'user_onboarding',
      eventName: step,
      stepIndex: stepIndex,
      completed: step == 'completed',
    );
  }
  
  /// Track appointment creation flow
  static Future<void> trackAppointmentCreationFlow(String step, {Map<String, dynamic>? stepData}) async {
    await AnalyticsService.trackUserJourney(
      journeyName: 'appointment_creation',
      stepName: step,
      stepData: stepData,
    );
    
    await AnalyticsService.trackAppointmentWorkflow(
      workflowStep: step,
      workflowData: stepData,
    );
  }
  
  /// Track salon setup flow
  static Future<void> trackSalonSetupFlow(String step, {Map<String, dynamic>? stepData}) async {
    await AnalyticsService.trackUserJourney(
      journeyName: 'salon_setup',
      stepName: step,
      stepData: stepData,
    );
    
    final stepIndex = _getSalonSetupStepIndex(step);
    await AnalyticsService.trackFunnelEvent(
      funnelName: 'salon_setup',
      eventName: step,
      stepIndex: stepIndex,
      completed: step == 'completed',
    );
  }
  
  // ========== BUSINESS METRICS TRACKING ==========
  
  /// Track key business events with comprehensive data
  static Future<void> trackBusinessEvent({
    required String eventType,
    required Map<String, dynamic> eventData,
  }) async {
    // Add session context to all business events
    final sessionStats = ScreenTimeService.getSessionStats();
    final retentionStats = await RetentionAnalyticsService.getRetentionStats();
    
    final enrichedData = {
      ...eventData,
      'session_context': sessionStats,
      'user_context': retentionStats,
      'timestamp': DateTime.now().toIso8601String(),
    };
    
    await AnalyticsService.trackFeatureUsed(
      featureName: 'business_$eventType',
      parameters: enrichedData,
    );
  }
  
  /// Track revenue-related events
  static Future<void> trackRevenueEvent({
    required String eventType, // 'service_purchased', 'subscription_started', etc.
    required double amount,
    String? currency,
    Map<String, dynamic>? additionalData,
  }) async {
    await trackBusinessEvent(
      eventType: 'revenue_$eventType',
      eventData: {
        'amount': amount,
        'currency': currency ?? 'RON',
        'event_type': eventType,
        ...?additionalData,
      },
    );
  }
  
  /// Track user engagement milestones
  static Future<void> trackEngagementMilestone({
    required String milestoneType,
    required int milestoneValue,
    Map<String, dynamic>? milestoneData,
  }) async {
    await trackBusinessEvent(
      eventType: 'engagement_milestone',
      eventData: {
        'milestone_type': milestoneType,
        'milestone_value': milestoneValue,
        ...?milestoneData,
      },
    );
  }
  
  // ========== FEATURE USAGE ANALYTICS ==========
  
  /// Track feature discovery and adoption
  static Future<void> trackFeatureDiscovery({
    required String featureName,
    required String discoveryMethod, // 'navigation', 'tutorial', 'search', etc.
    Map<String, dynamic>? discoveryData,
  }) async {
    await AnalyticsService.trackFeatureUsed(
      featureName: 'feature_discovery',
      parameters: {
        'feature_name': featureName,
        'discovery_method': discoveryMethod,
        ...?discoveryData,
      },
    );
  }
  
  /// Track feature abandonment
  static Future<void> trackFeatureAbandonment({
    required String featureName,
    required String abandonmentPoint,
    String? abandonmentReason,
    Map<String, dynamic>? abandonmentData,
  }) async {
    await AnalyticsService.trackFeatureUsed(
      featureName: 'feature_abandonment',
      parameters: {
        'feature_name': featureName,
        'abandonment_point': abandonmentPoint,
        'abandonment_reason': abandonmentReason,
        ...?abandonmentData,
      },
    );
  }
  
  // ========== ERROR AND PERFORMANCE INSIGHTS ==========
  
  /// Track user frustration indicators
  static Future<void> trackUserFrustration({
    required String frustrationType, // 'multiple_taps', 'back_navigation', 'form_errors', etc.
    required String screenName,
    Map<String, dynamic>? frustrationData,
  }) async {
    await AnalyticsService.trackFeatureUsed(
      featureName: 'user_frustration',
      parameters: {
        'frustration_type': frustrationType,
        'screen_name': screenName,
        ...?frustrationData,
      },
    );
  }
  
  /// Track app performance issues
  static Future<void> trackPerformanceIssue({
    required String issueType,
    required int durationMs,
    String? screenName,
    Map<String, dynamic>? issueData,
  }) async {
    await AnalyticsService.trackPerformance(
      metricName: 'performance_issue',
      durationMs: durationMs,
      parameters: {
        'issue_type': issueType,
        'screen_name': screenName,
        ...?issueData,
      },
    );
  }
  
  // ========== CONVERSION TRACKING ==========
  
  /// Track conversion events
  static Future<void> trackConversion({
    required String conversionType,
    required String funnelName,
    Map<String, dynamic>? conversionData,
  }) async {
    await AnalyticsService.trackFunnelEvent(
      funnelName: funnelName,
      eventName: 'conversion',
      completed: true,
      eventData: {
        'conversion_type': conversionType,
        ...?conversionData,
      },
    );
  }
  
  // ========== HELPER METHODS ==========
  
  static int _getOnboardingStepIndex(String step) {
    const steps = [
      'started',
      'login_completed',
      'profile_created',
      'salon_created',
      'services_configured',
      'first_appointment_created',
      'completed'
    ];
    return steps.indexOf(step);
  }
  
  static int _getSalonSetupStepIndex(String step) {
    const steps = [
      'started',
      'basic_info_entered',
      'address_selected',
      'schedule_configured',
      'services_added',
      'staff_invited',
      'completed'
    ];
    return steps.indexOf(step);
  }
  
  // ========== ANALYTICS SUMMARY ==========
  
  /// Generate analytics summary for debugging/monitoring
  static Future<Map<String, dynamic>> generateAnalyticsSummary() async {
    try {
      final sessionStats = ScreenTimeService.getSessionStats();
      final retentionStats = await RetentionAnalyticsService.getRetentionStats();
      
      return {
        'session_analytics': sessionStats,
        'retention_analytics': retentionStats,
        'analytics_status': {
          'firebase_enabled': true,
          'screen_time_tracking': true,
          'retention_tracking': true,
          'business_metrics': true,
        },
        'generated_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to generate analytics summary: $e');
      }
      return {
        'error': e.toString(),
        'generated_at': DateTime.now().toIso8601String(),
      };
    }
  }
}
