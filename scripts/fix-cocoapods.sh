#!/bin/bash
# Fix CocoaPods installation issues - SIMPLE VERSION
# This script uses system Ruby to avoid version conflicts

set -e

echo "🔧 Simple CocoaPods Fix"
echo "======================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    print_error "Not in Flutter project root. Please run from flutter-partykids directory."
    exit 1
fi

print_info "Using system Ruby to avoid version conflicts..."

# Method 1: Use system Ruby (most reliable)
print_info "Installing CocoaPods with system Ruby..."
sudo /usr/bin/gem install cocoapods --no-document

# Method 2: If system Ruby fails, try homebrew with specific path
if ! /usr/local/bin/pod --version > /dev/null 2>&1 && ! pod --version > /dev/null 2>&1; then
    print_warning "System Ruby failed, trying alternative method..."

    # Clean install with homebrew Ruby
    brew install cocoapods || true

    # Add to PATH
    export PATH="/opt/homebrew/bin:/usr/local/bin:$PATH"
fi

# Verify installation
print_info "Verifying CocoaPods installation..."
if /usr/local/bin/pod --version > /dev/null 2>&1; then
    POD_VERSION=$(/usr/local/bin/pod --version)
    print_success "CocoaPods installed successfully: $POD_VERSION"
    POD_CMD="/usr/local/bin/pod"
elif pod --version > /dev/null 2>&1; then
    POD_VERSION=$(pod --version)
    print_success "CocoaPods installed successfully: $POD_VERSION"
    POD_CMD="pod"
else
    print_error "CocoaPods installation failed completely"
    print_info "Try manually: brew install cocoapods"
    exit 1
fi

# Navigate to iOS directory and clean up
cd ios

# Remove old pods and lock file
print_info "Cleaning up old iOS dependencies..."
rm -rf Pods
rm -f Podfile.lock

# Install pods with the working pod command
print_info "Installing iOS dependencies..."
if $POD_CMD install; then
    print_success "iOS dependencies installed successfully"
else
    print_error "Failed to install iOS dependencies"

    # Try with repo update
    print_info "Retrying with repository update..."
    $POD_CMD install --repo-update
fi

cd ..

print_success "CocoaPods fix completed!"
echo ""
print_info "Next steps:"
echo "1. Try running the deployment: ./scripts/deploy-ios.sh"
echo "2. Or test the build: flutter build ios --no-codesign"
