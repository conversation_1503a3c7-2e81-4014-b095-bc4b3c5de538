import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:provider/provider.dart';

import 'package:partykidsapp/widgets/calendar_views/draggable_appointment_block.dart';
import 'package:partykidsapp/models/appointment.dart';
import 'package:partykidsapp/providers/calendar_provider.dart';

// Generate mocks
@GenerateMocks([CalendarProvider])
import 'draggable_appointment_block_test.mocks.dart';

void main() {
  group('DraggableAppointmentBlock', () {
    late MockCalendarProvider mockCalendarProvider;
    late Appointment testAppointment;

    setUp(() {
      mockCalendarProvider = MockCalendarProvider();
      testAppointment = Appointment(
        id: 'test-id',
        clientId: 'client-1',
        clientName: 'John <PERSON>',
        clientPhone: '+40123456789',
        petId: 'pet-1',
        petName: 'Buddy',
        petSpecies: 'Dog',
        service: 'Grooming',
        startTime: DateTime(2024, 1, 15, 10, 0),
        endTime: DateTime(2024, 1, 15, 11, 0),
        status: 'SCHEDULED',
        isPaid: false,
        groomerId: 'groomer-1',
        assignedGroomer: 'Jane Smith',
      );
    });

    Widget createTestWidget({
      bool isDragEnabled = true,
      Function(AppointmentDragData)? onDragStarted,
      Function(AppointmentDragData)? onDragCompleted,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: ChangeNotifierProvider<CalendarProvider>.value(
            value: mockCalendarProvider,
            child: DraggableAppointmentBlock(
              appointment: testAppointment,
              height: 60,
              isDragEnabled: isDragEnabled,
              onDragStarted: onDragStarted,
              onDragCompleted: onDragCompleted,
            ),
          ),
        ),
      );
    }

    testWidgets('renders appointment block when drag is disabled', (tester) async {
      await tester.pumpWidget(createTestWidget(isDragEnabled: false));

      expect(find.text('John Doe'), findsOneWidget);
      expect(find.text('Grooming'), findsOneWidget);
    });

    testWidgets('renders draggable appointment block when drag is enabled', (tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.byType(LongPressDraggable<AppointmentDragData>), findsOneWidget);
      expect(find.text('John Doe'), findsOneWidget);
    });

    testWidgets('triggers onDragStarted callback when drag starts', (tester) async {
      AppointmentDragData? capturedDragData;
      
      await tester.pumpWidget(createTestWidget(
        onDragStarted: (data) => capturedDragData = data,
      ));

      final draggable = find.byType(LongPressDraggable<AppointmentDragData>);
      await tester.longPress(draggable);
      await tester.pump();

      expect(capturedDragData, isNotNull);
      expect(capturedDragData!.appointment.id, equals('test-id'));
      expect(capturedDragData!.originalStaffId, equals('groomer-1'));
    });

    testWidgets('shows drag feedback during drag operation', (tester) async {
      await tester.pumpWidget(createTestWidget());

      final draggable = find.byType(LongPressDraggable<AppointmentDragData>);
      await tester.longPress(draggable);
      await tester.pump();

      // Start dragging
      await tester.drag(draggable, const Offset(100, 0));
      await tester.pump();

      // Verify feedback widget is shown
      expect(find.text('10:00 - 11:00'), findsWidgets);
      expect(find.text('John Doe'), findsWidgets);
    });

    testWidgets('shows placeholder when dragging', (tester) async {
      await tester.pumpWidget(createTestWidget());

      final draggable = find.byType(LongPressDraggable<AppointmentDragData>);
      await tester.longPress(draggable);
      await tester.pump();

      // Start dragging
      await tester.drag(draggable, const Offset(100, 0));
      await tester.pump();

      // Verify placeholder is shown
      expect(find.byIcon(Icons.drag_indicator), findsOneWidget);
    });

    testWidgets('triggers onDragCompleted callback when drag ends', (tester) async {
      AppointmentDragData? capturedDragData;
      
      await tester.pumpWidget(createTestWidget(
        onDragCompleted: (data) => capturedDragData = data,
      ));

      final draggable = find.byType(LongPressDraggable<AppointmentDragData>);
      await tester.longPress(draggable);
      await tester.pump();

      // Complete the drag
      await tester.drag(draggable, const Offset(100, 0));
      await tester.pumpAndSettle();

      expect(capturedDragData, isNotNull);
      expect(capturedDragData!.appointment.id, equals('test-id'));
    });

    testWidgets('handles mouse hover effects', (tester) async {
      await tester.pumpWidget(createTestWidget());

      final mouseRegion = find.byType(MouseRegion);
      expect(mouseRegion, findsOneWidget);

      // Simulate mouse enter
      await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
        'flutter/mousecursor',
        null,
        (data) {},
      );
    });

    testWidgets('creates correct drag data', (tester) async {
      AppointmentDragData? capturedDragData;
      
      await tester.pumpWidget(createTestWidget(
        onDragStarted: (data) => capturedDragData = data,
      ));

      final draggable = find.byType(LongPressDraggable<AppointmentDragData>);
      await tester.longPress(draggable);
      await tester.pump();

      expect(capturedDragData!.appointment, equals(testAppointment));
      expect(capturedDragData!.originalDate, equals(testAppointment.startTime));
      expect(capturedDragData!.originalStaffId, equals('groomer-1'));
    });

    testWidgets('handles appointment without groomer ID', (tester) async {
      final appointmentWithoutGroomer = testAppointment.copyWith(
        groomerId: null,
        assignedGroomer: '',
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ChangeNotifierProvider<CalendarProvider>.value(
              value: mockCalendarProvider,
              child: DraggableAppointmentBlock(
                appointment: appointmentWithoutGroomer,
                height: 60,
                isDragEnabled: true,
              ),
            ),
          ),
        ),
      );

      expect(find.byType(LongPressDraggable<AppointmentDragData>), findsOneWidget);
    });

    testWidgets('respects height constraints', (tester) async {
      await tester.pumpWidget(createTestWidget());

      final container = find.byType(Container).first;
      final containerWidget = tester.widget<Container>(container);
      
      // The height should be applied to the appointment block
      expect(find.byType(DraggableAppointmentBlock), findsOneWidget);
    });

    testWidgets('handles tap events when provided', (tester) async {
      bool tapped = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ChangeNotifierProvider<CalendarProvider>.value(
              value: mockCalendarProvider,
              child: DraggableAppointmentBlock(
                appointment: testAppointment,
                height: 60,
                onTap: () => tapped = true,
                isDragEnabled: true,
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.byType(DraggableAppointmentBlock));
      await tester.pump();

      expect(tapped, isTrue);
    });
  });

  group('AppointmentDragData', () {
    test('creates instance with correct properties', () {
      final appointment = Appointment(
        id: 'test-id',
        clientId: 'client-1',
        clientName: 'John Doe',
        clientPhone: '+40123456789',
        petId: 'pet-1',
        petName: 'Buddy',
        petSpecies: 'Dog',
        service: 'Grooming',
        startTime: DateTime(2024, 1, 15, 10, 0),
        endTime: DateTime(2024, 1, 15, 11, 0),
        status: 'SCHEDULED',
        isPaid: false,
        groomerId: 'groomer-1',
      );

      final dragData = AppointmentDragData(
        appointment: appointment,
        originalDate: appointment.startTime,
        originalStaffId: 'groomer-1',
      );

      expect(dragData.appointment, equals(appointment));
      expect(dragData.originalDate, equals(appointment.startTime));
      expect(dragData.originalStaffId, equals('groomer-1'));
    });
  });

  group('DropZoneIndicator', () {
    testWidgets('shows inactive state correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DropZoneIndicator(
              isActive: false,
              isValid: true,
              child: Container(
                width: 100,
                height: 100,
                color: Colors.blue,
              ),
            ),
          ),
        ),
      );

      expect(find.byType(Container), findsOneWidget);
      // Should not show any animation when inactive
    });

    testWidgets('shows active valid state correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DropZoneIndicator(
              isActive: true,
              isValid: true,
              child: Container(
                width: 100,
                height: 100,
                color: Colors.blue,
              ),
            ),
          ),
        ),
      );

      await tester.pump();
      
      expect(find.byType(AnimatedBuilder), findsOneWidget);
      expect(find.byType(Transform), findsOneWidget);
    });

    testWidgets('shows active invalid state correctly', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: DropZoneIndicator(
              isActive: true,
              isValid: false,
              child: Container(
                width: 100,
                height: 100,
                color: Colors.blue,
              ),
            ),
          ),
        ),
      );

      await tester.pump();
      
      expect(find.byType(AnimatedBuilder), findsOneWidget);
      // Should show red border for invalid state
    });
  });
}
