import 'package:flutter_test/flutter_test.dart';
import 'package:partykidsapp/services/staff_working_hours_service.dart';
import 'package:partykidsapp/models/staff_working_hours_settings.dart';
import 'package:partykidsapp/models/working_hours_settings.dart';

void main() {
  group('StaffWorkingHoursService', () {
    group('Validation', () {
      test('should validate working hours request correctly', () {
        // Valid request
        final validRequest = UpdateStaffWorkingHoursRequest(
          weeklySchedule: {
            'monday': const DaySchedule(
              startTime: '09:00',
              endTime: '17:00',
              isWorkingDay: true,
              breakStart: '12:00',
              breakEnd: '13:00',
            ),
            'tuesday': const DaySchedule(
              startTime: null,
              endTime: null,
              isWorkingDay: false,
              breakStart: null,
              breakEnd: null,
            ),
          },
          holidays: [
            Holiday(
              name: 'Anul Nou',
              date: DateTime(2024, 1, 1),
              isWorkingDay: false,
              type: HolidayType.legal,
            ),
          ],
          customClosures: [
            CustomClosure(
              reason: 'Concediu medical',
              date: DateTime(2024, 6, 15),
              description: 'Recuperare',
            ),
          ],
        );

        final validationError = StaffWorkingHoursService.validateStaffWorkingHoursRequest(validRequest);
        expect(validationError, isNull);
      });

      test('should reject invalid time formats', () {
        final invalidRequest = UpdateStaffWorkingHoursRequest(
          weeklySchedule: {
            'monday': const DaySchedule(
              startTime: '25:00', // Invalid hour
              endTime: '17:00',
              isWorkingDay: true,
              breakStart: null,
              breakEnd: null,
            ),
          },
          holidays: [],
          customClosures: [],
        );

        final validationError = StaffWorkingHoursService.validateStaffWorkingHoursRequest(invalidRequest);
        expect(validationError, isNotNull);
        expect(validationError, contains('nu este validă'));
      });

      test('should reject end time before start time', () {
        final invalidRequest = UpdateStaffWorkingHoursRequest(
          weeklySchedule: {
            'monday': const DaySchedule(
              startTime: '17:00',
              endTime: '09:00', // End before start
              isWorkingDay: true,
              breakStart: null,
              breakEnd: null,
            ),
          },
          holidays: [],
          customClosures: [],
        );

        final validationError = StaffWorkingHoursService.validateStaffWorkingHoursRequest(invalidRequest);
        expect(validationError, isNotNull);
        expect(validationError, contains('înainte de ora de sfârșit'));
      });

      test('should reject break times outside working hours', () {
        final invalidRequest = UpdateStaffWorkingHoursRequest(
          weeklySchedule: {
            'monday': const DaySchedule(
              startTime: '09:00',
              endTime: '17:00',
              isWorkingDay: true,
              breakStart: '08:00', // Break before work starts
              breakEnd: '09:00',
            ),
          },
          holidays: [],
          customClosures: [],
        );

        final validationError = StaffWorkingHoursService.validateStaffWorkingHoursRequest(invalidRequest);
        expect(validationError, isNotNull);
        expect(validationError, contains('în timpul programului de lucru'));
      });

      test('should reject break end before break start', () {
        final invalidRequest = UpdateStaffWorkingHoursRequest(
          weeklySchedule: {
            'monday': const DaySchedule(
              startTime: '09:00',
              endTime: '17:00',
              isWorkingDay: true,
              breakStart: '13:00',
              breakEnd: '12:00', // Break end before start
            ),
          },
          holidays: [],
          customClosures: [],
        );

        final validationError = StaffWorkingHoursService.validateStaffWorkingHoursRequest(invalidRequest);
        expect(validationError, isNotNull);
        expect(validationError, contains('înainte de ora de sfârșit'));
      });

      test('should reject empty holiday names', () {
        final invalidRequest = UpdateStaffWorkingHoursRequest(
          weeklySchedule: {
            'monday': const DaySchedule(
              startTime: '09:00',
              endTime: '17:00',
              isWorkingDay: true,
              breakStart: null,
              breakEnd: null,
            ),
          },
          holidays: [
            Holiday(
              name: '', // Empty name
              date: DateTime(2024, 1, 1),
              isWorkingDay: false,
              type: HolidayType.legal,
            ),
          ],
          customClosures: [],
        );

        final validationError = StaffWorkingHoursService.validateStaffWorkingHoursRequest(invalidRequest);
        expect(validationError, isNotNull);
        expect(validationError, contains('Numele sărbătorii nu poate fi gol'));
      });

      test('should reject empty custom closure reasons', () {
        final invalidRequest = UpdateStaffWorkingHoursRequest(
          weeklySchedule: {
            'monday': const DaySchedule(
              startTime: '09:00',
              endTime: '17:00',
              isWorkingDay: true,
              breakStart: null,
              breakEnd: null,
            ),
          },
          holidays: [],
          customClosures: [
            CustomClosure(
              reason: '', // Empty reason
              date: DateTime(2024, 6, 15),
              description: 'Test',
            ),
          ],
        );

        final validationError = StaffWorkingHoursService.validateStaffWorkingHoursRequest(invalidRequest);
        expect(validationError, isNotNull);
        expect(validationError, contains('Motivul închiderii nu poate fi gol'));
      });
    });

    group('Availability Checking', () {
      test('should check staff availability correctly', () async {
        // Note: This test would require mocking the API service
        // For now, we test the logic without actual API calls
        
        // Create a mock settings object
        final settings = StaffWorkingHoursSettings(
          staffId: 'staff123',
          salonId: 'salon456',
          weeklySchedule: {
            'monday': const DaySchedule(
              startTime: '09:00',
              endTime: '17:00',
              isWorkingDay: true,
              breakStart: '12:00',
              breakEnd: '13:00',
            ),
            'tuesday': const DaySchedule(
              startTime: null,
              endTime: null,
              isWorkingDay: false,
              breakStart: null,
              breakEnd: null,
            ),
          },
          holidays: [],
          customClosures: [],
          updatedAt: DateTime.now(),
        );

        // Test availability logic
        final mondayMorning = DateTime(2024, 1, 8, 10, 0); // Monday 10:00 AM
        expect(settings.isAvailableAtTime(mondayMorning), isTrue);

        final mondayBreak = DateTime(2024, 1, 8, 12, 30); // Monday 12:30 PM (break)
        expect(settings.isAvailableAtTime(mondayBreak), isFalse);

        final tuesdayMorning = DateTime(2024, 1, 9, 10, 0); // Tuesday (day off)
        expect(settings.isAvailableAtTime(tuesdayMorning), isFalse);
      });
    });

    group('Request Creation', () {
      test('should create update request from settings', () {
        final settings = StaffWorkingHoursSettings(
          staffId: 'staff123',
          salonId: 'salon456',
          weeklySchedule: {
            'monday': const DaySchedule(
              startTime: '09:00',
              endTime: '17:00',
              isWorkingDay: true,
              breakStart: '12:00',
              breakEnd: '13:00',
            ),
          },
          holidays: [
            Holiday(
              name: 'Test Holiday',
              date: DateTime(2024, 1, 1),
              isWorkingDay: false,
              type: HolidayType.legal,
            ),
          ],
          customClosures: [
            CustomClosure(
              reason: 'Test Closure',
              date: DateTime(2024, 6, 15),
              description: 'Test Description',
            ),
          ],
          updatedAt: DateTime.now(),
        );

        final request = UpdateStaffWorkingHoursRequest.fromStaffWorkingHoursSettings(settings);

        expect(request.weeklySchedule, equals(settings.weeklySchedule));
        expect(request.holidays, equals(settings.holidays));
        expect(request.customClosures, equals(settings.customClosures));
      });

      test('should serialize request to JSON correctly', () {
        final request = UpdateStaffWorkingHoursRequest(
          weeklySchedule: {
            'monday': const DaySchedule(
              startTime: '09:00',
              endTime: '17:00',
              isWorkingDay: true,
              breakStart: '12:00',
              breakEnd: '13:00',
            ),
          },
          holidays: [
            Holiday(
              name: 'Test Holiday',
              date: DateTime(2024, 1, 1),
              isWorkingDay: false,
              type: HolidayType.legal,
            ),
          ],
          customClosures: [
            CustomClosure(
              reason: 'Test Closure',
              date: DateTime(2024, 6, 15),
              description: 'Test Description',
            ),
          ],
        );

        final json = request.toJson();

        expect(json['weeklySchedule'], isA<Map<String, dynamic>>());
        expect(json['holidays'], isA<List>());
        expect(json['customClosures'], isA<List>());

        // Check specific values
        final mondaySchedule = json['weeklySchedule']['monday'] as Map<String, dynamic>;
        expect(mondaySchedule['startTime'], equals('09:00'));
        expect(mondaySchedule['endTime'], equals('17:00'));
        expect(mondaySchedule['isWorkingDay'], isTrue);

        final holidays = json['holidays'] as List;
        expect(holidays.length, equals(1));
        expect(holidays[0]['name'], equals('Test Holiday'));

        final closures = json['customClosures'] as List;
        expect(closures.length, equals(1));
        expect(closures[0]['reason'], equals('Test Closure'));
      });
    });

    group('Edge Cases', () {
      test('should handle null and empty values correctly', () {
        final request = UpdateStaffWorkingHoursRequest(
          weeklySchedule: {},
          holidays: [],
          customClosures: [],
        );

        final validationError = StaffWorkingHoursService.validateStaffWorkingHoursRequest(request);
        expect(validationError, isNull); // Empty schedule should be valid

        final json = request.toJson();
        expect(json['weeklySchedule'], isEmpty);
        expect(json['holidays'], isEmpty);
        expect(json['customClosures'], isEmpty);
      });

      test('should handle day off correctly', () {
        final request = UpdateStaffWorkingHoursRequest(
          weeklySchedule: {
            'sunday': const DaySchedule(
              startTime: null,
              endTime: null,
              isWorkingDay: false,
              breakStart: null,
              breakEnd: null,
            ),
          },
          holidays: [],
          customClosures: [],
        );

        final validationError = StaffWorkingHoursService.validateStaffWorkingHoursRequest(request);
        expect(validationError, isNull); // Day off should be valid
      });
    });
  });
}
