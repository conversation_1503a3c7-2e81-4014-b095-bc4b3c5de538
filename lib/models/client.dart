class Client {
  final String id;
  final String name;
  final String phone;
  final String email;
  final String address;
  final DateTime registrationDate;
  final List<String> kidIds; // References to kids
  final String notes;
  final bool isActive; // New field from API
  final int kidCount; // New field from API
  final DateTime? updatedAt; // New field from API

  Client({
    required this.id,
    required this.name,
    required this.phone,
    this.email = '',
    this.address = '',
    required this.registrationDate,
    this.kidIds = const [],
    this.notes = '',
    this.isActive = true,
    this.kidCount = 0,
    this.updatedAt,
  });

  // Convert from JSON
  factory Client.fromJson(Map<String, dynamic> json) {
    return Client(
      id: json['id']?.toString() ?? json['clientId']?.toString() ?? '',
      name: json['name'] ?? '',
      phone: json['phone'] ?? '',
      email: json['email'] ?? '',
      address: json['address'] ?? '',
      registrationDate: DateTime.parse(json['registrationDate'] ?? json['createdAt'] ?? DateTime.now().toIso8601String()),
      kidIds: List<String>.from(json['kidIds'] ?? json['petIds'] ?? []),
      notes: json['notes'] ?? '',
      isActive: json['isActive'] ?? true,
      kidCount: json['kidCount'] ?? json['petCount'] ?? 0,
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phone': phone,
      'email': email,
      'address': address,
      'registrationDate': registrationDate.toIso8601String(),
      'kidIds': kidIds,
      'notes': notes,
      'isActive': isActive,
      'kidCount': kidCount,
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  // Copy with method for immutable updates
  Client copyWith({
    String? id,
    String? name,
    String? phone,
    String? email,
    String? address,
    DateTime? registrationDate,
    List<String>? kidIds,
    String? notes,
    bool? isActive,
    int? kidCount,
    DateTime? updatedAt,
  }) {
    return Client(
      id: id ?? this.id,
      name: name ?? this.name,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      address: address ?? this.address,
      registrationDate: registrationDate ?? this.registrationDate,
      kidIds: kidIds ?? this.kidIds,
      notes: notes ?? this.notes,
      isActive: isActive ?? this.isActive,
      kidCount: kidCount ?? this.kidCount,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
