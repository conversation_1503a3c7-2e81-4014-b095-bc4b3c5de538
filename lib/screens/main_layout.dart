import 'package:partykidsapp/screens/profile/settings/profile_screen.dart';
import 'package:flutter/material.dart';
import 'package:partykidsapp/screens/reports/sales_screen.dart';
import 'package:provider/provider.dart';
import '../providers/role_provider.dart';
import '../services/feature_toggle_service.dart';
import 'appointments/calendar_screen.dart';
import 'clients/clients_screen.dart';
import 'notifications_screen.dart';

class MainLayout extends StatefulWidget {
  const MainLayout({super.key});

  /// Global key to access state from outside the widget tree
  static final GlobalKey<_MainLayoutState> globalKey =
      GlobalKey<_MainLayoutState>();

  /// Convenience method to open an appointment in the calendar tab
  static void openAppointmentInCalendar(String appointmentId) {
    globalKey.currentState?._openAppointmentInCalendar(appointmentId);
  }

  @override
  State<MainLayout> createState() => _MainLayoutState();
}

class _MainLayoutState extends State<MainLayout> {
  int _currentIndex = 0;
  bool _sellScreenEnabled = false;
  bool? _previousHasSalonAssociation; // Track previous salon association state
  final ValueNotifier<int> _unreadCount = ValueNotifier<int>(0);

  @override
  void initState() {
    super.initState();
    _loadFeatureToggles();
  }

  Future<void> _loadFeatureToggles() async {
    final enabled = await FeatureToggleService.isSellScreenEnabled();
    if (mounted) {
      setState(() {
        _sellScreenEnabled = enabled;
      });
    }
  }

  @override
  void dispose() {
    _unreadCount.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<RoleProvider>(
      builder: (context, roleProvider, child) {
        final availableScreens = _getAvailableScreens(roleProvider);
        final availableNavItems = _getAvailableNavItems(roleProvider);

        // Handle salon association changes
        final currentHasSalonAssociation = roleProvider.hasSalonAssociation;

        // Check if user just gained salon association (completed onboarding)
        if (_previousHasSalonAssociation == false && currentHasSalonAssociation == true) {
          _currentIndex = 0; // Calendar is always first when available
        }

        // Update the previous state for next comparison
        _previousHasSalonAssociation = currentHasSalonAssociation;

        if (!currentHasSalonAssociation) {
          // Find the profile tab index (it's always the last tab)
          final profileIndex = availableScreens.length - 1;
          _currentIndex = profileIndex;
        } else {
          // Ensure current index is valid for available screens
          if (_currentIndex >= availableScreens.length) {
            _currentIndex = 0;
          }
        }

        final notificationsIndex =
            availableScreens.indexWhere((s) => s is NotificationsScreen);

        return Scaffold(
          body: IndexedStack(
            index: _currentIndex,
            children: availableScreens,
          ),
          // Hide bottom navigation completely during onboarding
          bottomNavigationBar: roleProvider.hasSalonAssociation
              ? BottomNavigationBar(
                  currentIndex: _currentIndex,
                  onTap: (index) {
                    setState(() => _currentIndex = index);
                    if (index == notificationsIndex) {
                      NotificationsScreen.loadHistory();
                    }
                  },
                  type: BottomNavigationBarType.fixed,
                  // Let Material 3 handle the colors automatically
                  // backgroundColor, selectedItemColor, unselectedItemColor will be set by theme
                  items: availableNavItems,
                )
              : null, // Completely hide bottom navigation during onboarding
        );
      },
    );
  }

  List<Widget> _getAvailableScreens(RoleProvider roleProvider) {
    final List<Widget> screens = [];

    // Calendar is only available if user has salon association
    if (roleProvider.hasSalonAssociation) {
      screens.add(CalendarScreen(key: CalendarScreen.globalKey));
    }

    // Clients screen - requires client data access AND salon association
    if (roleProvider.hasSalonAssociation && roleProvider.canAccessClientData) {
      screens.add(const ClientsScreen());
    }

    // Sales screen - requires management access, salon association and toggle
    if (_sellScreenEnabled &&
        roleProvider.hasSalonAssociation &&
        roleProvider.canViewSalesData()) {
      screens.add(const SalesScreen());
    }

    // Notifications is always available
    screens.add(NotificationsScreen(
      key: NotificationsScreen.globalKey,
      unreadCountNotifier: _unreadCount,
    ));

    // Profile is always available
    screens.add(const ProfileScreen());
    return screens;
  }

  List<BottomNavigationBarItem> _getAvailableNavItems(RoleProvider roleProvider) {
    final List<BottomNavigationBarItem> items = [];

    // Calendar is only available if user has salon association
    if (roleProvider.hasSalonAssociation) {
      items.add(const BottomNavigationBarItem(
        icon: Icon(Icons.calendar_today),
        label: 'Calendar',
      ));
    }

    // Clients screen - requires client data access AND salon association
    if (roleProvider.hasSalonAssociation && roleProvider.canAccessClientData) {
      items.add(const BottomNavigationBarItem(
        icon: Icon(Icons.people),
        label: 'Clienti',
      ));
    }

    // Sales screen - requires management access, salon association and toggle
    if (_sellScreenEnabled &&
        roleProvider.hasSalonAssociation &&
        roleProvider.canViewSalesData()) {
      items.add(const BottomNavigationBarItem(
        icon: Icon(Icons.shopping_cart),
        label: 'Vanzari',
      ));
    }

    // Notifications is always available
    items.add(BottomNavigationBarItem(
      icon: _buildNotificationsIcon(),
      label: 'Notificari',
    ));

    // Profile is always available
    items.add(const BottomNavigationBarItem(
      icon: Icon(Icons.person),
      label: 'Profile',
    ));

    return items;
  }

  Widget _buildNotificationsIcon() {
    return ValueListenableBuilder<int>(
      valueListenable: _unreadCount,
      builder: (context, count, child) {
        return Stack(
          clipBehavior: Clip.none,
          children: [
            const Icon(Icons.notifications),
            if (count > 0)
              Positioned(
                right: -6,
                top: -4,
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  constraints: const BoxConstraints(minWidth: 16, minHeight: 16),
                  child: Text(
                    '$count',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  /// Switch to the calendar tab and open the given appointment
  void _openAppointmentInCalendar(String appointmentId) {
    setState(() {
      _currentIndex = 0; // Calendar tab is always first when available
    });
    CalendarScreen.openAppointment(appointmentId);
  }


}
