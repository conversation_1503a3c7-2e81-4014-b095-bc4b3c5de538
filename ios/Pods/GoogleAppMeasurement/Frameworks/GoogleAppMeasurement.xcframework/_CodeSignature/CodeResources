<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ios-arm64/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<data>
		r2oTqJZIqIT2rNJX0ZRI23HQqKQ=
		</data>
		<key>ios-arm64/GoogleAppMeasurement.framework/Info.plist</key>
		<data>
		fj3SZQeCTxjytShkKyhRqwkbxxs=
		</data>
		<key>ios-arm64/GoogleAppMeasurement.framework/Modules/module.modulemap</key>
		<data>
		on1mkulwTtm+ufPJ4eClavLWAuQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurement.framework/Versions/A/GoogleAppMeasurement</key>
		<data>
		tV3IY+pKf5dpchmocNNlU5A1jrs=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurement.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		on1mkulwTtm+ufPJ4eClavLWAuQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurement.framework/Versions/A/Resources/Info.plist</key>
		<data>
		Kh1Yq/+FFpyaVcPgOlaaBd5v5B8=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<data>
		XM/NAx4vNNgHHlTamYSNz6kCpXE=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurement.framework/Info.plist</key>
		<data>
		SIfoKOJijISlSw/qmv4pL4X+SdY=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurement.framework/Modules/module.modulemap</key>
		<data>
		on1mkulwTtm+ufPJ4eClavLWAuQ=
		</data>
		<key>macos-arm64_x86_64/GoogleAppMeasurement.framework/Versions/A/GoogleAppMeasurement</key>
		<data>
		EdsNHNZIPa85hNzygJikH+TzNuo=
		</data>
		<key>macos-arm64_x86_64/GoogleAppMeasurement.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		on1mkulwTtm+ufPJ4eClavLWAuQ=
		</data>
		<key>macos-arm64_x86_64/GoogleAppMeasurement.framework/Versions/A/Resources/Info.plist</key>
		<data>
		yUv7cR5/GBGFKKmNJ60XGpIDBt0=
		</data>
		<key>tvos-arm64/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<data>
		sOGzPOrhboHRYjM15shciGecMyE=
		</data>
		<key>tvos-arm64/GoogleAppMeasurement.framework/Info.plist</key>
		<data>
		02RRcShk3XEm4aj6TyZWf9UglqQ=
		</data>
		<key>tvos-arm64/GoogleAppMeasurement.framework/Modules/module.modulemap</key>
		<data>
		on1mkulwTtm+ufPJ4eClavLWAuQ=
		</data>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<data>
		l+3I18pqDVeCFJqj6ApjF1PSJbk=
		</data>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurement.framework/Info.plist</key>
		<data>
		wH4tTyaxcXrQyTFrOeD31Imnw68=
		</data>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurement.framework/Modules/module.modulemap</key>
		<data>
		on1mkulwTtm+ufPJ4eClavLWAuQ=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ios-arm64/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<dict>
			<key>hash</key>
			<data>
			r2oTqJZIqIT2rNJX0ZRI23HQqKQ=
			</data>
			<key>hash2</key>
			<data>
			XHIzB9XOQR/vL0vaP7oKy7yoP79FLNvTUHxD+Pnp9LE=
			</data>
		</dict>
		<key>ios-arm64/GoogleAppMeasurement.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			fj3SZQeCTxjytShkKyhRqwkbxxs=
			</data>
			<key>hash2</key>
			<data>
			6E0cW/TvoPyXiDlMJyBfqjLg0DLOxP4JwQ08Z42rerA=
			</data>
		</dict>
		<key>ios-arm64/GoogleAppMeasurement.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			on1mkulwTtm+ufPJ4eClavLWAuQ=
			</data>
			<key>hash2</key>
			<data>
			gd8e5hMrJihnXu8TQ0xecVPl3Z71GpsKaYxiLbmHezw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/GoogleAppMeasurement</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurement.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurement.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurement.framework/Versions/A/GoogleAppMeasurement</key>
		<dict>
			<key>hash</key>
			<data>
			tV3IY+pKf5dpchmocNNlU5A1jrs=
			</data>
			<key>hash2</key>
			<data>
			YBmjEiQ7fUIKH6UNKOgeT7qJRl40YGaP3Wb7FqpR3LQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurement.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			on1mkulwTtm+ufPJ4eClavLWAuQ=
			</data>
			<key>hash2</key>
			<data>
			gd8e5hMrJihnXu8TQ0xecVPl3Z71GpsKaYxiLbmHezw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurement.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			Kh1Yq/+FFpyaVcPgOlaaBd5v5B8=
			</data>
			<key>hash2</key>
			<data>
			5UTrUxg8MpPDiLhAJxnu2QJ4nIEO4eyBQDMlCUGfUMQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurement.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<dict>
			<key>hash</key>
			<data>
			XM/NAx4vNNgHHlTamYSNz6kCpXE=
			</data>
			<key>hash2</key>
			<data>
			K5LoleI4N4PI/OEAnG1ZsvHvoLaw9NRsIo6+EbBwhk4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurement.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			SIfoKOJijISlSw/qmv4pL4X+SdY=
			</data>
			<key>hash2</key>
			<data>
			BtLfTpAEB9yIVtTNwYXpYDcSzJ5cWBJwZqtcELljHnI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurement.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			on1mkulwTtm+ufPJ4eClavLWAuQ=
			</data>
			<key>hash2</key>
			<data>
			gd8e5hMrJihnXu8TQ0xecVPl3Z71GpsKaYxiLbmHezw=
			</data>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/GoogleAppMeasurement</string>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurement.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurement.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurement.framework/Versions/A/GoogleAppMeasurement</key>
		<dict>
			<key>hash</key>
			<data>
			EdsNHNZIPa85hNzygJikH+TzNuo=
			</data>
			<key>hash2</key>
			<data>
			LCZ+XPn8Mc7MlxP4xPYVojsitQzcdsXQs3QjLii68H0=
			</data>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurement.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			on1mkulwTtm+ufPJ4eClavLWAuQ=
			</data>
			<key>hash2</key>
			<data>
			gd8e5hMrJihnXu8TQ0xecVPl3Z71GpsKaYxiLbmHezw=
			</data>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurement.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			yUv7cR5/GBGFKKmNJ60XGpIDBt0=
			</data>
			<key>hash2</key>
			<data>
			jE6LOB2O+RxP+tzu446QgOPjt7HEaQhcC4aHtKpufgQ=
			</data>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurement.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
		<key>tvos-arm64/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<dict>
			<key>hash</key>
			<data>
			sOGzPOrhboHRYjM15shciGecMyE=
			</data>
			<key>hash2</key>
			<data>
			gfGdk/oKyAt7RHGOKT8nqGgVjhOyIsPXHJ0idH6JXQQ=
			</data>
		</dict>
		<key>tvos-arm64/GoogleAppMeasurement.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			02RRcShk3XEm4aj6TyZWf9UglqQ=
			</data>
			<key>hash2</key>
			<data>
			57w1cwRczvMGYQIHmrDnA8Cc8C/Zn/YaSeq0lOXdq2E=
			</data>
		</dict>
		<key>tvos-arm64/GoogleAppMeasurement.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			on1mkulwTtm+ufPJ4eClavLWAuQ=
			</data>
			<key>hash2</key>
			<data>
			gd8e5hMrJihnXu8TQ0xecVPl3Z71GpsKaYxiLbmHezw=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurement.framework/GoogleAppMeasurement</key>
		<dict>
			<key>hash</key>
			<data>
			l+3I18pqDVeCFJqj6ApjF1PSJbk=
			</data>
			<key>hash2</key>
			<data>
			L26DpgGlRywv1FQ+A70PE8Roif6SlaFCu/9keB0ya/Y=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurement.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			wH4tTyaxcXrQyTFrOeD31Imnw68=
			</data>
			<key>hash2</key>
			<data>
			LjhU8k/AwHEuPqIx0Eaeq04u7esQ5CpZAEJ3Bts9pCE=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurement.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			on1mkulwTtm+ufPJ4eClavLWAuQ=
			</data>
			<key>hash2</key>
			<data>
			gd8e5hMrJihnXu8TQ0xecVPl3Z71GpsKaYxiLbmHezw=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
