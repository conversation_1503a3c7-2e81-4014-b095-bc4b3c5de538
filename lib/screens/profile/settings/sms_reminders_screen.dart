import 'package:partykidsapp/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';

import '../../../models/sms_settings.dart';
import '../../../services/sms_settings_service.dart';
import '../../../widgets/permission_guard.dart';

class SmsRemindersScreen extends StatefulWidget {
  const SmsRemindersScreen({super.key});

  @override
  State<SmsRemindersScreen> createState() => _SmsRemindersScreenState();
}

class _SmsRemindersScreenState extends State<SmsRemindersScreen> {
  bool _smsEnabled = true;
  bool _appointmentConfirmations = true;
  bool _dayBeforeReminders = true;
  bool _followUpMessages = false;

  SmsSettings? _currentSettings;
  bool _isLoading = true;
  bool _isSaving = false;
  String? _error;


  @override
  void initState() {
    super.initState();
    _loadSmsSettings();
  }

  /// Load SMS settings from backend
  Future<void> _loadSmsSettings() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final response = await SmsSettingsService.getSmsSettings();

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
          _smsEnabled = response.data!.enabled;
          _appointmentConfirmations = response.data!.appointmentConfirmations;
          _dayBeforeReminders = response.data!.dayBeforeReminders;
          _followUpMessages = response.data!.followUpMessages;
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = response.error ?? 'Failed to load SMS settings';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading SMS settings: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'SMS Reminders',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        elevation: 0,
      ),
      body: PermissionGuard(
        requireManagementAccess: true,
        fallback: _buildNoPermissionView(),
        child: _isLoading
          ? Center(child: CircularProgressIndicator(color: Theme.of(context).colorScheme.onSurface))
          : _error != null
            ? _buildErrorView()
            : Column(
                children: [
                  // Scrollable content
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // SMS Settings Card
                          _buildSmsSettingsCard(),
                          SizedBox(height: 24),


                          // Message Templates Section
                          _buildSectionHeader('Template-uri Mesaje'),
                          SizedBox(height: 16),
                          _buildMessageTemplatesCard(),
                          SizedBox(height: 24),

                          SizedBox(height: 80), // Space for fixed button
                        ],
                      ),
                    ),
                  ),
                  // Fixed save button at bottom
                  _buildFixedSaveButton(),
                ],
              ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.bold,
        color: Theme.of(context).colorScheme.onSurface,
      ),
    );
  }

  Widget _buildSmsSettingsCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Setări Notificări SMS',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 16),
            _buildSwitchTile(
              title: 'SMS automat',
              subtitle: 'Activează trimiterea automată a SMS-urilor',
              value: _smsEnabled,
              onChanged: _isSaving ? (value) {} : (value) => _updateSetting('enabled', value),
            ),
            SizedBox(height: 8),
            _buildSwitchTile(
              title: 'Confirmări programări',
              subtitle: 'Trimite SMS de confirmare la programare',
              value: _appointmentConfirmations,
              onChanged: _isSaving ? (value) {} : (value) => _updateSetting('appointmentConfirmations', value),
            ),
            _buildSwitchTile(
              title: 'Reminder cu o zi înainte',
              subtitle: 'Trimite SMS cu 24h înainte de programare',
              value: _dayBeforeReminders,
              onChanged: _isSaving ? (value) {} : (value) => _updateSetting('dayBeforeReminders', value),
            ),
            _buildSwitchTile(
              title: 'Mesaje de follow-up',
              subtitle: 'Trimite SMS după finalizarea serviciului',
              value: _followUpMessages,
              onChanged: _isSaving ? (value) {} : (value) => _updateSetting('followUpMessages', value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }


  Widget _buildMessageTemplatesCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Template-uri Mesaje',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 16),
            _buildTemplateItem(
              'Confirmare programare',
              'Bună ziua! Programarea dumneavoastră la {salon_name} pentru {pet_name} este confirmată pe {date} la ora {time}. Vă așteptăm!',
            ),
            SizedBox(height: 12),
            _buildTemplateItem(
              'Reminder 24h',
              'Vă reamintim că mâine, {date} la ora {time}, aveți programare la {salon_name} pentru {pet_name}. Pentru reprogramări: {phone}',
            ),
            SizedBox(height: 12),
            _buildTemplateItem(
              'Follow-up',
              'Mulțumim că ați ales {salon_name}! Sperăm că {pet_name} este mulțumit(ă) de serviciile noastre. Evaluați experiența: {review_link}',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTemplateItem(String title, String content) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Theme.of(context).colorScheme.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          SizedBox(height: 8),
          Text(
            content,
            style: TextStyle(
              fontSize: 13,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildFixedSaveButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
    );
  }

  /// Update a specific SMS setting
  Future<void> _updateSetting(String settingKey, dynamic value) async {
    setState(() {
      _isSaving = true;
    });

    try {
      // Update local state first for immediate UI feedback
      setState(() {
        switch (settingKey) {
          case 'enabled':
            _smsEnabled = value as bool;
            break;
          case 'appointmentConfirmations':
            _appointmentConfirmations = value as bool;
            break;
          case 'dayBeforeReminders':
            _dayBeforeReminders = value as bool;
            break;
          case 'followUpMessages':
            _followUpMessages = value as bool;
            break;
        }
      });

      // Create update request with current values
      final request = UpdateSmsSettingsRequest(
        enabled: _smsEnabled,
        appointmentConfirmations: _appointmentConfirmations,
        dayBeforeReminders: _dayBeforeReminders,
        followUpMessages: _followUpMessages,
        selectedProvider: 'partykids',
      );

      // Send to backend
      final response = await SmsSettingsService.updateSmsSettings(request);

      if (response.success && response.data != null) {
        setState(() {
          _currentSettings = response.data!;
        });
        _showSuccessSnackBar('Setările SMS au fost actualizate cu succes!');
      } else {
        _showErrorSnackBar(response.error ?? 'Eroare la actualizarea setărilor SMS');
        // Revert local state
        await _loadSmsSettings();
      }
    } catch (e) {
      _showErrorSnackBar('Eroare la actualizarea setărilor: $e');
      // Revert local state
      await _loadSmsSettings();
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }



  /// Show success message
  void _showSuccessSnackBar(String message) {
    showTopSnackBar(context, 
      SnackBar(
        content: Text(message),
       
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Show error message
  void _showErrorSnackBar(String message) {
    showTopSnackBar(context, 
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Build error view
  Widget _buildErrorView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            SizedBox(height: 16),
            Text(
              'Eroare la încărcarea setărilor SMS',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              _error ?? 'Eroare necunoscută',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: _loadSmsSettings,
              style: ElevatedButton.styleFrom(
               
                
              ),
              child: Text('Încearcă din nou'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build no permission view
  Widget _buildNoPermissionView() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.lock_outline,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            SizedBox(height: 16),
            Text(
              'Acces restricționat',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              'Doar groomer-ii șefi pot configura setările SMS pentru salon.',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
               
                
              ),
              child: Text('Înapoi'),
            ),
          ],
        ),
      ),
    );
  }
}
