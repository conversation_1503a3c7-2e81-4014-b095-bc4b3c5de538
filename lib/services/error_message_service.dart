import 'package:flutter/foundation.dart';

/// Service for handling error messages and providing user-friendly Romanian translations
class ErrorMessageService {
  /// Map of error codes to Romanian error messages
  static const Map<String, String> _errorMessages = {
    // Appointment/Scheduling errors
    'SCHEDULING_CONFLICT': 'Nu se poate programa: Există deja o programare sau timp blocat în acest interval',
    'INVALID_TIME_SLOT': 'Intervalul de timp selectat nu este valid',
    'STAFF_UNAVAILABLE': 'Personalul selectat nu este disponibil în acest interval',
    'APPOINTMENT_NOT_FOUND': 'Programarea nu a fost găsită',
    'APPOINTMENT_ALREADY_CANCELLED': 'Programarea este deja anulată',
    'APPOINTMENT_ALREADY_COMPLETED': 'Programarea este deja finalizată',
    'APPOINTMENT_TOO_LATE_TO_CANCEL': 'Este prea târziu pentru a anula această programare',
    'APPOINTMENT_TOO_LATE_TO_RESCHEDULE': 'Este prea târziu pentru a reprograma această programare',
    
    // Block time errors
    'BLOCK_NOT_FOUND': 'Blocul de timp nu a fost găsit',
    'BLOCK_TIME_CONFLICT': 'Există conflicte cu programările existente',
    'INVALID_BLOCK_TIME_RANGE': 'Intervalul de timp pentru blocare nu este valid',
    
    // Client/Pet errors
    'CLIENT_NOT_FOUND': 'Clientul nu a fost găsit',
    'PET_NOT_FOUND': 'Animalul nu a fost găsit',
    'DUPLICATE_CLIENT_PHONE': 'Există deja un client cu acest număr de telefon',
    
    // Service errors
    'SERVICE_NOT_FOUND': 'Serviciul nu a fost găsit',
    'SERVICE_NOT_AVAILABLE': 'Serviciul nu este disponibil',
    'INVALID_SERVICE_DURATION': 'Durata serviciului nu este validă',
    
    // Staff errors
    'STAFF_NOT_FOUND': 'Membrul personalului nu a fost găsit',
    'INSUFFICIENT_PERMISSIONS': 'Nu aveți permisiunile necesare pentru această acțiune',
    'STAFF_ALREADY_EXISTS': 'Membrul personalului există deja',
    
    // Salon errors
    'SALON_NOT_FOUND': 'Salonul nu a fost găsit',
    'SALON_ACCESS_DENIED': 'Nu aveți acces la acest salon',
    'SALON_SCHEDULE_CONFLICT': 'Conflicte cu programul salonului',
    
    // Authentication errors
    'UNAUTHORIZED': 'Nu sunteți autorizat să efectuați această acțiune',
    'TOKEN_EXPIRED': 'Sesiunea a expirat. Vă rugăm să vă autentificați din nou',
    'INVALID_CREDENTIALS': 'Credențiale invalide',
    
    // Validation errors
    'VALIDATION_ERROR': 'Datele introduse nu sunt valide',
    'REQUIRED_FIELD_MISSING': 'Câmpuri obligatorii lipsesc',
    'INVALID_DATE_FORMAT': 'Formatul datei nu este valid',
    'INVALID_TIME_FORMAT': 'Formatul orei nu este valid',
    'INVALID_PHONE_NUMBER': 'Numărul de telefon nu este valid',
    'INVALID_EMAIL': 'Adresa de email nu este validă',
    
    // Network/Server errors
    'NETWORK_ERROR': 'Eroare de rețea. Verificați conexiunea la internet',
    'SERVER_ERROR': 'Eroare de server. Încercați din nou mai târziu',
    'SERVICE_UNAVAILABLE': 'Serviciul nu este disponibil momentan',
    'TIMEOUT': 'Timpul de așteptare a expirat. Încercați din nou',
    
    // General errors
    'UNKNOWN_ERROR': 'A apărut o eroare neașteptată',
    'OPERATION_FAILED': 'Operațiunea a eșuat',
    'DATA_NOT_FOUND': 'Datele nu au fost găsite',
    'DUPLICATE_ENTRY': 'Intrarea există deja',
  };

  /// Get user-friendly Romanian error message for a given error code
  static String getErrorMessage(String? errorCode, {String? fallbackMessage}) {
    if (errorCode == null || errorCode.isEmpty) {
      return fallbackMessage ?? _errorMessages['UNKNOWN_ERROR']!;
    }

    final message = _errorMessages[errorCode];
    if (message != null) {
      return message;
    }

    // If no specific message found, try to create a generic message
    if (fallbackMessage != null && fallbackMessage.isNotEmpty) {
      return fallbackMessage;
    }

    // Log unknown error codes in debug mode
    if (kDebugMode) {
      debugPrint('⚠️ Unknown error code: $errorCode');
    }

    return _errorMessages['UNKNOWN_ERROR']!;
  }

  /// Get error message from API response with proper fallback handling
  static String getApiErrorMessage({
    String? errorCode,
    String? errorMessage,
    String? fallbackMessage,
  }) {
    // If we have a specific error code, use it
    if (errorCode != null && errorCode.isNotEmpty) {
      return getErrorMessage(errorCode, fallbackMessage: errorMessage);
    }

    // If we have an error message but no code, use the message
    if (errorMessage != null && errorMessage.isNotEmpty) {
      return errorMessage;
    }

    // Use fallback or default
    return fallbackMessage ?? _errorMessages['UNKNOWN_ERROR']!;
  }

  /// Get specific error message for scheduling conflicts with additional context
  static String getSchedulingConflictMessage({
    String? specificMessage,
    DateTime? conflictTime,
    String? conflictType,
  }) {
    String baseMessage = _errorMessages['SCHEDULING_CONFLICT']!;
    
    if (specificMessage != null && specificMessage.isNotEmpty) {
      return specificMessage;
    }

    if (conflictTime != null) {
      final timeStr = '${conflictTime.hour.toString().padLeft(2, '0')}:${conflictTime.minute.toString().padLeft(2, '0')}';
      baseMessage += ' la ora $timeStr';
    }

    if (conflictType != null) {
      switch (conflictType.toLowerCase()) {
        case 'appointment':
          baseMessage += ' (programare existentă)';
          break;
        case 'block':
          baseMessage += ' (timp blocat)';
          break;
        case 'break':
          baseMessage += ' (pauză)';
          break;
      }
    }

    return baseMessage;
  }

  /// Check if an error code represents a user-actionable error
  static bool isUserActionableError(String? errorCode) {
    if (errorCode == null) return false;
    
    const userActionableErrors = {
      'SCHEDULING_CONFLICT',
      'INVALID_TIME_SLOT',
      'STAFF_UNAVAILABLE',
      'VALIDATION_ERROR',
      'REQUIRED_FIELD_MISSING',
      'DUPLICATE_CLIENT_PHONE',
      'INVALID_PHONE_NUMBER',
      'INVALID_EMAIL',
    };
    
    return userActionableErrors.contains(errorCode);
  }

  /// Check if an error code represents a temporary error that might resolve with retry
  static bool isRetryableError(String? errorCode) {
    if (errorCode == null) return false;
    
    const retryableErrors = {
      'NETWORK_ERROR',
      'SERVER_ERROR',
      'SERVICE_UNAVAILABLE',
      'TIMEOUT',
    };
    
    return retryableErrors.contains(errorCode);
  }

  /// Get suggested action for an error code
  static String? getSuggestedAction(String? errorCode) {
    if (errorCode == null) return null;
    
    const suggestions = {
      'SCHEDULING_CONFLICT': 'Alegeți un alt interval de timp sau verificați calendarul pentru conflicte',
      'INVALID_TIME_SLOT': 'Selectați un interval de timp valid în programul de lucru',
      'STAFF_UNAVAILABLE': 'Alegeți alt membru al personalului sau un alt interval de timp',
      'NETWORK_ERROR': 'Verificați conexiunea la internet și încercați din nou',
      'SERVER_ERROR': 'Încercați din nou în câteva minute',
      'TOKEN_EXPIRED': 'Vă rugăm să vă autentificați din nou',
      'VALIDATION_ERROR': 'Verificați datele introduse și corectați erorile',
      'DUPLICATE_CLIENT_PHONE': 'Folosiți un alt număr de telefon sau găsiți clientul existent',
    };
    
    return suggestions[errorCode];
  }
}
