# Drag and Drop Functionality for Appointment Management

## Overview

This document describes the comprehensive drag-and-drop functionality implemented for appointment rescheduling and groomer reassignment in the Partykids grooming salon calendar interface.

## Features

### 1. Appointment Rescheduling via Drag-and-Drop
- **Long-press to drag**: Users can long-press on any appointment to start dragging
- **Visual feedback**: Dragged appointments show a preview with appointment details
- **Time slot validation**: Drop zones validate business hours, lunch breaks, and conflicts
- **Cross-day support**: Appointments can be moved to different days
- **Automatic API updates**: Backend is automatically updated via reschedule API

### 2. Groomer Reassignment via Drag-and-Drop
- **Staff column drops**: Appointments can be dropped on different staff columns
- **Availability checking**: System validates target groomer availability
- **Conflict resolution**: Shows conflicts with existing appointments
- **Maintains time slot**: Only changes the assigned groomer, keeps the same time

### 3. User Experience Enhancements
- **Haptic feedback**: Light haptic feedback on mobile devices during drag operations
- **Visual indicators**: Green borders for valid drops, red for invalid
- **Loading states**: Shows progress during API calls
- **Error handling**: Graceful error messages using existing snackbar system
- **Drag cancellation**: Users can cancel by dragging back to original position

## Architecture

### Core Components

#### 1. `DraggableAppointmentBlock`
- **Location**: `lib/widgets/calendar_views/draggable_appointment_block.dart`
- **Purpose**: Enhanced appointment block with drag capabilities
- **Key Features**:
  - Long-press drag initiation
  - Custom drag feedback widget
  - Placeholder during drag
  - Hover effects for desktop
  - Configurable drag enable/disable

#### 2. `DroppableTimeSlot`
- **Location**: `lib/widgets/calendar_views/droppable_time_slot.dart`
- **Purpose**: Time slots that can accept appointment drops
- **Key Features**:
  - Real-time drop validation
  - Visual feedback during drag over
  - Staff-specific drop handling
  - Business hours validation

#### 3. `DroppableStaffColumn`
- **Location**: `lib/widgets/calendar_views/droppable_time_slot.dart`
- **Purpose**: Staff columns for groomer reassignment
- **Key Features**:
  - Staff-specific drop zones
  - Prevents dropping on same staff
  - Visual feedback for valid/invalid drops

#### 4. `AppointmentDragDropService`
- **Location**: `lib/services/appointment/appointment_drag_drop_service.dart`
- **Purpose**: Business logic for drag-and-drop operations
- **Key Features**:
  - Drop target validation
  - Conflict detection
  - API integration
  - Error handling

### Data Models

#### `AppointmentDragData`
```dart
class AppointmentDragData {
  final Appointment appointment;
  final DateTime originalDate;
  final String originalStaffId;
}
```

#### `DropValidationResult`
```dart
class DropValidationResult {
  final bool isValid;
  final String reason;
  final ConflictType conflictType;
  final Appointment? conflictingAppointment;
}
```

#### `DropOperationResult`
```dart
class DropOperationResult {
  final bool success;
  final String message;
  final ConflictType conflictType;
  final Appointment? updatedAppointment;
}
```

## Validation Rules

### Business Hours Validation
- Appointments can only be dropped within salon business hours (8 AM - 8 PM)
- Lunch break times (1 PM - 2 PM) are blocked
- Outside hours show appropriate error messages

### Conflict Detection
- **Time conflicts**: Checks for overlapping appointments with same staff
- **Staff availability**: Validates target groomer is available
- **Appointment status**: Only allows moving of schedulable appointments

### Error Handling
- **Validation failures**: Show user-friendly error messages
- **API errors**: Handle network and server errors gracefully
- **Conflict resolution**: Use existing `SchedulingConflictException` handler

## Integration Points

### Calendar Views
- **Day View**: Full drag-and-drop support with detailed time slots
- **Week View**: Compact drag-and-drop for week overview
- **Month View**: View-only (no drag-and-drop due to space constraints)

### Backend Integration
- **Reschedule API**: `/api/salons/{salonId}/appointments/{id}/reschedule`
- **Update API**: `/api/salons/{salonId}/appointments/{id}`
- **Conflict handling**: Uses existing `SchedulingConflictException` system

### State Management
- **Calendar refresh**: Automatic refresh after successful operations
- **Multi-date updates**: Refreshes both source and target dates when needed
- **Provider integration**: Works with existing `CalendarProvider`

## Testing

### Unit Tests
- **Widget tests**: Test drag-and-drop UI components
- **Service tests**: Test business logic and validation
- **Mock integration**: Comprehensive mocking of dependencies

### Integration Tests
- **End-to-end workflows**: Complete drag-and-drop scenarios
- **Conflict handling**: Test various conflict scenarios
- **Error conditions**: Test error handling and recovery

### Test Files
- `test/widgets/calendar_views/draggable_appointment_block_test.dart`
- `test/widgets/calendar_views/droppable_time_slot_test.dart`
- `test/services/appointment/appointment_drag_drop_service_test.dart`
- `test/integration/appointment_drag_drop_integration_test.dart`

### Running Tests
```bash
# Run all drag-and-drop tests
flutter test test/drag_drop_test_runner.dart

# Run specific test files
flutter test test/widgets/calendar_views/draggable_appointment_block_test.dart
flutter test test/services/appointment/appointment_drag_drop_service_test.dart
```

## Usage Examples

### Basic Appointment Rescheduling
1. Long-press on an appointment in the calendar
2. Drag to a new time slot (green border indicates valid drop)
3. Release to confirm the move
4. System automatically updates the appointment and refreshes the calendar

### Groomer Reassignment
1. Long-press on an appointment
2. Drag to a different staff column (blue border indicates staff reassignment)
3. Release to confirm the reassignment
4. System updates the appointment's assigned groomer

### Handling Conflicts
1. When dropping on a conflicting time slot (red border)
2. System shows conflict dialog with details
3. User can choose to resolve or cancel the operation

## Performance Considerations

### Optimization Strategies
- **Lazy validation**: Initial validation is quick, detailed validation on drop
- **Debounced updates**: Prevent excessive API calls during drag
- **Efficient rendering**: Minimal redraws during drag operations
- **Memory management**: Proper disposal of animation controllers

### Mobile Considerations
- **Touch targets**: Adequate size for finger interaction
- **Haptic feedback**: Provides tactile confirmation
- **Gesture conflicts**: Careful handling of scroll vs drag gestures
- **Performance**: Optimized for smooth 60fps animations

## Future Enhancements

### Potential Improvements
- **Multi-select drag**: Drag multiple appointments at once
- **Drag preview customization**: More detailed drag feedback
- **Keyboard shortcuts**: Desktop keyboard support for drag operations
- **Undo functionality**: Ability to undo recent drag operations
- **Batch operations**: Drag multiple appointments to bulk reschedule

### Accessibility
- **Screen reader support**: Proper semantic labels for drag operations
- **Keyboard navigation**: Alternative input methods for drag-and-drop
- **High contrast**: Visual indicators work in high contrast mode
- **Voice control**: Integration with voice control systems

## Troubleshooting

### Common Issues
1. **Drag not starting**: Ensure `isDragEnabled` is true
2. **Drop not working**: Check validation rules and business hours
3. **API errors**: Verify network connectivity and authentication
4. **Visual glitches**: Check animation controller disposal

### Debug Tools
- Enable debug prints in `AppointmentDragDropService`
- Use Flutter Inspector to examine widget tree during drag
- Monitor network requests in development tools
- Check calendar provider state updates

## Conclusion

The drag-and-drop functionality provides an intuitive and efficient way for salon staff to manage appointments. The implementation follows Flutter best practices and integrates seamlessly with the existing codebase while providing comprehensive error handling and user feedback.
