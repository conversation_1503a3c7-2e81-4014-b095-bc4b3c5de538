import 'package:partykidsapp/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../config/theme/app_theme.dart';
import '../../models/user_role.dart';
import '../../providers/role_provider.dart';
import '../../services/staff_service.dart';

class EditStaffDialog extends StatefulWidget {
  final StaffResponse staff;
  final Function(StaffResponse)? onStaffUpdated;

  const EditStaffDialog({
    super.key,
    required this.staff,
    this.onStaffUpdated,
  });

  @override
  State<EditStaffDialog> createState() => _EditStaffDialogState();
}

class _EditStaffDialogState extends State<EditStaffDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nicknameController = TextEditingController();
  final _notesController = TextEditingController();

  GroomerRole _selectedRole = GroomerRole.groomer;
  ClientDataPermission _selectedPermission = ClientDataPermission.limitedAccess;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeFields();
  }

  void _initializeFields() {
    _nicknameController.text = widget.staff.nickname ?? '';
    _notesController.text = widget.staff.notes ?? '';
    _selectedRole = widget.staff.groomerRole;
    _selectedPermission = widget.staff.clientDataPermission;
  }

  @override
  void dispose() {
    _nicknameController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: const BoxConstraints(maxWidth: 500),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(),
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildStaffInfo(),
                      const SizedBox(height: 24),
                      if (widget.staff.groomerRole == GroomerRole.chiefGroomer) ...[
                        _buildChiefGroomerRestriction(),
                        const SizedBox(height: 24),
                      ],
                      _buildNicknameSection(),
                      const SizedBox(height: 24),
                      _buildRoleSection(),
                      const SizedBox(height: 24),
                      _buildPermissionsSection(),
                      const SizedBox(height: 24),
                      _buildNotesSection(),
                    ],
                  ),
                ),
              ),
            ),
            _buildActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: const BoxDecoration(
        color: AppColors.forestGreen,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.edit,
            color: Colors.white,
            size: 28,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Editează Membru Echipă',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  widget.staff.name,
                  style: const TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildStaffInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Informații Membru',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.forestGreen,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              const Icon(Icons.person, color: AppColors.forestGreen, size: 20),
              const SizedBox(width: 8),
              Text(
                'Nume: ${widget.staff.name}',
                style: const TextStyle(fontSize: 14),
              ),
            ],
          ),
          if (widget.staff.phone != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.phone, color: AppColors.forestGreen, size: 20),
                const SizedBox(width: 8),
                Text(
                  'Telefon: ${widget.staff.phone}',
                  style: const TextStyle(fontSize: 14),
                ),
              ],
            ),
          ],
          if (widget.staff.email != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.email, color: AppColors.forestGreen, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Email: ${widget.staff.email}',
                    style: const TextStyle(fontSize: 14),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildChiefGroomerRestriction() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.amber.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.amber.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            color: Colors.amber.shade700,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Groomer Șef - Editare Restricționată',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.amber.shade800,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Groomer Șef nu poate fi editat. Doar porecelă și notițele pot fi modificate.',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.amber.shade700,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNicknameSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Nume afișat',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.forestGreen,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _nicknameController,
          decoration: const InputDecoration(
            hintText: 'ex: Ana, Mihai, Dr. Popescu',
            prefixIcon: Icon(Icons.badge, color: AppColors.forestGreen),
            border: OutlineInputBorder(),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.forestGreen),
            ),
            helperText: 'Numele care va apărea în programări și calendar',
          ),
          validator: (value) {
            // Nickname is optional, but if provided, should be reasonable length
            if (value != null && value.trim().isNotEmpty) {
              if (value.trim().length < 2) {
                return 'Numele afișat trebuie să aibă cel puțin 2 caractere';
              }
              if (value.trim().length > 50) {
                return 'Numele afișat nu poate depăși 50 de caractere';
              }
            }
            return null;
          },
        ),
        const SizedBox(height: 8),
        Text(
          'Dacă nu completezi, se va folosi numele complet (${widget.staff.name}).',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }

  Widget _buildRoleSection() {
    final isChiefGroomer = widget.staff.groomerRole == GroomerRole.chiefGroomer;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Rol în echipă',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.forestGreen,
          ),
        ),
        const SizedBox(height: 12),
        ...GroomerRole.values
            // ignore: deprecated_member_use_from_same_package
            .where((role) => role != GroomerRole.regularGroomer) // Exclude deprecated role
            .where((role) => _canAssignRole(role)) // Exclude roles that current user cannot assign
            .map((role) => RadioListTile<GroomerRole>(
                  title: Text(
                    role.displayName,
                    style: TextStyle(
                      color: isChiefGroomer ? Colors.grey : null,
                    ),
                  ),
                  subtitle: Text(
                    role.description,
                    style: TextStyle(
                      color: isChiefGroomer ? Colors.grey : null,
                    ),
                  ),
                  value: role,
                  groupValue: _selectedRole,
                  onChanged: isChiefGroomer ? null : (GroomerRole? value) {
                    if (value != null) {
                      setState(() {
                        _selectedRole = value;
                      });
                    }
                  },
                  activeColor: AppColors.forestGreen,
                )),
      ],
    );
  }

  Widget _buildPermissionsSection() {
    final isChiefGroomer = widget.staff.groomerRole == GroomerRole.chiefGroomer;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Permisiuni acces date clienți',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.forestGreen,
          ),
        ),
        const SizedBox(height: 12),
        ...ClientDataPermission.values.map((permission) => RadioListTile<ClientDataPermission>(
              title: Text(
                permission.displayName,
                style: TextStyle(
                  color: isChiefGroomer ? Colors.grey : null,
                ),
              ),
              subtitle: Text(
                permission.description,
                style: TextStyle(
                  color: isChiefGroomer ? Colors.grey : null,
                ),
              ),
              value: permission,
              groupValue: _selectedPermission,
              onChanged: isChiefGroomer ? null : (ClientDataPermission? value) {
                if (value != null) {
                  setState(() {
                    _selectedPermission = value;
                  });
                }
              },
              activeColor: AppColors.forestGreen,
            )),
      ],
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Notițe (opțional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _notesController,
          maxLines: 3,
          decoration: const InputDecoration(
            hintText: 'Notițe despre acest membru al echipei...',
            border: OutlineInputBorder(),
            focusedBorder: OutlineInputBorder(
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActions() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(16)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            child: const Text('Anulează'),
          ),
          const SizedBox(width: 16),
          ElevatedButton(
            onPressed: _isLoading ? null : _handleSave,
            style: ElevatedButton.styleFrom(
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            ),
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : const Text('Salvează'),
          ),
        ],
      ),
    );
  }

  Future<void> _handleSave() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      debugPrint('🔄 EditStaffDialog: Updating staff member: ${widget.staff.id}');

      final isChiefGroomer = widget.staff.groomerRole == GroomerRole.chiefGroomer;

      final request = UpdateStaffRequest(
        nickname: _nicknameController.text.trim().isNotEmpty
            ? _nicknameController.text.trim()
            : null,
        // Don't change role/permissions for chief groomer
        groomerRole: isChiefGroomer ? widget.staff.groomerRole : _selectedRole,
        clientDataPermission: isChiefGroomer ? widget.staff.clientDataPermission : _selectedPermission,
        notes: _notesController.text.trim().isNotEmpty
            ? _notesController.text.trim()
            : null,
      );

      final response = await StaffService.updateStaffInCurrentSalon(
        widget.staff.id,
        request,
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (response.success && response.data != null) {
          debugPrint('✅ EditStaffDialog: Staff member updated successfully');

          // Call the callback if provided
          widget.onStaffUpdated?.call(response.data!);

          // Show success message
          showTopSnackBar(context, 
            SnackBar(
              content: Text('Membrul echipei "${response.data!.displayName}" a fost actualizat cu succes!'),
            ),
          );

          // Close dialog
          Navigator.of(context).pop(response.data);
        } else {
          debugPrint('❌ EditStaffDialog: Failed to update staff member: ${response.error}');

          // Show error message
          showTopSnackBar(context, 
            SnackBar(
              content: Text('Eroare la actualizarea membrului echipei: ${response.error}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ EditStaffDialog: Exception during staff update: $e');

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        showTopSnackBar(context, 
          SnackBar(
            content: Text('Eroare la actualizarea membrului echipei: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Check if the current user can assign the given role
  bool _canAssignRole(GroomerRole role) {
    // If the role is not chief groomer, it can always be assigned
    if (role != GroomerRole.chiefGroomer) {
      return true;
    }

    // Only admins can assign chief groomer roles
    final roleProvider = Provider.of<RoleProvider>(context, listen: false);
    return roleProvider.isAdmin;
  }
}
