import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart' show debugPrint, kIsWeb;
// Temporarily commented out for macOS compatibility
// import 'package:google_sign_in/google_sign_in.dart';
// import 'package:flutter_facebook_auth/flutter_facebook_auth.dart'; // Removed Facebook auth

class SocialAuthService {
  final FirebaseAuth _auth;
  // Temporarily commented out for macOS compatibility
  // final GoogleSignIn _googleSignIn = GoogleSignIn();

  SocialAuthService(this._auth);

  // Google Sign In
  Future<UserCredential?> signInWithGoogle() async {
    try {
      debugPrint('Starting Google Sign In process');

      // Create a GoogleAuthProvider credential
      final googleProvider = GoogleAuthProvider();

      // Add scopes (optional)
      googleProvider.addScope('https://www.googleapis.com/auth/userinfo.email');
      googleProvider.addScope('https://www.googleapis.com/auth/userinfo.profile');

      // Set custom parameters for better UX
      googleProvider.setCustomParameters({
        'prompt': 'select_account'
      });

      // Sign in with popup/redirect for web or with Firebase directly for mobile
      if (kIsWeb) {
        // Web implementation
        debugPrint('Using web implementation for Google Sign In');
        try {
          // First try with popup
          final userCredential = await _auth.signInWithPopup(googleProvider);
          debugPrint('Google Sign In successful with popup: ${userCredential.user?.email}');
          return userCredential;
        } catch (popupError) {
          debugPrint('Popup sign-in failed, trying redirect: $popupError');
          // If popup fails, try with redirect
          await _auth.signInWithRedirect(googleProvider);
          // Note: This will redirect the page, so we won't reach the code below
          // The result will be handled when the page loads back
          return null;
        }
      } else {
        // Mobile/desktop implementation
        debugPrint('Using Firebase Auth directly for Google Sign In');
        final userCredential = await _auth.signInWithProvider(googleProvider);
        debugPrint('Google Sign In successful: ${userCredential.user?.email}');
        return userCredential;
      }
    } on FirebaseAuthException catch (e) {
      debugPrint('FirebaseAuthException during Google Sign In: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('Exception during Google Sign In: $e');
      rethrow;
    }
  }

  // Facebook Sign In - DISABLED
  Future<UserCredential?> signInWithFacebook() async {
    throw UnsupportedError('Facebook authentication has been disabled');
    /*
    try {
      debugPrint('Starting Facebook Sign In process');

      if (kIsWeb) {
        // Web implementation using Firebase directly
        debugPrint('Using web implementation for Facebook Sign In');
        try {
          // Create a FacebookAuthProvider
          final facebookProvider = FacebookAuthProvider();

          // Add permissions
          facebookProvider.addScope('email');
          facebookProvider.addScope('public_profile');

          // Set custom parameters
          facebookProvider.setCustomParameters({
            'display': 'popup'
          });

          debugPrint('Facebook provider created with scopes: email, public_profile');

          debugPrint('Attempting Facebook Sign In with popup...');
          try {
            // Sign in with popup
            final userCredential = await _auth.signInWithPopup(facebookProvider);
            debugPrint('Facebook Sign In successful with popup: ${userCredential.user?.email}');
            return userCredential;
          } catch (popupError) {
            debugPrint('Popup sign-in failed with detailed error: $popupError');

            // If popup fails, try using the flutter_facebook_auth package directly
            debugPrint('Trying flutter_facebook_auth package instead...');
            try {
              // Use the flutter_facebook_auth package directly
              final LoginResult result = await FacebookAuth.instance.login(
                permissions: ['email', 'public_profile'],
              );

              debugPrint('Facebook login direct result: ${result.status}');

              if (result.status == LoginStatus.success && result.accessToken != null) {
                // Create a credential from the access token
                final OAuthCredential credential = FacebookAuthProvider.credential(
                  result.accessToken!.token,
                );

                // Sign in to Firebase with the credential
                final userCredential = await _auth.signInWithCredential(credential);
                debugPrint('Facebook Sign In successful with direct method: ${userCredential.user?.email}');
                return userCredential;
              } else {
                debugPrint('Direct Facebook login failed: ${result.message}');
                throw Exception('Facebook login failed: ${result.message}');
              }
            } catch (directError) {
              debugPrint('Direct method also failed: $directError');
              throw Exception('Facebook authentication failed. Please check your Facebook Developer settings and try again later.');
            }
          }
        } catch (e) {
          debugPrint('Overall Facebook Sign In error: $e');
          rethrow;
        }
      } else {
        // Mobile implementation using flutter_facebook_auth
        try {
          debugPrint('Using mobile implementation for Facebook Sign In');

          // Check if Facebook app is installed
          final isInstalled = await FacebookAuth.instance.isWebSdkInitialized;
          debugPrint('Facebook SDK initialized: $isInstalled');

          // Trigger the sign-in flow with more detailed options
          debugPrint('Calling FacebookAuth.instance.login()...');
          final LoginResult result = await FacebookAuth.instance.login(
            permissions: ['email', 'public_profile'],
            loginBehavior: LoginBehavior.nativeWithFallback, // Try native first, then web
          );

          debugPrint('Facebook login result status: ${result.status}');
          debugPrint('Facebook login message: ${result.message}');

          if (result.status != LoginStatus.success) {
            debugPrint('Facebook login failed with status: ${result.status}');
            throw Exception('Facebook login failed: ${result.message}');
          }

          // Verify we have the access token
          if (result.accessToken == null) {
            debugPrint('Error: Access token is null');
            throw Exception('Failed to get access token from Facebook');
          }

          // Get user data for debugging
          final userData = await FacebookAuth.instance.getUserData();
          debugPrint('Facebook user data: $userData');

          // Create a credential from the access token
          debugPrint('Creating Firebase credential from Facebook token');
          final OAuthCredential credential = FacebookAuthProvider.credential(
            result.accessToken!.token,
          );

          // Sign in to Firebase with the Facebook credential
          debugPrint('Signing in to Firebase with Facebook credential');
          final userCredential = await _auth.signInWithCredential(credential);
          debugPrint('Facebook Sign In successful: ${userCredential.user?.email}');
          return userCredential;
        } catch (e) {
          debugPrint('Detailed Facebook Sign In error on mobile: $e');
          rethrow;
        }
      }
    } on FirebaseAuthException catch (e) {
      debugPrint('FirebaseAuthException during Facebook Sign In: ${e.code} - ${e.message}');

      // Provide specific guidance for common development mode issues
      if (e.code == 'invalid-credential' && e.message?.contains('Cannot parse access token') == true) {
        debugPrint('');
        debugPrint('🔥 FACEBOOK DEVELOPMENT MODE ISSUE DETECTED 🔥');
        debugPrint('This error commonly occurs when:');
        debugPrint('1. Facebook app is in Development Mode');
        debugPrint('2. Current user is not added as a developer/tester');
        debugPrint('3. Facebook provider is not enabled in Firebase Console');
        debugPrint('');
        debugPrint('Solutions:');
        debugPrint('1. Switch Facebook app to Live Mode in Facebook Developer Console');
        debugPrint('2. Add current user as a developer/tester in Facebook app');
        debugPrint('3. Ensure Facebook is enabled in Firebase Console Authentication');
        debugPrint('');
      }

      rethrow;
    } catch (e) {
      debugPrint('Exception during Facebook Sign In: $e');
      rethrow;
    }
  }

  // Apple Sign In
  Future<UserCredential?> signInWithApple() async {
    try {
      debugPrint('Starting Apple Sign In process');

      // Only allow Apple Sign In on Apple platforms
      if (!kIsWeb && !Platform.isIOS && !Platform.isMacOS) {
        debugPrint('Apple Sign In is only available on Apple devices');
        throw Exception('Sign in with Apple is only available on Apple devices');
      }

      if (kIsWeb) {
        // Web implementation using Firebase directly
        debugPrint('Using web implementation for Apple Sign In');
        try {
          // Create an AppleAuthProvider
          final appleProvider = OAuthProvider('apple.com');

          // Add scopes
          appleProvider.addScope('email');
          appleProvider.addScope('name');

          // Set custom parameters
          appleProvider.setCustomParameters({
            'locale': 'en',
            'prompt': 'consent'
          });

          debugPrint('Attempting Apple Sign In with popup...');
          try {
            // Sign in with popup
            final userCredential = await _auth.signInWithPopup(appleProvider);
            debugPrint('Apple Sign In successful with popup: ${userCredential.user?.email}');
            return userCredential;
          } catch (popupError) {
            debugPrint('Popup sign-in failed with detailed error: $popupError');

            // If popup fails, try with redirect
            debugPrint('Trying redirect method instead...');
            final appleProvider = OAuthProvider('apple.com');
            appleProvider.addScope('email');
            appleProvider.addScope('name');
            await _auth.signInWithRedirect(appleProvider);
            // Note: This will redirect the page, so we won't reach the code below
            // The result will be handled when the page loads back
            return null;
          }
        } catch (e) {
          debugPrint('Overall Apple Sign In error: $e');
          rethrow;
        }
      } else {
        // Mobile/desktop implementation using sign_in_with_apple package
        try {
          debugPrint('Using mobile/desktop implementation for Apple Sign In');
          debugPrint('Requesting Apple ID credential...');

          // Check if Apple Sign In is available on this device
          final isAvailable = await SignInWithApple.isAvailable();
          if (!isAvailable) {
            debugPrint('Sign in with Apple is not available on this device');
            throw Exception('Sign in with Apple is not available on this device');
          }

          // For iOS, we need to generate a secure nonce
          final rawNonce = generateNonce();
          final nonce = sha256ofString(rawNonce);

          // Perform the sign-in request
          final credential = await SignInWithApple.getAppleIDCredential(
            scopes: [
              AppleIDAuthorizationScopes.email,
              AppleIDAuthorizationScopes.fullName,
            ],
            nonce: nonce,
          );

          debugPrint('Apple ID credential received: ${credential.email ?? "email not provided"}');
          debugPrint('Identity token length: ${credential.identityToken?.length ?? 0}');

          // Verify we have the necessary tokens
          if (credential.identityToken == null) {
            debugPrint('Error: Identity token is null');
            throw Exception('Failed to get identity token from Apple');
          }

          // Create an OAuthCredential from the credential
          final oauthCredential = OAuthProvider('apple.com').credential(
            idToken: credential.identityToken!,
            rawNonce: rawNonce,
          );

          // Sign in to Firebase with the Apple credential
          debugPrint('Signing in to Firebase with Apple credential');
          final userCredential = await _auth.signInWithCredential(oauthCredential);
          debugPrint('Apple Sign In successful: ${userCredential.user?.email}');
          return userCredential;
        } catch (e) {
          debugPrint('Detailed Apple Sign In error on mobile: $e');
          rethrow;
        }
      }
    } on FirebaseAuthException catch (e) {
      debugPrint('FirebaseAuthException during Apple Sign In: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('Exception during Apple Sign In: $e');
      rethrow;
    }
  }

  // Sign out from social providers
  Future<void> signOutFromSocialProviders() async {
    try {
      // Sign out from Google if signed in with Google
      // Temporarily commented out for macOS compatibility
      /*
      if (await _googleSignIn.isSignedIn()) {
        await _googleSignIn.signOut();
      }
      */

      // Sign out from Facebook if signed in with Facebook
      await FacebookAuth.instance.logOut();

      debugPrint('Signed out from social providers');
    } catch (e) {
      debugPrint('Error signing out from social providers: $e');
      rethrow;
    }
  }
}

extension on AccessToken {
  String get token => tokenString;
}

String generateNonce([int length = 32]) {
  const charset = '0123456789ABCDEFGHIJKLMNOPQRSTUVXYZabcdefghijklmnopqrstuvwxyz-._';
  final random = Random.secure();
  return List.generate(length, (_) => charset[random.nextInt(charset.length)]).join();
}

/// Returns the sha256 hash of [input] in hex notation.
String sha256ofString(String input) {
  final bytes = utf8.encode(input);
  final digest = sha256.convert(bytes);
  return digest.toString();
}
