import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/client_statistics.dart';
import '../../config/theme/app_theme.dart';

class ClientStatisticsWidget extends StatelessWidget {
  final ClientStatistics? statistics;
  final bool isLoading;

  const ClientStatisticsWidget({
    super.key,
    this.statistics,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(20.0),
          child: Center(
            child: CircularProgressIndicator(
              color: AppColors.forestGreen,
            ),
          ),
        ),
      );
    }

    if (statistics == null) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(20.0),
          child: Center(
            child: Text(
              'Nu s-au putut încărca statisticile',
              style: TextStyle(
                color: AppColors.taupe,
                fontSize: 16,
              ),
            ),
          ),
        ),
      );
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                const Icon(
                  Icons.analytics,
                  color: AppColors.forestGreen,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Statistici Client',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.forestGreen,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getLoyaltyColor(statistics!.loyaltyScore),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'Loialitate: ${statistics!.loyaltyScore.toStringAsFixed(1)}%',
                    style: const TextStyle(
                      color: AppColors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Statistics grid
            LayoutBuilder(
              builder: (context, constraints) {
                return GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: 2,
                  childAspectRatio: 2.8,
                  crossAxisSpacing: 6,
                  mainAxisSpacing: 6,
                  children: [
                    _buildStatCard(
                      'Programări Totale',
                      statistics!.totalAppointments.toString(),
                      Icons.calendar_today,
                      AppColors.forestGreen,
                    ),
                    _buildStatCard(
                      'Programări Viitoare',
                      statistics!.upcomingAppointments.toString(),
                      Icons.schedule,
                      AppColors.logoBrown,
                    ),
                    _buildStatCard(
                      'Anulări',
                      statistics!.cancelledAppointments.toString(),
                      Icons.cancel,
                      Colors.orange,
                    ),
                    _buildStatCard(
                      'Absențe',
                      statistics!.noShowAppointments.toString(),
                      Icons.person_off,
                      Colors.red,
                    ),
                    _buildStatCard(
                      'Venituri Totale',
                      '${statistics!.totalRevenue.toStringAsFixed(0)} RON',
                      Icons.euro_symbol,
                      Colors.green,
                    ),
                    _buildStatCard(
                      'Valoare Medie',
                      '${statistics!.averageAppointmentValue.toStringAsFixed(0)} RON',
                      Icons.trending_up,
                      Colors.blue,
                    ),
                  ],
                );
              },
            ),

            const SizedBox(height: 16),

            // Last visit info
            if (statistics!.lastVisitDate != null) ...[
              const Divider(),
              const SizedBox(height: 8),
              Row(
                children: [
                  const Icon(
                    Icons.access_time,
                    color: AppColors.taupe,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Ultima vizită: ${DateFormat('dd MMM yyyy').format(statistics!.lastVisitDate!)}',
                    style: const TextStyle(
                      color: AppColors.taupe,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],

            // Completion rate
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(
                  Icons.check_circle,
                  color: AppColors.forestGreen,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Rata de finalizare: ${statistics!.completionRate.toStringAsFixed(1)}%',
                  style: const TextStyle(
                    color: AppColors.forestGreen,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 3),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        mainAxisSize: MainAxisSize.max,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 14),
              const Spacer(),
              Flexible(
                child: Text(
                  value,
                  style: TextStyle(
                    color: color,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 1),
          Text(
            title,
            style: TextStyle(
              color: color.withValues(alpha: 0.8),
              fontSize: 8,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Color _getLoyaltyColor(double score) {
    if (score >= 90) return Colors.green;
    if (score >= 80) return AppColors.forestGreen;
    if (score >= 70) return Colors.orange;
    return Colors.red;
  }
}
