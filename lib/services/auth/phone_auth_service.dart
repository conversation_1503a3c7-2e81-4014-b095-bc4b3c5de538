import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart' show debugPrint;

class PhoneAuthService {
  final FirebaseAuth _auth;
  String? _verificationId;

  PhoneAuthService(this._auth);

  // Phone Number Authentication - Send Code
  Future<void> verifyPhoneNumber(
    String phoneNumber, {
    required Function(PhoneAuthCredential) verificationCompleted,
    required Function(FirebaseAuthException) verificationFailed,
    required Function(String, int?) codeSent,
    required Function(String) codeAutoRetrievalTimeout,
  }) async {
    try {
      debugPrint('Starting phone verification for: $phoneNumber');
      debugPrint('Firebase Auth instance: ${_auth.toString()}');
      debugPrint('Current user: ${_auth.currentUser}');
      debugPrint('Firebase App: ${_auth.app.name}');
      debugPrint('Firebase Project ID: ${_auth.app.options.projectId}');
      debugPrint('Firebase App ID: ${_auth.app.options.appId}');

      await _auth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        timeout: const Duration(seconds: 60), // Add explicit timeout
        verificationCompleted: (PhoneAuthCredential credential) async {
          debugPrint('Auto-verification completed for: $phoneNumber');
          verificationCompleted(credential);
        },
        verificationFailed: (FirebaseAuthException e) {
          debugPrint('🚨 Phone verification failed for REAL number: ${e.code} - ${e.message}');
          debugPrint('📱 Phone number attempted: $phoneNumber');
          debugPrint('🔍 Error details: ${e.toString()}');
          debugPrint('📋 Stack trace: ${e.stackTrace}');
          debugPrint('');
          debugPrint('🎯 REAL NUMBER FAILURE ANALYSIS:');
          debugPrint('- Test numbers work ✅');
          debugPrint('- Real numbers fail ❌');
          debugPrint('- This indicates APNs configuration issue');
          debugPrint('');

          if (e.code == 'unknown' && e.message?.contains('custom URL scheme') == true) {
            debugPrint('🎯 URL SCHEME ERROR DETECTED 🎯');
            debugPrint('✅ FIXED: Updated Info.plist with correct URL scheme');
            debugPrint('Required: app-1-159713565362-ios-838930ed46df3a1213f977');
            debugPrint('This should now work after clean rebuild!');
          } else if (e.code == 'invalid-app-credential') {
            debugPrint('🎯 INVALID APP CREDENTIAL - REAL NUMBER ISSUE 🎯');
            debugPrint('This is the most common cause when test numbers work but real numbers fail!');
            debugPrint('');
            debugPrint('🔍 SINCE IT WORKED BEFORE, CHECK THESE CHANGES:');
            debugPrint('1. ❓ Did Firebase project configuration change?');
            debugPrint('2. ❓ Did you regenerate/redownload GoogleService-Info.plist?');
            debugPrint('3. ❓ Did APNs key expire or get regenerated?');
            debugPrint('4. ❓ Did bundle ID change in Firebase Console?');
            debugPrint('');
            debugPrint('📋 IMMEDIATE FIXES TO TRY:');
            debugPrint('1. 🔄 Re-upload APNs key to Firebase Console');
            debugPrint('2. 🔄 Verify Team ID in Firebase: 6MB735DMZ5');
            debugPrint('3. 🔄 Verify Bundle ID in Firebase: ro.partykidsprogramari.partykids');
            debugPrint('4. 🔄 Check if APNs key is for correct environment (dev/prod)');
            debugPrint('');
            debugPrint('🎯 QUICK TEST: Try different real phone number');
            debugPrint('- Sometimes specific numbers have issues');
            debugPrint('- Try +40 number vs international number');
            debugPrint('');
            debugPrint('📱 Failed phone number: $phoneNumber');
          } else if (e.code == 'internal-error') {
            debugPrint('🔥 INTERNAL ERROR IS BACK! 🔥');
            debugPrint('This suggests the new Firebase configuration has issues.');
            debugPrint('');
            debugPrint('🔍 CURRENT CONFIGURATION CHECK:');
            debugPrint('- Firebase Project ID: ${_auth.app.options.projectId}');
            debugPrint('- Firebase App ID: ${_auth.app.options.appId}');
            debugPrint('- Phone number: $phoneNumber');
            debugPrint('');
            debugPrint('📋 STEP-BY-STEP VERIFICATION NEEDED:');
            debugPrint('1. ❓ Did you download NEW GoogleService-Info.plist?');
            debugPrint('2. ❓ Did you replace the old file in ios/Runner/?');
            debugPrint('3. ❓ Did you upload APNs key to the NEW iOS app in Firebase?');
            debugPrint('4. ❓ Is phone authentication enabled for the NEW app?');
            debugPrint('5. ❓ Does the new app have bundle ID: ro.partykidsprogramari.partykids?');
            debugPrint('');
            debugPrint('🚨 MOST LIKELY ISSUES:');
            debugPrint('- Using old GoogleService-Info.plist file');
            debugPrint('- APNs key not uploaded to the NEW iOS app');
            debugPrint('- Phone auth not enabled for the NEW iOS app');
            debugPrint('- Bundle ID still mismatched');
            debugPrint('');
            debugPrint('🧪 TRY THIS: Use Firebase test phone number');
            debugPrint('- Add +40123456789 with code 123456 in Firebase Console');
            debugPrint('- This bypasses APNs and tests basic configuration');
          }
          verificationFailed(e);
        },
        codeSent: (String verificationId, int? resendToken) {
          debugPrint('Verification code sent to: $phoneNumber');
          _verificationId = verificationId;
          codeSent(verificationId, resendToken);
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          debugPrint('Auto-retrieval timeout for: $phoneNumber');
          _verificationId = verificationId;
          codeAutoRetrievalTimeout(verificationId);
        },
      );
    } catch (e) {
      debugPrint('Exception during phone verification: $e');
      rethrow;
    }
  }

  // Phone Number Authentication - Verify Code
  Future<UserCredential> verifyOTP(String otp) async {
    try {
      debugPrint('Verifying OTP code');

      if (_verificationId == null) {
        debugPrint('Error: Verification ID is null');
        throw Exception('Verification ID is null. Please request OTP again.');
      }

      // Create a PhoneAuthCredential with the code
      PhoneAuthCredential credential = PhoneAuthProvider.credential(
        verificationId: _verificationId!,
        smsCode: otp,
      );

      debugPrint('Created phone auth credential with OTP');

      // Sign in with the credential
      final userCredential = await _auth.signInWithCredential(credential);
      debugPrint('Successfully verified OTP and signed in: ${userCredential.user?.phoneNumber}');

      return userCredential;
    } on FirebaseAuthException catch (e) {
      debugPrint('FirebaseAuthException during OTP verification: ${e.code} - ${e.message}');
      rethrow;
    } catch (e) {
      debugPrint('Exception during OTP verification: $e');
      rethrow;
    }
  }
}