# Internationalization Guidelines

This project centralizes all user facing strings inside `lib/core/constants/app_strings.dart`.

* Keys use camelCase names that describe their usage (e.g. `login`, `logout`, `editClient`).
* Dynamic messages are exposed as helper methods such as `launchError(action)`.

When adding new screens, avoid hard coding text in widgets. Instead, reference the
appropriate constant from `AppStrings` or add a new key.

The project uses a `LocaleProvider` to persist the preferred language. Future
translation files will live under `lib/l10n` following <PERSON><PERSON><PERSON>'s `intl` package
conventions (`app_en.arb`, `app_ro.arb`, etc.).
