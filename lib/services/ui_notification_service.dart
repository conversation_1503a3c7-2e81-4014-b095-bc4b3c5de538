import 'package:flutter/material.dart';
import '../widgets/notifications/animated_notification.dart';
import '../widgets/notifications/schedule_conflict_notification.dart';
import '../models/working_hours_settings.dart';

/// Service for managing beautiful animated UI notifications
class UINotificationService {
  static OverlayEntry? _currentNotification;
  static final List<OverlayEntry> _notificationQueue = [];

  /// Show a success notification
  static void showSuccess({
    required BuildContext context,
    required String title,
    required String message,
    Duration duration = const Duration(seconds: 3),
    VoidCallback? onActionPressed,
    String? actionLabel,
  }) {
    _showNotification(
      context: context,
      title: title,
      message: message,
      type: NotificationType.success,
      duration: duration,
      onActionPressed: onActionPressed,
      actionLabel: actionLabel,
    );
  }

  /// Show a warning notification
  static void showWarning({
    required BuildContext context,
    required String title,
    required String message,
    Duration duration = const Duration(seconds: 4),
    VoidCallback? onActionPressed,
    String? actionLabel,
  }) {
    _showNotification(
      context: context,
      title: title,
      message: message,
      type: NotificationType.warning,
      duration: duration,
      onActionPressed: onActionPressed,
      actionLabel: actionLabel,
    );
  }

  /// Show an error notification
  static void showError({
    required BuildContext context,
    required String title,
    required String message,
    Duration duration = const Duration(seconds: 5),
    VoidCallback? onActionPressed,
    String? actionLabel,
  }) {
    _showNotification(
      context: context,
      title: title,
      message: message,
      type: NotificationType.error,
      duration: duration,
      onActionPressed: onActionPressed,
      actionLabel: actionLabel,
    );
  }

  /// Show an info notification
  static void showInfo({
    required BuildContext context,
    required String title,
    required String message,
    Duration duration = const Duration(seconds: 4),
    VoidCallback? onActionPressed,
    String? actionLabel,
  }) {
    _showNotification(
      context: context,
      title: title,
      message: message,
      type: NotificationType.info,
      duration: duration,
      onActionPressed: onActionPressed,
      actionLabel: actionLabel,
    );
  }

  /// Show a specialized schedule conflict notification
  static void showScheduleConflict({
    required BuildContext context,
    required String errorMessage,
    WorkingHoursSettings? businessHours,
    VoidCallback? onRetry,
    VoidCallback? onViewBusinessHours,
  }) {
    _dismissCurrent();

    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;
    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + 16,
        left: 0,
        right: 0,
        child: ScheduleConflictNotification(
          errorMessage: errorMessage,
          businessHours: businessHours,
          onDismiss: () {
            _removeNotification(overlayEntry);
          },
          onRetry: onRetry,
          onViewBusinessHours: onViewBusinessHours,
        ),
      ),
    );

    overlay.insert(overlayEntry);
    _currentNotification = overlayEntry;
  }

  /// Show a generic notification
  static void _showNotification({
    required BuildContext context,
    required String title,
    required String message,
    required NotificationType type,
    Duration duration = const Duration(seconds: 4),
    VoidCallback? onActionPressed,
    String? actionLabel,
    Widget? customIcon,
  }) {
    _dismissCurrent();

    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;
    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + 16,
        left: 0,
        right: 0,
        child: AnimatedNotification(
          title: title,
          message: message,
          type: type,
          duration: duration,
          onDismiss: () {
            _removeNotification(overlayEntry);
          },
          onActionPressed: onActionPressed,
          actionLabel: actionLabel,
          customIcon: customIcon,
        ),
      ),
    );

    overlay.insert(overlayEntry);
    _currentNotification = overlayEntry;
  }

  /// Dismiss the current notification
  static void _dismissCurrent() {
    if (_currentNotification != null) {
      _currentNotification!.remove();
      _currentNotification = null;
    }
  }

  /// Remove a specific notification
  static void _removeNotification(OverlayEntry entry) {
    entry.remove();
    if (_currentNotification == entry) {
      _currentNotification = null;
    }
    _notificationQueue.remove(entry);
  }

  /// Clear all notifications
  static void clearAll() {
    _dismissCurrent();
    for (final entry in _notificationQueue) {
      entry.remove();
    }
    _notificationQueue.clear();
  }

  /// Parse schedule error and show appropriate notification
  static void showScheduleError({
    required BuildContext context,
    required String errorMessage,
    WorkingHoursSettings? businessHours,
    VoidCallback? onRetry,
  }) {
    // Check if this is a business hours conflict
    if (_isBusinessHoursConflict(errorMessage)) {
      // Always show the schedule conflict notification with business hours
      showScheduleConflict(
        context: context,
        errorMessage: errorMessage,
        businessHours: businessHours,
        onRetry: onRetry,
        onViewBusinessHours: () {
          // Could navigate to business hours settings
        },
      );
    } else if (_isTimeFormatError(errorMessage)) {
      showWarning(
        context: context,
        title: 'Format oră invalid',
        message: 'Vă rugăm să folosiți formatul HH:MM (ex: 09:00, 17:30)',
        actionLabel: 'Încearcă din nou',
        onActionPressed: onRetry,
      );
    } else if (_isTimeOrderError(errorMessage)) {
      showWarning(
        context: context,
        title: 'Ordine ore incorectă',
        message: 'Ora de început trebuie să fie înainte de ora de sfârșit',
        actionLabel: 'Corectează',
        onActionPressed: onRetry,
      );
    } else {
      // For any other schedule-related error, show as schedule conflict if business hours available
      if (businessHours != null) {
        showScheduleConflict(
          context: context,
          errorMessage: errorMessage,
          businessHours: businessHours,
          onRetry: onRetry,
          onViewBusinessHours: () {
            // Could navigate to business hours settings
          },
        );
      } else {
        // Generic error without business hours context
        showError(
          context: context,
          title: 'Eroare program',
          message: _friendlyErrorMessage(errorMessage),
          actionLabel: 'Încearcă din nou',
          onActionPressed: onRetry,
        );
      }
    }
  }

  /// Check if error is related to business hours conflict
  static bool _isBusinessHoursConflict(String error) {
    return error.contains('business hours') || 
           error.contains('violates') ||
           error.contains('constraints');
  }

  /// Check if error is related to time format
  static bool _isTimeFormatError(String error) {
    return error.contains('time format') || 
           error.contains('invalid') ||
           error.contains('Format oră');
  }

  /// Check if error is related to time order
  static bool _isTimeOrderError(String error) {
    return error.contains('before') || 
           error.contains('înainte') ||
           error.contains('Start time must be');
  }

  /// Convert technical error to friendly message
  static String _friendlyErrorMessage(String error) {
    if (error.contains('network') || error.contains('connection')) {
      return 'Verifică conexiunea la internet și încearcă din nou';
    }
    if (error.contains('timeout')) {
      return 'Operațiunea a durat prea mult. Te rugăm să încerci din nou';
    }
    if (error.contains('server') || error.contains('500')) {
      return 'Problemă temporară cu serverul. Încearcă din nou în câteva momente';
    }
    
    // Return a generic friendly message for unknown errors
    return 'A apărut o problemă neașteptată. Te rugăm să încerci din nou';
  }
}
