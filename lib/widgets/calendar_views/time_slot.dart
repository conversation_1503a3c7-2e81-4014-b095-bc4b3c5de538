import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../config/theme/app_theme.dart';
import '../../providers/theme_provider.dart';

class TimeSlot extends StatelessWidget {
  final DateTime dateTime;
  final bool isBusinessHour;
  final bool isLunchBreak;
  final bool isAvailable;
  final bool isGreyedOut;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final double height;
  final Widget? child;

  const TimeSlot({
    super.key,
    required this.dateTime,
    required this.isBusinessHour,
    required this.isLunchBreak,
    required this.isAvailable,
    required this.isGreyedOut,
    this.onTap,
    this.onLongPress,
    this.height = 80, // Increased default height for better visibility
    this.child,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: isBusinessHour && !isLunchBreak ? onTap : null,
      onLongPress: isBusinessHour && !isLunchBreak ? onLongPress : null,
      child: Container(
        height: height,
        decoration: BoxDecoration(
          color: _getBackgroundColor(context),
          border: Border(
            top: BorderSide(
              color: Theme.of(context).colorScheme.outline,
              width: 0.1,
            ),
            right: BorderSide(
              color: Theme.of(context).colorScheme.outline,
              width: 0.1,
            ),
          ),
        ),
        child: Stack(
          children: [
            if (!isBusinessHour || isLunchBreak || isGreyedOut)
              Positioned.fill(
                child: Container(
                  color: _getOverlayColor(context),
                ),
              ),
            if (child != null) child!,
            if (isBusinessHour && !isLunchBreak && isAvailable)
              Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: onTap,
                  hoverColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  child: Container(),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Color _getBackgroundColor(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    if (!isBusinessHour) {
      return colorScheme.surfaceContainerHighest.withValues(alpha: 0.3);
    }
    if (isLunchBreak) {
      return AppTheme.getStatusColor(context, 'warning').withValues(alpha: 0.2);
    }
    if (!isAvailable) {
      return colorScheme.surfaceContainerHighest.withValues(alpha: 0.5);
    }
    return colorScheme.surface;
  }

  Color _getOverlayColor(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    if (isLunchBreak) {
      return AppTheme.getStatusColor(context, 'warning').withOpacity(0.4);
    }
    if (!isBusinessHour || isGreyedOut) {
      return colorScheme.surfaceContainerHighest.withValues(alpha: 0.5);
    }
    return Theme.of(context).colorScheme.surface.withValues(alpha: 0.0);
  }
}


class TimeLabel extends StatelessWidget {
  final DateTime time;
  final bool isCurrentHour;
  final double height;

  const TimeLabel({
    super.key,
    required this.time,
    this.isCurrentHour = false,
    this.height = 80.0, // Increased default height for better visibility
  });

  @override
  Widget build(BuildContext context) {
    final hour = time.hour;
    final displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    final period = hour < 12 ? 'AM' : 'PM';

    return Consumer<ThemeProvider>(
      builder: (context, themeProvider, child) {
        final isDark = Theme.of(context).brightness == Brightness.dark;

        return Container(
          width: 60,
          height: height, // Use dynamic height from provider
          decoration: BoxDecoration(
            color: isCurrentHour
                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                : null,
            border: Border(
              right: BorderSide(
                color: isDark ? AppColors.darkSurfaceVariant : AppColors.lightGray,
                width: 0.5,
              ),
              // Remove top border between hours
            ),
          ),
          child: Align(
        alignment: Alignment.topCenter,
        child: Padding(
          padding: const EdgeInsets.only(top: 8.0), // Increased padding for better spacing
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '$displayHour:00',
                style: TextStyle(
                  fontSize: 14, // Increased from 12 for better readability
                  fontWeight: isCurrentHour ? FontWeight.bold : FontWeight.w500,
                  color: isCurrentHour
                      ? Theme.of(context).colorScheme.primary
                      : themeProvider.getSecondaryTextColor(context),
                ),
              ),
              Text(
                period,
                style: TextStyle(
                  fontSize: 11, // Increased from 10 for better readability
                  fontWeight: FontWeight.w400,
                  color: isCurrentHour
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
      ),
        );
      },
    );
  }
}
