import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../providers/calendar_provider.dart';
import '../../services/appointment/appointment_drag_drop_service.dart';
import 'draggable_appointment_block.dart';
import 'time_slot.dart';

/// Enhanced time slot that can accept appointment drops
class DroppableTimeSlot extends StatefulWidget {
  final DateTime dateTime;
  final String? staffId;
  final bool isBusinessHour;
  final bool isLunchBreak;
  final bool isAvailable;
  final bool isGreyedOut;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final double height;
  final Widget? child;
  final bool isDragEnabled;

  const DroppableTimeSlot({
    super.key,
    required this.dateTime,
    this.staffId,
    required this.isBusinessHour,
    required this.isLunchBreak,
    required this.isAvailable,
    required this.isGreyedOut,
    this.onTap,
    this.onLongPress,
    this.height = 60,
    this.child,
    this.isDragEnabled = true,
  });

  @override
  State<DroppableTimeSlot> createState() => _DroppableTimeSlotState();
}

class _DroppableTimeSlotState extends State<DroppableTimeSlot> {
  bool _isDragOver = false;
  bool _isValidDropTarget = false;
  String _validationMessage = '';

  void _triggerHapticFeedback() {
    HapticFeedback.lightImpact();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isDragEnabled) {
      return TimeSlot(
        dateTime: widget.dateTime,
        isBusinessHour: widget.isBusinessHour,
        isLunchBreak: widget.isLunchBreak,
        isAvailable: widget.isAvailable,
        isGreyedOut: widget.isGreyedOut,
        onTap: widget.onTap,
        onLongPress: widget.onLongPress,
        height: widget.height,
        child: widget.child,
      );
    }

    return Consumer<CalendarProvider>(
      builder: (context, calendarProvider, child) {
        return DragTarget<AppointmentDragData>(
          onWillAcceptWithDetails: (details) => _onWillAccept(details.data, calendarProvider),
          onAcceptWithDetails: (details) => _onAccept(details.data, calendarProvider),
          onLeave: (data) => _onLeave(data),
          builder: (context, candidateData, rejectedData) {
            return Tooltip(
              message: _isDragOver && !_isValidDropTarget ? _validationMessage : '',
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                decoration: BoxDecoration(
                  border: _isDragOver
                      ? Border.all(
                          color: _isValidDropTarget
                              ? Colors.green.withValues(alpha: 0.8)
                              : Colors.red.withValues(alpha: 0.8),
                          width: 2,
                        )
                      : null,
                  color: _isDragOver
                      ? (_isValidDropTarget
                          ? Colors.green.withValues(alpha: 0.1)
                          : Colors.red.withValues(alpha: 0.1))
                      : null,
                ),
                child: TimeSlot(
                  dateTime: widget.dateTime,
                  isBusinessHour: widget.isBusinessHour,
                  isLunchBreak: widget.isLunchBreak,
                  isAvailable: widget.isAvailable,
                  isGreyedOut: widget.isGreyedOut,
                  onTap: widget.onTap,
                  onLongPress: widget.onLongPress,
                  height: widget.height,
                  child: widget.child,
                ),
              ),
            );
          },
        );
      },
    );
  }

  bool _onWillAccept(AppointmentDragData? data, CalendarProvider calendarProvider) {
    if (data == null) return false;

    setState(() {
      _isDragOver = true;
    });

    // Quick validation for immediate feedback
    _validateDropTarget(data, calendarProvider);

    return true; // Always return true to allow the drag operation to continue
  }

  void _validateDropTarget(AppointmentDragData data, CalendarProvider calendarProvider) {
    final appointment = data.appointment;
    final duration = appointment.endTime.difference(appointment.startTime);
    final newEndTime = widget.dateTime.add(duration);

    // Use fast validation for instant feedback
    final validation = AppointmentDragDropService.validateDropTargetFast(
      appointment: appointment,
      newStartTime: widget.dateTime,
      newEndTime: newEndTime,
      newStaffId: widget.staffId,
      calendarProvider: calendarProvider,
    );

    setState(() {
      _isValidDropTarget = validation.isValid;
      _validationMessage = validation.reason;
    });
  }

  void _onAccept(AppointmentDragData data, CalendarProvider calendarProvider) async {
    setState(() {
      _isDragOver = false;
    });

    if (!_isValidDropTarget) {
      _triggerHapticFeedback();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Nu se poate muta programarea în acest slot'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    _triggerHapticFeedback();

    // Show loading indicator with animation
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            SizedBox(width: 12),
            Text('Se actualizează programarea...'),
          ],
        ),
        duration: Duration(seconds: 10), // Longer duration for loading
      ),
    );

    final appointment = data.appointment;
    final duration = appointment.endTime.difference(appointment.startTime);
    final newEndTime = widget.dateTime.add(duration);

    final result = await AppointmentDragDropService.performDrop(
      appointment: appointment,
      newStartTime: widget.dateTime,
      newEndTime: newEndTime,
      newStaffId: widget.staffId,
      calendarProvider: calendarProvider,
    );

    if (mounted) {
      if (result.success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result.message),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        // Handle specific conflict types
        if (result.conflictType == ConflictType.appointment) {
          _showConflictDialog(result.message);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(result.message),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  void _onLeave(AppointmentDragData? data) {
    setState(() {
      _isDragOver = false;
      _isValidDropTarget = false;
    });
  }

  void _showConflictDialog(String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Conflict de programare'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

/// Enhanced staff column that can accept drops for groomer reassignment
class DroppableStaffColumn extends StatefulWidget {
  final String staffId;
  final String staffName;
  final Widget child;
  final bool isDragEnabled;

  const DroppableStaffColumn({
    super.key,
    required this.staffId,
    required this.staffName,
    required this.child,
    this.isDragEnabled = true,
  });

  @override
  State<DroppableStaffColumn> createState() => _DroppableStaffColumnState();
}

class _DroppableStaffColumnState extends State<DroppableStaffColumn> {
  bool _isDragOver = false;
  bool _isValidDropTarget = false;

  void _triggerHapticFeedback() {
    HapticFeedback.lightImpact();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isDragEnabled) {
      return widget.child;
    }

    return Consumer<CalendarProvider>(
      builder: (context, calendarProvider, child) {
        return DragTarget<AppointmentDragData>(
          onWillAcceptWithDetails: (details) => _onWillAccept(details.data, calendarProvider),
          onAcceptWithDetails: (details) => _onAccept(details.data, calendarProvider),
          onLeave: (data) => _onLeave(data),
          builder: (context, candidateData, rejectedData) {
            return AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              decoration: BoxDecoration(
                border: _isDragOver
                    ? Border.all(
                        color: _isValidDropTarget
                            ? Colors.blue.withValues(alpha: 0.8)
                            : Colors.red.withValues(alpha: 0.8),
                        width: 2,
                      )
                    : null,
                color: _isDragOver
                    ? (_isValidDropTarget
                        ? Colors.blue.withValues(alpha: 0.05)
                        : Colors.red.withValues(alpha: 0.05))
                    : null,
              ),
              child: widget.child,
            );
          },
        );
      },
    );
  }

  bool _onWillAccept(AppointmentDragData? data, CalendarProvider calendarProvider) {
    if (data == null) return false;

    // Don't accept if it's the same staff member
    if (data.appointment.groomerId == widget.staffId) {
      setState(() {
        _isDragOver = true;
        _isValidDropTarget = false;
      });
      return false;
    }

    setState(() {
      _isDragOver = true;
    });

    _validateStaffReassignment(data, calendarProvider);
    return true;
  }

  void _validateStaffReassignment(AppointmentDragData data, CalendarProvider calendarProvider) {
    final appointment = data.appointment;

    // Use fast validation for instant feedback
    final validation = AppointmentDragDropService.validateDropTargetFast(
      appointment: appointment,
      newStartTime: appointment.startTime,
      newEndTime: appointment.endTime,
      newStaffId: widget.staffId,
      calendarProvider: calendarProvider,
    );

    setState(() {
      _isValidDropTarget = validation.isValid;
    });
  }

  void _onAccept(AppointmentDragData data, CalendarProvider calendarProvider) async {
    setState(() {
      _isDragOver = false;
    });

    if (!_isValidDropTarget) {
      _triggerHapticFeedback();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Nu se poate reasigna programarea la ${widget.staffName}'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    _triggerHapticFeedback();

    // Show loading indicator with animation
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            SizedBox(width: 12),
            Text('Se reasignează programarea...'),
          ],
        ),
        duration: Duration(seconds: 10), // Longer duration for loading
      ),
    );

    final result = await AppointmentDragDropService.performDrop(
      appointment: data.appointment,
      newStartTime: data.appointment.startTime,
      newEndTime: data.appointment.endTime,
      newStaffId: widget.staffId,
      calendarProvider: calendarProvider,
    );

    if (mounted) {
      if (result.success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Programarea a fost reasignată la ${widget.staffName}'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(result.message),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _onLeave(AppointmentDragData? data) {
    setState(() {
      _isDragOver = false;
      _isValidDropTarget = false;
    });
  }
}
