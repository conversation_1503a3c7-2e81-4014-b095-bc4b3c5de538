import 'package:flutter/foundation.dart';
import '../models/notification_history.dart';
import '../models/api_response.dart';
import 'api_service.dart';

class NotificationService {
  // Get paginated notifications (new API format)
  static Future<ApiResponse<Map<String, dynamic>>> getNotificationsPaginated({
    int page = 0,
    int pageSize = 20,
    bool? unreadOnly,
    String? type,
  }) async {
    try {
      final queryParams = <String, String>{
        'page': page.toString(),
        'pageSize': pageSize.toString(),
      };

      if (unreadOnly != null) queryParams['unreadOnly'] = unreadOnly.toString();
      if (type != null) queryParams['type'] = type;

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/notifications',
        queryParams: queryParams,
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        // Parse the notifications array within the response
        final responseData = response.data!;
        if (responseData['notifications'] is List) {
          final notifications = <NotificationHistory>[];
          for (final item in responseData['notifications'] as List) {
            if (item is Map<String, dynamic>) {
              try {
                notifications.add(NotificationHistory.fromJson(item));
              } catch (e) {
                debugPrint('⚠️ Failed to parse notification: $e');
              }
            }
          }

          // Return the full response with parsed notifications
          return ApiResponse.success({
            'notifications': notifications,
            'totalCount': responseData['totalCount'] ?? 0,
            'unreadCount': responseData['unreadCount'] ?? 0,
            'page': responseData['page'] ?? page,
            'pageSize': responseData['pageSize'] ?? pageSize,
            'hasMore': responseData['hasMore'] ?? false,
          });
        }
      }

      return ApiResponse<Map<String, dynamic>>.error(response.error ?? 'Failed to get notifications');
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>.error('Notification API not available: $e');
    }
  }

  // Get all notifications (legacy method for backward compatibility)
  static Future<ApiResponse<List<NotificationHistory>>> getNotifications({
    bool? unreadOnly,
    String? type,
    int? limit,
  }) async {
    try {
      // Use the new paginated API but return just the notifications list
      final response = await getNotificationsPaginated(
        page: 0,
        pageSize: limit ?? 50,
        unreadOnly: unreadOnly,
        type: type,
      );

      if (response.success && response.data != null) {
        final notifications = response.data!['notifications'] as List<NotificationHistory>;
        return ApiResponse.success(notifications);
      }

      return ApiResponse<List<NotificationHistory>>.error(response.error ?? 'Failed to get notifications');
    } catch (e) {
      return ApiResponse<List<NotificationHistory>>.error('Notification API not available: $e');
    }
  }

  // Get notification by ID
  static Future<ApiResponse<NotificationHistory>> getNotification(String id) async {
    final response = await ApiService.get<NotificationHistory>(
      '/api/notifications/$id',
      fromJson: (data) => NotificationHistory.fromJson(data),
    );

    return response;
  }

  // Mark notification as read
  static Future<ApiResponse<NotificationHistory>> markAsRead(String id) async {
    final response = await ApiService.put<NotificationHistory>(
      '/api/notifications/$id/read',
      fromJson: (data) => NotificationHistory.fromJson(data),
    );

    return response;
  }

  // Mark notifications as read (bulk operation)
  static Future<ApiResponse<void>> markNotificationsAsRead({
    List<String>? notificationIds,
    bool markAll = false,
  }) async {
    final body = <String, dynamic>{};

    if (markAll) {
      body['markAll'] = true;
    } else if (notificationIds != null && notificationIds.isNotEmpty) {
      body['notificationIds'] = notificationIds;
    } else {
      return ApiResponse<void>.error('Either provide notification IDs or set markAll to true');
    }

    final response = await ApiService.post<void>(
      '/api/notifications/mark-read',
      body: body,
    );

    return response;
  }

  // Mark all notifications as read (legacy method)
  static Future<ApiResponse<void>> markAllAsRead() async {
    return markNotificationsAsRead(markAll: true);
  }

  // Delete notification
  static Future<ApiResponse<void>> deleteNotification(String id) async {
    final response = await ApiService.delete<void>('/api/notifications/$id');
    return response;
  }

  // Delete all notifications
  static Future<ApiResponse<void>> deleteAllNotifications() async {
    final response = await ApiService.delete<void>('/api/notifications/all');
    return response;
  }

  // Get unread count
  static Future<ApiResponse<int>> getUnreadCount() async {
    final response = await ApiService.get<int>(
      '/api/notifications/unread-count',
      fromJson: (data) => data as int,
    );

    return response;
  }

  // Get notifications by type
  static Future<ApiResponse<List<NotificationHistory>>> getNotificationsByType(String type) async {
    return getNotifications(type: type);
  }

  // Get recent notifications (last 24 hours)
  static Future<ApiResponse<List<NotificationHistory>>> getRecentNotifications({int limit = 10}) async {
    try {
      final response = await ApiService.get<List<dynamic>>(
        '/api/notifications/recent',
        queryParams: {'limit': limit.toString()},
        fromJson: (data) => data as List<dynamic>,
      );

      if (response.success && response.data != null) {
        final notifications = <NotificationHistory>[];
        for (final item in response.data!) {
          if (item is Map<String, dynamic>) {
            try {
              notifications.add(NotificationHistory.fromJson(item));
            } catch (e) {
              debugPrint('⚠️ Failed to parse recent notification: $e');
            }
          }
        }
        return ApiResponse.success(notifications);
      }

      return ApiResponse<List<NotificationHistory>>.error(response.error ?? 'Failed to get recent notifications');
    } catch (e) {
      return ApiResponse<List<NotificationHistory>>.error('Recent notifications API not available: $e');
    }
  }

  // Create custom notification
  static Future<ApiResponse<NotificationHistory>> createNotification({
    required String title,
    required String message,
    required String type,
    Map<String, dynamic>? metadata,
  }) async {
    final body = <String, dynamic>{
      'title': title,
      'message': message,
      'type': type,
      if (metadata != null) 'metadata': metadata,
    };

    final response = await ApiService.post<NotificationHistory>(
      '/api/notifications',
      body: body,
      fromJson: (data) => NotificationHistory.fromJson(data),
    );

    return response;
  }

  // Send push notification
  static Future<ApiResponse<void>> sendPushNotification({
    required String title,
    required String message,
    String? type,
    Map<String, dynamic>? data,
  }) async {
    final body = <String, dynamic>{
      'title': title,
      'message': message,
      if (type != null) 'type': type,
      if (data != null) 'data': data,
    };

    final response = await ApiService.post<void>(
      '/api/notifications/push',
      body: body,
    );

    return response;
  }

  // Schedule notification
  static Future<ApiResponse<NotificationHistory>> scheduleNotification({
    required String title,
    required String message,
    required String type,
    required DateTime scheduledTime,
    Map<String, dynamic>? metadata,
  }) async {
    final body = <String, dynamic>{
      'title': title,
      'message': message,
      'type': type,
      'scheduledTime': scheduledTime.toIso8601String(),
      if (metadata != null) 'metadata': metadata,
    };

    final response = await ApiService.post<NotificationHistory>(
      '/api/notifications/schedule',
      body: body,
      fromJson: (data) => NotificationHistory.fromJson(data),
    );

    return response;
  }

  // Cancel scheduled notification
  static Future<ApiResponse<void>> cancelScheduledNotification(String id) async {
    final response = await ApiService.delete<void>('/api/notifications/scheduled/$id');
    return response;
  }

  // Get notification statistics
  static Future<ApiResponse<Map<String, dynamic>>> getNotificationStats({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final queryParams = <String, String>{};
    
    if (startDate != null) {
      queryParams['startDate'] = startDate.toIso8601String().split('T')[0];
    }
    if (endDate != null) {
      queryParams['endDate'] = endDate.toIso8601String().split('T')[0];
    }

    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/notifications/stats',
      queryParams: queryParams.isNotEmpty ? queryParams : null,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Update notification preferences
  static Future<ApiResponse<Map<String, dynamic>>> updateNotificationPreferences(
    Map<String, dynamic> preferences,
  ) async {
    final response = await ApiService.put<Map<String, dynamic>>(
      '/api/notifications/preferences',
      body: preferences,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Get notification preferences
  static Future<ApiResponse<Map<String, dynamic>>> getNotificationPreferences() async {
    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/notifications/preferences',
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Generate test notification data
  static Future<ApiResponse<void>> generateTestData({int count = 10}) async {
    final response = await ApiService.post<void>(
      '/api/notifications/generate-test-data?count=${count.toString()}',
    );

    return response;
  }

  // Register FCM token (alternative endpoint - prefer NotificationHandler)
  static Future<ApiResponse<void>> registerToken({
    required String token,
    required String deviceId,
    required String platform, // 'ANDROID' or 'IOS'
  }) async {
    final body = <String, dynamic>{
      'token': token,
      'deviceId': deviceId,
      'platform': platform,
    };

    final response = await ApiService.post<void>(
      '/api/notifications/register-token',
      body: body,
    );

    return response;
  }

  // Test notification system
  static Future<ApiResponse<void>> testNotification({
    String? type = 'test',
  }) async {
    final body = <String, dynamic>{
      'type': type,
    };

    final response = await ApiService.post<void>(
      '/api/notifications/test',
      body: body,
    );

    return response;
  }

  // Export notification history
  static Future<ApiResponse<String>> exportNotifications({
    DateTime? startDate,
    DateTime? endDate,
    String format = 'csv',
    List<String>? types,
  }) async {
    final body = <String, dynamic>{
      'format': format,
      if (startDate != null) 'startDate': startDate.toIso8601String().split('T')[0],
      if (endDate != null) 'endDate': endDate.toIso8601String().split('T')[0],
      if (types != null) 'types': types,
    };

    final response = await ApiService.post<String>(
      '/api/notifications/export',
      body: body,
      fromJson: (data) => data.toString(),
    );

    return response;
  }

  // Bulk mark as read
  static Future<ApiResponse<void>> bulkMarkAsRead(List<String> notificationIds) async {
    final response = await ApiService.put<void>(
      '/api/notifications/bulk-read',
      body: {'notificationIds': notificationIds},
    );

    return response;
  }

  // Bulk delete notifications
  static Future<ApiResponse<void>> bulkDeleteNotifications(List<String> notificationIds) async {
    final response = await ApiService.delete<void>(
      '/api/notifications/bulk-delete',
    );

    return response;
  }

  // Get notification templates
  static Future<ApiResponse<List<Map<String, dynamic>>>> getNotificationTemplates() async {
    final response = await ApiService.get<List<Map<String, dynamic>>>(
      '/api/notifications/templates',
      fromJson: (data) => (data as List).map((item) => Map<String, dynamic>.from(item)).toList(),
    );

    return response;
  }

  // Create notification template
  static Future<ApiResponse<Map<String, dynamic>>> createNotificationTemplate({
    required String name,
    required String title,
    required String message,
    required String type,
    Map<String, dynamic>? variables,
  }) async {
    final body = <String, dynamic>{
      'name': name,
      'title': title,
      'message': message,
      'type': type,
      if (variables != null) 'variables': variables,
    };

    final response = await ApiService.post<Map<String, dynamic>>(
      '/api/notifications/templates',
      body: body,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Validate notification data
  static String? validateNotification({
    required String title,
    required String message,
    required String type,
  }) {
    if (title.trim().isEmpty) {
      return 'Titlul notificării este obligatoriu';
    }

    if (message.trim().isEmpty) {
      return 'Mesajul notificării este obligatoriu';
    }

    if (type.trim().isEmpty) {
      return 'Tipul notificării este obligatoriu';
    }

    if (title.length > 100) {
      return 'Titlul nu poate avea mai mult de 100 de caractere';
    }

    if (message.length > 500) {
      return 'Mesajul nu poate avea mai mult de 500 de caractere';
    }

    return null; // No validation errors
  }

  // Track notification sent
  static Future<ApiResponse<void>> trackNotificationSent({
    required String appointmentId,
    required String type,
    required Map<String, bool> results,
    required DateTime timestamp,
  }) async {
    final body = <String, dynamic>{
      'appointmentId': appointmentId,
      'type': type,
      'results': results,
      'timestamp': timestamp.toIso8601String(),
    };

    final response = await ApiService.post<void>(
      '/api/notifications/track',
      body: body,
    );

    return response;
  }
}
