import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../config/theme/app_theme.dart';
import '../../models/service.dart';
import '../../providers/calendar_provider.dart';
import '../../providers/role_provider.dart';
import 'appointment_form_data.dart';
import '../forms/service_form_dialog.dart';
import '../../services/ui_notification_service.dart';

class MultipleServicesWidget extends StatefulWidget {
  final AppointmentFormData formData;
  final Future<void> Function(String) onAddService;
  final Future<void> Function(String) onRemoveService;

  const MultipleServicesWidget({
    super.key,
    required this.formData,
    required this.onAddService,
    required this.onRemoveService,
  });

  @override
  State<MultipleServicesWidget> createState() => _MultipleServicesWidgetState();
}

class _MultipleServicesWidgetState extends State<MultipleServicesWidget> {

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            const Text(
              'Servicii',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            IconButton(
              onPressed: () => _showAddServiceDialog(context),
              icon: const Icon(Icons.add_circle, color: AppColors.forestGreen),
              tooltip: 'Adaugă serviciu',
            ),
          ],
        ),
        const SizedBox(height: 8),
        if (widget.formData.services.isEmpty)
          InkWell(
            onTap: () => _showAddServiceDialog(context),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.grey),
                  SizedBox(width: 8),
                  Text(
                    'Nu sunt servicii selectate',
                    style: TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ),
          )
        else
          ...widget.formData.services.map((service) => _buildServiceChip(service)),
      ],
    );
  }

  Widget _buildServiceChip(String service) {
    return Consumer<CalendarProvider>(
      builder: (context, calendarProvider, child) {
        final serviceDetails = calendarProvider.getServiceDetails();
        final details = serviceDetails[service];
        final duration = details?['duration'] ?? 60;

        // Create Service object to use new pricing methods
        String priceText;
        bool hasSizeBasedPricing = false;
        try {
          if (details != null) {
            final serviceObj = Service.fromJson(details);
            priceText = serviceObj.getFormattedPriceForSize(widget.formData.petSize);
            hasSizeBasedPricing = serviceObj.sizePrices != null;

            // Debug logging for price calculation
            debugPrint('💰 Service "$service" price for size ${widget.formData.petSize}: $priceText');
          } else {
            priceText = '50.00 RON';
          }
        } catch (e) {
          // Fallback to old logic
          final price = details?['price'] ?? 50.0;
          priceText = '${price.toStringAsFixed(0)} RON';
          debugPrint('⚠️ Fallback pricing for service "$service": $priceText');
        }

        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: Chip(
            label: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Flexible(
                      child: Text(
                        service,
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                    ),
                    if (hasSizeBasedPricing) ...[
                      const SizedBox(width: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                        decoration: BoxDecoration(
                          color: AppColors.forestGreen,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          widget.formData.petSize,
                          style: const TextStyle(
                            fontSize: 9,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                Text(
                  '${duration}min • $priceText',
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
            deleteIcon: const Icon(Icons.close, size: 18),
            onDeleted: () async => await widget.onRemoveService(service),
            backgroundColor: AppColors.forestGreen.withValues(alpha: 0.1),
            deleteIconColor: AppColors.forestGreen,
            side: const BorderSide(color: AppColors.forestGreen),
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          ),
        );
      },
    );
  }

  void _showAddServiceDialog(BuildContext context) {
    final availableServices = widget.formData.availableServices
        .where((service) => !widget.formData.services.contains(service))
        .toList();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Consumer2<CalendarProvider, RoleProvider>(
          builder: (context, calendarProvider, roleProvider, child) {
            final serviceDetails = calendarProvider.getServiceDetails();
            // Only add 1 to itemCount if user has permission to create services
            final canCreateServices = roleProvider.hasManagementAccess || roleProvider.canManageServices();
            final itemCount = availableServices.length + (canCreateServices ? 1 : 0);

            return AlertDialog(
              title: const Text('Selectează serviciu'),
              content: SizedBox(
                width: double.maxFinite,
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: itemCount,
                  itemBuilder: (context, index) {
                    if (index == availableServices.length && canCreateServices) {
                      return ListTile(
                        leading: const Icon(Icons.add, color: AppColors.forestGreen),
                        title: const Text('Creează serviciu nou'),
                        onTap: () async {
                          Navigator.of(context).pop();
                          await _showCreateServiceDialog(context);
                        },
                      );
                    }
                    final service = availableServices[index];
                    final details = serviceDetails[service];
                    final duration = details?['duration'] ?? 60;

                    // Create Service object to use new pricing methods
                    String priceText;
                    bool hasSizeBasedPricing = false;
                    try {
                      if (details != null) {
                        final serviceObj = Service.fromJson(details);
                        priceText = serviceObj.getFormattedPriceForSize(widget.formData.petSize);
                        hasSizeBasedPricing = serviceObj.sizePrices != null;

                        // Debug logging for service selection
                        debugPrint('🛍️ Service selection "$service" price for size ${widget.formData.petSize}: $priceText');
                      } else {
                        priceText = '50.00 RON';
                      }
                    } catch (e) {
                      // Fallback to old logic
                      final price = details?['price'] ?? 50.0;
                      priceText = '${price.toStringAsFixed(0)} RON';
                      debugPrint('⚠️ Fallback pricing for service selection "$service": $priceText');
                    }

                    return ListTile(
                      title: Row(
                        children: [
                          Expanded(child: Text(service)),
                          if (hasSizeBasedPricing) ...[
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: AppColors.forestGreen.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: AppColors.forestGreen, width: 0.5),
                              ),
                              child: Text(
                                'Mărime ${widget.formData.petSize}',
                                style: const TextStyle(
                                  fontSize: 10,
                                  color: AppColors.forestGreen,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${duration}min',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            priceText,
                            style: const TextStyle(
                              color: AppColors.forestGreen,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      trailing: const Icon(Icons.add, color: AppColors.forestGreen),
                      onTap: () async {
                        Navigator.of(context).pop();
                        await widget.onAddService(service);
                      },
                    );
                  },
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Anulează'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  Future<void> _showCreateServiceDialog(BuildContext context) async {
    // Store the context and provider reference before the async operation
    final calendarProvider = Provider.of<CalendarProvider>(context, listen: false);

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (_) => ServiceFormDialog(
        showCreateAnotherOption: false, // Disable "Create Another" workflow
        onServiceSaved: (service) async {
          // Handle the service creation without using the disposed context
          await _handleNewServiceCreated(service, calendarProvider);
        },
      ),
    );
  }

  Future<void> _handleNewServiceCreated(Service service, CalendarProvider calendarProvider) async {
    try {
      // Reload services from the backend
      await calendarProvider.loadServices();

      // Update the available services list in the form data
      if (mounted) {
        setState(() {
          widget.formData.availableServices = calendarProvider.getAvailableServiceNames();
        });
      }

      // Add the new service to the appointment
      await widget.onAddService(service.name);

      // Success feedback handled by ServiceFormDialog
    } catch (e) {
      // Handle errors
      if (mounted) {
        UINotificationService.showError(
          context: context,
          title: 'Eroare',
          message: 'Eroare la adăugarea serviciului: $e',
        );
      }
    }
  }
}
