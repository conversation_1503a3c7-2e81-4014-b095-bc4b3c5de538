import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Appointment Validation Tests', () {
    
    group('Time Validation', () {
      test('validateAppointmentTime_PastDateTime_ReturnsFalse', () {
        // Arrange
        final pastStartTime = DateTime.now().subtract(const Duration(hours: 1));
        final pastEndTime = pastStartTime.add(const Duration(hours: 1));

        // Act
        final isValidTime = _validateAppointmentTime(pastStartTime, pastEndTime);

        // Assert
        expect(isValidTime, isFalse);
      });

      test('validateAppointmentTime_FutureDateTime_ReturnsTrue', () {
        // Arrange
        final futureStartTime = DateTime.now().add(const Duration(hours: 1));
        final futureEndTime = futureStartTime.add(const Duration(hours: 1));

        // Act
        final isValidTime = _validateAppointmentTime(futureStartTime, futureEndTime);

        // Assert
        expect(isValidTime, isTrue);
      });

      test('validateAppointmentTime_EndBeforeStart_ReturnsFalse', () {
        // Arrange
        final startTime = DateTime(2024, 6, 15, 11, 0);
        final endTime = DateTime(2024, 6, 15, 10, 0); // End before start

        // Act
        final isValidTime = _validateAppointmentTime(startTime, endTime);

        // Assert
        expect(isValidTime, isFalse);
      });

      test('validateAppointmentTime_SameStartAndEnd_ReturnsFalse', () {
        // Arrange
        final startTime = DateTime(2024, 6, 15, 10, 0);
        final endTime = DateTime(2024, 6, 15, 10, 0); // Same time

        // Act
        final isValidTime = _validateAppointmentTime(startTime, endTime);

        // Assert
        expect(isValidTime, isFalse);
      });

      test('validateAppointmentDuration_TooShort_ReturnsFalse', () {
        // Arrange
        final startTime = DateTime(2024, 6, 15, 10, 0);
        final endTime = DateTime(2024, 6, 15, 10, 10); // Only 10 minutes

        // Act
        final isValidDuration = _validateAppointmentDuration(startTime, endTime);

        // Assert
        expect(isValidDuration, isFalse);
      });

      test('validateAppointmentDuration_MinimumValid_ReturnsTrue', () {
        // Arrange
        final startTime = DateTime(2024, 6, 15, 10, 0);
        final endTime = DateTime(2024, 6, 15, 10, 15); // 15 minutes (minimum)

        // Act
        final isValidDuration = _validateAppointmentDuration(startTime, endTime);

        // Assert
        expect(isValidDuration, isTrue);
      });

      test('validateAppointmentDuration_TooLong_ReturnsFalse', () {
        // Arrange
        final startTime = DateTime(2024, 6, 15, 10, 0);
        final endTime = DateTime(2024, 6, 15, 22, 0); // 12 hours (too long)

        // Act
        final isValidDuration = _validateAppointmentDuration(startTime, endTime);

        // Assert
        expect(isValidDuration, isFalse);
      });

      test('validateBusinessHours_WithinHours_ReturnsTrue', () {
        // Arrange
        final startTime = DateTime(2024, 6, 15, 10, 0); // 10:00 AM
        final endTime = DateTime(2024, 6, 15, 11, 0); // 11:00 AM

        // Act
        final isWithinBusinessHours = _validateBusinessHours(startTime, endTime);

        // Assert
        expect(isWithinBusinessHours, isTrue);
      });

      test('validateBusinessHours_BeforeHours_ReturnsFalse', () {
        // Arrange
        final startTime = DateTime(2024, 6, 15, 7, 0); // 7:00 AM (before business hours)
        final endTime = DateTime(2024, 6, 15, 8, 0); // 8:00 AM

        // Act
        final isWithinBusinessHours = _validateBusinessHours(startTime, endTime);

        // Assert
        expect(isWithinBusinessHours, isFalse);
      });

      test('validateBusinessHours_AfterHours_ReturnsFalse', () {
        // Arrange
        final startTime = DateTime(2024, 6, 15, 19, 0); // 7:00 PM (after business hours)
        final endTime = DateTime(2024, 6, 15, 20, 0); // 8:00 PM

        // Act
        final isWithinBusinessHours = _validateBusinessHours(startTime, endTime);

        // Assert
        expect(isWithinBusinessHours, isFalse);
      });
    });

    group('Service Validation', () {
      test('validateServices_EmptyList_ReturnsFalse', () {
        // Arrange
        final services = <String>[];

        // Act
        final isValidServices = _validateServices(services);

        // Assert
        expect(isValidServices, isFalse);
      });

      test('validateServices_ValidSingleService_ReturnsTrue', () {
        // Arrange
        final services = ['Tuns complet'];

        // Act
        final isValidServices = _validateServices(services);

        // Assert
        expect(isValidServices, isTrue);
      });

      test('validateServices_ValidMultipleServices_ReturnsTrue', () {
        // Arrange
        final services = ['Tuns complet', 'Spălare', 'Tăiere unghii'];

        // Act
        final isValidServices = _validateServices(services);

        // Assert
        expect(isValidServices, isTrue);
      });

      test('validateServices_DuplicateServices_ReturnsFalse', () {
        // Arrange
        final services = ['Tuns complet', 'Tuns complet', 'Spălare'];

        // Act
        final isValidServices = _validateServices(services);

        // Assert
        expect(isValidServices, isFalse);
      });

      test('validateServices_EmptyServiceName_ReturnsFalse', () {
        // Arrange
        final services = ['Tuns complet', '', 'Spălare'];

        // Act
        final isValidServices = _validateServices(services);

        // Assert
        expect(isValidServices, isFalse);
      });
    });

    group('Client and Pet Validation', () {
      test('validateClientId_ValidId_ReturnsTrue', () {
        // Arrange
        const clientId = 'client-123';

        // Act
        final isValidClientId = _validateClientId(clientId);

        // Assert
        expect(isValidClientId, isTrue);
      });

      test('validateClientId_EmptyId_ReturnsFalse', () {
        // Arrange
        const clientId = '';

        // Act
        final isValidClientId = _validateClientId(clientId);

        // Assert
        expect(isValidClientId, isFalse);
      });

      test('validatePetId_ValidId_ReturnsTrue', () {
        // Arrange
        const petId = 'pet-123';

        // Act
        final isValidPetId = _validatePetId(petId);

        // Assert
        expect(isValidPetId, isTrue);
      });

      test('validatePetId_EmptyId_ReturnsFalse', () {
        // Arrange
        const petId = '';

        // Act
        final isValidPetId = _validatePetId(petId);

        // Assert
        expect(isValidPetId, isFalse);
      });

      test('validateGroomerId_ValidId_ReturnsTrue', () {
        // Arrange
        const groomerId = 'staff-123';

        // Act
        final isValidGroomerId = _validateGroomerId(groomerId);

        // Assert
        expect(isValidGroomerId, isTrue);
      });

      test('validateGroomerId_EmptyId_ReturnsFalse', () {
        // Arrange
        const groomerId = '';

        // Act
        final isValidGroomerId = _validateGroomerId(groomerId);

        // Assert
        expect(isValidGroomerId, isFalse);
      });
    });

    group('Romanian Text Validation', () {
      test('validateRomanianNotes_ValidText_ReturnsTrue', () {
        // Arrange
        const notes = 'Câinele este foarte agitat și are nevoie de atenție specială. Să se folosească șampon pentru blană sensibilă.';

        // Act
        final isValidNotes = _validateRomanianNotes(notes);

        // Assert
        expect(isValidNotes, isTrue);
        expect(notes, contains('ă'));
        expect(notes, contains('ș'));
        expect(notes, contains('â'));
        expect(notes, contains('ț'));
      });

      test('validateRomanianNotes_EmptyText_ReturnsTrue', () {
        // Arrange
        const notes = '';

        // Act
        final isValidNotes = _validateRomanianNotes(notes);

        // Assert
        expect(isValidNotes, isTrue); // Empty notes are allowed
      });

      test('validateRomanianNotes_TooLong_ReturnsFalse', () {
        // Arrange
        final notes = 'A' * 1001; // 1001 characters (too long)

        // Act
        final isValidNotes = _validateRomanianNotes(notes);

        // Assert
        expect(isValidNotes, isFalse);
      });

      test('validateRomanianNotes_SpecialCharacters_ReturnsTrue', () {
        // Arrange
        const notes = 'Notă specială: câinele are probleme cu ăsta, îi place să mănânce și să doarmă. Șeful a spus că trebuie să ținem cont de asta.';

        // Act
        final isValidNotes = _validateRomanianNotes(notes);

        // Assert
        expect(isValidNotes, isTrue);
        expect(notes, contains('ă'));
        expect(notes, contains('â'));
        expect(notes, contains('î'));
        expect(notes, contains('ș'));
        expect(notes, contains('ț'));
      });
    });

    group('Conflict Detection Logic', () {
      test('checkTimeConflict_OverlappingTimes_ReturnsTrue', () {
        // Arrange
        final appointment1Start = DateTime(2024, 6, 15, 10, 0);
        final appointment1End = DateTime(2024, 6, 15, 11, 0);
        final appointment2Start = DateTime(2024, 6, 15, 10, 30);
        final appointment2End = DateTime(2024, 6, 15, 11, 30);
        const groomerId = 'staff-123';

        // Act
        final hasConflict = _checkTimeConflict(
          appointment1Start, appointment1End, groomerId,
          appointment2Start, appointment2End, groomerId,
        );

        // Assert
        expect(hasConflict, isTrue);
      });

      test('checkTimeConflict_NonOverlappingTimes_ReturnsFalse', () {
        // Arrange
        final appointment1Start = DateTime(2024, 6, 15, 10, 0);
        final appointment1End = DateTime(2024, 6, 15, 11, 0);
        final appointment2Start = DateTime(2024, 6, 15, 11, 0);
        final appointment2End = DateTime(2024, 6, 15, 12, 0);
        const groomerId = 'staff-123';

        // Act
        final hasConflict = _checkTimeConflict(
          appointment1Start, appointment1End, groomerId,
          appointment2Start, appointment2End, groomerId,
        );

        // Assert
        expect(hasConflict, isFalse);
      });

      test('checkTimeConflict_DifferentGroomers_ReturnsFalse', () {
        // Arrange
        final appointment1Start = DateTime(2024, 6, 15, 10, 0);
        final appointment1End = DateTime(2024, 6, 15, 11, 0);
        final appointment2Start = DateTime(2024, 6, 15, 10, 30);
        final appointment2End = DateTime(2024, 6, 15, 11, 30);
        const groomer1Id = 'staff-123';
        const groomer2Id = 'staff-456';

        // Act
        final hasConflict = _checkTimeConflict(
          appointment1Start, appointment1End, groomer1Id,
          appointment2Start, appointment2End, groomer2Id,
        );

        // Assert
        expect(hasConflict, isFalse);
      });

      test('checkBlockTimeConflict_AppointmentOverlapsBlock_ReturnsTrue', () {
        // Arrange
        final appointmentStart = DateTime(2024, 6, 15, 10, 30);
        final appointmentEnd = DateTime(2024, 6, 15, 11, 30);
        final blockStart = DateTime(2024, 6, 15, 10, 0);
        final blockEnd = DateTime(2024, 6, 15, 11, 0);
        const groomerId = 'staff-123';

        // Act
        final hasConflict = _checkBlockTimeConflict(
          appointmentStart, appointmentEnd, groomerId,
          blockStart, blockEnd, [groomerId],
        );

        // Assert
        expect(hasConflict, isTrue);
      });

      test('checkBlockTimeConflict_AppointmentAfterBlock_ReturnsFalse', () {
        // Arrange
        final appointmentStart = DateTime(2024, 6, 15, 11, 0);
        final appointmentEnd = DateTime(2024, 6, 15, 12, 0);
        final blockStart = DateTime(2024, 6, 15, 10, 0);
        final blockEnd = DateTime(2024, 6, 15, 11, 0);
        const groomerId = 'staff-123';

        // Act
        final hasConflict = _checkBlockTimeConflict(
          appointmentStart, appointmentEnd, groomerId,
          blockStart, blockEnd, [groomerId],
        );

        // Assert
        expect(hasConflict, isFalse);
      });

      test('checkBlockTimeConflict_DifferentGroomer_ReturnsFalse', () {
        // Arrange
        final appointmentStart = DateTime(2024, 6, 15, 10, 30);
        final appointmentEnd = DateTime(2024, 6, 15, 11, 30);
        final blockStart = DateTime(2024, 6, 15, 10, 0);
        final blockEnd = DateTime(2024, 6, 15, 11, 0);
        const appointmentGroomerId = 'staff-123';
        const blockGroomerId = 'staff-456';

        // Act
        final hasConflict = _checkBlockTimeConflict(
          appointmentStart, appointmentEnd, appointmentGroomerId,
          blockStart, blockEnd, [blockGroomerId],
        );

        // Assert
        expect(hasConflict, isFalse);
      });
    });
  });
}

// Helper functions for appointment validation
bool _validateAppointmentTime(DateTime startTime, DateTime endTime) {
  // Check if times are in the future
  if (startTime.isBefore(DateTime.now()) || endTime.isBefore(DateTime.now())) {
    return false;
  }
  
  // Check if end time is after start time
  if (endTime.isBefore(startTime) || endTime.isAtSameMomentAs(startTime)) {
    return false;
  }
  
  return true;
}

bool _validateAppointmentDuration(DateTime startTime, DateTime endTime) {
  final duration = endTime.difference(startTime);
  
  // Minimum 15 minutes, maximum 8 hours
  return duration.inMinutes >= 15 && duration.inHours <= 8;
}

bool _validateBusinessHours(DateTime startTime, DateTime endTime) {
  // Business hours: 8:00 AM to 6:00 PM
  const businessStart = 8;
  const businessEnd = 18;
  
  return startTime.hour >= businessStart && 
         endTime.hour <= businessEnd &&
         startTime.weekday >= DateTime.monday &&
         startTime.weekday <= DateTime.saturday;
}

bool _validateServices(List<String> services) {
  if (services.isEmpty) return false;
  
  // Check for empty service names
  if (services.any((service) => service.trim().isEmpty)) return false;
  
  // Check for duplicates
  final uniqueServices = services.toSet();
  return uniqueServices.length == services.length;
}

bool _validateClientId(String clientId) {
  return clientId.isNotEmpty && clientId.trim().isNotEmpty;
}

bool _validatePetId(String petId) {
  return petId.isNotEmpty && petId.trim().isNotEmpty;
}

bool _validateGroomerId(String groomerId) {
  return groomerId.isNotEmpty && groomerId.trim().isNotEmpty;
}

bool _validateRomanianNotes(String notes) {
  // Empty notes are allowed
  if (notes.isEmpty) return true;
  
  // Maximum 1000 characters
  return notes.length <= 1000;
}

bool _checkTimeConflict(
  DateTime start1, DateTime end1, String groomer1,
  DateTime start2, DateTime end2, String groomer2,
) {
  // Different groomers can't conflict
  if (groomer1 != groomer2) return false;
  
  // Check for time overlap
  return start1.isBefore(end2) && end1.isAfter(start2);
}

bool _checkBlockTimeConflict(
  DateTime appointmentStart, DateTime appointmentEnd, String appointmentGroomer,
  DateTime blockStart, DateTime blockEnd, List<String> blockGroomers,
) {
  // Check if appointment groomer is in blocked groomers
  if (!blockGroomers.contains(appointmentGroomer)) return false;
  
  // Check for time overlap
  return appointmentStart.isBefore(blockEnd) && appointmentEnd.isAfter(blockStart);
}
