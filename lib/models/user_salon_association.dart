import 'user_role.dart';
import 'salon.dart';

/// Model representing a user's association with a salon
class UserSalonAssociation {
  final String id;
  final String userId;
  final String salonId;
  final Salon salon;
  final GroomerRole groomerRole;
  final ClientDataPermission clientDataPermission;
  final bool isActive;
  final bool isCurrentSalon;
  final DateTime joinedAt;
  final DateTime? updatedAt;
  final int clientCount;
  final String? notes;

  UserSalonAssociation({
    required this.id,
    required this.userId,
    required this.salonId,
    required this.salon,
    required this.groomerRole,
    required this.clientDataPermission,
    required this.isActive,
    required this.isCurrentSalon,
    required this.joinedAt,
    this.updatedAt,
    this.clientCount = 0,
    this.notes,
  });

  /// Create from JSON
  factory UserSalonAssociation.fromJson(Map<String, dynamic> json) {
    return UserSalonAssociation(
      id: json['id'] ?? '',
      userId: json['userId'] ?? '',
      salonId: json['salonId'] ?? '',
      salon: Salon.fromJson(Map<String, dynamic>.from(json['salon'] ?? {})),
      groomerRole: GroomerRole.fromString(json['groomerRole'] ?? 'GROOMER'),
      clientDataPermission: ClientDataPermission.fromString(json['clientDataPermission'] ?? 'NONE'),
      isActive: json['isActive'] ?? true,
      isCurrentSalon: json['isCurrentSalon'] ?? false,
      joinedAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : (json['joinedAt'] != null ? DateTime.parse(json['joinedAt']) : DateTime.now()),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      clientCount: json['clientCount'] ?? 0,
      notes: json['notes'],
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'salonId': salonId,
      'salon': salon.toJson(),
      'groomerRole': groomerRole.value,
      'clientDataPermission': clientDataPermission.value,
      'isActive': isActive,
      'isCurrentSalon': isCurrentSalon,
      'joinedAt': joinedAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'clientCount': clientCount,
      'notes': notes,
    };
  }

  /// Copy with method for immutable updates
  UserSalonAssociation copyWith({
    String? id,
    String? userId,
    String? salonId,
    Salon? salon,
    GroomerRole? groomerRole,
    ClientDataPermission? clientDataPermission,
    bool? isActive,
    bool? isCurrentSalon,
    DateTime? joinedAt,
    DateTime? updatedAt,
    int? clientCount,
    String? notes,
  }) {
    return UserSalonAssociation(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      salonId: salonId ?? this.salonId,
      salon: salon ?? this.salon,
      groomerRole: groomerRole ?? this.groomerRole,
      clientDataPermission: clientDataPermission ?? this.clientDataPermission,
      isActive: isActive ?? this.isActive,
      isCurrentSalon: isCurrentSalon ?? this.isCurrentSalon,
      joinedAt: joinedAt ?? this.joinedAt,
      updatedAt: updatedAt ?? this.updatedAt,
      clientCount: clientCount ?? this.clientCount,
      notes: notes ?? this.notes,
    );
  }

  /// Check if user has management access in this salon
  bool get hasManagementAccess => groomerRole.hasManagementAccess;

  /// Check if user can access client data in this salon
  bool get canAccessClientData => clientDataPermission != ClientDataPermission.noAccess;

  /// Check if user can see full client details in this salon
  bool get canSeeFullClientDetails => clientDataPermission == ClientDataPermission.fullAccess;

  /// Get role display name
  String get roleDisplayName => groomerRole.displayName;

  /// Get permission display name
  String get permissionDisplayName => clientDataPermission.displayName;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserSalonAssociation && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'UserSalonAssociation(id: $id, salonName: ${salon.name}, role: ${groomerRole.displayName}, isCurrentSalon: $isCurrentSalon)';
}
