import 'package:flutter_test/flutter_test.dart';
import 'package:partykidsapp/utils/formatters/phone_number_utils.dart';

void main() {
  group('Phone Number Normalization Tests', () {
    group('PhoneNumberUtils Normalization', () {
      test('should normalize ********** to +***********', () {
        const input = '**********';
        const expected = '+***********';
        
        final result = PhoneNumberUtils.formatToRomanianStandard(input);
        
        expect(result, equals(expected));
      });

      test('should normalize ********* to +***********', () {
        const input = '*********';
        const expected = '+***********';
        
        final result = PhoneNumberUtils.formatToRomanianStandard(input);
        
        expect(result, equals(expected));
      });

      test('should keep +*********** unchanged', () {
        const input = '+***********';
        const expected = '+***********';
        
        final result = PhoneNumberUtils.formatToRomanianStandard(input);
        
        expect(result, equals(expected));
      });

      test('should normalize various Romanian phone formats to same result', () {
        const testCases = [
          '**********',
          '*********', 
          '+***********',
          '+40 731 446 895',
          '0731 446 895',
          '731 446 895',
        ];
        const expected = '+***********';
        
        for (final testCase in testCases) {
          final result = PhoneNumberUtils.formatToRomanianStandard(testCase);
          expect(result, equals(expected), 
            reason: 'Failed for input: $testCase');
        }
      });

      test('should handle edge cases correctly', () {
        // Empty string
        expect(PhoneNumberUtils.formatToRomanianStandard(''), equals(''));
        
        // Invalid format
        expect(PhoneNumberUtils.formatToRomanianStandard('123'), equals('123'));
        
        // Non-Romanian number
        expect(PhoneNumberUtils.formatToRomanianStandard('+1234567890'), 
               equals('+1234567890'));
      });
    });

    group('Authentication Flow Normalization', () {
      test('should normalize phone number before Firebase authentication', () async {
        // Test that both formats result in same normalized number
        const phoneVariants = ['**********', '*********'];
        const expectedNormalized = '+***********';
        
        for (final phone in phoneVariants) {
          final normalized = PhoneNumberUtils.formatToRomanianStandard(phone);
          expect(normalized, equals(expectedNormalized),
            reason: 'Phone $phone should normalize to $expectedNormalized');
        }
      });
    });

    group('Login Consistency Tests', () {
      test('should treat ********** and ********* as same account', () async {
        // This test verifies that both phone number formats
        // would result in the same user authentication
        const phone1 = '**********';
        const phone2 = '*********';
        
        final normalized1 = PhoneNumberUtils.formatToRomanianStandard(phone1);
        final normalized2 = PhoneNumberUtils.formatToRomanianStandard(phone2);
        
        expect(normalized1, equals(normalized2),
          reason: 'Different phone formats should normalize to same value for same account');
        
        // Verify the normalized format is correct Romanian standard
        expect(normalized1, equals('+***********'));
        expect(normalized2, equals('+***********'));
      });

      test('should validate Romanian mobile prefixes correctly', () {
        const validPrefixes = ['72', '73', '74', '75', '76', '77', '78', '79'];
        
        for (final prefix in validPrefixes) {
          final phoneWithLeadingZero = '0${prefix}1446895';
          final phoneWithoutLeadingZero = '${prefix}1446895';
          
          final normalized1 = PhoneNumberUtils.formatToRomanianStandard(phoneWithLeadingZero);
          final normalized2 = PhoneNumberUtils.formatToRomanianStandard(phoneWithoutLeadingZero);
          
          expect(normalized1, equals(normalized2),
            reason: 'Prefix $prefix should normalize consistently');
          expect(normalized1, startsWith('+40$prefix'),
            reason: 'Should start with +40$prefix');
        }
      });
    });
  });
}
