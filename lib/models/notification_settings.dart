/// Notification settings for a salon
class NotificationSettings {
  final String salonId;
  final bool pushNotificationsEnabled;
  final String soundPreference;
  final bool vibrationEnabled;
  final DoNotDisturbSettings doNotDisturb;
  final NotificationRules notificationRules;
  final DateTime updatedAt;

  const NotificationSettings({
    required this.salonId,
    required this.pushNotificationsEnabled,
    required this.soundPreference,
    required this.vibrationEnabled,
    required this.doNotDisturb,
    required this.notificationRules,
    required this.updatedAt,
  });

  /// Create NotificationSettings from JSON
  factory NotificationSettings.fromJson(Map<String, dynamic> json) {
    return NotificationSettings(
      salonId: json['salonId'] as String,
      pushNotificationsEnabled: json['pushNotificationsEnabled'] as bool? ?? true,
      soundPreference: json['soundPreference'] as String? ?? 'default',
      vibrationEnabled: json['vibrationEnabled'] as bool? ?? true,
      doNotDisturb: DoNotDisturbSettings.from<PERSON>son(json['doNotDisturb'] as Map<String, dynamic>? ?? {}),
      notificationRules: NotificationRules.fromJson(json['notificationRules'] as Map<String, dynamic>? ?? {}),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Convert NotificationSettings to JSON
  Map<String, dynamic> toJson() {
    return {
      'salonId': salonId,
      'pushNotificationsEnabled': pushNotificationsEnabled,
      'soundPreference': soundPreference,
      'vibrationEnabled': vibrationEnabled,
      'doNotDisturb': doNotDisturb.toJson(),
      'notificationRules': notificationRules.toJson(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated values
  NotificationSettings copyWith({
    String? salonId,
    bool? pushNotificationsEnabled,
    String? soundPreference,
    bool? vibrationEnabled,
    DoNotDisturbSettings? doNotDisturb,
    NotificationRules? notificationRules,
    DateTime? updatedAt,
  }) {
    return NotificationSettings(
      salonId: salonId ?? this.salonId,
      pushNotificationsEnabled: pushNotificationsEnabled ?? this.pushNotificationsEnabled,
      soundPreference: soundPreference ?? this.soundPreference,
      vibrationEnabled: vibrationEnabled ?? this.vibrationEnabled,
      doNotDisturb: doNotDisturb ?? this.doNotDisturb,
      notificationRules: notificationRules ?? this.notificationRules,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// Do Not Disturb settings
class DoNotDisturbSettings {
  final bool enabled;
  final String startTime; // Format: "22:00"
  final String endTime;   // Format: "08:00"
  final bool allowCritical;

  const DoNotDisturbSettings({
    required this.enabled,
    required this.startTime,
    required this.endTime,
    required this.allowCritical,
  });

  factory DoNotDisturbSettings.fromJson(Map<String, dynamic> json) {
    return DoNotDisturbSettings(
      enabled: json['enabled'] as bool? ?? false,
      startTime: json['startTime'] as String? ?? '22:00',
      endTime: json['endTime'] as String? ?? '08:00',
      allowCritical: json['allowCritical'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'enabled': enabled,
      'startTime': startTime,
      'endTime': endTime,
      'allowCritical': allowCritical,
    };
  }

  DoNotDisturbSettings copyWith({
    bool? enabled,
    String? startTime,
    String? endTime,
    bool? allowCritical,
  }) {
    return DoNotDisturbSettings(
      enabled: enabled ?? this.enabled,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      allowCritical: allowCritical ?? this.allowCritical,
    );
  }
}

/// Notification rules for different event types
class NotificationRules {
  final bool newAppointments;
  final bool appointmentCancellations;
  final bool paymentConfirmations;
  final bool teamMemberUpdates;
  final bool systemMaintenanceAlerts;
  final NotificationPriority defaultPriority;

  const NotificationRules({
    required this.newAppointments,
    required this.appointmentCancellations,
    required this.paymentConfirmations,
    required this.teamMemberUpdates,
    required this.systemMaintenanceAlerts,
    required this.defaultPriority,
  });

  factory NotificationRules.fromJson(Map<String, dynamic> json) {
    return NotificationRules(
      newAppointments: json['newAppointments'] as bool? ?? true,
      appointmentCancellations: json['appointmentCancellations'] as bool? ?? true,
      paymentConfirmations: json['paymentConfirmations'] as bool? ?? true,
      teamMemberUpdates: json['teamMemberUpdates'] as bool? ?? true,
      systemMaintenanceAlerts: json['systemMaintenanceAlerts'] as bool? ?? true,
      defaultPriority: NotificationPriority.fromString(json['defaultPriority'] as String? ?? 'NORMAL'),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'newAppointments': newAppointments,
      'appointmentCancellations': appointmentCancellations,
      'paymentConfirmations': paymentConfirmations,
      'teamMemberUpdates': teamMemberUpdates,
      'systemMaintenanceAlerts': systemMaintenanceAlerts,
      'defaultPriority': defaultPriority.value,
    };
  }

  NotificationRules copyWith({
    bool? newAppointments,
    bool? appointmentCancellations,
    bool? paymentConfirmations,
    bool? teamMemberUpdates,
    bool? systemMaintenanceAlerts,
    NotificationPriority? defaultPriority,
  }) {
    return NotificationRules(
      newAppointments: newAppointments ?? this.newAppointments,
      appointmentCancellations: appointmentCancellations ?? this.appointmentCancellations,
      paymentConfirmations: paymentConfirmations ?? this.paymentConfirmations,
      teamMemberUpdates: teamMemberUpdates ?? this.teamMemberUpdates,
      systemMaintenanceAlerts: systemMaintenanceAlerts ?? this.systemMaintenanceAlerts,
      defaultPriority: defaultPriority ?? this.defaultPriority,
    );
  }
}

/// Notification priority levels
enum NotificationPriority {
  critical('CRITICAL'),
  normal('NORMAL');

  const NotificationPriority(this.value);
  final String value;

  static NotificationPriority fromString(String value) {
    switch (value.toUpperCase()) {
      case 'CRITICAL':
        return NotificationPriority.critical;
      case 'NORMAL':
        return NotificationPriority.normal;
      default:
        return NotificationPriority.normal;
    }
  }

  String get displayName {
    switch (this) {
      case NotificationPriority.critical:
        return 'Critică';
      case NotificationPriority.normal:
        return 'Normală';
    }
  }
}

/// Request model for updating notification settings
class UpdateNotificationSettingsRequest {
  final bool pushNotificationsEnabled;
  final String soundPreference;
  final bool vibrationEnabled;
  final DoNotDisturbSettings doNotDisturb;
  final NotificationRules notificationRules;

  const UpdateNotificationSettingsRequest({
    required this.pushNotificationsEnabled,
    required this.soundPreference,
    required this.vibrationEnabled,
    required this.doNotDisturb,
    required this.notificationRules,
  });

  /// Convert to JSON for API request
  Map<String, dynamic> toJson() {
    return {
      'pushNotificationsEnabled': pushNotificationsEnabled,
      'soundPreference': soundPreference,
      'vibrationEnabled': vibrationEnabled,
      'doNotDisturb': doNotDisturb.toJson(),
      'notificationRules': notificationRules.toJson(),
    };
  }

  /// Create from NotificationSettings
  factory UpdateNotificationSettingsRequest.fromNotificationSettings(NotificationSettings settings) {
    return UpdateNotificationSettingsRequest(
      pushNotificationsEnabled: settings.pushNotificationsEnabled,
      soundPreference: settings.soundPreference,
      vibrationEnabled: settings.vibrationEnabled,
      doNotDisturb: settings.doNotDisturb,
      notificationRules: settings.notificationRules,
    );
  }
}
