import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('Hero Tag Fix Tests', () {
    testWidgets('should allow multiple FloatingActionButtons with unique hero tags', (WidgetTester tester) async {
      // Test that we can have multiple FloatingActionButtons with unique hero tags
      // without getting Hero tag conflicts
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Stack(
              children: [
                // Simulate multiple FABs that might exist in the app
                Positioned(
                  bottom: 16,
                  right: 16,
                  child: FloatingActionButton(
                    heroTag: "clients_add_fab",
                    onPressed: () {},
                    child: const Icon(Icons.person_add),
                  ),
                ),
                Positioned(
                  bottom: 80,
                  right: 16,
                  child: FloatingActionButton(
                    heroTag: "calendar_add_fab",
                    mini: true,
                    onPressed: () {},
                    child: const Icon(Icons.add),
                  ),
                ),
                Positioned(
                  bottom: 130,
                  right: 16,
                  child: FloatingActionButton(
                    heroTag: "map_location_fab",
                    mini: true,
                    onPressed: () {},
                    child: const Icon(Icons.my_location),
                  ),
                ),
              ],
            ),
          ),
        ),
      );

      // Should find all three FABs without Hero tag conflicts
      expect(find.byType(FloatingActionButton), findsNWidgets(3));
      expect(find.byIcon(Icons.person_add), findsOneWidget);
      expect(find.byIcon(Icons.add), findsOneWidget);
      expect(find.byIcon(Icons.my_location), findsOneWidget);
    });

    testWidgets('should handle navigation between screens with FABs', (WidgetTester tester) async {
      // Test navigation between screens that have FloatingActionButtons
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: AppBar(title: const Text('Screen 1')),
            body: Builder(
              builder: (context) => Center(
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => Scaffold(
                          appBar: AppBar(title: const Text('Screen 2')),
                          body: const Center(child: Text('Screen 2')),
                          floatingActionButton: FloatingActionButton(
                            heroTag: "screen2_fab",
                            onPressed: () {},
                            child: const Icon(Icons.star),
                          ),
                        ),
                      ),
                    );
                  },
                  child: const Text('Go to Screen 2'),
                ),
              ),
            ),
            floatingActionButton: FloatingActionButton(
              heroTag: "screen1_fab",
              onPressed: () {},
              child: const Icon(Icons.home),
            ),
          ),
        ),
      );

      // Verify Screen 1 FAB
      expect(find.byIcon(Icons.home), findsOneWidget);

      // Navigate to Screen 2
      await tester.tap(find.text('Go to Screen 2'));
      await tester.pumpAndSettle();

      // Verify Screen 2 FAB
      expect(find.byIcon(Icons.star), findsOneWidget);
      expect(find.byIcon(Icons.home), findsNothing);

      // Navigate back
      await tester.tap(find.byType(BackButton));
      await tester.pumpAndSettle();

      // Verify back to Screen 1 FAB
      expect(find.byIcon(Icons.home), findsOneWidget);
      expect(find.byIcon(Icons.star), findsNothing);
    });

    test('should document all Hero tag assignments', () {
      // Document all the Hero tags we've assigned to prevent conflicts
      const heroTags = {
        'clients_add_fab': 'Clients list screen add client button',
        'calendar_add_fab': 'Simple calendar screen add appointment button',
        'notifications_test_fab': 'Notifications screen test menu button',
        'staff_schedule_save_fab': 'Staff schedule screen save button',
        'map_location_fab': 'Full screen map picker location button',
        'map_widget_location_fab': 'Map widget location button',
      };

      expect(heroTags.length, greaterThan(0));
      
      // Verify no duplicate tags
      final tagValues = heroTags.keys.toSet();
      expect(tagValues.length, equals(heroTags.length),
        reason: 'All Hero tags should be unique');

      print('✅ Hero Tag Assignments:');
      heroTags.forEach((tag, description) {
        print('  • $tag: $description');
      });
    });
  });
}
