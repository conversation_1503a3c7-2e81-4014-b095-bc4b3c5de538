# iOS Code Signing Troubleshooting Guide

This guide helps resolve common iOS code signing issues encountered during CI/CD builds.

## Common Error Messages and Solutions

### 1. "Revoke certificate: Your account already has an Apple Development signing certificate"

**Problem**: The CI environment doesn't have access to the private key for the existing certificate.

**Solution**:
```bash
# Run the fix script
./scripts/fix-ios-signing.sh

# Or manually clean and rebuild
flutter clean
rm -rf ios/Pods ios/Podfile.lock
cd ios && pod install && cd ..
```

### 2. "No profiles for 'ro.partykidsprogramari.partykids' were found"

**Problem**: Missing or incorrect provisioning profile configuration.

**Solutions**:
1. **Check App Store Connect**: Ensure you have an App Store provisioning profile for `ro.partykidsprogramari.partykids`
2. **Verify team ID**: Confirm team ID `6MB735DMZ5` is correct in your Apple Developer account
3. **Update ExportOptions.plist**: The file should contain the correct team ID and bundle identifier

### 3. "Authentication failed" or API key issues

**Problem**: App Store Connect API key is not properly configured.

**Solutions**:
1. **Verify GitHub Secrets**:
   - `APPSTORE_KEY_ID`: Your App Store Connect API key ID
   - `APPSTORE_ISSUER_ID`: Your App Store Connect issuer ID
   - `APPSTORE_PRIVATE_KEY`: Your App Store Connect API private key (base64 encoded)
   - `APPLE_ID`: Your Apple ID email
   - `APPLE_APP_SPECIFIC_PASSWORD`: App-specific password for your Apple ID

2. **Check API Key Permissions**: Ensure your App Store Connect API key has:
   - App Manager role or higher
   - Access to the specific app

## Quick Fix Commands

### 1. Run the automated fix script:
```bash
cd flutter-partykids
./scripts/fix-ios-signing.sh
```

### 2. Debug current configuration:
```bash
cd flutter-partykids
./scripts/debug-ios-signing.sh
```

### 3. Manual cleanup and rebuild:
```bash
cd flutter-partykids
flutter clean
rm -rf ios/build build
rm -rf ios/Pods ios/Podfile.lock
flutter pub get
cd ios && pod install && cd ..
flutter build ios --release --no-codesign
```

## Required GitHub Secrets

Set these secrets in your GitHub repository settings:

| Secret Name | Description | Example |
|-------------|-------------|---------|
| `APPSTORE_KEY_ID` | App Store Connect API Key ID | `ABC123DEF4` |
| `APPSTORE_ISSUER_ID` | App Store Connect Issuer ID | `12345678-1234-1234-1234-123456789012` |
| `APPSTORE_PRIVATE_KEY` | App Store Connect API Private Key | `-----BEGIN PRIVATE KEY-----\n...` |
| `APPLE_ID` | Your Apple ID email | `<EMAIL>` |
| `APPLE_APP_SPECIFIC_PASSWORD` | App-specific password | `abcd-efgh-ijkl-mnop` |

## App Store Connect Setup

### 1. Create API Key:
1. Go to [App Store Connect](https://appstoreconnect.apple.com)
2. Navigate to Users and Access → Keys
3. Create a new API key with App Manager role
4. Download the `.p8` file and note the Key ID and Issuer ID

### 2. Create App-Specific Password:
1. Go to [Apple ID account page](https://appleid.apple.com)
2. Sign in with your Apple ID
3. Go to Security → App-Specific Passwords
4. Generate a new password for "GitHub Actions"

### 3. Verify App Configuration:
1. Ensure your app exists in App Store Connect
2. Bundle ID must be `ro.partykidsprogramari.partykids`
3. Team ID must be `6MB735DMZ5`

## File Verification Checklist

### ✅ ExportOptions.plist
```xml
<key>teamID</key>
<string>6MB735DMZ5</string>
<key>signingStyle</key>
<string>automatic</string>
```

### ✅ project.pbxproj
```
DEVELOPMENT_TEAM = 6MB735DMZ5;
CODE_SIGN_STYLE = Automatic;
PRODUCT_BUNDLE_IDENTIFIER = ro.partykidsprogramari.partykids;
```

### ✅ Info.plist
```xml
<key>CFBundleIdentifier</key>
<string>ro.partykidsprogramari.partykids</string>
```

## CI/CD Workflow Improvements

The updated workflow includes:
- ✅ Better error handling and logging
- ✅ Keychain setup for certificate management
- ✅ API key validation before build
- ✅ Detailed error reporting on failure
- ✅ Automatic provisioning profile updates

## Still Having Issues?

1. **Run the debug script**: `./scripts/debug-ios-signing.sh`
2. **Check the build logs**: Look for specific error messages
3. **Verify Apple Developer account**: Ensure certificates and profiles are valid
4. **Test locally**: Try building and archiving on a local Mac first
5. **Contact support**: If issues persist, check Apple Developer forums

## Additional Resources

- [Apple Developer Documentation](https://developer.apple.com/documentation/xcode/distributing-your-app-for-beta-testing-and-releases)
- [App Store Connect API Documentation](https://developer.apple.com/documentation/appstoreconnectapi)
- [Flutter iOS Deployment Guide](https://docs.flutter.dev/deployment/ios)
