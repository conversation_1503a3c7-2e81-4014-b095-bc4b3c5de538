import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/pet.dart';
import '../../models/appointment.dart';
import '../../config/theme/app_theme.dart';

class AnimalGroomingHistoryWidget extends StatelessWidget {
  final Pet pet;
  final List<Appointment> groomingHistory;

  const AnimalGroomingHistoryWidget({
    super.key,
    required this.pet,
    required this.groomingHistory,
  });

  Widget _buildGroomingCard(Appointment appointment, BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with service and date
          Row(
            children: [
              Expanded(
                child: Text(
                  appointment.service,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getStatusColor(appointment.status, context).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  appointment.status,
                  style: TextStyle(
                    color: _getStatusColor(appointment.status.toLowerCase(), context),
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Date and groomer
          Row(
            children: [
              Icon(
                Icons.calendar_today,
                size: 16,
                color: AppColors.taupe,
              ),
              const SizedBox(width: 4),
              Text(
                DateFormat('dd MMM yyyy, HH:mm', 'ro').format(appointment.startTime),
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.taupe,
                ),
              ),
              const Spacer(),
              Icon(
                Icons.person,
                size: 16,
                color: AppColors.taupe,
              ),
              const SizedBox(width: 4),
              Text(
                'Groomer',
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.taupe,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),

          // Notes if any
          if (appointment.notes.isNotEmpty) ...[
            const SizedBox(height: 8),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Observații:',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppColors.logoBrown,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    appointment.notes,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.forestGreen,
                      height: 1.3,
                    ),
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: 8),

          // Payment status
          Row(
            children: [
              const Spacer(),
              if (appointment.isPaid)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(
                        Icons.check_circle,
                        color: Colors.green,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      const Text(
                        'Plătit',
                        style: TextStyle(
                          color: Colors.green,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                )
              else
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.getStatusColor(context, 'warning').withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'Neplătit',
                    style: TextStyle(
                      color: AppTheme.getStatusColor(context, 'warning'),
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status, BuildContext context) {
    switch (status.toLowerCase()) {
      case 'finalizat':
      case 'completed':
        return AppTheme.getStatusColor(context, 'success');
      case 'programat':
      case 'scheduled':
        return AppTheme.getStatusColor(context, 'info');
      case 'anulat':
      case 'cancelled':
        return AppTheme.getStatusColor(context, 'error');
      case 'în desfășurare':
      case 'in progress':
        return AppTheme.getStatusColor(context, 'warning');
      default:
        return Theme.of(context).colorScheme.onSurfaceVariant;
    }
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            title,
            style: TextStyle(
              color: color.withValues(alpha: 0.8),
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final totalSessions = groomingHistory.length;
    final lastVisit = groomingHistory.isNotEmpty
        ? groomingHistory.first.startTime
        : null;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Istoric grooming',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.forestGreen,
          ),
        ),
        const SizedBox(height: 16),

        // Statistics
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          childAspectRatio: 1.5,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          children: [
            _buildStatCard(
              'Total\nSesiuni',
              totalSessions.toString(),
              Icons.pets,
              AppColors.forestGreen,
            ),
            _buildStatCard(
              'Ultima\nVizită',
              lastVisit != null
                  ? DateFormat('dd MMM', 'ro').format(lastVisit)
                  : 'N/A',
              Icons.schedule,
              AppColors.logoBrown,
            ),
          ],
        ),

        const SizedBox(height: 16),

        // History list
        if (groomingHistory.isEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Icon(
                  Icons.pets,
                  size: 48,
                  color: AppColors.taupe.withValues(alpha: 0.5),
                ),
                const SizedBox(height: 16),
                Text(
                  'Niciun istoric de grooming',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: AppColors.taupe,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Programează prima sesiune de grooming pentru ${pet.name}',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.taupe.withValues(alpha: 0.8),
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          )
        else
          ...groomingHistory.map((appointment) => _buildGroomingCard(appointment, context)),
      ],
    );
  }
}
