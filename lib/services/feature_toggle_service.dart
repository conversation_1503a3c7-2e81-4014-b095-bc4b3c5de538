import 'package:flutter/foundation.dart';
import '../models/feature_toggles.dart';
import '../models/api_response.dart';
import '../config/api_config.dart';
import 'api_service.dart';

/// Service for managing feature toggles
class FeatureToggleService {
  static FeatureToggles? _cachedToggles;
  static DateTime? _lastFetchTime;
  static const Duration _cacheTimeout = Duration(minutes: 30);

  /// Get feature toggles with caching
  static Future<ApiResponse<FeatureToggles>> getFeatureToggles() async {
    try {
      // Check if we have valid cached data
      if (_cachedToggles != null && 
          _lastFetchTime != null && 
          DateTime.now().difference(_lastFetchTime!) < _cacheTimeout) {
        if (ApiConfig.enableLogging) {
          debugPrint('🎛️ Using cached feature toggles');
        }
        return ApiResponse<FeatureToggles>.success(_cachedToggles!);
      }

      if (ApiConfig.enableLogging) {
        debugPrint('🎛️ Fetching feature toggles from API...');
      }

      final response = await ApiService.get<FeatureToggles>(
        '/api/feature-toggles',
        fromJson: (data) => FeatureToggles.fromJson(data),
      );

      if (response.success && response.data != null) {
        // Cache the successful response
        _cachedToggles = response.data;
        _lastFetchTime = DateTime.now();
        
        if (ApiConfig.enableLogging) {
          debugPrint('🎛️ Feature toggles fetched successfully:');
          debugPrint('   Monthly view: ${response.data!.monthlyViewEnabled}');
          debugPrint('   Theme selection: ${response.data!.themeSelectionEnabled}');
          debugPrint('   Translations: ${response.data!.translationsEnabled}');
          debugPrint('   Sell screen: ${response.data!.sellScreenEnabled}');
          debugPrint('   Reviews: ${response.data!.reviewsEnabled}');
        }
      }

      return response;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error fetching feature toggles: $e');
        debugPrint('🎛️ Falling back to default feature toggles');
      }
      
      // Return default toggles on error
      return ApiResponse<FeatureToggles>.success(FeatureToggles.defaultToggles);
    }
  }

  /// Get cached feature toggles or default if not available
  static FeatureToggles getCachedToggles() {
    return _cachedToggles ?? FeatureToggles.defaultToggles;
  }

  /// Check if monthly view is enabled
  static Future<bool> isMonthlyViewEnabled() async {
    final response = await getFeatureToggles();
    return response.data?.monthlyViewEnabled ?? false;
  }

  /// Check if theme selection is enabled
  static Future<bool> isThemeSelectionEnabled() async {
    final response = await getFeatureToggles();
    return response.data?.themeSelectionEnabled ?? true; // Default to true to show theme toggle
  }

  /// Check if translations are enabled
  static Future<bool> isTranslationsEnabled() async {
    final response = await getFeatureToggles();
    return response.data?.translationsEnabled ?? false;
  }

  /// Check if sales screen is enabled
  static Future<bool> isSellScreenEnabled() async {
    final response = await getFeatureToggles();
    return response.data?.sellScreenEnabled ?? false;
  }

  /// Check if reviews feature is enabled
  static Future<bool> isReviewsEnabled() async {
    final response = await getFeatureToggles();
    return response.data?.reviewsEnabled ?? false;
  }

  /// Clear cached feature toggles (force refresh on next request)
  static void clearCache() {
    _cachedToggles = null;
    _lastFetchTime = null;
    
    if (ApiConfig.enableLogging) {
      debugPrint('🎛️ Feature toggles cache cleared');
    }
  }

  /// Refresh feature toggles from server
  static Future<ApiResponse<FeatureToggles>> refresh() async {
    clearCache();
    return await getFeatureToggles();
  }

  /// Initialize feature toggles (call during app startup)
  static Future<void> initialize() async {
    try {
      if (ApiConfig.enableLogging) {
        debugPrint('🎛️ Initializing feature toggles...');
      }
      
      await getFeatureToggles();
      
      if (ApiConfig.enableLogging) {
        debugPrint('🎛️ Feature toggles initialized successfully');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to initialize feature toggles: $e');
      }
    }
  }
}
