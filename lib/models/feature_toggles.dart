/// Feature toggles model for controlling application features
class FeatureToggles {
  final bool monthlyViewEnabled;
  final bool themeSelectionEnabled;
  final bool translationsEnabled;
  final bool sellScreenEnabled;
  final bool reviewsEnabled;

  const FeatureToggles({
    required this.monthlyViewEnabled,
    required this.themeSelectionEnabled,
    required this.translationsEnabled,
    required this.sellScreenEnabled,
    required this.reviewsEnabled,
  });

  /// Default feature toggles with all features disabled
  static const FeatureToggles defaultToggles = FeatureToggles(
    monthlyViewEnabled: false,
    themeSelectionEnabled: false,
    translationsEnabled: false,
    sellScreenEnabled: false,
    reviewsEnabled: false,
  );

  /// Create FeatureToggles from JSON
  factory FeatureToggles.fromJson(Map<String, dynamic> json) {
    return FeatureToggles(
      monthlyViewEnabled:
          json['monthly_view_enabled'] ?? json['monthlyViewEnabled'] ?? false,
      themeSelectionEnabled:
          json['theme_selection_enabled'] ?? json['themeSelectionEnabled'] ?? false,
      translationsEnabled:
          json['translations_enabled'] ?? json['translationsEnabled'] ?? false,
      sellScreenEnabled:
          json['sell_screen'] ?? json['sellScreen'] ?? false,
      reviewsEnabled: json['reviews'] ?? json['reviews_enabled'] ?? false,
    );
  }

  /// Convert FeatureToggles to JSON
  Map<String, dynamic> toJson() {
    return {
      'monthly_view_enabled': monthlyViewEnabled,
      'theme_selection_enabled': themeSelectionEnabled,
      'translations_enabled': translationsEnabled,
      'sell_screen': sellScreenEnabled,
      'reviews': reviewsEnabled,
    };
  }

  /// Create a copy with updated values
  FeatureToggles copyWith({
    bool? monthlyViewEnabled,
    bool? themeSelectionEnabled,
    bool? translationsEnabled,
    bool? sellScreenEnabled,
    bool? reviewsEnabled,
  }) {
    return FeatureToggles(
      monthlyViewEnabled: monthlyViewEnabled ?? this.monthlyViewEnabled,
      themeSelectionEnabled: themeSelectionEnabled ?? this.themeSelectionEnabled,
      translationsEnabled: translationsEnabled ?? this.translationsEnabled,
      sellScreenEnabled: sellScreenEnabled ?? this.sellScreenEnabled,
      reviewsEnabled: reviewsEnabled ?? this.reviewsEnabled,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FeatureToggles &&
        other.monthlyViewEnabled == monthlyViewEnabled &&
        other.themeSelectionEnabled == themeSelectionEnabled &&
        other.translationsEnabled == translationsEnabled &&
        other.sellScreenEnabled == sellScreenEnabled &&
        other.reviewsEnabled == reviewsEnabled;
  }

  @override
  int get hashCode {
    return monthlyViewEnabled.hashCode ^
        themeSelectionEnabled.hashCode ^
        translationsEnabled.hashCode ^
        sellScreenEnabled.hashCode ^
        reviewsEnabled.hashCode;
  }

  @override
  String toString() {
    return 'FeatureToggles(monthlyViewEnabled: $monthlyViewEnabled, '
        'themeSelectionEnabled: $themeSelectionEnabled, '
        'translationsEnabled: $translationsEnabled, '
        'sellScreenEnabled: $sellScreenEnabled, '
        'reviewsEnabled: $reviewsEnabled)';
  }
}
