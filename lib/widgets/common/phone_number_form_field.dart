import 'package:partykidsapp/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../config/theme/app_theme.dart';
import '../../utils/formatters/phone_number_utils.dart';

/// Specialized phone number form field with Romanian validation and formatting
class PhoneNumberFormField extends StatefulWidget {
  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final String? Function(String?)? validator;
  final Function(String)? onChanged;
  final Function(String)? onFieldSubmitted;
  final FocusNode? focusNode;
  final bool enabled;
  final bool readOnly;
  final bool isRequired;
  final String? initialValue;

  const PhoneNumberFormField({
    super.key,
    this.controller,
    this.labelText,
    this.hintText,
    this.validator,
    this.onChanged,
    this.onFieldSubmitted,
    this.focusNode,
    this.enabled = true,
    this.readOnly = false,
    this.isRequired = true,
    this.initialValue,
  });

  @override
  State<PhoneNumberFormField> createState() => _PhoneNumberFormFieldState();
}

class _PhoneNumberFormFieldState extends State<PhoneNumberFormField> {
  late TextEditingController _controller;
  bool _isControllerInternal = false;

  @override
  void initState() {
    super.initState();
    
    if (widget.controller != null) {
      _controller = widget.controller!;
    } else {
      _controller = TextEditingController(text: widget.initialValue);
      _isControllerInternal = true;
    }

    // Format initial value if provided
    if (_controller.text.isNotEmpty) {
      _controller.text = PhoneNumberUtils.formatForInput(_controller.text);
    }
  }

  @override
  void dispose() {
    if (_isControllerInternal) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StandardFormField(
      controller: _controller,
      labelText: widget.labelText ?? AppStrings.phoneNumber,
      hintText: widget.hintText ?? '+40 728 626 399',
      prefixIcon: Icons.phone,
      keyboardType: TextInputType.phone,
      inputFormatters: [
        FilteringTextInputFormatter.allow(RegExp(r'[0-9+\s\-\(\)]')),
        RomanianPhoneNumberFormatter(),
      ],
      validator: widget.validator ?? _defaultValidator,
      onChanged: _onChanged,
      onFieldSubmitted: widget.onFieldSubmitted,
      focusNode: widget.focusNode,
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      isRequired: widget.isRequired,
      suffixIcon: _buildSuffixIcon(),
    );
  }

  Widget? _buildSuffixIcon() {
    if (_controller.text.isEmpty) return null;

    final isValid = PhoneNumberUtils.isValidRomanianMobile(_controller.text);
    return Icon(
      isValid ? Icons.check_circle : Icons.error,
      color: isValid ? Colors.green : Colors.red,
      size: 20,
    );
  }

  void _onChanged(String value) {
    // Trigger rebuild to update suffix icon
    setState(() {});
    
    // Call external onChanged callback
    widget.onChanged?.call(value);
  }

  String? _defaultValidator(String? value) {
    if (value == null || value.trim().isEmpty) {
      return widget.isRequired ? AppStrings.fieldRequired : null;
    }

    if (!PhoneNumberUtils.isValidRomanianMobile(value)) {
      return AppStrings.invalidPhoneFormat;
    }

    return null;
  }
}

/// Romanian phone number formatter with proper formatting
class RomanianPhoneNumberFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Don't format if user is deleting
    if (newValue.text.length < oldValue.text.length) {
      return newValue;
    }

    // Don't format if text is empty
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // Apply Romanian phone number formatting
    final formatted = PhoneNumberUtils.formatForInput(newValue.text);

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}

/// Phone number input dialog for editing phone numbers
class PhoneNumberEditDialog extends StatefulWidget {
  final String? initialValue;
  final String title;
  final String? subtitle;

  const PhoneNumberEditDialog({
    super.key,
    this.initialValue,
    this.title = 'Editează numărul de telefon',
    this.subtitle,
  });

  @override
  State<PhoneNumberEditDialog> createState() => _PhoneNumberEditDialogState();
}

class _PhoneNumberEditDialogState extends State<PhoneNumberEditDialog> {
  late TextEditingController _controller;
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.title),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.subtitle != null) ...[
              Text(
                widget.subtitle!,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppColors.taupe,
                ),
              ),
              AppDimensions.verticalSpaceStandard,
            ],
            PhoneNumberFormField(
              controller: _controller,
              labelText: AppStrings.phoneNumber,
              hintText: '+40 728 626 399',
              isRequired: true,
            ),
            AppDimensions.verticalSpaceSmall,
            Text(
              'Formatul acceptat: +40 XXX XXX XXX',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text(AppStrings.cancel),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _savePhoneNumber,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.forestGreen,
            foregroundColor: Colors.white,
          ),
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text(AppStrings.save),
        ),
      ],
    );
  }

  void _savePhoneNumber() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      // Simulate API call delay
      await Future.delayed(const Duration(milliseconds: 500));
      
      final formattedNumber = PhoneNumberUtils.formatForStorage(_controller.text);
      
      if (mounted) {
        Navigator.of(context).pop(formattedNumber);
      }
    } catch (e) {
      if (mounted) {
        showTopSnackBar(context, 
          SnackBar(
            content: Text('Eroare la salvarea numărului: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
}

/// Utility methods for phone number widgets
class PhoneNumberWidgetUtils {
  /// Show phone number edit dialog
  static Future<String?> showEditDialog(
    BuildContext context, {
    String? initialValue,
    String? title,
    String? subtitle,
  }) {
    return showDialog<String>(
      context: context,
      builder: (context) => PhoneNumberEditDialog(
        initialValue: initialValue,
        title: title ?? 'Editează numărul de telefon',
        subtitle: subtitle,
      ),
    );
  }

  /// Create a phone number display widget with edit capability
  static Widget buildEditableDisplay(
    BuildContext context, {
    required String phoneNumber,
    required Function(String) onChanged,
    String? label,
    bool showEditIcon = true,
  }) {
    return ListTile(
      leading: const Icon(Icons.phone, color: AppColors.forestGreen),
      title: Text(label ?? AppStrings.phoneNumber),
      subtitle: Text(
        phoneNumber.isEmpty ? 'Nu este setat' : phoneNumber,
        style: TextStyle(
          color: phoneNumber.isEmpty ? Colors.grey : null,
        ),
      ),
      trailing: showEditIcon
          ? IconButton(
              icon: const Icon(Icons.edit, color: AppColors.forestGreen),
              onPressed: () async {
                final result = await showEditDialog(
                  context,
                  initialValue: phoneNumber,
                );
                if (result != null) {
                  onChanged(result);
                }
              },
            )
          : null,
    );
  }
}
