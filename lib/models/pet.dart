class Pet {
  final String id;
  final String name;
  final String species; // 'dog', 'cat', 'bird', etc.
  final String breed;
  final String gender; // 'male', 'female'
  final DateTime birthDate;
  final double weight;
  final String color;
  final String ownerId; // Reference to the client/owner
  final String microchipNumber;
  final List<String> vaccinations;
  final String notes;
  final String photoUrl;

  Pet({
    required this.id,
    required this.name,
    required this.species,
    required this.breed,
    required this.gender,
    required this.birthDate,
    required this.weight,
    required this.color,
    required this.ownerId,
    this.microchipNumber = '',
    this.vaccinations = const [],
    this.notes = '',
    this.photoUrl = '',
  });

  /// Calculate age in years
  int get ageInYears {
    final now = DateTime.now();
    int age = now.year - birthDate.year;

    // Check if birthday hasn't occurred this year yet
    if (now.month < birthDate.month ||
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }

    return age;
  }

  // Calculate age in years and months
  String get age {
    final now = DateTime.now();
    final years = now.year - birthDate.year;
    final months = now.month - birthDate.month;

    if (years > 0) {
      return months > 0
          ? '$years ani și $months luni'
          : '$years ani';
    } else {
      return '$months luni';
    }
  }

  // Convert from JSON
  factory Pet.fromJson(Map<String, dynamic> json) {
    return Pet(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      species: json['species'] ?? '',
      breed: json['breed'] ?? '',
      gender: json['gender'] ?? '',
      birthDate: json['birthDate'] != null ? DateTime.parse(json['birthDate']) : DateTime.now(),
      weight: json['weight'] != null ? json['weight'].toDouble() : 0.0,
      color: json['color'] ?? '',
      ownerId: json['ownerId'] ?? json['clientId'] ?? '',
      microchipNumber: json['microchipNumber'] ?? '',
      vaccinations: json['vaccinations'] != null ? List<String>.from(json['vaccinations']) : [],
      notes: json['notes'] ?? '',
      photoUrl: json['photoUrl'] ?? '',
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'species': species,
      'breed': breed,
      'gender': gender,
      'birthDate': birthDate.toIso8601String(),
      'weight': weight,
      'color': color,
      'ownerId': ownerId,
      'microchipNumber': microchipNumber,
      'vaccinations': vaccinations,
      'notes': notes,
      'photoUrl': photoUrl,
    };
  }

  // Copy with method for immutable updates
  Pet copyWith({
    String? id,
    String? name,
    String? species,
    String? breed,
    String? gender,
    DateTime? birthDate,
    double? weight,
    String? color,
    String? ownerId,
    String? microchipNumber,
    List<String>? vaccinations,
    String? notes,
    String? photoUrl,
  }) {
    return Pet(
      id: id ?? this.id,
      name: name ?? this.name,
      species: species ?? this.species,
      breed: breed ?? this.breed,
      gender: gender ?? this.gender,
      birthDate: birthDate ?? this.birthDate,
      weight: weight ?? this.weight,
      color: color ?? this.color,
      ownerId: ownerId ?? this.ownerId,
      microchipNumber: microchipNumber ?? this.microchipNumber,
      vaccinations: vaccinations ?? this.vaccinations,
      notes: notes ?? this.notes,
      photoUrl: photoUrl ?? this.photoUrl,
    );
  }
}
