#!/bin/bash
# Setup script for iOS CI/CD environment
# This script ensures proper CocoaPods configuration for CI/CD builds

set -e

echo "🔧 Setting up iOS environment for CI/CD..."

# Navigate to iOS directory
cd ios

# Check if CocoaPods is installed
if ! command -v pod &> /dev/null; then
    echo "❌ CocoaPods not found. Please install CocoaPods first."
    echo "   Run: sudo gem install cocoapods"
    exit 1
fi

echo "📦 Installing CocoaPods dependencies..."
pod install --repo-update

echo "✅ iOS environment setup complete!"
echo "   - CocoaPods dependencies installed"
echo "   - Configuration files regenerated"
echo "   - Ready for iOS build"
