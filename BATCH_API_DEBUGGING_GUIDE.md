# Ghid de Debugging pentru Batch API Staff Working Hours

## Situația Actuală

✅ **Backend implementat** - Endpoint-ul batch este disponibil  
❌ **Frontend folosește încă request-uri individuale** - Trebuie să investigăm de ce

## Pași pentru Debugging

### 1. **Testarea Explicită a Batch API**

**În aplicația Flutter:**
1. Deschide Calendar
2. Apasă butonul de test (🧪) din header
3. Verifică mesajul "Test batch endpoint completat"

### 2. **Log-uri de Urmărit**

**Dacă batch API funcționează corect:**
```
🧪 [timestamp] Testing batch staff working hours endpoint
🌐 [timestamp] Testing batch endpoint with X staff: [staff-ids]
🗑️ [timestamp] Cleared all cache for fresh batch test
🚀 Loading staff working hours in batch for X staff members
🌐 Making batch API call to: /api/salons/{salonId}/staff/working-hours/batch
📋 Staff IDs for batch request: [staff-ids]
🔄 Calling StaffWorkingHoursService.getBatchStaffWorkingHours...
🌐 Making batch API request to: /api/salons/{salonId}/staff/working-hours/batch
📋 Query params: {staffIds: id1,id2,id3}
📥 Batch API raw response:
   Success: true
   Data is null: false
   Error: null
   Data keys: [staff-id-1, staff-id-2, staff-id-3]
   Data type: _Map<String, dynamic>
🔄 Processing batch data with X entries
📋 Processing staff: [staff-id]
   Settings data type: _InternalLinkedHashMap<String, dynamic>
✅ Successfully parsed settings for staff: [staff-id]
✅ Batch staff working hours retrieved successfully for X staff members
✅ Batch API response received for X staff members
📋 Received staff IDs: [staff-ids]
✅ Cached working hours for staff: [staff-id]
🚀 Batch loading completed successfully - X/X staff loaded
✅ [timestamp] Batch endpoint test SUCCESSFUL!
📦 [timestamp] Cache populated for X/X staff members
```

**Dacă batch API eșuează:**
```
🧪 [timestamp] Testing batch staff working hours endpoint
🌐 [timestamp] Testing batch endpoint with X staff: [staff-ids]
🗑️ [timestamp] Cleared all cache for fresh batch test
🚀 Loading staff working hours in batch for X staff members
🌐 Making batch API call to: /api/salons/{salonId}/staff/working-hours/batch
📋 Staff IDs for batch request: [staff-ids]
🔄 Calling StaffWorkingHoursService.getBatchStaffWorkingHours...
🌐 Making batch API request to: /api/salons/{salonId}/staff/working-hours/batch
📋 Query params: {staffIds: id1,id2,id3}
📥 Batch API raw response:
   Success: false
   Data is null: true
   Error: [error message]
❌ Error getting batch staff working hours: [error]
❌ Batch loading failed with exception: [exception]
❌ Exception type: [exception type]
❌ [timestamp] Batch endpoint test FAILED: [error]
🔄 [timestamp] This indicates the backend batch endpoint may not be working correctly
```

### 3. **Posibile Probleme și Soluții**

#### **Problema 1: Endpoint nu există (404)**
**Log-uri:**
```
📥 Batch API raw response:
   Success: false
   Error: 404 Not Found
```
**Soluție:** Verifică că backend-ul are endpoint-ul implementat la calea corectă

#### **Problema 2: Format de răspuns incorect**
**Log-uri:**
```
📥 Batch API raw response:
   Success: true
   Data keys: [success, data]  // În loc de staff IDs direct
```
**Soluție:** Backend-ul returnează `{success: true, data: {...}}` în loc de datele direct

#### **Problema 3: Parsing JSON eșuează**
**Log-uri:**
```
📋 Processing staff: [staff-id]
   Settings data type: String  // În loc de Map
❌ Settings data is not Map<String, dynamic>
```
**Soluție:** Backend-ul returnează date în format incorect

#### **Problema 4: Autentificare eșuează**
**Log-uri:**
```
📥 Batch API raw response:
   Success: false
   Error: 401 Unauthorized
```
**Soluție:** Verifică token-ul de autentificare

### 4. **Verificarea Backend-ului**

**Testează endpoint-ul direct cu curl:**
```bash
curl -X GET \
  "https://api.partykids-programari.ro/api/salons/{salonId}/staff/working-hours/batch?staffIds=id1,id2,id3" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json"
```

**Răspunsul așteptat:**
```json
{
  "staff-id-1": {
    "staffId": "staff-id-1",
    "salonId": "salon-id",
    "weeklySchedule": { ... },
    "holidays": [ ... ],
    "customClosures": [ ... ],
    "updatedAt": "2025-06-12T12:00:22.557994"
  },
  "staff-id-2": { ... }
}
```

**NU:**
```json
{
  "success": true,
  "data": {
    "staff-id-1": { ... },
    "staff-id-2": { ... }
  }
}
```

### 5. **Următorii Pași**

1. **Rulează testul** și trimite-mi log-urile complete
2. **Identifică problema** din log-uri
3. **Corectează backend-ul** dacă este necesar
4. **Testează din nou** până funcționează

### 6. **Când Funcționează Corect**

Când batch API funcționează, vei vedea:
- **UN SINGUR request** pentru mai mulți staff
- **Log-uri de succes** pentru batch processing
- **Cache populat** pentru toți staff members
- **Performance îmbunătățit** în calendar

## Concluzie

Cu logging-ul detaliat implementat, acum putem identifica exact unde eșuează batch API-ul și să îl reparăm rapid.

**Rulează testul și trimite-mi log-urile complete pentru a continua debugging-ul!**
