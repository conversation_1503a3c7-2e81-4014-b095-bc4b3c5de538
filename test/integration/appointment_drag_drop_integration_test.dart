import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:provider/provider.dart';

import 'package:partykidsapp/widgets/calendar_views/draggable_appointment_block.dart';
import 'package:partykidsapp/widgets/calendar_views/droppable_time_slot.dart';
import 'package:partykidsapp/models/appointment.dart';
import 'package:partykidsapp/providers/calendar_provider.dart';
import 'package:partykidsapp/services/appointment/appointment_service.dart';
import 'package:partykidsapp/utils/api_response.dart';

// Generate mocks
@GenerateMocks([CalendarProvider, AppointmentService])
import 'appointment_drag_drop_integration_test.mocks.dart';

void main() {
  group('Appointment Drag and Drop Integration', () {
    late MockCalendarProvider mockCalendarProvider;
    late Appointment testAppointment;

    setUp(() {
      mockCalendarProvider = MockCalendarProvider();
      testAppointment = Appointment(
        id: 'test-id',
        clientId: 'client-1',
        clientName: '<PERSON>',
        clientPhone: '+40123456789',
        petId: 'pet-1',
        petName: 'Buddy',
        petSpecies: 'Dog',
        service: 'Grooming',
        startTime: DateTime(2024, 1, 15, 10, 0),
        endTime: DateTime(2024, 1, 15, 11, 0),
        status: 'SCHEDULED',
        isPaid: false,
        groomerId: 'groomer-1',
        assignedGroomer: 'Jane Smith',
      );

      // Setup default mock responses
      when(mockCalendarProvider.getFilteredAppointmentsForDate(any))
          .thenReturn([]);
      when(mockCalendarProvider.fetchAppointmentsForDate(any, forceRefresh: true))
          .thenAnswer((_) async {
            return null;
          });
    });

    Widget createTestCalendarWidget() {
      return MaterialApp(
        home: Scaffold(
          body: ChangeNotifierProvider<CalendarProvider>.value(
            value: mockCalendarProvider,
            child: Column(
              children: [
                // Source appointment
                DraggableAppointmentBlock(
                  appointment: testAppointment,
                  height: 60,
                  isDragEnabled: true,
                ),
                SizedBox(height: 20),
                // Target time slot
                DroppableTimeSlot(
                  dateTime: DateTime(2024, 1, 15, 11, 0),
                  staffId: 'groomer-1',
                  isBusinessHour: true,
                  isLunchBreak: false,
                  isAvailable: true,
                  isGreyedOut: false,
                  height: 60,
                  isDragEnabled: true,
                ),
                SizedBox(height: 20),
                // Target time slot for different staff
                DroppableTimeSlot(
                  dateTime: DateTime(2024, 1, 15, 10, 0),
                  staffId: 'groomer-2',
                  isBusinessHour: true,
                  isLunchBreak: false,
                  isAvailable: true,
                  isGreyedOut: false,
                  height: 60,
                  isDragEnabled: true,
                ),
              ],
            ),
          ),
        ),
      );
    }

    testWidgets('complete drag and drop workflow for time change', (tester) async {
      await tester.pumpWidget(createTestCalendarWidget());

      // Find the draggable appointment
      final draggableAppointment = find.byType(DraggableAppointmentBlock);
      expect(draggableAppointment, findsOneWidget);

      // Find the target time slot
      final targetTimeSlots = find.byType(DroppableTimeSlot);
      expect(targetTimeSlots, findsNWidgets(2));

      // Start drag operation
      await tester.longPress(draggableAppointment);
      await tester.pump();

      // Verify drag feedback is shown
      expect(find.text('10:00 - 11:00'), findsWidgets);
      expect(find.text('John Doe'), findsWidgets);

      // Drag to target time slot (same staff, different time)
      await tester.drag(draggableAppointment, const Offset(0, 80));
      await tester.pump();

      // Complete the drag
      await tester.pumpAndSettle();

      // Verify calendar refresh was called
      verify(mockCalendarProvider.fetchAppointmentsForDate(any, forceRefresh: true))
          .called(greaterThan(0));
    });

    testWidgets('complete drag and drop workflow for staff reassignment', (tester) async {
      await tester.pumpWidget(createTestCalendarWidget());

      // Find the draggable appointment
      final draggableAppointment = find.byType(DraggableAppointmentBlock);
      expect(draggableAppointment, findsOneWidget);

      // Start drag operation
      await tester.longPress(draggableAppointment);
      await tester.pump();

      // Drag to target time slot (different staff, same time)
      await tester.drag(draggableAppointment, const Offset(0, 120));
      await tester.pump();

      // Complete the drag
      await tester.pumpAndSettle();

      // Verify calendar refresh was called
      verify(mockCalendarProvider.fetchAppointmentsForDate(any, forceRefresh: true))
          .called(greaterThan(0));
    });

    testWidgets('drag and drop shows visual feedback during operation', (tester) async {
      await tester.pumpWidget(createTestCalendarWidget());

      final draggableAppointment = find.byType(DraggableAppointmentBlock);
      final targetTimeSlot = find.byType(DroppableTimeSlot).first;

      // Start drag
      await tester.longPress(draggableAppointment);
      await tester.pump();

      // Verify placeholder is shown in original position
      expect(find.byIcon(Icons.drag_indicator), findsOneWidget);

      // Move over target
      await tester.drag(draggableAppointment, const Offset(0, 80));
      await tester.pump();

      // Verify visual feedback on target
      expect(find.byType(AnimatedContainer), findsWidgets);
    });

    testWidgets('drag and drop handles conflicts gracefully', (tester) async {
      // Mock conflicting appointment
      final conflictingAppointment = Appointment(
        id: 'conflict-id',
        clientId: 'client-2',
        clientName: 'Jane Doe',
        clientPhone: '+40987654321',
        petId: 'pet-2',
        petName: 'Max',
        petSpecies: 'Cat',
        service: 'Grooming',
        startTime: DateTime(2024, 1, 15, 11, 0),
        endTime: DateTime(2024, 1, 15, 12, 0),
        status: 'SCHEDULED',
        isPaid: false,
        groomerId: 'groomer-1',
      );

      when(mockCalendarProvider.getFilteredAppointmentsForDate(any))
          .thenReturn([conflictingAppointment]);

      await tester.pumpWidget(createTestCalendarWidget());

      final draggableAppointment = find.byType(DraggableAppointmentBlock);

      // Start drag operation
      await tester.longPress(draggableAppointment);
      await tester.pump();

      // Drag to conflicting time slot
      await tester.drag(draggableAppointment, const Offset(0, 80));
      await tester.pump();

      // Complete the drag
      await tester.pumpAndSettle();

      // Should show error feedback (red border during drag over)
      // The actual conflict handling would show snackbar or dialog
    });

    testWidgets('drag and drop cancellation works correctly', (tester) async {
      await tester.pumpWidget(createTestCalendarWidget());

      final draggableAppointment = find.byType(DraggableAppointmentBlock);

      // Start drag operation
      await tester.longPress(draggableAppointment);
      await tester.pump();

      // Drag away from any valid target
      await tester.drag(draggableAppointment, const Offset(200, 200));
      await tester.pump();

      // Complete the drag (should cancel)
      await tester.pumpAndSettle();

      // Verify no API calls were made
      verifyNever(mockCalendarProvider.fetchAppointmentsForDate(any, forceRefresh: true));
    });

    testWidgets('multiple appointments can be dragged independently', (tester) async {
      final secondAppointment = testAppointment.copyWith(
        id: 'test-id-2',
        clientName: 'Jane Doe',
        startTime: DateTime(2024, 1, 15, 12, 0),
        endTime: DateTime(2024, 1, 15, 13, 0),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ChangeNotifierProvider<CalendarProvider>.value(
              value: mockCalendarProvider,
              child: Column(
                children: [
                  DraggableAppointmentBlock(
                    appointment: testAppointment,
                    height: 60,
                    isDragEnabled: true,
                  ),
                  DraggableAppointmentBlock(
                    appointment: secondAppointment,
                    height: 60,
                    isDragEnabled: true,
                  ),
                  DroppableTimeSlot(
                    dateTime: DateTime(2024, 1, 15, 14, 0),
                    staffId: 'groomer-1',
                    isBusinessHour: true,
                    isLunchBreak: false,
                    isAvailable: true,
                    isGreyedOut: false,
                    height: 60,
                    isDragEnabled: true,
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // Find both draggable appointments
      final draggableAppointments = find.byType(DraggableAppointmentBlock);
      expect(draggableAppointments, findsNWidgets(2));

      // Test dragging first appointment
      await tester.longPress(draggableAppointments.first);
      await tester.pump();
      await tester.drag(draggableAppointments.first, const Offset(0, 120));
      await tester.pumpAndSettle();

      // Test dragging second appointment
      await tester.longPress(draggableAppointments.last);
      await tester.pump();
      await tester.drag(draggableAppointments.last, const Offset(0, 120));
      await tester.pumpAndSettle();

      // Both operations should work independently
      verify(mockCalendarProvider.fetchAppointmentsForDate(any, forceRefresh: true))
          .called(greaterThan(1));
    });

    testWidgets('drag and drop respects business hours constraints', (tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ChangeNotifierProvider<CalendarProvider>.value(
              value: mockCalendarProvider,
              child: Column(
                children: [
                  DraggableAppointmentBlock(
                    appointment: testAppointment,
                    height: 60,
                    isDragEnabled: true,
                  ),
                  // Time slot outside business hours
                  DroppableTimeSlot(
                    dateTime: DateTime(2024, 1, 15, 6, 0), // 6 AM - outside business hours
                    staffId: 'groomer-1',
                    isBusinessHour: false,
                    isLunchBreak: false,
                    isAvailable: false,
                    isGreyedOut: true,
                    height: 60,
                    isDragEnabled: true,
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      final draggableAppointment = find.byType(DraggableAppointmentBlock);

      // Start drag operation
      await tester.longPress(draggableAppointment);
      await tester.pump();

      // Drag to time slot outside business hours
      await tester.drag(draggableAppointment, const Offset(0, 80));
      await tester.pump();

      // Complete the drag
      await tester.pumpAndSettle();

      // Should show error feedback and not make API calls
      // The validation should prevent the operation
    });
  });
}
