
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import '../models/appointment.dart';
import '../models/pet.dart';
import '../models/client.dart';
import '../models/service.dart';
import '../services/staff_service.dart';
import '../services/auth/auth_service.dart';
import '../services/appointment/calendar_service.dart';
import '../services/service_management_service.dart';
import '../config/theme/app_theme.dart';
import '../models/working_hours_settings.dart';
import '../services/working_hours_service.dart';
import '../services/calendar_preferences_service.dart';
import '../services/staff_working_hours_service.dart';
import '../models/staff_working_hours_settings.dart';
import '../utils/romanian_holidays.dart';

class CalendarProvider extends ChangeNotifier {
  final CalendarService _calendarService = CalendarService();

  // Calendar view settings
  bool _showCanceledAppointments = false;
  bool _showUnpaidAppointments = false;
  CalendarHourViewMode _hourViewMode = CalendarHourViewMode.businessHours;

  // Dynamic time range settings
  bool _useDynamicTimeRange = true; // Enable dynamic time range based on working hours
  int _timeRangePaddingHours = 1; // Hours to add before/after calculated range
  int _minimumTimeRangeHours = 8; // Minimum hours to show (e.g., 8 hours minimum)

  // Zoom and accessibility settings
  double _timeSlotHeight = 80.0; // Increased default height for better visibility
  static const double _minTimeSlotHeight = 40.0;
  static const double _maxTimeSlotHeight = 120.0;

  // Search and filter settings
  String _searchQuery = '';
  String _selectedServiceFilter = '';
  String _selectedStatusFilter = '';

  // Staff management
  List<StaffResponse> _availableStaff = [];
  List<String> _selectedStaff = []; // IDs of selected staff members
  bool _isLoadingStaff = false;
  String? _staffError;

  // Appointments data
  List<Appointment> _appointments = [];
  final Map<String, List<Appointment>> _appointmentsByDate = {}; // Cache appointments by date
  bool _isLoadingAppointments = false;
  String? _appointmentsError;

  // Blocked times data
  final Map<String, List<Map<String, dynamic>>> _blockedTimesByDate = {};
  bool _isLoadingBlockedTimes = false;
  String? _blockedTimesError;



  // Client and pet data
  Map<String, Client> clientsCache = {};
  Map<String, Pet> petsCache = {};
  Map<String, List<Pet>> clientPetsCache = {};
  bool _isLoadingClientData = false;
  String? _clientDataError;

  // Service data
  List<Service> _services = [];
  final Map<String, Service> _servicesCache = {};
  bool _isLoadingServices = false;
  String? _servicesError;

  // Working hours data
  WorkingHoursSettings? _workingHoursSettings;
  bool _isLoadingWorkingHours = false;
  String? _workingHoursError;

  // Expose the calendar service for mock data access
  CalendarService get calendarService => _calendarService;

  // Getters
  bool get showCanceledAppointments => _showCanceledAppointments;
  bool get showUnpaidAppointments => _showUnpaidAppointments;
  CalendarHourViewMode get hourViewMode => _hourViewMode;
  bool get showFullDay => _hourViewMode == CalendarHourViewMode.fullDay;

  // Dynamic time range getters
  bool get useDynamicTimeRange => _useDynamicTimeRange;
  int get timeRangePaddingHours => _timeRangePaddingHours;
  int get minimumTimeRangeHours => _minimumTimeRangeHours;
  String get searchQuery => _searchQuery;
  String get selectedServiceFilter => _selectedServiceFilter;
  String get selectedStatusFilter => _selectedStatusFilter;

  // Zoom and accessibility getters
  double get timeSlotHeight => _timeSlotHeight;
  double get minTimeSlotHeight => _minTimeSlotHeight;
  double get maxTimeSlotHeight => _maxTimeSlotHeight;
  List<Appointment> get appointments => _appointments;
  bool get isLoadingAppointments => _isLoadingAppointments;
  String? get appointmentsError => _appointmentsError;
  bool get isLoadingBlockedTimes => _isLoadingBlockedTimes;
  String? get blockedTimesError => _blockedTimesError;

  bool get isLoadingClientData => _isLoadingClientData;
  String? get clientDataError => _clientDataError;

  List<StaffResponse> get availableStaff => _availableStaff;
  List<String> get selectedStaff => _selectedStaff;
  bool get isLoadingStaff => _isLoadingStaff;
  String? get staffError => _staffError;

  // Service getters
  List<Service> get services => _services;
  bool get isLoadingServices => _isLoadingServices;
  String? get servicesError => _servicesError;

  // Working hours getters
  WorkingHoursSettings? get workingHoursSettings => _workingHoursSettings;
  bool get isLoadingWorkingHours => _isLoadingWorkingHours;
  String? get workingHoursError => _workingHoursError;

  // Filtered appointments based on settings
  List<Appointment> getFilteredAppointments() {
    return _appointments.where((appointment) {
      // Filter by staff selection
      // If no staff are selected, show no appointments
      if (_selectedStaff.isEmpty) {
        return false;
      }

      // Use the assignedGroomer field directly, with null safety
      String appointmentStaffName;
      try {
        appointmentStaffName = appointment.assignedGroomer.isNotEmpty == true
            ? appointment.assignedGroomer
            : 'Ana Popescu'; // Fallback if no staff assigned or if it's null
      } catch (e) {
        appointmentStaffName = 'Ana Popescu'; // Fallback on any error
      }

      // Debug logging
      debugPrint('🔍 Filtering appointment ${appointment.id}: staff = "$appointmentStaffName", selected staff = $_selectedStaff');

      // Check if the appointment's staff is in the selected list
      StaffResponse? appointmentStaff;

      // First try to find by groomerId (most reliable)
      if (appointment.groomerId != null && appointment.groomerId!.isNotEmpty) {
        try {
          appointmentStaff = _availableStaff.firstWhere(
            (s) => s.id == appointment.groomerId,
          );
        } catch (e) {
          // Staff not found by ID, continue to name matching
        }
      }

      // Fallback to name matching if not found by ID
      if (appointmentStaff == null) {
        try {
          appointmentStaff = _availableStaff.firstWhere(
            (s) => s.name == appointmentStaffName || s.displayName == appointmentStaffName,
          );
        } catch (e) {
          // If staff not found, use first available staff
          appointmentStaff = _availableStaff.isNotEmpty ? _availableStaff.first : null;
        }
      }

      if (appointmentStaff != null && !_selectedStaff.contains(appointmentStaff.id)) {
        return false;
      }

      // Filter by canceled appointments
      if (!_showCanceledAppointments && appointment.status == 'canceled') {
        return false;
      }

      // Filter by unpaid appointments
      if (_showUnpaidAppointments && appointment.isPaid) {
        return false;
      }

      // Filter by search query
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final matchesClient = appointment.clientName.toLowerCase().contains(query);
        final matchesPet = appointment.petName.toLowerCase().contains(query);
        final matchesService = appointment.service.toLowerCase().contains(query);
        final matchesPhone = appointment.clientPhone.contains(query);

        if (!matchesClient && !matchesPet && !matchesService && !matchesPhone) {
          return false;
        }
      }

      // Filter by service type
      if (_selectedServiceFilter.isNotEmpty && appointment.service != _selectedServiceFilter) {
        return false;
      }

      // Filter by status
      if (_selectedStatusFilter.isNotEmpty && appointment.status != _selectedStatusFilter) {
        return false;
      }

      return true;
    }).toList();
  }

  // Setters for calendar view settings
  void setShowCanceledAppointments(bool value) {
    _showCanceledAppointments = value;
    notifyListeners();
  }

  void setShowUnpaidAppointments(bool value) {
    _showUnpaidAppointments = value;
    notifyListeners();
  }

  void setHourViewMode(CalendarHourViewMode mode) {
    _hourViewMode = mode;
    CalendarPreferencesService.setHourViewMode(mode);
    notifyListeners();
  }

  // Dynamic time range setters
  void setUseDynamicTimeRange(bool value) {
    if (_useDynamicTimeRange != value) {
      _useDynamicTimeRange = value;
      _refreshCalendarAfterTimeRangeChange();
    }
  }

  void setTimeRangePaddingHours(int hours) {
    if (hours >= 0 && hours <= 4 && _timeRangePaddingHours != hours) { // Reasonable limits
      _timeRangePaddingHours = hours;
      _refreshCalendarAfterTimeRangeChange();
    }
  }

  void setMinimumTimeRangeHours(int hours) {
    if (hours >= 4 && hours <= 16 && _minimumTimeRangeHours != hours) { // Reasonable limits
      _minimumTimeRangeHours = hours;
      _refreshCalendarAfterTimeRangeChange();
    }
  }

  /// Refresh calendar views when time range settings change
  void _refreshCalendarAfterTimeRangeChange() {
    debugPrint('🔄 Refreshing calendar after time range settings change');
    debugPrint('   Dynamic range enabled: $_useDynamicTimeRange');
    debugPrint('   Padding hours: $_timeRangePaddingHours');
    debugPrint('   Minimum range hours: $_minimumTimeRangeHours');
    notifyListeners();
  }

  // Zoom and accessibility setters
  void setTimeSlotHeight(double height) {
    _timeSlotHeight = height.clamp(_minTimeSlotHeight, _maxTimeSlotHeight);
    CalendarPreferencesService.setTimeSlotHeight(_timeSlotHeight);
    notifyListeners();
  }

  void zoomIn() {
    final newHeight = (_timeSlotHeight + 10).clamp(_minTimeSlotHeight, _maxTimeSlotHeight);
    setTimeSlotHeight(newHeight);
  }

  void zoomOut() {
    final newHeight = (_timeSlotHeight - 10).clamp(_minTimeSlotHeight, _maxTimeSlotHeight);
    setTimeSlotHeight(newHeight);
  }

  void resetZoom() {
    setTimeSlotHeight(80.0); // Updated default height for better visibility
  }

  /// Initialize calendar preferences from storage
  Future<void> initializePreferences() async {
    debugPrint('🔄 CalendarProvider: Initializing preferences...');

    // Migrate legacy preference if needed
    await CalendarPreferencesService.migrateLegacyPreference();

    // Load current preferences
    _hourViewMode = await CalendarPreferencesService.getHourViewMode();
    _timeSlotHeight = await CalendarPreferencesService.getTimeSlotHeight();

    // Load working hours to get correct business hours (critical for closure checks)
    debugPrint('🔄 CalendarProvider: Loading working hours during initialization...');
    await loadWorkingHours();

    notifyListeners();
    debugPrint('✅ CalendarProvider: Preferences initialization completed');
    debugPrint('📏 CalendarProvider: Time slot height loaded: ${_timeSlotHeight}px');
  }

  // Setters for search and filter
  void setSearchQuery(String query) {
    _searchQuery = query;
    notifyListeners();
  }

  void setServiceFilter(String service) {
    _selectedServiceFilter = service;
    notifyListeners();
  }

  void setStatusFilter(String status) {
    _selectedStatusFilter = status;
    notifyListeners();
  }

  void clearFilters() {
    _searchQuery = '';
    _selectedServiceFilter = '';
    _selectedStatusFilter = '';
    notifyListeners();
  }

  // Fetch appointments for a specific date (build-safe)
  Future<void> fetchAppointmentsForDate(DateTime date, {bool forceRefresh = false}) async {
    final dateKey = _formatDateKey(date);

    // Ensure underlying service cache is bypassed when forceRefresh is true
    if (forceRefresh) {
      _calendarService.clearCacheForDate(date);
    }

    // Check if we already have appointments for this date (unless forcing refresh)
    if (!forceRefresh && _appointmentsByDate.containsKey(dateKey)) {
      _appointments = _appointmentsByDate[dateKey]!;
      // Defer notifyListeners to avoid build-time state updates
      SchedulerBinding.instance.addPostFrameCallback((_) {
        notifyListeners();
      });
      return;
    }

    // Use microtask to defer the async operation
    Future.microtask(() async {
      _isLoadingAppointments = true;
      _appointmentsError = null;
      notifyListeners();

      try {
        final appointments = await _calendarService.getAppointmentsForDate(date);
        _appointmentsByDate[dateKey] = appointments;
        _appointments = appointments;
        _appointmentsError = null;
        debugPrint('✅ Appointments refreshed for date: $dateKey (${appointments.length} appointments)');
      } catch (e) {
        _appointmentsError = 'Eroare la încărcarea programărilor: $e';
        _appointments = [];
        debugPrint('❌ Error fetching appointments for date: $dateKey - $e');
      } finally {
        _isLoadingAppointments = false;
        notifyListeners();
      }
    });
  }

  // Get appointments for a specific date from cache
  List<Appointment> getAppointmentsForDate(DateTime date) {
    final dateKey = _formatDateKey(date);
    return _appointmentsByDate[dateKey] ?? [];
  }

  // Get blocked times for a specific date from cache
  List<Map<String, dynamic>> getBlockedTimesForDate(DateTime date) {
    final dateKey = _formatDateKey(date);
    return _blockedTimesByDate[dateKey] ?? [];
  }

  // Get filtered appointments for a specific date
  List<Appointment> getFilteredAppointmentsForDate(DateTime date) {
    final dateKey = _formatDateKey(date);
    final appointments = _appointmentsByDate[dateKey] ?? [];

    return appointments.where((appointment) {
      // Filter by staff selection
      if (_selectedStaff.isNotEmpty) {
        // Use the assignedGroomer field directly, with null safety
        String appointmentStaffName;
        try {
          appointmentStaffName = appointment.assignedGroomer.isNotEmpty == true
              ? appointment.assignedGroomer
              : 'Ana Popescu'; // Fallback if no staff assigned or if it's null
        } catch (e) {
          appointmentStaffName = 'Ana Popescu'; // Fallback on any error
        }

        // Check if the appointment's staff is in the selected list
        StaffResponse? appointmentStaff;

        // First try to find by groomerId (most reliable)
        if (appointment.groomerId != null && appointment.groomerId!.isNotEmpty) {
          try {
            appointmentStaff = _availableStaff.firstWhere(
              (s) => s.id == appointment.groomerId,
            );
          } catch (e) {
            // Staff not found by ID, continue to name matching
          }
        }

        // Fallback to name matching if not found by ID
        if (appointmentStaff == null) {
          try {
            appointmentStaff = _availableStaff.firstWhere(
              (s) => s.name == appointmentStaffName || s.displayName == appointmentStaffName,
            );
          } catch (e) {
            // If staff not found, use first available staff
            appointmentStaff = _availableStaff.isNotEmpty ? _availableStaff.first : null;
          }
        }

        if (appointmentStaff != null && !_selectedStaff.contains(appointmentStaff.id)) {
          return false;
        }
      }

      // Include or exclude canceled appointments based on preference
      if (!_showCanceledAppointments && appointment.status == 'canceled') {
        return false;
      }


      // Filter by unpaid appointments
      if (_showUnpaidAppointments && appointment.isPaid) {
        return false;
      }

      // Filter by search query
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        final matchesClient = appointment.clientName.toLowerCase().contains(query);
        final matchesPet = appointment.petName.toLowerCase().contains(query);
        final matchesService = appointment.service.toLowerCase().contains(query);
        final matchesPhone = appointment.clientPhone.contains(query);

        if (!matchesClient && !matchesPet && !matchesService && !matchesPhone) {
          return false;
        }
      }

      // Filter by service type
      if (_selectedServiceFilter.isNotEmpty && appointment.service != _selectedServiceFilter) {
        return false;
      }

      // Filter by status
      if (_selectedStatusFilter.isNotEmpty && appointment.status != _selectedStatusFilter) {
        return false;
      }

      return true;
    }).toList();
  }

  // Fetch appointments for a date range (optimized - single API call)
  Future<void> fetchAppointmentsForDateRange(DateTime startDate, DateTime endDate) async {
    _isLoadingAppointments = true;
    _appointmentsError = null;
    notifyListeners();

    try {
      // Make a single API call for the entire date range
      final appointments = await _calendarService.getAppointmentsForDateRange(startDate, endDate);

      // Group appointments by date and cache them
      for (final appointment in appointments) {
        final dateKey = _formatDateKey(appointment.startTime);
        if (!_appointmentsByDate.containsKey(dateKey)) {
          _appointmentsByDate[dateKey] = [];
        }

        // Avoid duplicates
        if (!_appointmentsByDate[dateKey]!.any((a) => a.id == appointment.id)) {
          _appointmentsByDate[dateKey]!.add(appointment);
        }
      }

      // Update current appointments to show the range
      _appointments = [];
      for (DateTime date = startDate;
           date.isBefore(endDate.add(const Duration(days: 1)));
           date = date.add(const Duration(days: 1))) {
        _appointments.addAll(getAppointmentsForDate(date));
      }

      _appointmentsError = null;
      debugPrint('✅ Appointments fetched for date range: ${_formatDateKey(startDate)} to ${_formatDateKey(endDate)} (${appointments.length} total appointments)');
    } catch (e) {
      _appointmentsError = 'Eroare la încărcarea programărilor: $e';
      _appointments = [];
      debugPrint('❌ Error fetching appointments for date range: $startDate to $endDate - $e');
    } finally {
      _isLoadingAppointments = false;
      notifyListeners();
    }
  }

  // Fetch appointments for a week (optimized - single API call)
  Future<void> fetchAppointmentsForWeek(DateTime weekStart) async {
    final weekEnd = weekStart.add(const Duration(days: 6)); // 7 days total
    debugPrint('📅 Fetching appointments for week: ${_formatDateKey(weekStart)} to ${_formatDateKey(weekEnd)}');

    // Check if we already have all appointments for this week cached
    bool hasAllWeekData = true;
    for (int i = 0; i < 7; i++) {
      final day = weekStart.add(Duration(days: i));
      final dateKey = _formatDateKey(day);
      if (!_appointmentsByDate.containsKey(dateKey)) {
        hasAllWeekData = false;
        break;
      }
    }

    if (hasAllWeekData) {
      debugPrint('📋 Using cached appointments for week');
      return;
    }

    await fetchAppointmentsForDateRange(weekStart, weekEnd);
  }

  // Fetch blocked times for a date
  Future<void> fetchBlockedTimesForDate(DateTime date, {bool forceRefresh = false}) async {
    final dateKey = _formatDateKey(date);
    debugPrint('🔄 CalendarProvider.fetchBlockedTimesForDate: $dateKey (forceRefresh: $forceRefresh)');

    if (!forceRefresh && _blockedTimesByDate.containsKey(dateKey)) {
      debugPrint('📋 Using cached block times for $dateKey (${_blockedTimesByDate[dateKey]!.length} blocks)');
      return;
    }

    _isLoadingBlockedTimes = true;
    _blockedTimesError = null;
    notifyListeners();

    try {
      debugPrint('🔄 Fetching block times from service for date: $date');
      final blocks = await _calendarService.getBlockedTimes(
        startDate: date,
        endDate: date,
      );
      _blockedTimesByDate[dateKey] = blocks;
      _blockedTimesError = null;
      debugPrint('✅ CalendarProvider: Cached ${blocks.length} block times for $dateKey');
    } catch (e) {
      debugPrint('❌ CalendarProvider: Error fetching block times for $dateKey: $e');
      _blockedTimesError = 'Eroare la încărcarea blocărilor: $e';
      _blockedTimesByDate[dateKey] = [];
    } finally {
      _isLoadingBlockedTimes = false;
      notifyListeners();
    }
  }

  // Fetch blocked times for a date range (optimized - single API call)
  Future<void> fetchBlockedTimesForDateRange(DateTime startDate, DateTime endDate) async {
    _isLoadingBlockedTimes = true;
    _blockedTimesError = null;
    notifyListeners();

    try {
      // Make a single API call for the entire date range
      final blocks = await _calendarService.getBlockedTimes(
        startDate: startDate,
        endDate: endDate,
      );

      // Group blocked times by date and cache them
      for (final block in blocks) {
        final startTime = DateTime.parse(block['startTime']);
        final dateKey = _formatDateKey(startTime);
        if (!_blockedTimesByDate.containsKey(dateKey)) {
          _blockedTimesByDate[dateKey] = [];
        }

        // Avoid duplicates
        if (!_blockedTimesByDate[dateKey]!.any((b) => b['id'] == block['id'])) {
          _blockedTimesByDate[dateKey]!.add(block);
        }
      }

      debugPrint('✅ Blocked times fetched for date range: ${_formatDateKey(startDate)} to ${_formatDateKey(endDate)} (${blocks.length} total blocks)');
    } catch (e) {
      _blockedTimesError = 'Eroare la încărcarea blocărilor: $e';
      debugPrint('❌ Error fetching blocked times for date range: $startDate to $endDate - $e');
    } finally {
      _isLoadingBlockedTimes = false;
      notifyListeners();
    }
  }

  // Fetch blocked times for a week (optimized - single API call)
  Future<void> fetchBlockedTimesForWeek(DateTime weekStart) async {
    final weekEnd = weekStart.add(const Duration(days: 6)); // 7 days total
    debugPrint('📅 Fetching blocked times for week: ${_formatDateKey(weekStart)} to ${_formatDateKey(weekEnd)}');

    // Check if we already have all blocked times for this week cached
    bool hasAllWeekData = true;
    for (int i = 0; i < 7; i++) {
      final day = weekStart.add(Duration(days: i));
      final dateKey = _formatDateKey(day);
      if (!_blockedTimesByDate.containsKey(dateKey)) {
        hasAllWeekData = false;
        break;
      }
    }

    if (hasAllWeekData) {
      debugPrint('📋 Using cached blocked times for week');
      return;
    }

    await fetchBlockedTimesForDateRange(weekStart, weekEnd);
  }

  // Helper method to format date as key
  String _formatDateKey(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }



  // Add a new appointment
  Future<Appointment?> addAppointment(Appointment appointment) async {
    try {
      final newAppointment = await _calendarService.addAppointment(appointment);

      // Only proceed if we got a valid appointment back
      if (newAppointment != null) {
        // Clear the cache for the appointment date to force refresh
        final dateKey = _formatDateKey(newAppointment.startTime);
        _appointmentsByDate.remove(dateKey);

        // Add to current appointments list if it's for the same date
        final currentDate = _appointments.isNotEmpty ? _appointments.first.startTime : DateTime.now();
        final currentDateKey = _formatDateKey(currentDate);

        if (dateKey == currentDateKey) {
          _appointments.add(newAppointment);
          _appointments.sort((a, b) => a.startTime.compareTo(b.startTime));

          // Update the cache with the new list
          _appointmentsByDate[dateKey] = List.from(_appointments);
          notifyListeners();
        } else {
          // If it's for a different date, just clear that date's cache
          // The appointments will be loaded when that date is viewed
          debugPrint('✅ Appointment created for $dateKey, cache cleared');
        }
      }

      return newAppointment;
    } catch (e) {
      debugPrint('❌ Error adding appointment: $e');
      return null;
    }
  }

  // Add a new appointment from form data using ScheduleAppointmentRequest DTO
  Future<AppointmentCreationResult> addAppointmentFromFormData(Map<String, dynamic> scheduleRequest) async {
    try {
      final result = await _calendarService.addAppointmentFromFormData(scheduleRequest);

      // Only proceed if we got a successful result
      if (result.success && result.appointment != null) {
        final newAppointment = result.appointment!;

        // Clear the cache for the appointment date to force refresh
        final dateKey = _formatDateKey(newAppointment.startTime);
        _appointmentsByDate.remove(dateKey);

        // Add to current appointments list if it's for the same date
        final currentDate = _appointments.isNotEmpty ? _appointments.first.startTime : DateTime.now();
        final currentDateKey = _formatDateKey(currentDate);

        if (dateKey == currentDateKey) {
          _appointments.add(newAppointment);
          _appointments.sort((a, b) => a.startTime.compareTo(b.startTime));

          // Update the cache with the new list
          _appointmentsByDate[dateKey] = List.from(_appointments);
          notifyListeners();
        } else {
          // If it's for a different date, just clear that date's cache
          // The appointments will be loaded when that date is viewed
          debugPrint('✅ Appointment created for $dateKey, cache cleared');
        }
      }

      return result;
    } catch (e) {
      debugPrint('❌ Error adding appointment from form data: $e');
      return AppointmentCreationResult.failure('Error creating appointment: $e');
    }
  }

  // Block time in the calendar
  Future<bool> blockTime(DateTime start, DateTime end, String reason, {String? staffId}) async {
    try {
      debugPrint('🔄 CalendarProvider.blockTime: $start to $end for reason: $reason${staffId != null ? ' (staff: $staffId)' : ''}');

      final success = await _calendarService.blockTime(start, end, reason, staffId: staffId);

      if (success) {
        debugPrint('✅ CalendarProvider: Block time successful, refreshing calendar data');

        // Refresh appointments for the affected date
        await fetchAppointmentsForDate(start);

        // Refresh blocked times for the affected date (CRITICAL: this was missing!)
        await fetchBlockedTimesForDate(start, forceRefresh: true);

        // Also refresh staff data to ensure calendar shows updated availability
        await refreshStaffData();

        notifyListeners();
        debugPrint('🎨 CalendarProvider: Block time created and calendar refreshed - UI should show new block');
      } else {
        debugPrint('❌ CalendarProvider: Block time failed');
      }

      return success;
    } catch (e) {
      debugPrint('❌ CalendarProvider.blockTime error: $e');
      return false;
    }
  }

  // Check availability for blocking time
  Future<Map<String, dynamic>?> checkBlockTimeAvailability({
    required DateTime startTime,
    required DateTime endTime,
    required List<String> staffIds,
  }) async {
    try {
      debugPrint('🔄 CalendarProvider.checkBlockTimeAvailability: Checking availability');

      final availability = await _calendarService.checkBlockTimeAvailability(
        startTime: startTime,
        endTime: endTime,
        staffIds: staffIds,
      );

      if (availability != null) {
        debugPrint('✅ CalendarProvider: Availability check completed');
        return availability;
      } else {
        debugPrint('❌ CalendarProvider: Availability check failed');
        return null;
      }
    } catch (e) {
      debugPrint('❌ CalendarProvider.checkBlockTimeAvailability error: $e');
      return null;
    }
  }

  // Delete (cancel) a blocked time slot with optimistic UI updates
  Future<bool> deleteBlockTime(String blockId, {String? reason, bool refreshWeek = false}) async {
    try {
      debugPrint('🔄 CalendarProvider.deleteBlockTime: Deleting block $blockId (refreshWeek: $refreshWeek)');

      // Optimistic update: Remove from cache immediately for instant UI feedback
      final removedBlock = removeBlockTimeFromCache(blockId);
      if (removedBlock == null) {
        debugPrint('❌ Block $blockId not found in cache, cannot perform optimistic delete');
        return false;
      }

      // Make the API call
      final success = await _calendarService.deleteBlockTime(blockId, reason: reason);

      if (success) {
        debugPrint('✅ CalendarProvider: Block time deleted successfully on server');
        // Block is already removed from cache, no need to refresh unless we want to sync other changes
        // Just refresh staff data to ensure availability is updated
        await refreshStaffData();
      } else {
        debugPrint('❌ CalendarProvider: Failed to delete block time on server, restoring to cache');
        // Restore the block to cache since the API call failed
        restoreBlockTimeToCache(removedBlock);
      }

      return success;
    } catch (e) {
      debugPrint('❌ CalendarProvider.deleteBlockTime error: $e');
      // If there was an error and we have the removed block, restore it
      return false;
    }
  }

  // Get available grooming services with their durations
  Future<Map<String, int>> getServiceDurations() async {
    return await _calendarService.getServiceDurations();
  }

  // Clear cache for specific date(s) and force refresh
  void clearCacheForDate(DateTime date) {
    final dateKey = _formatDateKey(date);
    _appointmentsByDate.remove(dateKey);
    _blockedTimesByDate.remove(dateKey);
    debugPrint('🗑️ Cache cleared for date: $dateKey');
  }

  // Clear cache for multiple dates and force refresh
  void clearCacheForDates(List<DateTime> dates) {
    for (final date in dates) {
      clearCacheForDate(date);
    }
  }

  // Remove a specific block time from cache (optimistic update)
  Map<String, dynamic>? removeBlockTimeFromCache(String blockId) {
    Map<String, dynamic>? removedBlock;

    // Search through all dates to find and remove the block
    for (final entry in _blockedTimesByDate.entries) {
      final dateKey = entry.key;
      final blocks = entry.value;

      for (int i = 0; i < blocks.length; i++) {
        if (blocks[i]['blockId'] == blockId) {
          removedBlock = blocks[i];
          blocks.removeAt(i);
          debugPrint('🗑️ Optimistically removed block $blockId from cache for date $dateKey');
          notifyListeners(); // Immediately update UI
          return removedBlock;
        }
      }
    }

    debugPrint('⚠️ Block $blockId not found in cache for removal');
    return null;
  }

  // Restore a block time to cache (if API call fails)
  void restoreBlockTimeToCache(Map<String, dynamic> block) {
    try {
      final startTime = DateTime.parse(block['startTime']).toLocal();
      final dateKey = _formatDateKey(startTime);

      if (_blockedTimesByDate.containsKey(dateKey)) {
        _blockedTimesByDate[dateKey]!.add(block);
      } else {
        _blockedTimesByDate[dateKey] = [block];
      }

      debugPrint('🔄 Restored block ${block['blockId']} to cache for date $dateKey');
      notifyListeners();
    } catch (e) {
      debugPrint('❌ Error restoring block to cache: $e');
    }
  }

  // Force refresh appointments for current date
  Future<void> forceRefreshCurrentDate() async {
    if (_appointments.isNotEmpty) {
      final currentDate = _appointments.first.startTime;
      await fetchAppointmentsForDate(currentDate, forceRefresh: true);
    }
  }

  /// Clear all salon-specific caches for salon switching
  void clearAllCachesForSalonSwitch(String newSalonId) {
    debugPrint('🔄 CalendarProvider: Salon switch initiated');
    debugPrint('📍 CalendarProvider: New salon ID: $newSalonId');
    debugPrint('🗑️ CalendarProvider: Clearing all salon-specific caches...');

    // Count current cache sizes for logging
    final clientsCacheCount = clientsCache.length;
    final petsCacheCount = petsCache.length;
    final clientPetsCacheCount = clientPetsCache.length;
    final appointmentsCacheCount = _appointmentsByDate.length;
    final blockedTimesCacheCount = _blockedTimesByDate.length;
    final currentAppointmentsCount = _appointments.length;
    final staffCount = _availableStaff.length;
    final servicesCount = _services.length;

    // Clear all client-related caches
    clientsCache.clear();
    petsCache.clear();
    clientPetsCache.clear();

    // Clear all appointment-related caches
    _appointments.clear();
    _appointmentsByDate.clear();
    _blockedTimesByDate.clear();
    _appointmentsError = null;

    // Clear staff data (will be reloaded for new salon)
    _availableStaff.clear();
    _selectedStaff.clear();
    _staffError = null;

    // Clear services data (will be reloaded for new salon)
    _services.clear();
    _servicesCache.clear();
    _servicesError = null;

    // Clear staff working hours cache (salon-specific data)
    final staffWorkingHoursCacheCount = _staffWorkingHoursCache.length;
    clearStaffWorkingHoursCache();

    // Reset filters and search
    _searchQuery = '';
    _selectedServiceFilter = '';
    _selectedStatusFilter = '';

    // Notify listeners that all data has been cleared
    notifyListeners();

    debugPrint('✅ CalendarProvider: Cleared $clientsCacheCount clients, $petsCacheCount pets, $clientPetsCacheCount client-pet associations');
    debugPrint('✅ CalendarProvider: Cleared $currentAppointmentsCount current appointments, $appointmentsCacheCount cached appointment dates');
    debugPrint('✅ CalendarProvider: Cleared $blockedTimesCacheCount cached block time dates');
    debugPrint('✅ CalendarProvider: Cleared $staffCount staff members, $servicesCount services');
    debugPrint('✅ CalendarProvider: Cleared $staffWorkingHoursCacheCount staff working hours cache entries');
    debugPrint('🔔 CalendarProvider: UI notified of cache clearing');
    debugPrint('✅ CalendarProvider: All cache clearing completed for salon switch');
  }

  /// Reload all data for the new salon after switching
  Future<void> reloadDataForNewSalon(String newSalonId) async {
    debugPrint('🔄 CalendarProvider: Reloading data for new salon: $newSalonId');

    try {
      // Clear staff working hours cache for new salon
      debugPrint('🔄 CalendarProvider: Clearing staff working hours cache for new salon...');
      clearStaffWorkingHoursCache();

      // Load staff first (required for other operations)
      debugPrint('🔄 CalendarProvider: Loading staff for new salon...');
      await loadStaff();

      // Load services
      debugPrint('🔄 CalendarProvider: Loading services for new salon...');
      await loadServices();

      // Load working hours
      debugPrint('🔄 CalendarProvider: Loading working hours for new salon...');
      await loadWorkingHours();

      // Load appointments for today
      debugPrint('🔄 CalendarProvider: Loading appointments for today...');
      await fetchAppointmentsForDate(DateTime.now(), forceRefresh: true);

      // Load block times for today
      debugPrint('🔄 CalendarProvider: Loading block times for today...');
      await fetchBlockedTimesForDate(DateTime.now(), forceRefresh: true);

      debugPrint('✅ CalendarProvider: All data reloaded for new salon');
    } catch (e) {
      debugPrint('❌ CalendarProvider: Error reloading data for new salon: $e');
    }
  }

  // Get business hours - dynamically adjusts based on working hours configuration
  Map<String, dynamic> getBusinessHours() {
    // Trigger loading of working hours data if dynamic range is enabled (async, don't wait)
    if (_useDynamicTimeRange) {
      ensureWorkingHoursDataForDynamicRange().catchError((e) {
        debugPrint('⚠️ Failed to load working hours data for dynamic range: $e');
      });
    }

    // Calculate dynamic time range based on staff working hours if enabled
    if (_useDynamicTimeRange && _staffWorkingHoursCache.isNotEmpty) {
      return _calculateDynamicBusinessHours();
    }

    // Fallback to salon working hours if available
    if (_useDynamicTimeRange && _workingHoursSettings != null) {
      return _calculateSalonBusinessHours();
    }

    // Default wide range for maximum flexibility (legacy behavior)
    debugPrint('📅 CalendarProvider.getBusinessHours: Using default wide hours (6 AM - 10 PM)');
    return {
      'openTime': 6,   // 6 AM wide range for full-screen calendar
      'closeTime': 22, // 10 PM wide range for full-screen calendar
      'workDays': [1, 2, 3, 4, 5, 6, 7], // All days for staff-based scheduling
      'lunchBreak': {
        'start': 25, // Disabled for staff-based scheduling
        'end': 25,   // Disabled for staff-based scheduling
      },
    };
  }

  /// Calculate business hours based on staff working schedules
  Map<String, dynamic> _calculateDynamicBusinessHours() {
    int earliestStart = 24; // Initialize to end of day
    int latestEnd = 0;     // Initialize to start of day
    Set<int> workingDays = {};

    // Analyze all staff schedules to find actual business hours
    for (final staffSettings in _staffWorkingHoursCache.values) {
      for (final entry in staffSettings.weeklySchedule.entries) {
        final daySchedule = entry.value;
        if (daySchedule.isWorkingDay && daySchedule.startTime != null && daySchedule.endTime != null) {
          // Add this day to working days
          final dayNumber = _getDayNumber(entry.key);
          if (dayNumber != null) {
            workingDays.add(dayNumber);
          }

          // Check for earliest start time
          final startHour = int.tryParse(daySchedule.startTime!.split(':')[0]) ?? 9;
          if (startHour < earliestStart) {
            earliestStart = startHour;
          }

          // Check for latest end time
          final endHour = int.tryParse(daySchedule.endTime!.split(':')[0]) ?? 18;
          if (endHour > latestEnd) {
            latestEnd = endHour;
          }
        }
      }
    }

    // Apply calculated hours with padding and minimum range
    return _applyTimeRangeConstraints(earliestStart, latestEnd, workingDays);
  }

  /// Calculate business hours based on salon working hours settings
  Map<String, dynamic> _calculateSalonBusinessHours() {
    if (_workingHoursSettings == null) {
      return _getDefaultBusinessHours();
    }

    int earliestStart = 24;
    int latestEnd = 0;
    Set<int> workingDays = {};

    // Analyze salon weekly schedule
    for (final entry in _workingHoursSettings!.weeklySchedule.entries) {
      final daySchedule = entry.value;
      if (daySchedule.isWorkingDay && daySchedule.startTime != null && daySchedule.endTime != null) {
        // Add this day to working days
        final dayNumber = _getDayNumber(entry.key);
        if (dayNumber != null) {
          workingDays.add(dayNumber);
        }

        // Check for earliest start time - parse from string format "HH:mm"
        final startHour = int.tryParse(daySchedule.startTime!.split(':')[0]) ?? 9;
        if (startHour < earliestStart) {
          earliestStart = startHour;
        }

        // Check for latest end time - parse from string format "HH:mm"
        final endHour = int.tryParse(daySchedule.endTime!.split(':')[0]) ?? 18;
        if (endHour > latestEnd) {
          latestEnd = endHour;
        }
      }
    }

    return _applyTimeRangeConstraints(earliestStart, latestEnd, workingDays);
  }

  /// Apply time range constraints (padding, minimum range) and return business hours
  Map<String, dynamic> _applyTimeRangeConstraints(int earliestStart, int latestEnd, Set<int> workingDays) {
    // Ensure we have reasonable defaults if no valid hours were found
    if (earliestStart == 24 || latestEnd == 0) {
      debugPrint('📅 CalendarProvider: No valid working hours found, using defaults');
      return _getDefaultBusinessHours();
    }

    // Apply padding
    int openTime = (earliestStart - _timeRangePaddingHours).clamp(0, 23);
    int closeTime = (latestEnd + _timeRangePaddingHours).clamp(1, 24);

    // Ensure minimum time range
    final currentRange = closeTime - openTime;
    if (currentRange < _minimumTimeRangeHours) {
      final additionalHours = _minimumTimeRangeHours - currentRange;
      final hoursToAddBefore = additionalHours ~/ 2;
      final hoursToAddAfter = additionalHours - hoursToAddBefore;

      openTime = (openTime - hoursToAddBefore).clamp(0, 23);
      closeTime = (closeTime + hoursToAddAfter).clamp(1, 24);
    }

    // Ensure we have at least some working days
    final List<int> workDays = workingDays.isEmpty
        ? [1, 2, 3, 4, 5, 6] // Default to Monday-Saturday
        : workingDays.toList()..sort();

    debugPrint('📅 CalendarProvider.getBusinessHours: Dynamic range calculated');
    debugPrint('   Original range: $earliestStart:00 - $latestEnd:00');
    debugPrint('   With padding (+${_timeRangePaddingHours}h): $openTime:00 - $closeTime:00');
    debugPrint('   Working days: ${workDays.join(', ')}');

    return {
      'openTime': openTime,
      'closeTime': closeTime,
      'workDays': workDays,
      'lunchBreak': {
        'start': 25, // Disabled for staff-based scheduling
        'end': 25,   // Individual staff lunch breaks will be handled by their schedules
      },
    };
  }

  /// Get default business hours when no working hours are available
  Map<String, dynamic> _getDefaultBusinessHours() {
    return {
      'openTime': 6,   // 6 AM default
      'closeTime': 22, // 10 PM default
      'workDays': [1, 2, 3, 4, 5, 6, 7], // All days
      'lunchBreak': {
        'start': 25, // Disabled
        'end': 25,   // Disabled
      },
    };
  }

  // Helper method to convert day name to weekday number
  int? _getDayNumber(String dayName) {
    switch (dayName.toLowerCase()) {
      case 'monday': return 1;
      case 'tuesday': return 2;
      case 'wednesday': return 3;
      case 'thursday': return 4;
      case 'friday': return 5;
      case 'saturday': return 6;
      case 'sunday': return 7;
      default: return null;
    }
  }

  // Check if a time slot is available
  Future<bool> isTimeSlotAvailable(DateTime start, DateTime end, [String? excludeAppointmentId]) async {
    return await _calendarService.isTimeSlotAvailable(start, end, excludeAppointmentId);
  }



  // Get a client by ID (with caching)
  Future<Client?> getClientById(String clientId) async {
    // Check if client is already in cache
    if (clientsCache.containsKey(clientId)) {
      return clientsCache[clientId];
    }

    _isLoadingClientData = true;
    _clientDataError = null;
    notifyListeners();

    try {
      final client = await _calendarService.getClientById(clientId);
      if (client != null) {
        clientsCache[clientId] = client;
      }
      _isLoadingClientData = false;
      notifyListeners();
      return client;
    } catch (e) {
      _isLoadingClientData = false;
      _clientDataError = 'Failed to load client data: ${e.toString()}';
      notifyListeners();
      return null;
    }
  }

  // Get a pet by ID (with caching)
  Future<Pet?> getPetById(String petId) async {
    // Check if pet is already in cache
    if (petsCache.containsKey(petId)) {
      return petsCache[petId];
    }

    _isLoadingClientData = true;
    _clientDataError = null;
    notifyListeners();

    try {
      final pet = await _calendarService.getPetById(petId);
      if (pet != null) {
        petsCache[petId] = pet;
      }
      _isLoadingClientData = false;
      notifyListeners();
      return pet;
    } catch (e) {
      _isLoadingClientData = false;
      _clientDataError = 'Failed to load pet data: ${e.toString()}';
      notifyListeners();
      return null;
    }
  }

  // Get all pets for a client (with caching)
  Future<List<Pet>> getPetsForClient(String clientId) async {
    debugPrint('🔄 CalendarProvider: getPetsForClient called for client: $clientId');

    // Check if client's pets are already in cache
    if (clientPetsCache.containsKey(clientId)) {
      final cachedPets = clientPetsCache[clientId]!;
      debugPrint('📦 CalendarProvider: Found ${cachedPets.length} cached pets for client $clientId');
      return cachedPets;
    }

    debugPrint('🔄 CalendarProvider: No cached pets found, fetching from service...');
    _isLoadingClientData = true;
    _clientDataError = null;
    notifyListeners();

    try {
      final pets = await _calendarService.getPetsForClient(clientId);
      debugPrint('📡 CalendarProvider: Service returned ${pets.length} pets for client $clientId');

      clientPetsCache[clientId] = pets;

      // Also cache individual pets
      for (final pet in pets) {
        petsCache[pet.id] = pet;
        debugPrint('📦 CalendarProvider: Cached pet ${pet.name} (${pet.id})');
      }

      _isLoadingClientData = false;
      notifyListeners();
      return pets;
    } catch (e) {
      debugPrint('❌ CalendarProvider: Error loading pets for client $clientId: $e');
      _isLoadingClientData = false;
      _clientDataError = 'Failed to load client pets: ${e.toString()}';
      notifyListeners();
      return [];
    }
  }

  // Force refresh pets for a client (bypass cache)
  Future<List<Pet>> refreshPetsForClient(String clientId) async {
    debugPrint('🔄 CalendarProvider: Force refreshing pets for client: $clientId');

    // Clear cache for this client
    clientPetsCache.remove(clientId);

    // Fetch fresh data
    return await getPetsForClient(clientId);
  }

  // Staff management methods
  Future<void> loadStaff() async {
    debugPrint('🔄 CalendarProvider.loadStaff() called');
    _isLoadingStaff = true;
    _staffError = null;
    notifyListeners();

    try {
      final response = await StaffService.getCurrentSalonStaff(activeOnly: true);
      debugPrint('🔍 Staff service response: success=${response.success}, data=${response.data}');

      if (response.success && response.data != null) {
        _availableStaff = response.data!.activeStaff;
        debugPrint('🔍 Loaded ${_availableStaff.length} staff members');

        // Debug log each staff member with nickname info
        for (final staff in _availableStaff) {
          debugPrint('🔍 Staff: ${staff.name} (${staff.id}) - Display: ${staff.displayName} - Role: ${staff.groomerRole}');
        }

        // If no staff are selected, select the current user by default
        if (_selectedStaff.isEmpty) {
          final currentUserId = await AuthService.getCurrentUserId();
          if (currentUserId != null &&
              _availableStaff.any((s) => s.id == currentUserId)) {
            _selectedStaff = [currentUserId];
            debugPrint('🔍 Auto-selected current user: $_selectedStaff');
          } else {
            _selectedStaff = _availableStaff.map((s) => s.id).toList();
            debugPrint('🔍 Auto-selected all staff (fallback): $_selectedStaff');
          }
        }
        _staffError = null;
      } else {
        debugPrint('❌ Failed to load staff: ${response.error}');
        _staffError = response.error ?? 'Failed to load staff';
        _availableStaff = [];
        _selectedStaff = [];
      }
    } catch (e) {
      debugPrint('❌ Error loading staff: $e');
      _staffError = 'Error loading staff: $e';
      _availableStaff = [];
      _selectedStaff = [];
    } finally {
      _isLoadingStaff = false;
      debugPrint('🔄 CalendarProvider.loadStaff() completed');
      notifyListeners();
    }
  }

  /// Force refresh staff data (useful when staff info like nicknames are updated)
  Future<void> refreshStaffData() async {
    debugPrint('🔄 CalendarProvider.refreshStaffData() called - forcing staff reload');
    await loadStaff();
  }

  void selectStaff(String staffId) {
    if (!_selectedStaff.contains(staffId)) {
      _selectedStaff.add(staffId);
      notifyListeners();
    }
  }

  void deselectStaff(String staffId) {
    _selectedStaff.remove(staffId);
    notifyListeners();
  }

  void selectAllStaff() {
    _selectedStaff = _availableStaff.map((s) => s.id).toList();
    notifyListeners();
  }

  Future<void> selectCurrentUserOnly() async {
    final currentUserId = await AuthService.getCurrentUserId();
    if (currentUserId != null &&
        _availableStaff.any((s) => s.id == currentUserId)) {
      _selectedStaff = [currentUserId];
      notifyListeners();
    }
  }

  void clearStaffSelection() {
    _selectedStaff.clear();
    notifyListeners();
  }

  StaffResponse? getStaffById(String staffId) {
    try {
      return _availableStaff.firstWhere((s) => s.id == staffId);
    } catch (e) {
      return null;
    }
  }

  Color getStaffColor(String staffId) {
    return AppTheme.getStaffColor(staffId);
  }

  // Working hours management
  Future<void> loadWorkingHours() async {
    debugPrint('⏰ Loading working hours settings...');
    _isLoadingWorkingHours = true;
    _workingHoursError = null;
    notifyListeners();

    try {
      final response = await WorkingHoursService.getWorkingHours();
      if (response.success && response.data != null) {
        _workingHoursSettings = response.data!;
        _workingHoursError = null;

        debugPrint('✅ Working hours loaded successfully');
        debugPrint('📅 Custom closures count: ${_workingHoursSettings!.customClosures.length}');
        for (final closure in _workingHoursSettings!.customClosures) {
          debugPrint('   🚫 Closure: ${closure.startDate.toIso8601String().split('T')[0]}-${closure.endDate.toIso8601String().split('T')[0]} - ${closure.reason}');
        }

        // Notify listeners so calendar views refresh with new business hours
        notifyListeners();
      } else {
        _workingHoursError = response.error ?? 'Failed to load working hours';
        _workingHoursSettings = null;
        debugPrint('❌ Failed to load working hours: $_workingHoursError');
      }
    } catch (e) {
      _workingHoursError = 'Error loading working hours: $e';
      _workingHoursSettings = null;
      debugPrint('❌ Error loading working hours: $e');
    } finally {
      _isLoadingWorkingHours = false;
      notifyListeners();
    }
  }

  // Method to refresh calendar when working hours change
  Future<void> refreshCalendarAfterWorkingHoursChange() async {
    await loadWorkingHours();
    // Clear staff working hours cache to ensure fresh data
    clearStaffWorkingHoursCache();
    // The notifyListeners() in loadWorkingHours will trigger calendar rebuild
    notifyListeners();
  }

  /// Ensure working hours data is loaded for dynamic time range calculation
  Future<void> ensureWorkingHoursDataForDynamicRange() async {
    if (!_useDynamicTimeRange) {
      return; // Dynamic range is disabled, no need to load data
    }

    // Load salon working hours if not available
    if (_workingHoursSettings == null && !_isLoadingWorkingHours) {
      debugPrint('🔄 Loading salon working hours for dynamic time range...');
      await loadWorkingHours();
    }

    // Load staff working hours for selected staff if cache is empty
    if (_staffWorkingHoursCache.isEmpty && _selectedStaff.isNotEmpty) {
      debugPrint('🔄 Loading staff working hours for dynamic time range...');
      await loadStaffWorkingHoursOnDemand(_selectedStaff, reason: 'Dynamic time range calculation');
    }
  }



  bool isWorkingDay(DateTime date) {
    // Only check for holidays and custom closures, not salon working days
    // Staff schedules will determine actual working days
    return !RomanianHolidays.isHoliday(date) && !hasCustomClosure(date);
  }

  /// Check if the salon is closed on a specific date
  bool isClosedOnDate(DateTime date) {
    // Only check for holidays and custom closures, not salon working days
    // Staff schedules will determine actual working days
    return RomanianHolidays.isHoliday(date) || hasCustomClosure(date);
  }

  /// Check if a date is a Romanian holiday (immediate, no async)
  bool isRomanianHoliday(DateTime date) {
    final isHoliday = RomanianHolidays.isHoliday(date);
    if (isHoliday) {
      // TODO optimize this debugPrint('🎄 Romanian holiday detected: ${date.toIso8601String().split('T')[0]}');
    }
    return isHoliday;
  }

  /// Check if a date has a custom closure (immediate, no async)
  bool hasCustomClosure(DateTime date) {
    if (_workingHoursSettings == null) {
      // Only log warning if we're not currently loading working hours
      if (!_isLoadingWorkingHours) {
        debugPrint('⚠️ No working hours settings available for custom closure check - consider calling loadWorkingHours()');
      }
      return false;
    }

    final hasCustom = _workingHoursSettings!.customClosures.any((c) =>
      !date.isBefore(c.startDate) && !date.isAfter(c.endDate)
    );

    if (hasCustom) {
      final closure = _workingHoursSettings!.customClosures.firstWhere(
        (c) => !date.isBefore(c.startDate) && !date.isAfter(c.endDate),
      );
      //debugPrint('🚫 Custom closure detected: ${date.toIso8601String().split('T')[0]} - ${closure.reason}');
    }

    return hasCustom;
  }

  /// Ensure working hours are loaded before checking closures
  Future<void> ensureWorkingHoursLoaded() async {
    if (_workingHoursSettings == null && !_isLoadingWorkingHours) {
      debugPrint('🔄 Working hours not loaded, loading now...');
      await loadWorkingHours();
    }
  }

  /// Get comprehensive closure information for a date (immediate, no async)
  ({
    bool isRomanianHoliday,
    bool hasCustomClosure,
    bool isSalonClosed,
    String? closureReason,
    String? holidayName
  }) getDateClosureInfo(DateTime date) {
    final dateStr = date.toIso8601String().split('T')[0];

    // Trigger working hours loading if not available (async, don't wait)
    if (_workingHoursSettings == null && !_isLoadingWorkingHours) {
      loadWorkingHours().catchError((e) {
      });
    }

    final isHoliday = isRomanianHoliday(date);
    final hasCustom = hasCustomClosure(date);
    final isClosed = isClosedOnDate(date);

    String? reason;
    String? holidayName;

    if (isHoliday) {
      final holiday = RomanianHolidays.getHolidayForDate(date);
      holidayName = holiday?.name;
      reason = 'Sărbătoare legală: ${holidayName ?? 'Sărbătoare'}';
    } else if (hasCustom && _workingHoursSettings != null) {
      final closure = _workingHoursSettings!.customClosures.firstWhere(
        (c) => c.date.year == date.year &&
               c.date.month == date.month &&
               c.date.day == date.day,
        orElse: () => CustomClosure(reason: 'Închis', startDate: date, endDate: date),
      );
      reason = closure.reason;
    }

    final result = (
      isRomanianHoliday: isHoliday,
      hasCustomClosure: hasCustom,
      isSalonClosed: isClosed,
      closureReason: reason,
      holidayName: holidayName,
    );

    return result;
  }

  /// Check if staff member is working on date (synchronous with cache)
  bool isStaffWorkingOnDateSync(String staffId, DateTime date) {
    final dateStr = date.toIso8601String().split('T')[0];

    final settings = _staffWorkingHoursCache[staffId];
    if (settings == null) {
      // DO NOT trigger async load automatically to prevent API spam
      // Staff working hours should be loaded proactively when needed

      // Return false when staff settings aren't available - no salon schedule fallback
      return false;
    }

    final staffWorking = settings.isAvailableOnDate(date);

    // Additional logging for staff-specific constraints
    if (!staffWorking) {
      final dayOfWeek = _getDayOfWeekString(date);
      final daySchedule = settings.getScheduleForDay(dayOfWeek);
      if (daySchedule == null || !daySchedule.isWorkingDay) {
      } else {
        // Check for custom closures or holidays
        final hasCustomClosure = settings.customClosures.any((closure) =>
            !date.isBefore(closure.startDate) && !date.isAfter(closure.endDate));
        if (hasCustomClosure) {
          final closure = settings.customClosures.firstWhere((c) =>
              !date.isBefore(c.startDate) && !date.isAfter(c.endDate));
        }
      }
    }

    return staffWorking;
  }

  /// Helper method to get day of week string
  String _getDayOfWeekString(DateTime date) {
    const dayNames = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    return dayNames[date.weekday - 1];
  }

  /// Get visual styling information for a time slot
  ({
    bool isAvailable,
    bool isGreyedOut,
    bool isInteractive,
    String? disabledReason
  }) getTimeSlotStyling(DateTime dateTime, String? staffId) {
    final date = DateTime(dateTime.year, dateTime.month, dateTime.day);
    final timeStr = '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';

    final closureInfo = getDateClosureInfo(date);

    // Check if salon is closed
    if (closureInfo.isSalonClosed) {
      return (
        isAvailable: false,
        isGreyedOut: true,
        isInteractive: false,
        disabledReason: closureInfo.closureReason,
      );
    }

    // Check staff availability if staffId provided
    if (staffId != null) {
      final staffWorking = isStaffWorkingOnDateSync(staffId, date);
      if (!staffWorking) {
        return (
          isAvailable: false,
          isGreyedOut: true,
          isInteractive: false,
          disabledReason: 'Personal indisponibil',
        );
      }

      // Check staff-specific working hours for this time slot
      final staffSettings = _staffWorkingHoursCache[staffId];
      if (staffSettings != null) {
        final isStaffAvailableAtTime = staffSettings.isAvailableAtTime(dateTime);

        if (!isStaffAvailableAtTime) {
          return (
            isAvailable: false,
            isGreyedOut: true,
            isInteractive: false,
            disabledReason: 'Personal indisponibil la această oră',
          );
        }

        // Staff is available at this time - no need to check salon hours
        return (
          isAvailable: true,
          isGreyedOut: false,
          isInteractive: true,
          disabledReason: null,
        );
      } else {
        return (
          isAvailable: false,
          isGreyedOut: true,
          isInteractive: false,
          disabledReason: 'Personal indisponibil (setări lipsă)',
        );
      }
    }

    // Only apply salon business hours when no staff is specified
    final businessHours = getBusinessHours();
    final openTime = businessHours['openTime'] as int;
    final closeTime = businessHours['closeTime'] as int;
    final lunchStart = businessHours['lunchBreak']['start'] as int;
    final lunchEnd = businessHours['lunchBreak']['end'] as int;

    final hour = dateTime.hour;
    final isBusinessHour = hour >= openTime && hour < closeTime;
    final isLunchBreak = hour >= lunchStart && hour < lunchEnd;


    if (!isBusinessHour) {
      return (
        isAvailable: false,
        isGreyedOut: true,
        isInteractive: false,
        disabledReason: 'În afara programului salonului',
      );
    }

    if (isLunchBreak) {
      return (
        isAvailable: false,
        isGreyedOut: true,
        isInteractive: false,
        disabledReason: 'Pauza de prânz',
      );
    }

    return (
      isAvailable: true,
      isGreyedOut: false,
      isInteractive: true,
      disabledReason: null,
    );
  }

  // Service management methods
  Future<void> loadServices() async {
    _isLoadingServices = true;
    _servicesError = null;
    notifyListeners();

    try {
      final response = await ServiceManagementService.getServices(isActive: true);
      if (response.success && response.data != null) {
        _services = response.data!;
        // Update cache
        _servicesCache.clear();
        for (final service in _services) {
          _servicesCache[service.id] = service;
        }
        _servicesError = null;
      } else {
        _servicesError = response.error ?? 'Failed to load services';
        _services = [];
        _servicesCache.clear();
      }
    } catch (e) {
      _servicesError = 'Error loading services: $e';
      _services = [];
      _servicesCache.clear();
    } finally {
      _isLoadingServices = false;
      notifyListeners();
    }
  }

  Service? getServiceById(String serviceId) {
    return _servicesCache[serviceId];
  }

  Service? getServiceByName(String serviceName) {
    try {
      return _services.firstWhere((s) => s.name == serviceName);
    } catch (e) {
      return null;
    }
  }

  // Get service details in the format expected by appointment widgets
  Map<String, Map<String, dynamic>> getServiceDetails() {
    final Map<String, Map<String, dynamic>> details = {};

    for (final service in _services) {
      details[service.name] = {
        'id': service.id,
        'duration': service.duration,
        'price': service.price,
        if (service.sizePrices != null) 'sizePrices': service.sizePrices,
        'description': service.description,
        'category': service.category,
      };
    }

    return details;
  }

  // Get available service names for appointment form
  List<String> getAvailableServiceNames() {
    return _services.where((s) => s.isActive).map((s) => s.name).toList();
  }

  // Get service IDs from service names (for backend compatibility)
  List<String> getServiceIds(List<String> serviceNames) {
    final List<String> serviceIds = [];
    for (final serviceName in serviceNames) {
      final service = getServiceByName(serviceName);
      if (service != null) {
        serviceIds.add(service.id);
      }
    }
    return serviceIds;
  }

  // Get service name to ID mapping (for appointment form)
  Map<String, String> getServiceNameToIdMap() {
    final Map<String, String> mapping = {};
    for (final service in _services) {
      mapping[service.name] = service.id;
    }
    return mapping;
  }

  // Staff working hours cache to prevent repeated API calls
  final Map<String, StaffWorkingHoursSettings> _staffWorkingHoursCache = {};
  final Map<String, DateTime> _staffWorkingHoursCacheTimestamp = {};
  static const Duration _cacheValidityDuration = Duration(minutes: 30); // Increased cache duration

  /// Load staff working hours in batch (OPTIMIZED - single API call for multiple staff)
  Future<void> _loadStaffWorkingHoursBatch(List<String> staffIds) async {
    final now = DateTime.now();
    debugPrint('🚀 Loading staff working hours in batch for ${staffIds.length} staff members');
    debugPrint('🌐 Making batch API call to: /api/salons/{salonId}/staff/working-hours/batch');
    debugPrint('📋 Staff IDs for batch request: ${staffIds.join(', ')}');

    try {
      debugPrint('🔄 Calling StaffWorkingHoursService.getBatchStaffWorkingHours...');
      final response = await StaffWorkingHoursService.getBatchStaffWorkingHours(staffIds);

      debugPrint('📥 Batch API response received:');
      debugPrint('   Success: ${response.success}');
      debugPrint('   Data is null: ${response.data == null}');
      debugPrint('   Error: ${response.error}');

      if (response.success && response.data != null) {
        final batchData = response.data!;

        debugPrint('✅ Batch API response received for ${batchData.length} staff members');
        debugPrint('📋 Received staff IDs: ${batchData.keys.join(', ')}');

        // Cache all the received data
        for (final entry in batchData.entries) {
          final staffId = entry.key;
          final settings = entry.value;

          _staffWorkingHoursCache[staffId] = settings;
          _staffWorkingHoursCacheTimestamp[staffId] = now;

          debugPrint('✅ Cached working hours for staff: $staffId');
          debugPrint('📅 Staff custom closures count: ${settings.customClosures.length}');
          for (final closure in settings.customClosures) {
            debugPrint('   🚫 Staff closure: ${closure.startDate.toIso8601String().split('T')[0]}-${closure.endDate.toIso8601String().split('T')[0]} - ${closure.reason}');
          }
        }

        debugPrint('🚀 Batch loading completed successfully - ${batchData.length}/${staffIds.length} staff loaded');

        // Log any missing staff
        final loadedStaffIds = batchData.keys.toSet();
        final requestedStaffIds = staffIds.toSet();
        final missingStaffIds = requestedStaffIds.difference(loadedStaffIds);
        if (missingStaffIds.isNotEmpty) {
          debugPrint('⚠️ Some staff not returned by batch API: ${missingStaffIds.join(', ')}');
        }
      } else {
        debugPrint('❌ Batch API response indicates failure');
        debugPrint('   Response success: ${response.success}');
        debugPrint('   Response error: ${response.error}');
        throw Exception(response.error ?? 'Batch API failed - no success or no data');
      }
    } catch (e) {
      debugPrint('❌ Batch loading failed with exception: $e');
      debugPrint('❌ Exception type: ${e.runtimeType}');
      rethrow;
    }
  }

  /// Get staff working hours with caching to prevent API spam
  Future<StaffWorkingHoursSettings?> _getStaffWorkingHoursWithCache(String staffId) async {
    final now = DateTime.now();
    debugPrint('👤 Getting staff working hours for: $staffId');

    // Check if we have cached data that's still valid
    if (_staffWorkingHoursCache.containsKey(staffId) &&
        _staffWorkingHoursCacheTimestamp.containsKey(staffId)) {
      final cacheTime = _staffWorkingHoursCacheTimestamp[staffId]!;
      final cacheAge = now.difference(cacheTime);
      if (cacheAge < _cacheValidityDuration) {
        debugPrint('📦 Using cached staff working hours (age: ${cacheAge.inMinutes}min)');
        return _staffWorkingHoursCache[staffId];
      } else {
        debugPrint('⏰ Cache expired for staff $staffId (age: ${cacheAge.inMinutes}min)');
      }
    } else {
      debugPrint('📭 No cached data for staff $staffId');
    }

    try {
      debugPrint('🌐 Fetching fresh staff working hours from API...');
      final response = await StaffWorkingHoursService.getStaffWorkingHours(staffId);
      if (response.success && response.data != null) {
        _staffWorkingHoursCache[staffId] = response.data!;
        _staffWorkingHoursCacheTimestamp[staffId] = now;

        final settings = response.data!;
        debugPrint('✅ Staff working hours cached successfully');
        debugPrint('📅 Staff custom closures count: ${settings.customClosures.length}');
        for (final closure in settings.customClosures) {
          debugPrint('   🚫 Staff closure: ${closure.startDate.toIso8601String().split('T')[0]}-${closure.endDate.toIso8601String().split('T')[0]} - ${closure.reason}');
        }

        return response.data!;
      } else {
        debugPrint('❌ Failed to get staff working hours: ${response.error}');
      }
    } catch (e) {
      debugPrint('❌ Error getting staff working hours for $staffId: $e');
    }

    return null;
  }

  /// Check if staff member is working on a specific date
  Future<bool> isStaffWorkingOnDate(String staffId, DateTime date) async {
    final settings = await _getStaffWorkingHoursWithCache(staffId);
    if (settings == null) {
      return false; // Default to not working if can't get settings
    }

    return settings.isAvailableOnDate(date);
  }

  /// Check if staff member is available at a specific time
  Future<bool> isStaffAvailableAtTime(String staffId, DateTime dateTime) async {
    final settings = await _getStaffWorkingHoursWithCache(staffId);
    if (settings == null) {
      return false; // Default to not available if can't get settings
    }

    return settings.isAvailableAtTime(dateTime);
  }

  /// Clear staff working hours cache (useful when staff schedules are updated)
  void clearStaffWorkingHoursCache([String? specificStaffId]) {
    if (specificStaffId != null) {
      _staffWorkingHoursCache.remove(specificStaffId);
      _staffWorkingHoursCacheTimestamp.remove(specificStaffId);
      debugPrint('🗑️ Cleared working hours cache for staff: $specificStaffId');
    } else {
      _staffWorkingHoursCache.clear();
      _staffWorkingHoursCacheTimestamp.clear();
      debugPrint('🗑️ Cleared all staff working hours cache');
    }
  }

  /// Handle real-time updates when salon or staff schedules change
  Future<void> handleScheduleUpdate({
    bool salonScheduleChanged = false,
    String? staffId,
    bool customClosuresChanged = false,
  }) async {
    final timestamp = DateTime.now().toIso8601String();
    debugPrint('🔄 [$timestamp] Handling schedule update:');
    debugPrint('   📊 Salon schedule changed: $salonScheduleChanged');
    debugPrint('   👤 Staff ID: $staffId');
    debugPrint('   🚫 Custom closures changed: $customClosuresChanged');

    if (salonScheduleChanged || customClosuresChanged) {
      // Reload salon working hours
      debugPrint('🏢 [$timestamp] Reloading salon working hours...');
      await loadWorkingHours();
      debugPrint('✅ [$timestamp] Salon working hours reloaded');
    }

    if (staffId != null) {
      // Clear specific staff cache and reload only that staff member
      debugPrint('👤 [$timestamp] Refreshing staff cache for: $staffId');
      clearStaffWorkingHoursCache(staffId);
      // Load fresh staff data using optimized method
      await loadStaffWorkingHoursOnDemand([staffId], reason: 'Staff schedule update');
      debugPrint('✅ [$timestamp] Staff cache refreshed for: $staffId');
    } else if (salonScheduleChanged) {
      // Clear all staff cache if salon schedule changed
      debugPrint('👥 [$timestamp] Clearing all staff cache due to salon schedule change');
      clearStaffWorkingHoursCache();
      // Reload for currently selected staff only
      if (_selectedStaff.isNotEmpty) {
        await loadStaffWorkingHoursOnDemand(_selectedStaff, reason: 'Salon schedule change');
      }
      debugPrint('✅ [$timestamp] All staff cache cleared and reloaded for selected staff');
    }

    // Force immediate UI update
    notifyListeners();
    debugPrint('🎨 [$timestamp] UI refresh triggered - calendar views should update immediately');
    debugPrint('✅ [$timestamp] Schedule update completed successfully');
  }

  /// Force refresh of staff working hours (useful for debugging)
  Future<void> forceRefreshStaffWorkingHours(String staffId) async {
    debugPrint('🔄 Force refreshing staff working hours for: $staffId');
    clearStaffWorkingHoursCache(staffId);
    await _getStaffWorkingHoursWithCache(staffId);
    notifyListeners();
    debugPrint('✅ Staff working hours force refreshed');
  }

  // Testing helper methods
  void setStaffCacheForTesting(String staffId, dynamic data) {
    _staffWorkingHoursCache[staffId] = data;
  }

  bool hasStaffCacheForTesting(String staffId) {
    return _staffWorkingHoursCache.containsKey(staffId);
  }

  /// Force complete calendar refresh (useful for debugging real-time updates)
  Future<void> forceCalendarRefresh({String? reason}) async {
    final timestamp = DateTime.now().toIso8601String();
    debugPrint('🔄 [$timestamp] Force calendar refresh triggered${reason != null ? ' - Reason: $reason' : ''}');

    // Reload all working hours data
    await loadWorkingHours();

    // Clear all staff caches
    clearStaffWorkingHoursCache();

    // Reload staff working hours for currently selected staff
    final selectedStaffIds = _selectedStaff;
    if (selectedStaffIds.isNotEmpty) {
      await loadStaffWorkingHoursOnDemand(selectedStaffIds, reason: 'Force calendar refresh');
    }

    // Force UI update
    notifyListeners();

    debugPrint('✅ [$timestamp] Force calendar refresh completed');
  }

  /// Refresh staff working hours when user explicitly requests it
  Future<void> refreshStaffWorkingHours({String? reason}) async {
    final timestamp = DateTime.now().toIso8601String();
    debugPrint('🔄 [$timestamp] Refreshing staff working hours${reason != null ? ' - Reason: $reason' : ''}');

    // Clear all staff caches to force fresh data
    clearStaffWorkingHoursCache();

    // Reload for currently selected staff
    final selectedStaffIds = _selectedStaff;
    if (selectedStaffIds.isNotEmpty) {
      debugPrint('🚀 [$timestamp] Using batch API for refresh of ${selectedStaffIds.length} staff members');
      await loadStaffWorkingHoursOnDemand(selectedStaffIds, reason: reason ?? 'User refresh');
    }

    // Force UI update
    notifyListeners();

    debugPrint('✅ [$timestamp] Staff working hours refresh completed');
  }

  /// Test the new batch endpoint explicitly
  Future<void> testBatchStaffWorkingHours() async {
    final timestamp = DateTime.now().toIso8601String();
    debugPrint('🧪 [$timestamp] Testing batch staff working hours endpoint');

    final selectedStaffIds = _selectedStaff;
    if (selectedStaffIds.isEmpty) {
      debugPrint('⚠️ [$timestamp] No staff selected for batch test');
      return;
    }

    try {
      debugPrint('🌐 [$timestamp] Testing batch endpoint with ${selectedStaffIds.length} staff: ${selectedStaffIds.join(', ')}');
      await _loadStaffWorkingHoursBatch(selectedStaffIds);
      debugPrint('✅ [$timestamp] Batch endpoint test SUCCESSFUL!');

      // Verify cache was populated
      int cachedCount = 0;
      for (final staffId in selectedStaffIds) {
        if (_staffWorkingHoursCache.containsKey(staffId)) {
          cachedCount++;
        }
      }
      debugPrint('📦 [$timestamp] Cache populated for $cachedCount/${selectedStaffIds.length} staff members');

    } catch (e) {
      debugPrint('❌ [$timestamp] Batch endpoint test FAILED: $e');
      debugPrint('🔄 [$timestamp] This indicates the backend batch endpoint may not be working correctly');
    }
  }

  /// Preload staff working hours for all active staff members
  Future<void> preloadStaffWorkingHours(List<String> staffIds) async {
    final timestamp = DateTime.now().toIso8601String();
    debugPrint('🔄 [$timestamp] Preloading staff working hours for ${staffIds.length} staff members');

    final futures = staffIds.map((staffId) async {
      try {
        await _getStaffWorkingHoursWithCache(staffId);
        debugPrint('✅ [$timestamp] Preloaded working hours for staff: $staffId');
      } catch (e) {
        debugPrint('❌ [$timestamp] Failed to preload working hours for staff $staffId: $e');
      }
    });

    await Future.wait(futures);
    debugPrint('✅ [$timestamp] Staff working hours preloading completed');
  }

  /// Load staff working hours only when explicitly needed (optimized approach)
  Future<void> loadStaffWorkingHoursOnDemand(List<String> staffIds, {String? reason}) async {
    debugPrint('🔄  Loading staff working hours on demand for ${staffIds.length} staff members${reason != null ? ' - Reason: $reason' : ''}');

    // Only load for staff that don't have valid cache
    final staffToLoad = <String>[];
    final now = DateTime.now();

    for (final staffId in staffIds) {
      if (!_staffWorkingHoursCache.containsKey(staffId) ||
          !_staffWorkingHoursCacheTimestamp.containsKey(staffId)) {
        staffToLoad.add(staffId);
        debugPrint('📭  Staff $staffId has no cache - will load');
      } else {
        final cacheTime = _staffWorkingHoursCacheTimestamp[staffId]!;
        final cacheAge = now.difference(cacheTime);
        if (cacheAge >= _cacheValidityDuration) {
          staffToLoad.add(staffId);
          debugPrint('⏰  Staff $staffId cache expired (age: ${cacheAge.inMinutes}min) - will reload');
        } else {
          debugPrint('📦  Staff $staffId has valid cache (age: ${cacheAge.inMinutes}min) - skipping');
        }
      }
    }

    if (staffToLoad.isEmpty) {
      debugPrint('✅  All staff have valid cache - no API requests needed');
      return;
    }

    debugPrint('🌐  Making API requests for ${staffToLoad.length} staff members: ${staffToLoad.join(', ')}');

    // Try batch loading first (backend now supports it!)
    if (staffToLoad.isNotEmpty) { // Use batch even for single staff for consistency
      try {
        debugPrint('🚀  Attempting batch loading for ${staffToLoad.length} staff members');
        await _loadStaffWorkingHoursBatch(staffToLoad);
        debugPrint('✅  Batch staff working hours loading completed successfully');
        return;
      } catch (e) {
        debugPrint('⚠️  Batch loading failed, falling back to individual requests: $e');
        debugPrint('🔄  This might indicate backend batch endpoint is not available yet');
      }
    }

    // Fallback to individual requests
    final futures = staffToLoad.map((staffId) async {
      try {
        await _getStaffWorkingHoursWithCache(staffId);
        debugPrint('✅  Loaded working hours for staff: $staffId');
      } catch (e) {
        debugPrint('❌  Failed to load working hours for staff $staffId: $e');
      }
    });

    await Future.wait(futures);
    debugPrint('✅  Staff working hours on-demand loading completed');
  }

  /// Get all cached staff IDs (useful for debugging)
  List<String> getCachedStaffIds() {
    return _staffWorkingHoursCache.keys.toList();
  }

  /// Get cached staff working hours (for fast validation)
  StaffWorkingHoursSettings? getStaffWorkingHours(String staffId) {
    return _staffWorkingHoursCache[staffId];
  }

  /// Get cache status for debugging
  Map<String, dynamic> getStaffCacheStatus() {
    final now = DateTime.now();
    final status = <String, dynamic>{};

    for (final staffId in _staffWorkingHoursCache.keys) {
      final cacheTime = _staffWorkingHoursCacheTimestamp[staffId];
      final age = cacheTime != null ? now.difference(cacheTime) : null;
      final isValid = age != null && age < _cacheValidityDuration;

      status[staffId] = {
        'cached': true,
        'cacheAge': age?.inMinutes,
        'isValid': isValid,
        'hasSettings': _staffWorkingHoursCache[staffId] != null,
      };
    }

    return status;
  }

  /// Handle staff schedule changes with real-time calendar updates
  Future<void> onStaffScheduleChanged(String staffId, {String? reason}) async {
    final timestamp = DateTime.now().toIso8601String();
    debugPrint('📅 [$timestamp] Staff schedule changed for $staffId${reason != null ? ' - Reason: $reason' : ''}');

    // Clear cache for the specific staff member
    clearStaffWorkingHoursCache(staffId);
    debugPrint('🗑️ [$timestamp] Cleared cache for staff: $staffId');

    // Reload the staff's working hours using optimized method
    try {
      await loadStaffWorkingHoursOnDemand([staffId], reason: reason ?? 'Staff schedule change');
      debugPrint('✅ [$timestamp] Reloaded working hours for staff: $staffId');
    } catch (e) {
      debugPrint('❌ [$timestamp] Failed to reload working hours for staff $staffId: $e');
    }

    // Trigger UI update
    notifyListeners();
    debugPrint('🔄 [$timestamp] Calendar UI updated for staff schedule change');
  }

  /// Handle salon schedule changes with real-time calendar updates
  Future<void> onSalonScheduleChanged({String? reason}) async {
    final timestamp = DateTime.now().toIso8601String();
    debugPrint('🏢 [$timestamp] Salon schedule changed${reason != null ? ' - Reason: $reason' : ''}');

    // Reload salon working hours
    await loadWorkingHours();
    debugPrint('✅ [$timestamp] Reloaded salon working hours');

    // Clear all staff caches since salon changes might affect staff availability
    clearStaffWorkingHoursCache();
    debugPrint('🗑️ [$timestamp] Cleared all staff working hours cache');

    // Reload for currently selected staff only
    if (_selectedStaff.isNotEmpty) {
      await loadStaffWorkingHoursOnDemand(_selectedStaff, reason: reason ?? 'Salon schedule change');
      debugPrint('✅ [$timestamp] Reloaded working hours for selected staff');
    }

    // Trigger UI update
    notifyListeners();
    debugPrint('🔄 [$timestamp] Calendar UI updated for salon schedule change');
  }

  /// Handle holiday/closure changes with real-time calendar updates
  Future<void> onHolidayClosureChanged({String? reason}) async {
    final timestamp = DateTime.now().toIso8601String();
    debugPrint('🎄 [$timestamp] Holiday/closure changed${reason != null ? ' - Reason: $reason' : ''}');

    // Clear holiday cache (if we implement one in the future)
    // For now, holidays are calculated dynamically

    // Clear all staff caches since holidays affect all staff
    clearStaffWorkingHoursCache();
    debugPrint('🗑️ [$timestamp] Cleared all staff working hours cache due to holiday change');

    // Reload for currently selected staff only
    if (_selectedStaff.isNotEmpty) {
      await loadStaffWorkingHoursOnDemand(_selectedStaff, reason: reason ?? 'Holiday/closure change');
      debugPrint('✅ [$timestamp] Reloaded working hours for selected staff');
    }

    // Trigger UI update
    notifyListeners();
    debugPrint('🔄 [$timestamp] Calendar UI updated for holiday/closure change');
  }

  /// Testing method to set working hours directly
  void setWorkingHoursForTesting(WorkingHoursSettings workingHours) {
    _workingHoursSettings = workingHours;
  }

  // Public getter for staff working hours settings
  StaffWorkingHoursSettings? getStaffWorkingHoursSettings(String staffId) {
    return _staffWorkingHoursCache[staffId];
  }

  // Public getter for staff working hours cache (for testing)
  Map<String, StaffWorkingHoursSettings> get staffWorkingHoursCache => Map.unmodifiable(_staffWorkingHoursCache);
}

