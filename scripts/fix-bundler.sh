#!/bin/bash
# Fix Bundler version compatibility issues
# This script resolves Ruby/Bundler version conflicts

set -e

echo "🔧 Fixing Bundler Version Compatibility"
echo "======================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    print_error "Not in Flutter project root. Please run from flutter-partykids directory."
    exit 1
fi

# Navigate to iOS directory
cd ios

print_info "Current directory: $(pwd)"

# Check current Ruby version
RUBY_VERSION=$(ruby -v)
print_info "Ruby version: $RUBY_VERSION"

# Check current Bundler version
if command -v bundle &> /dev/null; then
    BUNDLER_VERSION=$(bundle -v)
    print_info "Current Bundler version: $BUNDLER_VERSION"
else
    print_warning "Bundler not found, installing..."
    gem install bundler
fi

# Check Gemfile.lock for old Bundler version
if [ -f "Gemfile.lock" ]; then
    LOCK_BUNDLER_VERSION=$(grep -A1 "BUNDLED WITH" Gemfile.lock | tail -n1 | tr -d ' ')
    print_info "Gemfile.lock Bundler version: $LOCK_BUNDLER_VERSION"
    
    # Check if it's an old version (1.x)
    if [[ $LOCK_BUNDLER_VERSION == 1.* ]]; then
        print_warning "Old Bundler version detected in Gemfile.lock"
        print_info "Updating Bundler version..."
        
        # Update Bundler version in Gemfile.lock
        bundle update --bundler
        print_success "Bundler version updated"
    else
        print_success "Bundler version is compatible"
    fi
else
    print_warning "No Gemfile.lock found"
fi

# Install/update Bundler to latest version
print_info "Ensuring latest Bundler is installed..."
gem install bundler --no-document

# Install dependencies
print_info "Installing Fastlane dependencies..."
bundle install

# Verify installation
print_info "Verifying Fastlane installation..."
if bundle exec fastlane --version > /dev/null 2>&1; then
    FASTLANE_VERSION=$(bundle exec fastlane --version)
    print_success "Fastlane installed successfully: $FASTLANE_VERSION"
else
    print_error "Fastlane installation failed"
    exit 1
fi

# Test Fastlane lanes
print_info "Testing Fastlane configuration..."
if bundle exec fastlane lanes > /dev/null 2>&1; then
    print_success "Fastlane configuration is valid"
    
    # Check if deploy_main lane exists
    if bundle exec fastlane lanes | grep -q "deploy_main"; then
        print_success "deploy_main lane is available"
    else
        print_warning "deploy_main lane not found"
    fi
else
    print_error "Fastlane configuration has issues"
    exit 1
fi

cd ..

print_success "Bundler fix completed successfully!"
echo ""
print_info "Next steps:"
echo "1. Try running the deployment script: ./scripts/deploy-ios.sh"
echo "2. Or run Fastlane directly: cd ios && bundle exec fastlane deploy_main"
echo ""
print_info "If you still encounter issues, try:"
echo "- Delete ios/Gemfile.lock and run this script again"
echo "- Update Ruby to a newer version if using an old version"
