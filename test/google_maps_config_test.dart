import 'package:flutter_test/flutter_test.dart';
import 'package:partykidsapp/config/environment.dart';

void main() {
  group('Google Maps Configuration Tests', () {
    test('should have valid Google Maps API key configured', () {
      // Test that the API key is not the placeholder value
      final apiKey = EnvironmentConfig.googleMapsApiKey;
      
      expect(apiKey, isNotNull);
      expect(apiKey, isNotEmpty);
      expect(apiKey, isNot('YOUR_GOOGLE_MAPS_API_KEY'));
      expect(apiKey.startsWith('AIza'), isTrue, 
        reason: 'Google Maps API key should start with "AIza"');
      
      print('✅ Google Maps API Key: ${apiKey.substring(0, 10)}...');
    });

    test('should have consistent API key across environments', () {
      // For now, all environments use the same key
      // In production, you might want different keys per environment
      
      final devKey = EnvironmentConfig.googleMapsApiKey;
      
      // Switch to staging and test
      // Note: This is a simplified test - in real scenarios you'd test environment switching
      expect(devKey, isNotEmpty);
      expect(devKey.length, greaterThan(30), 
        reason: 'Google Maps API key should be at least 30 characters long');
      
      print('✅ API Key length: ${devKey.length} characters');
    });

    test('should validate API key format', () {
      final apiKey = EnvironmentConfig.googleMapsApiKey;
      
      // Google Maps API keys typically:
      // - Start with "AIza"
      // - Are 39 characters long
      // - Contain only alphanumeric characters and hyphens/underscores
      
      expect(apiKey.startsWith('AIza'), isTrue);
      expect(apiKey.length, equals(39));
      expect(RegExp(r'^[A-Za-z0-9_-]+$').hasMatch(apiKey), isTrue,
        reason: 'API key should only contain alphanumeric characters, hyphens, and underscores');
      
      print('✅ API Key format validation passed');
    });
  });
}
