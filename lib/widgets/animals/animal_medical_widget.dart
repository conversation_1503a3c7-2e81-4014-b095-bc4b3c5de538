import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/pet.dart';
import '../../config/theme/app_theme.dart';

class AnimalMedicalWidget extends StatelessWidget {
  final Pet pet;

  const AnimalMedicalWidget({
    super.key,
    required this.pet,
  });

  Widget _buildMedicalCard(String title, String value, IconData icon, Color color, {bool isAlert = false}) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isAlert ? color.withValues(alpha: 0.1) : AppColors.white,
        borderRadius: BorderRadius.circular(12),
        border: isAlert ? Border.all(color: color, width: 1) : null,
        boxShadow: !isAlert ? [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ] : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    color: color,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: isAlert ? color : AppColors.forestGreen,
              fontSize: 14,
              fontWeight: isAlert ? FontWeight.w600 : FontWeight.w500,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildVaccinationStatus() {
    // Mock vaccination data
    final vaccinations = [
      {'name': 'Antirabică', 'date': DateTime.now().subtract(const Duration(days: 180)), 'valid': true},
      {'name': 'Polivalentă', 'date': DateTime.now().subtract(const Duration(days: 90)), 'valid': true},
      {'name': 'Bordetella', 'date': DateTime.now().subtract(const Duration(days: 400)), 'valid': false},
    ];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.vaccines, color: Colors.green, size: 20),
              SizedBox(width: 8),
              Text(
                'Status vaccinări',
                style: TextStyle(
                  color: Colors.green,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...vaccinations.map((vaccination) {
            final isValid = vaccination['valid'] as bool;
            final date = vaccination['date'] as DateTime;
            final name = vaccination['name'] as String;

            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Icon(
                    isValid ? Icons.check_circle : Icons.warning,
                    color: isValid ? Colors.green : Colors.orange,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      name,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Text(
                    DateFormat('dd.MM.yyyy').format(date),
                    style: TextStyle(
                      fontSize: 12,
                      color: isValid ? AppColors.taupe : Colors.orange,
                    ),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Informații medicale',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.forestGreen,
          ),
        ),
        const SizedBox(height: 16),

        // Vaccination status
        _buildVaccinationStatus(),

        const SizedBox(height: 12),

        // Medical info grid
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 1,
          childAspectRatio: 4.5,
          mainAxisSpacing: 12,
          children: [
            _buildMedicalCard(
              'Alergii cunoscute',
              'Niciuna cunoscută',
              Icons.warning_amber,
              Colors.orange,
            ),
            _buildMedicalCard(
              'Medicație curentă',
              'Niciuna',
              Icons.medication,
              Colors.blue,
            ),
          ],
        ),

        const SizedBox(height: 12),

        // Health notes
        _buildMedicalCard(
          'Observații medicale',
          'Animal sănătos, fără probleme de sănătate cunoscute. Recomandat control anual.',
          Icons.health_and_safety,
          Colors.green,
        ),
      ],
    );
  }
}
