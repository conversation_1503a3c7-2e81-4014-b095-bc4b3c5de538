#!/bin/bash

echo "XCODE CLOUD PRE-XCODEBUILD SCRIPT STARTING"
echo "Current directory: $(pwd)"

# Ensure environment is set (should be set by post-clone script)
if [ -z "$FLUTTER_ENV" ]; then
    echo "⚠️ FLUTTER_ENV not set, detecting from branch..."
    BRANCH_NAME=$(git branch --show-current 2>/dev/null || git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
    case "$BRANCH_NAME" in
        "main"|"master")
            export FLUTTER_ENV="production"
            ;;
        "staging"|"stage"|"develop")
            export FLUTTER_ENV="staging"
            ;;
        *)
            export FLUTTER_ENV="development"
            ;;
    esac
fi

echo "🌍 Building with environment: $FLUTTER_ENV"

# Ensure Flutter dependencies are up to date
echo "Running flutter pub get to ensure dependencies are current..."
flutter pub get

# Change to iOS directory
cd ios

# Check if Podfile.lock exists and if Pods directory is in sync
if [ ! -f "Podfile.lock" ]; then
    echo "Podfile.lock not found, running pod install..."
    pod install
elif [ ! -d "Pods" ]; then
    echo "Pods directory not found, running pod install..."
    pod install
else
    echo "Checking if Pods are in sync with Podfile.lock..."
    # Compare Podfile.lock with Manifest.lock if it exists
    if [ -f "Pods/Manifest.lock" ]; then
        if ! cmp -s "Podfile.lock" "Pods/Manifest.lock"; then
            echo "Podfile.lock and Manifest.lock are out of sync, running pod install..."
            pod install
        else
            echo "Pods are already in sync"
        fi
    else
        echo "Manifest.lock not found, running pod install..."
        pod install
    fi
fi

echo "XCODE CLOUD PRE-XCODEBUILD SCRIPT COMPLETED"
