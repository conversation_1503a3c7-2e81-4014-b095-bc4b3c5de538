import 'package:flutter_test/flutter_test.dart';
import 'package:partykidsapp/services/staff_service.dart';
import 'package:partykidsapp/models/user_role.dart';

void main() {
  group('Nickname Display Debug Tests', () {
    test('should parse staff response with nickname correctly', () {
      // Simulate the API response that contains nickname field
      final apiResponse = {
        'staffId': 'staff-123',
        'userName': '<PERSON>',
        'nickname': 'msak<PERSON>',
        'userPhone': '+40123456789',
        'userEmail': '<EMAIL>',
        'role': 'GROOMER',
        'clientDataPermission': 'LIMITED_ACCESS',
        'isActive': true,
        'hiredAt': '2024-01-01T00:00:00Z',
      };

      print('🔍 Testing API response parsing:');
      print('Raw API response: $apiResponse');

      // Parse the response
      final staff = StaffResponse.fromJson(apiResponse);

      print('✅ Parsed StaffResponse:');
      print('   - ID: ${staff.id}');
      print('   - Name: ${staff.name}');
      print('   - Nickname: ${staff.nickname}');
      print('   - Display Name: ${staff.displayName}');
      print('   - Has Nickname: ${staff.nickname?.isNotEmpty == true}');

      // Verify the parsing
      expect(staff.id, equals('staff-123')); // userId should be preserved
      expect(staff.name, equals('Maria Popescu'));
      expect(staff.nickname, equals('msakK'));
      expect(staff.displayName, equals('msakK')); // Should use nickname
      expect(staff.nickname?.isNotEmpty == true, isTrue);
    });

    test('should handle staff without nickname correctly', () {
      // Simulate API response without nickname
      final apiResponse = {
        'staffId': 'staff-456',
        'userName': 'Ion Georgescu',
        'userPhone': '+40123456789',
        'userEmail': '<EMAIL>',
        'role': 'GROOMER',
        'clientDataPermission': 'LIMITED_ACCESS',
        'isActive': true,
        'hiredAt': '2024-01-01T00:00:00Z',
      };

      print('🔍 Testing API response without nickname:');
      print('Raw API response: $apiResponse');

      // Parse the response
      final staff = StaffResponse.fromJson(apiResponse);

      print('✅ Parsed StaffResponse:');
      print('   - ID: ${staff.id}');
      print('   - Name: ${staff.name}');
      print('   - Nickname: ${staff.nickname}');
      print('   - Display Name: ${staff.displayName}');
      print('   - Has Nickname: ${staff.nickname?.isNotEmpty == true}');

      // Verify the parsing
      expect(staff.id, equals('staff-456')); // userId should be preserved
      expect(staff.name, equals('Ion Georgescu'));
      expect(staff.nickname, isNull);
      expect(staff.displayName, equals('Ion Georgescu')); // Should use full name
      expect(staff.nickname?.isNotEmpty == true, isFalse);
    });

    test('should handle staff with empty nickname correctly', () {
      // Simulate API response with empty nickname
      final apiResponse = {
        'staffId': 'staff-789',
        'userName': 'Ana Ionescu',
        'nickname': '',
        'userPhone': '+40123456789',
        'userEmail': '<EMAIL>',
        'role': 'GROOMER',
        'clientDataPermission': 'LIMITED_ACCESS',
        'isActive': true,
        'hiredAt': '2024-01-01T00:00:00Z',
      };

      print('🔍 Testing API response with empty nickname:');
      print('Raw API response: $apiResponse');

      // Parse the response
      final staff = StaffResponse.fromJson(apiResponse);

      print('✅ Parsed StaffResponse:');
      print('   - ID: ${staff.id}');
      print('   - Name: ${staff.name}');
      print('   - Nickname: "${staff.nickname}"');
      print('   - Display Name: ${staff.displayName}');
      print('   - Has Nickname: ${staff.nickname?.isNotEmpty == true}');

      // Verify the parsing
      expect(staff.id, equals('staff-789'));
      expect(staff.name, equals('Ana Ionescu'));
      expect(staff.nickname, equals(''));
      expect(staff.displayName, equals('Ana Ionescu')); // Should use full name when nickname is empty
      expect(staff.nickname?.isNotEmpty == true, isFalse);
    });

    test('should parse pending invitation with nickname correctly', () {
      // Simulate pending invitation API response with nickname
      final apiResponse = {
        'invitationId': 'inv-123',
        'phoneNumber': '+40123456789',
        'nickname': 'Ana',
        'groomerRole': 'GROOMER',
        'clientDataPermission': 'LIMITED_ACCESS',
        'status': 'PENDING',
        'invitedBy': 'admin-1',
        'invitedAt': '2024-01-01T00:00:00Z',
        'expiresAt': '2024-01-08T00:00:00Z',
        'isExpired': false,
      };

      print('🔍 Testing pending invitation with nickname:');
      print('Raw API response: $apiResponse');

      // Parse the response
      final invitation = PendingStaffInvitation.fromJson(apiResponse);

      print('✅ Parsed PendingStaffInvitation:');
      print('   - ID: ${invitation.invitationId}');
      print('   - Phone: ${invitation.phoneNumber}');
      print('   - Nickname: ${invitation.nickname}');
      print('   - Display Name: ${invitation.displayName}');
      print('   - Has Nickname: ${invitation.nickname?.isNotEmpty == true}');

      // Verify the parsing
      expect(invitation.invitationId, equals('inv-123'));
      expect(invitation.phoneNumber, equals('+40123456789'));
      expect(invitation.nickname, equals('Ana'));
      expect(invitation.displayName, equals('Ana')); // Should use nickname
      expect(invitation.nickname?.isNotEmpty == true, isTrue);
    });

    test('should simulate team management screen display logic', () {
      // Create a staff member with nickname
      final staffWithNickname = StaffResponse(
        id: 'staff-1',
        name: 'Maria Popescu',
        nickname: 'msakK',
        phone: '+40123456789',
        email: '<EMAIL>',
        groomerRole: GroomerRole.groomer,
        clientDataPermission: ClientDataPermission.limitedAccess,
        isActive: true,
        joinedAt: DateTime.parse('2024-01-01T00:00:00Z'),
      );

      print('🎨 Simulating team management screen display logic:');
      print('   - Staff Name: ${staffWithNickname.name}');
      print('   - Staff Nickname: ${staffWithNickname.nickname}');
      print('   - Display Name: ${staffWithNickname.displayName}');
      print('   - Has Nickname: ${staffWithNickname.nickname?.isNotEmpty == true}');
      print('   - Nickname != Name: ${staffWithNickname.nickname != staffWithNickname.name}');
      print('   - Should show full name: ${staffWithNickname.nickname != null && staffWithNickname.nickname!.isNotEmpty && staffWithNickname.nickname != staffWithNickname.name}');

      // Test the display logic
      expect(staffWithNickname.displayName, equals('msakK'));
      expect(staffWithNickname.nickname != null && staffWithNickname.nickname!.isNotEmpty && staffWithNickname.nickname != staffWithNickname.name, isTrue);

      // Create a staff member without nickname
      final staffWithoutNickname = StaffResponse(
        id: 'staff-2',
        name: 'Ion Georgescu',
        phone: '+40123456789',
        email: '<EMAIL>',
        groomerRole: GroomerRole.groomer,
        clientDataPermission: ClientDataPermission.limitedAccess,
        isActive: true,
        joinedAt: DateTime.parse('2024-01-01T00:00:00Z'),
      );

      print('   - Staff 2 Name: ${staffWithoutNickname.name}');
      print('   - Staff 2 Nickname: ${staffWithoutNickname.nickname}');
      print('   - Staff 2 Display Name: ${staffWithoutNickname.displayName}');
      print('   - Staff 2 Has Nickname: ${staffWithoutNickname.nickname?.isNotEmpty == true}');

      // Test the display logic
      expect(staffWithoutNickname.displayName, equals('Ion Georgescu'));
      expect(staffWithoutNickname.nickname?.isNotEmpty == true, isFalse);
    });

    test('should test StaffListResponse parsing with nickname data', () {
      // Simulate a complete staff list response
      final apiResponse = {
        'activeStaff': [
          {
            'staffId': 'staff-1',
            'userName': 'Maria Popescu',
            'nickname': 'msakK',
            'userPhone': '+40123456789',
            'userEmail': '<EMAIL>',
            'role': 'GROOMER',
            'clientDataPermission': 'LIMITED_ACCESS',
            'isActive': true,
            'hiredAt': '2024-01-01T00:00:00Z',
          },
          {
            'staffId': 'staff-2',
            'userName': 'Ion Georgescu',
            'userPhone': '+40123456788',
            'userEmail': '<EMAIL>',
            'role': 'GROOMER',
            'clientDataPermission': 'LIMITED_ACCESS',
            'isActive': true,
            'hiredAt': '2024-01-01T00:00:00Z',
          }
        ],
        'pendingStaff': [
          {
            'invitationId': 'inv-1',
            'phoneNumber': '+40123456787',
            'nickname': 'Ana',
            'groomerRole': 'GROOMER',
            'clientDataPermission': 'LIMITED_ACCESS',
            'status': 'PENDING',
            'invitedBy': 'admin-1',
            'invitedAt': '2024-01-01T00:00:00Z',
            'expiresAt': '2024-01-08T00:00:00Z',
            'isExpired': false,
          }
        ],
        'totalActiveCount': 2,
        'totalPendingCount': 1,
        'activeCount': 2,
        'inactiveCount': 0,
      };

      print('🔍 Testing complete staff list response:');
      print('Raw API response: $apiResponse');

      // Parse the response
      final staffList = StaffListResponse.fromJson(apiResponse);

      print('✅ Parsed StaffListResponse:');
      print('   - Active Staff Count: ${staffList.activeStaff.length}');
      print('   - Pending Staff Count: ${staffList.pendingStaff.length}');

      for (int i = 0; i < staffList.activeStaff.length; i++) {
        final staff = staffList.activeStaff[i];
        print('   - Active Staff $i: ${staff.name} (nickname: ${staff.nickname}, display: ${staff.displayName})');
      }

      for (int i = 0; i < staffList.pendingStaff.length; i++) {
        final pending = staffList.pendingStaff[i];
        print('   - Pending Staff $i: ${pending.phoneNumber} (nickname: ${pending.nickname}, display: ${pending.displayName})');
      }

      // Verify the parsing
      expect(staffList.activeStaff.length, equals(2));
      expect(staffList.pendingStaff.length, equals(1));
      
      // Check first staff member with nickname
      expect(staffList.activeStaff[0].name, equals('Maria Popescu'));
      expect(staffList.activeStaff[0].nickname, equals('msakK'));
      expect(staffList.activeStaff[0].displayName, equals('msakK'));
      
      // Check second staff member without nickname
      expect(staffList.activeStaff[1].name, equals('Ion Georgescu'));
      expect(staffList.activeStaff[1].nickname, isNull);
      expect(staffList.activeStaff[1].displayName, equals('Ion Georgescu'));
      
      // Check pending invitation with nickname
      expect(staffList.pendingStaff[0].phoneNumber, equals('+40123456787'));
      expect(staffList.pendingStaff[0].nickname, equals('Ana'));
      expect(staffList.pendingStaff[0].displayName, equals('Ana'));
    });
  });
}
