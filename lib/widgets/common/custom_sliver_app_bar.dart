import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../config/theme/app_theme.dart';

/// Reusable SliverAppBar with consistent styling for detail screens
class CustomSliverAppBar extends StatelessWidget {
  final Widget headerContent;
  final double expandedHeight;
  final List<Widget>? actions;
  final VoidCallback? onBackPressed;
  final Color backgroundColor;
  final bool pinned;
  final bool floating;
  final bool snap;

  const CustomSliverAppBar({
    super.key,
    required this.headerContent,
    this.expandedHeight = 200,
    this.actions,
    this.onBackPressed,
    this.backgroundColor = const Color(0xFF2E7D32),
    this.pinned = true,
    this.floating = false,
    this.snap = false,
  });

  @override
  Widget build(BuildContext context) {
    // Set system UI overlay style for green status bar
    SystemChrome.setSystemUIOverlayStyle(
      SystemUiOverlayStyle(
        statusBarColor: backgroundColor,
        statusBarIconBrightness: Brightness.light,
        statusBarBrightness: Brightness.dark,
        systemNavigationBarColor: const Color(0xFFFFEED3),
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );

    return SliverAppBar(
      expandedHeight: expandedHeight,
      pinned: pinned,
      floating: floating,
      snap: snap,
      backgroundColor: backgroundColor,
      surfaceTintColor: backgroundColor,
      foregroundColor: backgroundColor,
      flexibleSpace: FlexibleSpaceBar(
        background: Padding(
          padding: const EdgeInsets.only(top: 56), // Add top padding for app bar
          child: headerContent,
        ),
        collapseMode: CollapseMode.pin,
      ),
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: onBackPressed ?? () => Navigator.of(context).pop(),
      ),
      actions: actions,
    );
  }
}

/// Reusable detail screen scaffold with SliverAppBar pattern
class DetailScreenScaffold extends StatelessWidget {
  final Widget headerContent;
  final Widget body;
  final Widget? floatingActionButton;
  final double expandedHeight;
  final List<Widget>? actions;
  final VoidCallback? onBackPressed;
  final Color backgroundColor;

  const DetailScreenScaffold({
    super.key,
    required this.headerContent,
    required this.body,
    this.floatingActionButton,
    this.expandedHeight = 200,
    this.actions,
    this.onBackPressed,
    this.backgroundColor = AppColors.forestGreen,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor,
      floatingActionButton: floatingActionButton,
      body: SafeArea(
        child: NestedScrollView(
          headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
            return [
              CustomSliverAppBar(
                headerContent: headerContent,
                expandedHeight: expandedHeight,
                actions: actions,
                onBackPressed: onBackPressed,
                backgroundColor: backgroundColor,
              ),
            ];
          },
          body: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: body,
          ),
        ),
      ),
    );
  }
}

/// Reusable quick action button for detail screens
class QuickActionButton extends StatelessWidget {
  final IconData icon;
  final String label;
  final VoidCallback onPressed;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const QuickActionButton({
    super.key,
    required this.icon,
    required this.label,
    required this.onPressed,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 4),
        child: ElevatedButton.icon(
          onPressed: onPressed,
          icon: Icon(icon, size: 20),
          label: Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: backgroundColor ?? Theme.of(context).colorScheme.primary,
            foregroundColor: foregroundColor ?? Theme.of(context).colorScheme.onPrimary,
            padding: const EdgeInsets.symmetric(vertical: 12),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            elevation: 2,
          ),
        ),
      ),
    );
  }
}

/// Reusable quick actions row for detail screens
class QuickActionsRow extends StatelessWidget {
  final List<QuickActionButton> actions;
  final EdgeInsets? padding;

  const QuickActionsRow({
    super.key,
    required this.actions,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.all(16),
      child: Row(
        children: actions,
      ),
    );
  }
}
