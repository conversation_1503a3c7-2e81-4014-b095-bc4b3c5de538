import 'package:flutter_test/flutter_test.dart';

void main() {
  group('SalonProvider Business Logic Tests', () {
    
    group('Salon Data Validation', () {
      test('validateSalonData_ValidSalon_ReturnsTrue', () {
        // Arrange
        final validSalon = {
          'name': 'Partykids Grooming Salon',
          'phone': '+***********',
          'address': 'Strada Florilor 123, București',
          'email': '<EMAIL>',
        };

        // Act
        final isValid = _validateSalonData(validSalon);

        // Assert
        expect(isValid, isTrue);
      });

      test('validateSalonData_InvalidName_ReturnsFalse', () {
        // Arrange
        final invalidSalons = [
          {'name': '', 'phone': '+***********'}, // Empty name
          {'name': 'A', 'phone': '+***********'}, // Too short
          {'name': 'A' * 201, 'phone': '+***********'}, // Too long
        ];

        // Act & Assert
        for (final salon in invalidSalons) {
          final isValid = _validateSalonData(salon);
          expect(isValid, isFalse, reason: 'Salon with name "${salon['name']}" should be invalid');
        }
      });

      test('validateSalonData_InvalidPhone_ReturnsFalse', () {
        // Arrange
        final invalidSalons = [
          {'name': 'Test Salon', 'phone': '0123456789'}, // Missing country code
          {'name': 'Test Salon', 'phone': '+40123'}, // Too short
          {'name': 'Test Salon', 'phone': 'invalid'}, // Not a number
        ];

        // Act & Assert
        for (final salon in invalidSalons) {
          final isValid = _validateSalonData(salon);
          expect(isValid, isFalse, reason: 'Salon with phone "${salon['phone']}" should be invalid');
        }
      });

      test('validateSalonData_OptionalFields_HandlesCorrectly', () {
        // Arrange
        final salonWithOptionalFields = {
          'name': 'Test Salon',
          'phone': '+***********',
          // email is optional
          // address is optional
          // description is optional
        };

        // Act
        final isValid = _validateSalonData(salonWithOptionalFields);

        // Assert
        expect(isValid, isTrue);
      });
    });

    group('Salon Name Processing', () {
      test('normalizeSalonName_VariousFormats_ReturnsNormalizedFormat', () {
        // Arrange & Act & Assert
        expect(_normalizeSalonName('partykids grooming'), equals('Partykids Grooming'));
        expect(_normalizeSalonName('PARTYKIDS GROOMING'), equals('Partykids Grooming'));
        expect(_normalizeSalonName('  partykids   grooming  '), equals('Partykids Grooming'));
        expect(_normalizeSalonName('salon de înfrumusețare'), equals('Salon De Înfrumusețare'));
      });

      test('validateSalonName_ValidNames_ReturnsTrue', () {
        // Arrange
        const validNames = [
          'Partykids Grooming',
          'Salon de Înfrumusețare Partykids',
          'Pet Beauty Salon',
          'Grooming & Spa Center',
          'Salon Frumusețe Animale',
        ];

        // Act & Assert
        for (final name in validNames) {
          final isValid = _validateSalonName(name);
          expect(isValid, isTrue, reason: 'Name "$name" should be valid');
        }
      });

      test('validateSalonName_InvalidNames_ReturnsFalse', () {
        // Arrange
        final invalidNames = [
          '', // Empty
          'A', // Too short
          'A' * 201, // Too long
          '123 Salon', // Starts with number
          'Salon@Test', // Contains invalid characters
        ];

        // Act & Assert
        for (final name in invalidNames) {
          final isValid = _validateSalonName(name);
          expect(isValid, isFalse, reason: 'Name "$name" should be invalid');
        }
      });
    });

    group('Business Hours Validation', () {
      test('validateBusinessHours_ValidHours_ReturnsTrue', () {
        // Arrange
        final validHours = {
          'monday': {'open': '08:00', 'close': '18:00', 'isOpen': true},
          'tuesday': {'open': '08:00', 'close': '18:00', 'isOpen': true},
          'wednesday': {'open': '08:00', 'close': '18:00', 'isOpen': true},
          'thursday': {'open': '08:00', 'close': '18:00', 'isOpen': true},
          'friday': {'open': '08:00', 'close': '18:00', 'isOpen': true},
          'saturday': {'open': '09:00', 'close': '16:00', 'isOpen': true},
          'sunday': {'isOpen': false},
        };

        // Act
        final isValid = _validateBusinessHours(validHours);

        // Assert
        expect(isValid, isTrue);
      });

      test('validateBusinessHours_InvalidTimeFormat_ReturnsFalse', () {
        // Arrange
        final invalidHours = {
          'monday': {'open': '25:00', 'close': '18:00', 'isOpen': true}, // Invalid hour
          'tuesday': {'open': '08:00', 'close': '18:60', 'isOpen': true}, // Invalid minute
        };

        // Act
        final isValid = _validateBusinessHours(invalidHours);

        // Assert
        expect(isValid, isFalse);
      });

      test('validateBusinessHours_CloseBeforeOpen_ReturnsFalse', () {
        // Arrange
        final invalidHours = {
          'monday': {'open': '18:00', 'close': '08:00', 'isOpen': true}, // Close before open
        };

        // Act
        final isValid = _validateBusinessHours(invalidHours);

        // Assert
        expect(isValid, isFalse);
      });

      test('isWithinBusinessHours_OpenDay_ReturnsCorrectly', () {
        // Arrange
        final businessHours = {
          'monday': {'open': '08:00', 'close': '18:00', 'isOpen': true},
        };
        final mondayMorning = DateTime(2024, 6, 17, 10, 0); // Monday 10:00 AM
        final mondayEvening = DateTime(2024, 6, 17, 20, 0); // Monday 8:00 PM

        // Act
        final morningResult = _isWithinBusinessHours(mondayMorning, businessHours);
        final eveningResult = _isWithinBusinessHours(mondayEvening, businessHours);

        // Assert
        expect(morningResult, isTrue);
        expect(eveningResult, isFalse);
      });

      test('isWithinBusinessHours_ClosedDay_ReturnsFalse', () {
        // Arrange
        final businessHours = {
          'sunday': {'isOpen': false},
        };
        final sundayMorning = DateTime(2024, 6, 23, 10, 0); // Sunday 10:00 AM

        // Act
        final result = _isWithinBusinessHours(sundayMorning, businessHours);

        // Assert
        expect(result, isFalse);
      });
    });

    group('Salon Settings Management', () {
      test('validateNotificationSettings_ValidSettings_ReturnsTrue', () {
        // Arrange
        final validSettings = {
          'emailNotifications': true,
          'smsNotifications': true,
          'pushNotifications': true,
          'reminderTime': 24, // 24 hours before
          'confirmationRequired': true,
        };

        // Act
        final isValid = _validateNotificationSettings(validSettings);

        // Assert
        expect(isValid, isTrue);
      });

      test('validateNotificationSettings_InvalidReminderTime_ReturnsFalse', () {
        // Arrange
        final invalidSettings = {
          'emailNotifications': true,
          'reminderTime': -1, // Invalid negative time
        };

        // Act
        final isValid = _validateNotificationSettings(invalidSettings);

        // Assert
        expect(isValid, isFalse);
      });

      test('validateSalonServices_ValidServices_ReturnsTrue', () {
        // Arrange
        final validServices = [
          {
            'name': 'Tuns complet',
            'duration': 60,
            'price': 150.0,
            'category': 'GROOMING',
            'isActive': true,
          },
          {
            'name': 'Spălare',
            'duration': 30,
            'price': 50.0,
            'category': 'WASHING',
            'isActive': true,
          },
        ];

        // Act
        final isValid = _validateSalonServices(validServices);

        // Assert
        expect(isValid, isTrue);
      });

      test('validateSalonServices_InvalidDuration_ReturnsFalse', () {
        // Arrange
        final invalidServices = [
          {
            'name': 'Test Service',
            'duration': 0, // Invalid duration
            'price': 100.0,
            'category': 'GROOMING',
          },
        ];

        // Act
        final isValid = _validateSalonServices(invalidServices);

        // Assert
        expect(isValid, isFalse);
      });

      test('validateSalonServices_InvalidPrice_ReturnsFalse', () {
        // Arrange
        final invalidServices = [
          {
            'name': 'Test Service',
            'duration': 60,
            'price': -50.0, // Invalid negative price
            'category': 'GROOMING',
          },
        ];

        // Act
        final isValid = _validateSalonServices(invalidServices);

        // Assert
        expect(isValid, isFalse);
      });
    });

    group('Salon State Management', () {
      test('updateSalonData_ValidData_UpdatesCorrectly', () {
        // Arrange
        final originalSalon = {
          'id': 'salon-123',
          'name': 'Original Name',
          'phone': '+***********',
        };
        final updates = {
          'name': 'Updated Name',
          'email': '<EMAIL>',
        };

        // Act
        final updatedSalon = _updateSalonData(originalSalon, updates);

        // Assert
        expect(updatedSalon['id'], equals('salon-123')); // ID unchanged
        expect(updatedSalon['name'], equals('Updated Name')); // Name updated
        expect(updatedSalon['phone'], equals('+***********')); // Phone unchanged
        expect(updatedSalon['email'], equals('<EMAIL>')); // Email added
      });

      test('mergeSalonSettings_ValidSettings_MergesCorrectly', () {
        // Arrange
        final existingSettings = {
          'notifications': {'email': true, 'sms': false},
          'businessHours': {'monday': {'open': '08:00', 'close': '18:00'}},
        };
        final newSettings = {
          'notifications': {'sms': true, 'push': true},
          'theme': {'primaryColor': '#4CAF50'},
        };

        // Act
        final mergedSettings = _mergeSalonSettings(existingSettings, newSettings);

        // Assert
        expect(mergedSettings['notifications']['email'], isTrue); // Preserved
        expect(mergedSettings['notifications']['sms'], isTrue); // Updated
        expect(mergedSettings['notifications']['push'], isTrue); // Added
        expect(mergedSettings['businessHours'], isNotNull); // Preserved
        expect(mergedSettings['theme'], isNotNull); // Added
      });
    });

    group('Error Handling', () {
      test('handleSalonError_DuplicateName_ReturnsRomanianMessage', () {
        // Arrange
        const error = 'Salon name already exists';

        // Act
        final result = _handleSalonError(error);

        // Assert
        expect(result, contains('Nume'));
        expect(result, contains('există'));
      });

      test('handleSalonError_InvalidData_ReturnsRomanianMessage', () {
        // Arrange
        const error = 'Invalid salon data';

        // Act
        final result = _handleSalonError(error);

        // Assert
        expect(result, contains('Date'));
        expect(result, contains('invalide'));
      });

      test('handleSalonError_NetworkError_ReturnsRomanianMessage', () {
        // Arrange
        const error = 'Network connection failed';

        // Act
        final result = _handleSalonError(error);

        // Assert
        expect(result, contains('rețea'));
      });
    });

    group('Romanian Localization', () {
      test('formatSalonInfo_RomanianText_PreservesCharacters', () {
        // Arrange
        const salonName = 'Salon de Înfrumusețare Partykids';
        const salonAddress = 'Strada Ștefan cel Mare 123, București';
        const salonDescription = 'Salon modern cu servicii complete de îngrijire pentru animale de companie';

        // Act
        final formattedName = _formatSalonInfo(salonName);
        final formattedAddress = _formatSalonInfo(salonAddress);
        final formattedDescription = _formatSalonInfo(salonDescription);

        // Assert
        expect(formattedName, contains('Î'));
        expect(formattedName, contains('ț'));
        expect(formattedAddress, contains('Ș'));
        expect(formattedDescription, contains('î'));
        expect(formattedDescription, contains('de')); // Changed to a word that exists
      });

      test('translateServiceCategories_RomanianCategories_ReturnsCorrectTranslations', () {
        // Arrange
        const categories = ['GROOMING', 'WASHING', 'NAIL_CARE', 'EAR_CLEANING'];

        // Act & Assert
        expect(_translateServiceCategory('GROOMING'), equals('Îngrijire'));
        expect(_translateServiceCategory('WASHING'), equals('Spălare'));
        expect(_translateServiceCategory('NAIL_CARE'), equals('Tăiere unghii'));
        expect(_translateServiceCategory('EAR_CLEANING'), equals('Curățare urechi'));
      });
    });

    group('Edge Cases', () {
      test('handleEmptyData_EmptyInputs_HandlesGracefully', () {
        // Act & Assert
        expect(() => _validateSalonName(''), returnsNormally);
        expect(() => _validateSalonData({}), returnsNormally);
        expect(() => _validateBusinessHours({}), returnsNormally);
      });

      test('handleLargeData_ManyServices_ProcessesEfficiently', () {
        // Arrange
        final manyServices = List.generate(100, (index) => {
          'name': 'Service $index',
          'duration': 30 + (index % 120), // 30-150 minutes
          'price': 50.0 + (index * 10), // Varying prices
          'category': 'GROOMING',
          'isActive': index % 2 == 0, // Alternating active/inactive
        });

        // Act
        final stopwatch = Stopwatch()..start();
        final isValid = _validateSalonServices(manyServices);
        stopwatch.stop();

        // Assert
        expect(isValid, isTrue);
        expect(stopwatch.elapsedMilliseconds, lessThan(100)); // Should be fast
      });

      test('handleSpecialCharacters_RomanianSalonData_HandlesCorrectly', () {
        // Arrange
        final romanianSalon = {
          'name': 'Salon Frumusețe Animale Ștefan',
          'phone': '+***********', // Add required phone field
          'address': 'Strada Mănuțiu 123, Cluj-Napoca',
          'description': 'Servicii complete de îngrijire și înfrumusețare',
        };

        // Act
        final isValid = _validateSalonData(romanianSalon);

        // Assert
        expect(isValid, isTrue);
      });
    });
  });
}

// Helper functions for testing salon provider business logic
bool _validateSalonData(Map<String, dynamic> salon) {
  final name = salon['name'] as String? ?? '';
  final phone = salon['phone'] as String? ?? '';
  
  if (!_validateSalonName(name)) return false;
  if (!_validateRomanianPhone(phone)) return false;
  
  return true;
}

bool _validateSalonName(String name) {
  if (name.isEmpty || name.length < 2 || name.length > 200) return false;
  
  // Should start with letter and contain only letters, numbers, spaces, and common punctuation
  final nameRegex = RegExp(r'^[A-ZĂÂÎȘȚa-zăâîșț][A-ZĂÂÎȘȚa-zăâîșț0-9\s&\-\.]*$');
  return nameRegex.hasMatch(name);
}

String _normalizeSalonName(String name) {
  return name.trim()
      .split(RegExp(r'\s+'))
      .map((word) => word.isEmpty ? '' : word[0].toUpperCase() + word.substring(1).toLowerCase())
      .join(' ');
}

bool _validateRomanianPhone(String phone) {
  final romanianPhoneRegex = RegExp(r'^\+40[0-9]{9}$');
  return romanianPhoneRegex.hasMatch(phone);
}

bool _validateBusinessHours(Map<String, dynamic> hours) {
  for (final entry in hours.entries) {
    final dayData = entry.value as Map<String, dynamic>;
    final isOpen = dayData['isOpen'] as bool? ?? false;
    
    if (isOpen) {
      final openTime = dayData['open'] as String?;
      final closeTime = dayData['close'] as String?;
      
      if (openTime == null || closeTime == null) return false;
      if (!_validateTimeFormat(openTime) || !_validateTimeFormat(closeTime)) return false;
      if (_compareTime(openTime, closeTime) >= 0) return false; // Close must be after open
    }
  }
  return true;
}

bool _validateTimeFormat(String time) {
  final timeRegex = RegExp(r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$');
  return timeRegex.hasMatch(time);
}

int _compareTime(String time1, String time2) {
  final parts1 = time1.split(':').map(int.parse).toList();
  final parts2 = time2.split(':').map(int.parse).toList();
  
  final minutes1 = parts1[0] * 60 + parts1[1];
  final minutes2 = parts2[0] * 60 + parts2[1];
  
  return minutes1.compareTo(minutes2);
}

bool _isWithinBusinessHours(DateTime dateTime, Map<String, dynamic> businessHours) {
  final dayNames = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
  final dayName = dayNames[dateTime.weekday - 1];
  
  final dayData = businessHours[dayName] as Map<String, dynamic>?;
  if (dayData == null || !(dayData['isOpen'] as bool? ?? false)) return false;
  
  final openTime = dayData['open'] as String?;
  final closeTime = dayData['close'] as String?;
  if (openTime == null || closeTime == null) return false;
  
  final currentTime = '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  
  return _compareTime(currentTime, openTime) >= 0 && _compareTime(currentTime, closeTime) < 0;
}

bool _validateNotificationSettings(Map<String, dynamic> settings) {
  final reminderTime = settings['reminderTime'] as int?;
  if (reminderTime != null && reminderTime < 0) return false;
  
  return true;
}

bool _validateSalonServices(List<Map<String, dynamic>> services) {
  for (final service in services) {
    final duration = service['duration'] as int? ?? 0;
    final price = service['price'] as double? ?? 0.0;
    
    if (duration <= 0) return false;
    if (price < 0) return false;
  }
  return true;
}

Map<String, dynamic> _updateSalonData(Map<String, dynamic> original, Map<String, dynamic> updates) {
  final result = Map<String, dynamic>.from(original);
  result.addAll(updates);
  return result;
}

Map<String, dynamic> _mergeSalonSettings(Map<String, dynamic> existing, Map<String, dynamic> newSettings) {
  final result = Map<String, dynamic>.from(existing);
  
  for (final entry in newSettings.entries) {
    if (result.containsKey(entry.key) && result[entry.key] is Map && entry.value is Map) {
      result[entry.key] = {...(result[entry.key] as Map), ...(entry.value as Map)};
    } else {
      result[entry.key] = entry.value;
    }
  }
  
  return result;
}

String _handleSalonError(String error) {
  final lowerError = error.toLowerCase();
  
  if (lowerError.contains('name') && lowerError.contains('exists')) {
    return 'Numele salonului există deja în sistem.';
  } else if (lowerError.contains('invalid')) {
    return 'Datele salonului sunt invalide.';
  } else if (lowerError.contains('network')) {
    return 'Eroare de conexiune la rețea.';
  } else {
    return 'A apărut o eroare neașteptată.';
  }
}

String _formatSalonInfo(String info) {
  // Simply return the info to test Romanian character preservation
  return info;
}

String _translateServiceCategory(String category) {
  switch (category) {
    case 'GROOMING':
      return 'Îngrijire';
    case 'WASHING':
      return 'Spălare';
    case 'NAIL_CARE':
      return 'Tăiere unghii';
    case 'EAR_CLEANING':
      return 'Curățare urechi';
    default:
      return category;
  }
}
