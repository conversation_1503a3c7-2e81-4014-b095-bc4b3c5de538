import 'package:flutter/foundation.dart';
import '../models/api_response.dart';
import '../models/notification_settings.dart';
import '../services/api_service.dart';
import '../config/api_config.dart';
import 'auth/auth_service.dart';

/// Service for managing salon notification settings
class NotificationSettingsService {
  /// Get notification settings for the current salon
  static Future<ApiResponse<NotificationSettings>> getNotificationSettings() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<NotificationSettings>.error('No salon ID found');
      }

      if (ApiConfig.enableLogging) {
        debugPrint('🔔 Getting notification settings for salon: $salonId');
      }

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/salons/$salonId/notification-settings',
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final settings = NotificationSettings.fromJson(response.data!);
        
        if (ApiConfig.enableLogging) {
          debugPrint('✅ Notification settings retrieved successfully');
        }
        
        return ApiResponse.success(settings);
      }

      return ApiResponse<NotificationSettings>.error(response.error ?? 'Failed to get notification settings');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error getting notification settings: $e');
      }
      return ApiResponse<NotificationSettings>.error('Failed to get notification settings: $e');
    }
  }

  /// Update notification settings for the current salon
  /// Only users with CHIEF_GROOMER role can update these settings
  static Future<ApiResponse<NotificationSettings>> updateNotificationSettings(UpdateNotificationSettingsRequest request) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<NotificationSettings>.error('No salon ID found');
      }

      if (ApiConfig.enableLogging) {
        debugPrint('🔔 Updating notification settings for salon: $salonId');
        debugPrint('   Push notifications: ${request.pushNotificationsEnabled}');
        debugPrint('   Sound preference: ${request.soundPreference}');
        debugPrint('   Vibration: ${request.vibrationEnabled}');
        debugPrint('   Do Not Disturb: ${request.doNotDisturb.enabled}');
      }

      final response = await ApiService.put<Map<String, dynamic>>(
        '/api/salons/$salonId/notification-settings',
        body: request.toJson(),
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final updatedSettings = NotificationSettings.fromJson(response.data!);
        
        if (ApiConfig.enableLogging) {
          debugPrint('✅ Notification settings updated successfully');
        }
        
        return ApiResponse.success(updatedSettings);
      }

      return ApiResponse<NotificationSettings>.error(response.error ?? 'Failed to update notification settings');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error updating notification settings: $e');
      }
      return ApiResponse<NotificationSettings>.error('Failed to update notification settings: $e');
    }
  }

  /// Get notification settings for a specific salon (admin only)
  static Future<ApiResponse<NotificationSettings>> getNotificationSettingsForSalon(String salonId) async {
    try {
      if (ApiConfig.enableLogging) {
        debugPrint('🔔 Getting notification settings for specific salon: $salonId');
      }

      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/salons/$salonId/notification-settings',
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success && response.data != null) {
        final settings = NotificationSettings.fromJson(response.data!);
        
        if (ApiConfig.enableLogging) {
          debugPrint('✅ Notification settings retrieved successfully for salon: $salonId');
        }
        
        return ApiResponse.success(settings);
      }

      return ApiResponse<NotificationSettings>.error(response.error ?? 'Failed to get notification settings');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error getting notification settings for salon $salonId: $e');
      }
      return ApiResponse<NotificationSettings>.error('Failed to get notification settings: $e');
    }
  }

  /// Reset notification settings to default values
  /// Only users with CHIEF_GROOMER role can reset settings
  static Future<ApiResponse<NotificationSettings>> resetNotificationSettings() async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<NotificationSettings>.error('No salon ID found');
      }

      if (ApiConfig.enableLogging) {
        debugPrint('🔔 Resetting notification settings for salon: $salonId');
      }

      final defaultRequest = UpdateNotificationSettingsRequest(
        pushNotificationsEnabled: true,
        soundPreference: 'default',
        vibrationEnabled: true,
        doNotDisturb: const DoNotDisturbSettings(
          enabled: false,
          startTime: '22:00',
          endTime: '08:00',
          allowCritical: true,
        ),
        notificationRules: const NotificationRules(
          newAppointments: true,
          appointmentCancellations: true,
          paymentConfirmations: true,
          teamMemberUpdates: true,
          systemMaintenanceAlerts: true,
          defaultPriority: NotificationPriority.normal,
        ),
      );

      return await updateNotificationSettings(defaultRequest);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error resetting notification settings: $e');
      }
      return ApiResponse<NotificationSettings>.error('Failed to reset notification settings: $e');
    }
  }

  /// Validate notification settings request
  static String? validateNotificationSettingsRequest(UpdateNotificationSettingsRequest request) {
    // Validate Do Not Disturb time format
    if (request.doNotDisturb.enabled) {
      if (!_isValidTimeFormat(request.doNotDisturb.startTime)) {
        return 'Ora de început pentru Nu Deranja nu este validă';
      }
      if (!_isValidTimeFormat(request.doNotDisturb.endTime)) {
        return 'Ora de sfârșit pentru Nu Deranja nu este validă';
      }
    }

    // Validate sound preference
    final validSounds = ['default', 'silent', 'custom1', 'custom2', 'custom3'];
    if (!validSounds.contains(request.soundPreference)) {
      return 'Preferința de sunet nu este validă';
    }

    return null; // Valid request
  }

  /// Check if time format is valid (HH:MM)
  static bool _isValidTimeFormat(String time) {
    final timeRegex = RegExp(r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$');
    return timeRegex.hasMatch(time);
  }

  /// Check if current user can modify notification settings
  /// This should be used in UI to show/hide edit controls
  static Future<bool> canModifyNotificationSettings() async {
    try {
      // This would typically check the user's role in the current salon
      // For now, we'll assume the permission check is done at the UI level
      // using PermissionGuard with requireManagementAccess
      final salonId = await AuthService.getCurrentSalonId();
      return salonId != null;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error checking notification settings permissions: $e');
      }
      return false;
    }
  }

  /// Check if notification should be sent based on current settings
  static Future<bool> shouldSendNotification({
    required String notificationType,
    required NotificationPriority priority,
  }) async {
    try {
      final response = await getNotificationSettings();
      if (!response.success || response.data == null) {
        return true; // Default to sending if settings can't be loaded
      }

      final settings = response.data!;

      // Check if push notifications are enabled
      if (!settings.pushNotificationsEnabled) {
        return false;
      }

      // Check Do Not Disturb
      if (settings.doNotDisturb.enabled && !_isInDoNotDisturbPeriod(settings.doNotDisturb)) {
        // Allow critical notifications during DND if configured
        if (priority == NotificationPriority.critical && settings.doNotDisturb.allowCritical) {
          return true;
        }
        return false;
      }

      // Check specific notification type rules
      switch (notificationType.toLowerCase()) {
        case 'new_appointment':
          return settings.notificationRules.newAppointments;
        case 'appointment_cancellation':
          return settings.notificationRules.appointmentCancellations;
        case 'payment_confirmation':
          return settings.notificationRules.paymentConfirmations;
        case 'team_member_update':
          return settings.notificationRules.teamMemberUpdates;
        case 'system_maintenance':
          return settings.notificationRules.systemMaintenanceAlerts;
        default:
          return true; // Default to sending for unknown types
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error checking notification permissions: $e');
      }
      return true; // Default to sending on error
    }
  }

  /// Check if current time is in Do Not Disturb period
  static bool _isInDoNotDisturbPeriod(DoNotDisturbSettings dnd) {
    final now = DateTime.now();
    final currentTime = '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
    
    // Simple time comparison - this could be enhanced for cross-day periods
    return currentTime.compareTo(dnd.startTime) >= 0 && currentTime.compareTo(dnd.endTime) <= 0;
  }
}
