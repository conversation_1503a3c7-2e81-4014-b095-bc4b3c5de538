#!/bin/bash
# Fix script for iOS signing issues
# This script attempts to resolve common iOS code signing problems

set -e

echo "🔧 iOS Code Signing Fix Script"
echo "==============================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "ios/Runner.xcodeproj/project.pbxproj" ]; then
    print_error "Not in Flutter project root. Please run from flutter-partykids directory."
    exit 1
fi

print_status "Starting iOS signing fixes..."

# 1. Clean build artifacts
echo ""
echo "🧹 Cleaning build artifacts..."
flutter clean > /dev/null 2>&1
rm -rf ios/build build
rm -rf ios/Pods ios/Podfile.lock
print_status "Build artifacts cleaned"

# 2. Update Flutter dependencies
echo ""
echo "📦 Updating Flutter dependencies..."
flutter pub get > /dev/null 2>&1
print_status "Flutter dependencies updated"

# 3. Update CocoaPods
echo ""
echo "🍫 Updating CocoaPods..."
cd ios
pod install --repo-update > /dev/null 2>&1
cd ..
print_status "CocoaPods updated"

# 4. Fix Xcode project settings
echo ""
echo "⚙️ Fixing Xcode project settings..."

# Update project.pbxproj with correct settings
if [ -f "ios/Runner.xcodeproj/project.pbxproj" ]; then
    # Backup original file
    cp ios/Runner.xcodeproj/project.pbxproj ios/Runner.xcodeproj/project.pbxproj.backup
    
    # Set correct team ID and signing style
    sed -i '' 's/DEVELOPMENT_TEAM = [^;]*/DEVELOPMENT_TEAM = 6MB735DMZ5/g' ios/Runner.xcodeproj/project.pbxproj
    sed -i '' 's/CODE_SIGN_STYLE = [^;]*/CODE_SIGN_STYLE = Automatic/g' ios/Runner.xcodeproj/project.pbxproj
    # Note: Keeping CODE_SIGN_IDENTITY as is - let automatic signing choose the right certificate
    
    print_status "Xcode project settings updated"
else
    print_error "Xcode project file not found"
    exit 1
fi

# 5. Verify bundle identifier
echo ""
echo "📱 Verifying bundle identifier..."
BUNDLE_ID=$(grep -o 'PRODUCT_BUNDLE_IDENTIFIER = [^;]*' ios/Runner.xcodeproj/project.pbxproj | head -1 | cut -d' ' -f3 | tr -d '"')
if [ "$BUNDLE_ID" = "com.partykids.partykidsapp" ]; then
    print_status "Bundle identifier is correct: $BUNDLE_ID"
else
    print_warning "Bundle identifier might be incorrect: $BUNDLE_ID"
    print_warning "Expected: com.partykids.partykidsapp"
fi

# 6. Check and fix Info.plist
echo ""
echo "📋 Checking Info.plist..."
if [ -f "ios/Runner/Info.plist" ]; then
    # Ensure CFBundleIdentifier matches
    if grep -q "com.partykids.partykidsapp" ios/Runner/Info.plist; then
        print_status "Info.plist bundle identifier is correct"
    else
        print_warning "Info.plist bundle identifier might need updating"
    fi
else
    print_error "Info.plist not found"
fi

# 7. Verify entitlements
echo ""
echo "🔐 Checking entitlements..."
if [ -f "ios/Runner/Runner.entitlements" ]; then
    print_status "Entitlements file found"
else
    print_warning "Entitlements file not found"
fi

# 8. Check ExportOptions.plist
echo ""
echo "📤 Checking ExportOptions.plist..."
if [ -f "ios/ExportOptions.plist" ]; then
    if grep -q "6MB735DMZ5" ios/ExportOptions.plist; then
        print_status "ExportOptions.plist team ID is correct"
    else
        print_warning "ExportOptions.plist team ID might be incorrect"
    fi
    
    if grep -q "com.partykids.partykidsapp" ios/ExportOptions.plist; then
        print_status "ExportOptions.plist bundle ID is correct"
    else
        print_warning "ExportOptions.plist bundle ID might be incorrect"
    fi
else
    print_error "ExportOptions.plist not found"
fi

# 9. Test build (no codesign)
echo ""
echo "🔨 Testing build (no codesign)..."
if flutter build ios --release --no-codesign > /dev/null 2>&1; then
    print_status "Build test successful"
else
    print_error "Build test failed"
    echo "Run 'flutter build ios --release --no-codesign' manually to see errors"
fi

echo ""
print_status "iOS signing fixes completed!"
echo ""
echo "📋 Next steps for CI/CD:"
echo "1. Ensure GitHub secrets are properly set:"
echo "   - APPSTORE_KEY_ID"
echo "   - APPSTORE_ISSUER_ID" 
echo "   - APPSTORE_PRIVATE_KEY"
echo "   - APPLE_ID"
echo "   - APPLE_APP_SPECIFIC_PASSWORD"
echo ""
echo "2. Verify your Apple Developer account has:"
echo "   - Valid Apple Distribution certificate"
echo "   - App Store provisioning profile for com.partykids.partykidsapp"
echo "   - App Store Connect API key with proper permissions"
echo ""
echo "3. Run the debug script for more information:"
echo "   ./scripts/debug-ios-signing.sh"
