import 'package:flutter/foundation.dart';
import '../core/constants/app_strings.dart';

/// Service for parsing and formatting staff schedule validation errors
class StaffValidationErrorService {
  /// Day name mappings from English to Romanian
  static const Map<String, String> _dayNames = {
    'monday': AppStrings.monday,
    'tuesday': AppStrings.tuesday,
    'wednesday': AppStrings.wednesday,
    'thursday': AppStrings.thursday,
    'friday': AppStrings.friday,
    'saturday': AppStrings.saturday,
    'sunday': AppStrings.sunday,
    'MONDAY': AppStrings.monday,
    'TUESDAY': AppStrings.tuesday,
    'WEDNESDAY': AppStrings.wednesday,
    'THURSDAY': AppStrings.thursday,
    'FRIDAY': AppStrings.friday,
    'SATURDAY': AppStrings.saturday,
    'SUNDAY': AppStrings.sunday,
  };

  /// Parse backend validation error message into user-friendly Romanian format
  static String parseValidationError(String error) {
    if (kDebugMode) {
      debugPrint('🔍 Parsing validation error: $error');
    }

    // Check if this is a staff schedule validation error
    if (error.contains('Staff schedule violates business hours constraints')) {
      return _parseBusinessHoursViolation(error);
    }

    // Check for other common validation errors
    if (error.contains('Invalid time format')) {
      return _parseTimeFormatError(error);
    }

    if (error.contains('Start time must be before end time')) {
      return AppStrings.startTimeBeforeEndTime;
    }

    if (error.contains('Break time must be within working hours')) {
      return AppStrings.breakTimeWithinWorkingHours;
    }

    if (error.contains('Staff member not found')) {
      return 'Membrul personalului nu a fost găsit';
    }

    if (error.contains('Unauthorized')) {
      return 'Nu aveți permisiunea să modificați acest program';
    }

    // Return original error if no specific parsing applies
    return error;
  }

  /// Parse business hours constraint violations
  static String _parseBusinessHoursViolation(String error) {
    if (kDebugMode) {
      debugPrint('🔍 Parsing business hours violation: $error');
    }

    final violations = <String>[];

    // First, try to extract the specific violation after the colon
    String violationText = error;
    if (error.contains('Staff schedule violates business hours constraints:')) {
      violationText = error.split('Staff schedule violates business hours constraints:').last.trim();
      if (kDebugMode) {
        debugPrint('🔍 Extracted violation text: "$violationText"');
      }
    }

    // Split by semicolon to get individual violations
    final parts = violationText.split(';');

    if (kDebugMode) {
      debugPrint('🔍 Split into ${parts.length} parts: ${parts.map((p) => '"${p.trim()}"').join(', ')}');
    }

    for (final part in parts) {
      final trimmed = part.trim();
      if (trimmed.isEmpty) continue;

      if (kDebugMode) {
        debugPrint('🔍 Processing part: "$trimmed"');
      }

      // Parse different types of violations
      if (trimmed.contains('Staff end time') && trimmed.contains('is after business end time')) {
        final violation = _parseEndTimeViolation(trimmed);
        if (violation != null) violations.add(violation);
      } else if (trimmed.contains('Staff start time') && trimmed.contains('is before business start time')) {
        final violation = _parseStartTimeViolation(trimmed);
        if (violation != null) violations.add(violation);
      } else if (trimmed.contains('Staff cannot work on') && trimmed.contains('when business is closed')) {
        final violation = _parseClosedDayViolation(trimmed);
        if (violation != null) violations.add(violation);
      } else if (trimmed.contains('Staff schedule violates business hours constraints')) {
        // Skip the main error message, we'll handle the specific violations
        continue;
      } else {
        // Add any other violations as-is
        violations.add(trimmed);
      }
    }
    
    if (violations.isNotEmpty) {
      final result = '${AppStrings.scheduleValidationMessage}:\n\n• ${violations.join('\n• ')}';
      if (kDebugMode) {
        debugPrint('🔍 Final parsed result: $result');
      }
      return result;
    }

    if (kDebugMode) {
      debugPrint('🔍 No violations parsed, returning default message');
    }
    return AppStrings.scheduleValidationMessage;
  }

  /// Parse end time violation (staff end time after business end time)
  static String? _parseEndTimeViolation(String violation) {
    if (kDebugMode) {
      debugPrint('🔍 Parsing end time violation: $violation');
    }

    // Pattern: "Staff end time 21:00 on MONDAY is after business end time 17:00"
    // Make regex more flexible to handle different time formats
    final dayMatch = RegExp(r'on (\w+)', caseSensitive: false).firstMatch(violation);
    final staffTimeMatch = RegExp(r'Staff end time (\d{1,2}:\d{2})', caseSensitive: false).firstMatch(violation);
    final businessTimeMatch = RegExp(r'business end time (\d{1,2}:\d{2})', caseSensitive: false).firstMatch(violation);

    if (kDebugMode) {
      debugPrint('🔍 Day match: ${dayMatch?.group(1)}');
      debugPrint('🔍 Staff time match: ${staffTimeMatch?.group(1)}');
      debugPrint('🔍 Business time match: ${businessTimeMatch?.group(1)}');
    }

    if (dayMatch != null && staffTimeMatch != null && businessTimeMatch != null) {
      final day = _dayNames[dayMatch.group(1)?.toLowerCase()] ?? dayMatch.group(1);
      final staffTime = staffTimeMatch.group(1);
      final businessTime = businessTimeMatch.group(1);

      final result = '$day: ${AppStrings.staffEndTimeAfterBusiness} ($businessTime)';
      if (kDebugMode) {
        debugPrint('🔍 Parsed result: $result');
      }
      return result;
    }

    if (kDebugMode) {
      debugPrint('🔍 Failed to parse end time violation');
    }
    return null;
  }

  /// Parse start time violation (staff start time before business start time)
  static String? _parseStartTimeViolation(String violation) {
    // Pattern: "Staff start time 07:00 on MONDAY is before business start time 09:00"
    final dayMatch = RegExp(r'on (\w+)', caseSensitive: false).firstMatch(violation);
    final staffTimeMatch = RegExp(r'Staff start time (\d{1,2}:\d{2})', caseSensitive: false).firstMatch(violation);
    final businessTimeMatch = RegExp(r'business start time (\d{1,2}:\d{2})', caseSensitive: false).firstMatch(violation);
    
    if (dayMatch != null && staffTimeMatch != null && businessTimeMatch != null) {
      final day = _dayNames[dayMatch.group(1)?.toLowerCase()] ?? dayMatch.group(1);
      final staffTime = staffTimeMatch.group(1);
      final businessTime = businessTimeMatch.group(1);
      
      return '$day: ${AppStrings.staffStartTimeBeforeBusiness} ($businessTime)';
    }
    
    return null;
  }

  /// Parse closed day violation (staff working when business is closed)
  static String? _parseClosedDayViolation(String violation) {
    // Pattern: "Staff cannot work on SATURDAY when business is closed"
    final dayMatch = RegExp(r'Staff cannot work on (\w+)', caseSensitive: false).firstMatch(violation);
    
    if (dayMatch != null) {
      final day = _dayNames[dayMatch.group(1)?.toLowerCase()] ?? dayMatch.group(1);
      return '$day: ${AppStrings.staffCannotWorkWhenClosed}';
    }
    
    return null;
  }

  /// Parse time format errors
  static String _parseTimeFormatError(String error) {
    if (error.contains('start time')) {
      return 'Format invalid pentru ora de început. Folosiți formatul HH:MM (ex: 09:00)';
    }
    
    if (error.contains('end time')) {
      return 'Format invalid pentru ora de sfârșit. Folosiți formatul HH:MM (ex: 17:00)';
    }
    
    if (error.contains('break')) {
      return 'Format invalid pentru ora pauzei. Folosiți formatul HH:MM (ex: 12:00)';
    }
    
    return AppStrings.invalidTimeFormat;
  }

  /// Check if error is a validation error that should show business hours context
  static bool shouldShowBusinessHoursContext(String? error) {
    if (error == null) return false;
    
    return error.contains('Staff schedule violates business hours constraints') ||
           error.contains('business end time') ||
           error.contains('business start time') ||
           error.contains('when business is closed');
  }

  /// Get user-friendly error title based on error type
  static String getErrorTitle(String? error) {
    if (error == null) return 'Eroare necunoscută';
    
    if (shouldShowBusinessHoursContext(error)) {
      return AppStrings.scheduleValidationTitle;
    }
    
    if (error.contains('Invalid time format')) {
      return 'Format oră invalid';
    }
    
    if (error.contains('Unauthorized')) {
      return 'Acces interzis';
    }
    
    return 'Eroare de validare';
  }

  /// Get appropriate icon for error type
  static String getErrorIcon(String? error) {
    if (error == null) return 'error_outline';

    if (shouldShowBusinessHoursContext(error)) {
      return 'schedule_problem';
    }

    if (error.contains('Invalid time format')) {
      return 'access_time';
    }

    if (error.contains('Unauthorized')) {
      return 'lock';
    }

    return 'error_outline';
  }

  /// Test method to verify error parsing (debug only)
  static void testErrorParsing() {
    if (!kDebugMode) return;

    debugPrint('🧪 Testing error parsing...');

    // Test case 1: End time violation
    const testError1 = 'Staff schedule violates business hours constraints: Staff end time 21:00 on MONDAY is after business end time 17:00';
    final result1 = parseValidationError(testError1);
    debugPrint('🧪 Test 1 result: $result1');

    // Test case 2: Multiple violations
    const testError2 = 'Staff schedule violates business hours constraints: Staff end time 20:00 on TUESDAY is after business end time 18:00; Staff cannot work on SATURDAY when business is closed';
    final result2 = parseValidationError(testError2);
    debugPrint('🧪 Test 2 result: $result2');

    // Test case 3: Start time violation
    const testError3 = 'Staff schedule violates business hours constraints: Staff start time 07:00 on MONDAY is before business start time 09:00';
    final result3 = parseValidationError(testError3);
    debugPrint('🧪 Test 3 result: $result3');

    // Test case 4: Closed day violation (your exact error)
    const testError4 = 'Staff schedule violates business hours constraints: Staff cannot work on SUNDAY when business is closed';
    final result4 = parseValidationError(testError4);
    debugPrint('🧪 Test 4 result: $result4');

    debugPrint('🧪 Error parsing tests completed');
  }
}
