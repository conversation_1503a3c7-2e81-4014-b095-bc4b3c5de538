import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:partykidsapp/services/appointment/appointment_drag_drop_service.dart';
import 'package:partykidsapp/services/appointment/appointment_service.dart';
import 'package:partykidsapp/models/appointment.dart';
import 'package:partykidsapp/providers/calendar_provider.dart';
import 'package:partykidsapp/utils/api_response.dart';

// Generate mocks
@GenerateMocks([CalendarProvider, AppointmentService])
import 'appointment_drag_drop_service_test.mocks.dart';

void main() {
  group('AppointmentDragDropService', () {
    late MockCalendarProvider mockCalendarProvider;
    late Appointment testAppointment;

    setUp(() {
      mockCalendarProvider = MockCalendarProvider();
      testAppointment = Appointment(
        id: 'test-id',
        clientId: 'client-1',
        clientName: '<PERSON>',
        clientPhone: '+***********',
        petId: 'pet-1',
        petName: 'Buddy',
        petSpecies: 'Dog',
        service: 'Grooming',
        startTime: DateTime(2024, 1, 15, 10, 0),
        endTime: DateTime(2024, 1, 15, 11, 0),
        status: 'SCHEDULED',
        isPaid: false,
        groomerId: 'groomer-1',
        assignedGroomer: 'Jane Smith',
      );
    });

    group('validateDropTarget', () {
      testWidgets('validates business hours correctly', (tester) async {
        // Test valid business hours (10 AM - 11 AM)
        final validResult = await AppointmentDragDropService.validateDropTarget(
          appointment: testAppointment,
          newStartTime: DateTime(2024, 1, 15, 10, 0),
          newEndTime: DateTime(2024, 1, 15, 11, 0),
          newStaffId: 'groomer-1',
          calendarProvider: mockCalendarProvider,
        );

        expect(validResult.isValid, isTrue);
        expect(validResult.conflictType, equals(ConflictType.none));
      });

      testWidgets('rejects times outside business hours', (tester) async {
        // Test invalid business hours (6 AM - 7 AM)
        final invalidResult = await AppointmentDragDropService.validateDropTarget(
          appointment: testAppointment,
          newStartTime: DateTime(2024, 1, 15, 6, 0),
          newEndTime: DateTime(2024, 1, 15, 7, 0),
          newStaffId: 'groomer-1',
          calendarProvider: mockCalendarProvider,
        );

        expect(invalidResult.isValid, isFalse);
        expect(invalidResult.conflictType, equals(ConflictType.businessHours));
        expect(invalidResult.reason, contains('În afara programului'));
      });

      testWidgets('rejects times during lunch break', (tester) async {
        // Test lunch break hours (1 PM - 2 PM)
        final lunchResult = await AppointmentDragDropService.validateDropTarget(
          appointment: testAppointment,
          newStartTime: DateTime(2024, 1, 15, 13, 0),
          newEndTime: DateTime(2024, 1, 15, 14, 0),
          newStaffId: 'groomer-1',
          calendarProvider: mockCalendarProvider,
        );

        expect(lunchResult.isValid, isFalse);
        expect(lunchResult.conflictType, equals(ConflictType.lunchBreak));
        expect(lunchResult.reason, contains('Pauza de prânz'));
      });

      testWidgets('detects appointment conflicts', (tester) async {
        // Mock conflicting appointment
        final conflictingAppointment = Appointment(
          id: 'conflict-id',
          clientId: 'client-2',
          clientName: 'Jane Doe',
          clientPhone: '+40987654321',
          petId: 'pet-2',
          petName: 'Max',
          petSpecies: 'Cat',
          service: 'Grooming',
          startTime: DateTime(2024, 1, 15, 10, 30),
          endTime: DateTime(2024, 1, 15, 11, 30),
          status: 'SCHEDULED',
          isPaid: false,
          groomerId: 'groomer-1',
        );

        when(mockCalendarProvider.getFilteredAppointmentsForDate(any))
            .thenReturn([conflictingAppointment]);

        final conflictResult = await AppointmentDragDropService.validateDropTarget(
          appointment: testAppointment,
          newStartTime: DateTime(2024, 1, 15, 10, 0),
          newEndTime: DateTime(2024, 1, 15, 11, 0),
          newStaffId: 'groomer-1',
          calendarProvider: mockCalendarProvider,
        );

        expect(conflictResult.isValid, isFalse);
        expect(conflictResult.conflictType, equals(ConflictType.appointment));
        expect(conflictResult.reason, contains('Conflict cu programarea'));
      });

      testWidgets('allows same appointment to be moved', (tester) async {
        // Mock the same appointment in the list
        when(mockCalendarProvider.getFilteredAppointmentsForDate(any))
            .thenReturn([testAppointment]);

        final result = await AppointmentDragDropService.validateDropTarget(
          appointment: testAppointment,
          newStartTime: DateTime(2024, 1, 15, 11, 0),
          newEndTime: DateTime(2024, 1, 15, 12, 0),
          newStaffId: 'groomer-1',
          calendarProvider: mockCalendarProvider,
        );

        expect(result.isValid, isTrue);
        expect(result.conflictType, equals(ConflictType.none));
      });

      testWidgets('validates staff availability for reassignment', (tester) async {
        when(mockCalendarProvider.getFilteredAppointmentsForDate(any))
            .thenReturn([]);
        when(mockCalendarProvider.getStaffById('groomer-2'))
            .thenReturn(null);

        final result = await AppointmentDragDropService.validateDropTarget(
          appointment: testAppointment,
          newStartTime: DateTime(2024, 1, 15, 10, 0),
          newEndTime: DateTime(2024, 1, 15, 11, 0),
          newStaffId: 'groomer-2',
          calendarProvider: mockCalendarProvider,
        );

        expect(result.isValid, isFalse);
        expect(result.conflictType, equals(ConflictType.staffUnavailable));
      });
    });

    group('performDrop', () {
      testWidgets('handles time-only changes with reschedule API', (tester) async {
        when(mockCalendarProvider.getFilteredAppointmentsForDate(any))
            .thenReturn([]);

        // Mock successful reschedule
        // Note: We can't easily mock static methods, so this test focuses on the logic flow

        final result = await AppointmentDragDropService.performDrop(
          appointment: testAppointment,
          newStartTime: DateTime(2024, 1, 15, 11, 0),
          newEndTime: DateTime(2024, 1, 15, 12, 0),
          newStaffId: null,
          calendarProvider: mockCalendarProvider,
        );

        // Verify calendar refresh was called
        verify(mockCalendarProvider.fetchAppointmentsForDate(any, forceRefresh: true))
            .called(1);
      });

      testWidgets('handles staff-only changes with update API', (tester) async {
        when(mockCalendarProvider.getFilteredAppointmentsForDate(any))
            .thenReturn([]);
        when(mockCalendarProvider.getStaffById('groomer-2'))
            .thenReturn(MockStaffResponse());

        final result = await AppointmentDragDropService.performDrop(
          appointment: testAppointment,
          newStartTime: testAppointment.startTime,
          newEndTime: testAppointment.endTime,
          newStaffId: 'groomer-2',
          calendarProvider: mockCalendarProvider,
        );

        // Verify calendar refresh was called
        verify(mockCalendarProvider.fetchAppointmentsForDate(any, forceRefresh: true))
            .called(1);
      });

      testWidgets('handles both time and staff changes', (tester) async {
        when(mockCalendarProvider.getFilteredAppointmentsForDate(any))
            .thenReturn([]);
        when(mockCalendarProvider.getStaffById('groomer-2'))
            .thenReturn(MockStaffResponse());

        final result = await AppointmentDragDropService.performDrop(
          appointment: testAppointment,
          newStartTime: DateTime(2024, 1, 15, 11, 0),
          newEndTime: DateTime(2024, 1, 15, 12, 0),
          newStaffId: 'groomer-2',
          calendarProvider: mockCalendarProvider,
        );

        // Verify calendar refresh was called
        verify(mockCalendarProvider.fetchAppointmentsForDate(any, forceRefresh: true))
            .called(1);
      });

      testWidgets('refreshes both dates when date changes', (tester) async {
        when(mockCalendarProvider.getFilteredAppointmentsForDate(any))
            .thenReturn([]);

        final result = await AppointmentDragDropService.performDrop(
          appointment: testAppointment,
          newStartTime: DateTime(2024, 1, 16, 10, 0), // Different date
          newEndTime: DateTime(2024, 1, 16, 11, 0),
          newStaffId: null,
          calendarProvider: mockCalendarProvider,
        );

        // Verify calendar refresh was called for both dates
        verify(mockCalendarProvider.fetchAppointmentsForDate(any, forceRefresh: true))
            .called(2);
      });

      testWidgets('returns success when no changes needed', (tester) async {
        when(mockCalendarProvider.getFilteredAppointmentsForDate(any))
            .thenReturn([]);

        final result = await AppointmentDragDropService.performDrop(
          appointment: testAppointment,
          newStartTime: testAppointment.startTime,
          newEndTime: testAppointment.endTime,
          newStaffId: testAppointment.groomerId,
          calendarProvider: mockCalendarProvider,
        );

        expect(result.success, isTrue);
        expect(result.message, contains('Nicio modificare'));
      });

      testWidgets('handles validation failures', (tester) async {
        // Mock validation failure
        when(mockCalendarProvider.getFilteredAppointmentsForDate(any))
            .thenReturn([]);

        final result = await AppointmentDragDropService.performDrop(
          appointment: testAppointment,
          newStartTime: DateTime(2024, 1, 15, 6, 0), // Outside business hours
          newEndTime: DateTime(2024, 1, 15, 7, 0),
          newStaffId: null,
          calendarProvider: mockCalendarProvider,
        );

        expect(result.success, isFalse);
        expect(result.conflictType, equals(ConflictType.businessHours));
      });
    });

    group('ConflictType', () {
      test('has all expected values', () {
        expect(ConflictType.values, contains(ConflictType.none));
        expect(ConflictType.values, contains(ConflictType.businessHours));
        expect(ConflictType.values, contains(ConflictType.lunchBreak));
        expect(ConflictType.values, contains(ConflictType.appointment));
        expect(ConflictType.values, contains(ConflictType.staffUnavailable));
        expect(ConflictType.values, contains(ConflictType.error));
      });
    });

    group('DropValidationResult', () {
      test('creates instance with correct properties', () {
        final result = DropValidationResult(
          isValid: true,
          reason: 'Test reason',
          conflictType: ConflictType.none,
          conflictingAppointment: testAppointment,
        );

        expect(result.isValid, isTrue);
        expect(result.reason, equals('Test reason'));
        expect(result.conflictType, equals(ConflictType.none));
        expect(result.conflictingAppointment, equals(testAppointment));
      });
    });

    group('DropOperationResult', () {
      test('creates instance with correct properties', () {
        final result = DropOperationResult(
          success: true,
          message: 'Success message',
          conflictType: ConflictType.none,
          updatedAppointment: testAppointment,
        );

        expect(result.success, isTrue);
        expect(result.message, equals('Success message'));
        expect(result.conflictType, equals(ConflictType.none));
        expect(result.updatedAppointment, equals(testAppointment));
      });
    });
  });
}

// Mock class for StaffResponse
class MockStaffResponse {
  final String id = 'groomer-2';
  final String name = 'John Smith';
}
