#!/bin/bash
# iOS Deployment Script using Fastlane
# This script builds and deploys your Flutter iOS app to TestFlight

set -e

echo "🚀 iOS Deployment Script"
echo "========================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    print_error "Not in Flutter project root. Please run from flutter-partykids directory."
    exit 1
fi

# Check CocoaPods first
if ! command -v pod &> /dev/null; then
    print_warning "CocoaPods not found. Please run: ./scripts/fix-cocoapods.sh"
    exit 1
else
    # Test if CocoaPods is working
    if ! pod --version &> /dev/null; then
        print_warning "CocoaPods is broken. Running fix script..."
        ./scripts/fix-cocoapods.sh
    fi
fi

# Check if Fastlane is installed
if ! command -v fastlane &> /dev/null; then
    print_warning "Fastlane not found. Installing..."

    # Install bundler if not present
    if ! command -v bundle &> /dev/null; then
        print_info "Installing bundler..."
        gem install bundler --no-document
    fi

    # Install dependencies
    cd ios
    print_info "Installing Ruby dependencies..."

    # Fix Bundler version compatibility
    if [ -f "Gemfile.lock" ] && grep -q "BUNDLED WITH" Gemfile.lock && grep -A1 "BUNDLED WITH" Gemfile.lock | grep -q "1\."; then
        print_info "Fixing Bundler version compatibility..."
        bundle update --bundler
    fi

    bundle install
    cd ..

    print_status "Fastlane installed successfully"
else
    print_status "Fastlane is already installed"

    # Still check for Bundler compatibility issues
    cd ios
    if [ -f "Gemfile.lock" ] && grep -q "BUNDLED WITH" Gemfile.lock && grep -A1 "BUNDLED WITH" Gemfile.lock | grep -q "1\."; then
        print_info "Fixing Bundler version compatibility..."
        bundle update --bundler
    fi
    cd ..
fi

# Set up environment variables
export FASTLANE_SKIP_UPDATE_CHECK=1
export FASTLANE_HIDE_CHANGELOG=1

# Check if running in automatic mode (CI environment)
if [ "$1" = "--auto" ] || [ "$CI" = "true" ]; then
    print_info "Running in automatic mode (CI/CD) - Tests skipped for faster deployment"
    choice="auto"
else
    # Ask user which lane to run
    echo ""
    echo "📱 Choose deployment option:"
    echo "1) Build and upload to TestFlight (Simple - Recommended)"
    echo "2) Build and upload to TestFlight (With Match)"
    echo "3) Build only (no upload)"
    echo "4) Upload existing IPA"
    echo "5) Auto deploy (CI/CD optimized)"
    echo ""
    read -p "Enter your choice (1-5): " choice
fi

case $choice in
    1)
        print_info "Running simple TestFlight deployment..."
        cd ios
        bundle exec fastlane beta_simple
        ;;
    2)
        print_info "Running TestFlight deployment with Match..."
        cd ios
        bundle exec fastlane beta
        ;;
    3)
        print_info "Building app only..."
        cd ios
        bundle exec fastlane build_only
        ;;
    4)
        print_info "Uploading existing IPA..."
        read -p "Enter path to IPA file: " ipa_path
        cd ios
        bundle exec fastlane upload_only ipa:"$ipa_path"
        ;;
    5)
        print_info "Running automatic deployment (CI/CD optimized)..."
        cd ios
        bundle exec fastlane deploy_main
        ;;
    auto)
        print_info "Running automatic deployment for main branch..."
        cd ios
        bundle exec fastlane deploy_main
        ;;
    *)
        print_error "Invalid choice. Exiting."
        exit 1
        ;;
esac

print_status "Deployment script completed!"
echo ""
print_info "Next steps:"
echo "1. Check App Store Connect for your build"
echo "2. Wait for processing (usually 5-15 minutes)"
echo "3. Add to TestFlight for testing"
echo ""
print_info "App Store Connect: https://appstoreconnect.apple.com"
