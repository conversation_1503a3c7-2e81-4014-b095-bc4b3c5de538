import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../utils/formatters/phone_number_utils.dart';
import '../common/standard_form_field.dart';
import '../../models/client.dart';
import '../../config/theme/app_theme.dart';
import 'appointment_form_data.dart';

class ClientSelectionWidget extends StatelessWidget {
  final AppointmentFormData formData;
  final bool isLoadingClients;
  final Function(bool) onClientTypeChanged;
  final Function(Client) onClientSelected;
  final Function(String) onClientNameChanged;
  final Function(String) onClientPhoneChanged;

  const ClientSelectionWidget({
    super.key,
    required this.formData,
    this.isLoadingClients = false,
    required this.onClientTypeChanged,
    required this.onClientSelected,
    required this.onClientNameChanged,
    required this.onClientPhoneChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildClientTypeSelector(context),
        SizedBox(height: 16),
        if (formData.isExistingClient)
          _buildExistingClientDropdown(context)
        else
          _buildNewClientFields(),
      ],
    );
  }

  Widget _buildClientTypeSelector(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Radio<bool>(
              value: true,
              groupValue: formData.isExistingClient,
              onChanged: (value) => onClientTypeChanged(value ?? true),
              activeColor: Theme.of(context).colorScheme.primary,
            ),
            Text('Client existent'),
            SizedBox(width: 20),
            Radio<bool>(
              value: false,
              groupValue: formData.isExistingClient,
              onChanged: (value) => onClientTypeChanged(value ?? true),
              activeColor: Theme.of(context).colorScheme.primary,
            ),
            Text('Client nou'),
          ],
        ),
      ],
    );
  }

  Widget _buildExistingClientDropdown(BuildContext context) {
    if (isLoadingClients) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Theme.of(context).colorScheme.outline),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            SizedBox(width: 12),
            Text('Se încarcă clienții...'),
          ],
        ),
      );
    }

    return _buildClientSearchButton(context);
  }

  Widget _buildClientSearchButton(BuildContext context) {
    final selectedClient = formData.clientId.isNotEmpty
        ? formData.availableClients.where((c) => c.id == formData.clientId).firstOrNull
        : null;

    return InkWell(
      onTap: () => _navigateToClientSearch(context),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Theme.of(context).colorScheme.outline),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Client',
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    selectedClient != null
                        ? '${selectedClient.name} - ${selectedClient.phone}'
                        : 'Selectează un client',
                    style: TextStyle(
                      fontSize: 16,
                      color: selectedClient != null ? Theme.of(context).colorScheme.onSurface : Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
             Icon(Icons.search, color: Theme.of(context).colorScheme.onSurface),
          ],
        ),
      ),
    );
  }

  void _navigateToClientSearch(BuildContext context) async {
    final result = await Navigator.of(context).pushNamed(
      '/client-search',
      arguments: formData.availableClients,
    );

    if (result != null) {
      if (result is Client) {
        onClientSelected(result);
      } else if (result == 'new_client') {
        // Switch to new client mode
        onClientTypeChanged(false);
      }
    }
  }

  Widget _buildNewClientFields() {
    return Column(
      children: [
        TextFormField(
          decoration: InputDecoration(
            labelText: 'Nume client',
            border: OutlineInputBorder(),
          ),
          initialValue: formData.clientName,
          onChanged: onClientNameChanged,
        ),
        SizedBox(height: 16),
        TextFormField(
          decoration: InputDecoration(
            labelText: 'Telefon client *',
            border: OutlineInputBorder(),
            hintText: '+40 XXX XXX XXX',
            helperText: 'Format: +40 XXX XXX XXX',
          ),
          keyboardType: TextInputType.phone,
          initialValue: formData.clientPhone,
          onChanged: onClientPhoneChanged,
          inputFormatters: [
            FilteringTextInputFormatter.allow(RegExp(r'[\d\s\+\-\(\)]')),
            PhoneNumberFormatter(),
          ],
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Telefonul este obligatoriu';
            }
            if (!PhoneNumberUtils.isValidRomanianMobile(value)) {
              return 'Numărul de telefon nu este valid';
            }
            return null;
          },
        ),
      ],
    );
  }
}
