#!/bin/bash

# Optimized CI Test Script for Flutter Partykids Project
# This script provides fast, reliable testing for CI/CD environments

set -e  # Exit on any error

# Colors for clean output
readonly GREEN='\033[0;32m'
readonly RED='\033[0;31m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# Configuration
readonly SCRIPT_NAME="Flutter CI Test Suite"
readonly MAX_PARALLEL_TESTS=4
readonly TEST_TIMEOUT="30s"
readonly COVERAGE_THRESHOLD=85

# Logging functions
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_section() {
    echo ""
    echo -e "${BLUE}$1${NC}"
    echo "$(printf '=%.0s' {1..50})"
}

# Cleanup function
cleanup() {
    local exit_code=$?
    if [ $exit_code -ne 0 ]; then
        log_error "Test execution failed with exit code $exit_code"
    fi
    exit $exit_code
}

trap cleanup EXIT

# Main execution
main() {
    local start_time=$(date +%s)
    
    log_section "🧪 $SCRIPT_NAME"
    log_info "Starting optimized CI test execution..."
    log_info "Configuration: Parallel=$MAX_PARALLEL_TESTS, Timeout=$TEST_TIMEOUT"
    
    # Step 1: Environment validation
    validate_environment
    
    # Step 2: Dependency setup
    setup_dependencies
    
    # Step 3: Static analysis
    run_static_analysis
    
    # Step 4: Execute tests
    run_test_suite
    
    # Step 5: Generate reports
    generate_reports
    
    # Step 6: Summary
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    log_section "📊 Test Execution Summary"
    log_success "All tests completed successfully!"
    log_info "Total execution time: ${duration}s"
    log_info "Test artifacts available in coverage/ directory"
}

validate_environment() {
    log_section "🔍 Environment Validation"
    
    # Check Flutter installation
    if ! command -v flutter &> /dev/null; then
        log_error "Flutter not found. Please install Flutter SDK."
        exit 1
    fi
    
    local flutter_version=$(flutter --version | head -n 1)
    log_info "Flutter version: $flutter_version"
    
    # Check if we're in the correct directory
    if [ ! -f "pubspec.yaml" ]; then
        log_error "pubspec.yaml not found. Please run from Flutter project root."
        exit 1
    fi
    
    # Validate test directory structure
    if [ ! -d "test" ]; then
        log_error "Test directory not found."
        exit 1
    fi
    
    local test_count=$(find test -name "*_test.dart" | wc -l)
    log_info "Found $test_count test files"
    
    log_success "Environment validation completed"
}

setup_dependencies() {
    log_section "📦 Dependency Setup"
    
    log_info "Installing Flutter dependencies..."
    if flutter pub get --suppress-analytics > /dev/null 2>&1; then
        log_success "Dependencies installed successfully"
    else
        log_error "Failed to install dependencies"
        exit 1
    fi
    
    # Generate mocks if needed
    if [ -f "build.yaml" ] && grep -q "build_runner" pubspec.yaml; then
        log_info "Generating mocks and code generation..."
        flutter packages pub run build_runner build --delete-conflicting-outputs > /dev/null 2>&1 || true
        log_success "Code generation completed"
    fi
    
    # Clean previous test artifacts
    log_info "Cleaning previous test artifacts..."
    rm -rf coverage/ .dart_tool/test_driver/
    
    log_success "Dependency setup completed"
}

run_static_analysis() {
    log_section "🔍 Static Analysis"
    
    log_info "Running Flutter analyze..."
    if flutter analyze --no-fatal-infos --no-fatal-warnings > /dev/null 2>&1; then
        log_success "Static analysis passed"
    else
        log_warning "Static analysis found issues (continuing with tests)"
        # Don't fail on analysis issues in CI
    fi
}

run_test_suite() {
    log_section "🧪 Test Suite Execution"
    
    local test_categories=("unit" "widget" "integration" "features")
    local failed_categories=()
    
    for category in "${test_categories[@]}"; do
        if [ -d "test/$category" ]; then
            run_test_category "$category" || failed_categories+=("$category")
        else
            log_warning "Test category '$category' not found, skipping"
        fi
    done
    
    # Check for failures
    if [ ${#failed_categories[@]} -gt 0 ]; then
        log_error "Failed test categories: ${failed_categories[*]}"
        exit 1
    fi
    
    log_success "All test categories passed"
}

run_test_category() {
    local category=$1
    local test_dir="test/$category"
    
    log_info "Running $category tests..."
    
    # Count tests in category
    local test_count=$(find "$test_dir" -name "*_test.dart" | wc -l)
    log_info "Found $test_count test files in $category"
    
    # Run tests with optimized settings
    local test_cmd="flutter test $test_dir"
    test_cmd+=" --reporter=compact"
    test_cmd+=" --timeout=$TEST_TIMEOUT"
    test_cmd+=" --concurrency=$MAX_PARALLEL_TESTS"
    
    # Add coverage for unit tests
    if [ "$category" = "unit" ]; then
        test_cmd+=" --coverage"
    fi
    
    # Execute tests
    if eval "$test_cmd" > /dev/null 2>&1; then
        log_success "$category tests passed ($test_count files)"
        return 0
    else
        log_error "$category tests failed"
        return 1
    fi
}

generate_reports() {
    log_section "📊 Report Generation"
    
    # Check if coverage was generated
    if [ -f "coverage/lcov.info" ]; then
        log_info "Processing coverage report..."
        
        # Install lcov if not available (for local development)
        if ! command -v lcov &> /dev/null; then
            if command -v brew &> /dev/null; then
                log_info "Installing lcov via Homebrew..."
                brew install lcov > /dev/null 2>&1 || true
            fi
        fi
        
        # Generate filtered coverage report
        if command -v lcov &> /dev/null; then
            # Remove generated files from coverage
            lcov --remove coverage/lcov.info \
                '*/generated/*' \
                '*/l10n/*' \
                '*/test/*' \
                '*/test_driver/*' \
                '*/integration_test/*' \
                '*/.dart_tool/*' \
                '*/build/*' \
                '**/main.dart' \
                -o coverage/lcov_filtered.info > /dev/null 2>&1
            
            # Generate HTML report
            genhtml coverage/lcov_filtered.info -o coverage/html --quiet > /dev/null 2>&1
            
            # Extract coverage percentage
            local coverage_summary=$(lcov --summary coverage/lcov_filtered.info 2>&1)
            local coverage_percentage=$(echo "$coverage_summary" | grep -o '[0-9]*\.[0-9]*%' | tail -1)
            
            if [ ! -z "$coverage_percentage" ]; then
                log_info "Test coverage: $coverage_percentage"
                
                # Check coverage threshold
                local coverage_numeric=$(echo "$coverage_percentage" | sed 's/%//')
                if (( $(echo "$coverage_numeric >= $COVERAGE_THRESHOLD" | bc -l) )); then
                    log_success "Coverage threshold met ($coverage_percentage >= $COVERAGE_THRESHOLD%)"
                else
                    log_warning "Coverage below threshold ($coverage_percentage < $COVERAGE_THRESHOLD%)"
                fi
            fi
            
            log_success "Coverage report generated: coverage/html/index.html"
        else
            log_warning "lcov not available, skipping HTML report generation"
        fi
    else
        log_warning "No coverage data generated"
    fi
    
    # Generate test summary
    generate_test_summary
}

generate_test_summary() {
    local summary_file="coverage/test-summary.txt"
    mkdir -p coverage
    
    cat > "$summary_file" << EOF
Flutter Test Execution Summary
==============================
Execution Date: $(date)
Script Version: CI Optimized v1.0

Test Configuration:
- Parallel Tests: $MAX_PARALLEL_TESTS
- Test Timeout: $TEST_TIMEOUT
- Coverage Threshold: $COVERAGE_THRESHOLD%

Test Categories Executed:
$(find test -type d -name "*" | grep -v "^test$" | sed 's/test\//- /' | sort)

Test Files Summary:
- Total Test Files: $(find test -name "*_test.dart" | wc -l)
- Unit Tests: $(find test/unit -name "*_test.dart" 2>/dev/null | wc -l || echo "0")
- Widget Tests: $(find test/widget* -name "*_test.dart" 2>/dev/null | wc -l || echo "0")
- Integration Tests: $(find test/integration -name "*_test.dart" 2>/dev/null | wc -l || echo "0")
- Feature Tests: $(find test/features -name "*_test.dart" 2>/dev/null | wc -l || echo "0")

Artifacts Generated:
- Coverage Report: coverage/html/index.html
- LCOV Data: coverage/lcov.info
- Filtered LCOV: coverage/lcov_filtered.info
- Test Summary: coverage/test-summary.txt

Status: SUCCESS
EOF
    
    log_success "Test summary generated: $summary_file"
}

# Execute main function
main "$@"
