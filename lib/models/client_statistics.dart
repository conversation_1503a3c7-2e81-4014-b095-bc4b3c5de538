class ClientStatistics {
  final int totalAppointments;
  final int completedAppointments;
  final int cancelledAppointments;
  final int noShowAppointments;
  final DateTime? lastVisitDate;
  final double totalRevenue;
  final double averageAppointmentValue;
  final int totalPets;
  final int upcomingAppointments;
  final double loyaltyScore; // 0-100 based on various factors

  ClientStatistics({
    required this.totalAppointments,
    required this.completedAppointments,
    required this.cancelledAppointments,
    required this.noShowAppointments,
    this.lastVisitDate,
    required this.totalRevenue,
    required this.averageAppointmentValue,
    required this.totalPets,
    required this.upcomingAppointments,
    required this.loyaltyScore,
  });

  // Calculated properties
  double get cancellationRate => 
      totalAppointments > 0 ? (cancelledAppointments / totalAppointments) * 100 : 0;

  double get noShowRate => 
      totalAppointments > 0 ? (noShowAppointments / totalAppointments) * 100 : 0;

  double get completionRate => 
      totalAppointments > 0 ? (completedAppointments / totalAppointments) * 100 : 0;

  // Convert from JSON
  factory ClientStatistics.fromJson(Map<String, dynamic> json) {
    return ClientStatistics(
      totalAppointments: json['totalAppointments'] ?? 0,
      completedAppointments: json['completedAppointments'] ?? 0,
      cancelledAppointments: json['cancelledAppointments'] ?? 0,
      noShowAppointments: json['noShowAppointments'] ?? 0,
      lastVisitDate: json['lastVisitDate'] != null 
          ? DateTime.parse(json['lastVisitDate']) 
          : null,
      totalRevenue: (json['totalRevenue'] ?? 0).toDouble(),
      averageAppointmentValue: (json['averageAppointmentValue'] ?? 0).toDouble(),
      totalPets: json['totalPets'] ?? 0,
      upcomingAppointments: json['upcomingAppointments'] ?? 0,
      loyaltyScore: (json['loyaltyScore'] ?? 0).toDouble(),
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'totalAppointments': totalAppointments,
      'completedAppointments': completedAppointments,
      'cancelledAppointments': cancelledAppointments,
      'noShowAppointments': noShowAppointments,
      'lastVisitDate': lastVisitDate?.toIso8601String(),
      'totalRevenue': totalRevenue,
      'averageAppointmentValue': averageAppointmentValue,
      'totalPets': totalPets,
      'upcomingAppointments': upcomingAppointments,
      'loyaltyScore': loyaltyScore,
    };
  }
}
