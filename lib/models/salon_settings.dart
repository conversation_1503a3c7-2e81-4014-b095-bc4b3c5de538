class SalonSettings {
  final String id;
  final String name;
  final String address;
  final String phone;
  final String email;
  final String website;
  final Map<String, dynamic> businessHours;
  final List<DateTime> holidays;
  final Map<String, dynamic> notificationSettings;
  final Map<String, dynamic> smsSettings;
  final DateTime updatedAt;

  SalonSettings({
    required this.id,
    required this.name,
    required this.address,
    required this.phone,
    required this.email,
    this.website = '',
    required this.businessHours,
    this.holidays = const [],
    this.notificationSettings = const {},
    this.smsSettings = const {},
    required this.updatedAt,
  });

  factory SalonSettings.fromJson(Map<String, dynamic> json) {
    return SalonSettings(
      id: json['id'],
      name: json['name'],
      address: json['address'],
      phone: json['phone'],
      email: json['email'],
      website: json['website'] ?? '',
      businessHours: Map<String, dynamic>.from(json['businessHours']),
      holidays: (json['holidays'] as List<dynamic>?)
          ?.map((date) => DateTime.parse(date))
          .toList() ?? [],
      notificationSettings: Map<String, dynamic>.from(json['notificationSettings'] ?? {}),
      smsSettings: Map<String, dynamic>.from(json['smsSettings'] ?? {}),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'phone': phone,
      'email': email,
      'website': website,
      'businessHours': businessHours,
      'holidays': holidays.map((date) => date.toIso8601String()).toList(),
      'notificationSettings': notificationSettings,
      'smsSettings': smsSettings,
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  SalonSettings copyWith({
    String? id,
    String? name,
    String? address,
    String? phone,
    String? email,
    String? website,
    Map<String, dynamic>? businessHours,
    List<DateTime>? holidays,
    Map<String, dynamic>? notificationSettings,
    Map<String, dynamic>? smsSettings,
    DateTime? updatedAt,
  }) {
    return SalonSettings(
      id: id ?? this.id,
      name: name ?? this.name,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      website: website ?? this.website,
      businessHours: businessHours ?? this.businessHours,
      holidays: holidays ?? this.holidays,
      notificationSettings: notificationSettings ?? this.notificationSettings,
      smsSettings: smsSettings ?? this.smsSettings,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}



class DashboardStats {
  final int totalClients;
  final int totalPets;
  final int todayAppointments;
  final int weekAppointments;
  final double weekRevenue;
  final double monthRevenue;
  final int newClientsThisMonth;
  final double averageRating;
  final int totalReviews;
  final Map<String, int> serviceStats;
  final Map<String, double> groomerStats;

  DashboardStats({
    required this.totalClients,
    required this.totalPets,
    required this.todayAppointments,
    required this.weekAppointments,
    required this.weekRevenue,
    required this.monthRevenue,
    required this.newClientsThisMonth,
    required this.averageRating,
    required this.totalReviews,
    required this.serviceStats,
    required this.groomerStats,
  });

  factory DashboardStats.fromJson(Map<String, dynamic> json) {
    return DashboardStats(
      totalClients: json['totalClients'] ?? 0,
      totalPets: json['totalPets'] ?? 0,
      todayAppointments: json['todayAppointments'] ?? 0,
      weekAppointments: json['weekAppointments'] ?? 0,
      weekRevenue: (json['weekRevenue'] ?? 0.0).toDouble(),
      monthRevenue: (json['monthRevenue'] ?? 0.0).toDouble(),
      newClientsThisMonth: json['newClientsThisMonth'] ?? 0,
      averageRating: (json['averageRating'] ?? 0.0).toDouble(),
      totalReviews: json['totalReviews'] ?? 0,
      serviceStats: json['serviceStats'] != null
          ? Map<String, int>.from(json['serviceStats'])
          : <String, int>{},
      groomerStats: json['groomerStats'] != null
          ? Map<String, double>.from(json['groomerStats'])
          : <String, double>{},
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'totalClients': totalClients,
      'totalPets': totalPets,
      'todayAppointments': todayAppointments,
      'weekAppointments': weekAppointments,
      'weekRevenue': weekRevenue,
      'monthRevenue': monthRevenue,
      'newClientsThisMonth': newClientsThisMonth,
      'averageRating': averageRating,
      'totalReviews': totalReviews,
      'serviceStats': serviceStats,
      'groomerStats': groomerStats,
    };
  }
}
