import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:http/http.dart' as http;
import '../utils/logging_client.dart';
import '../models/api_response.dart';
import '../config/api_config.dart';

class ApiService {
  static String get baseUrl => ApiConfig.baseUrl;

  static Duration get defaultTimeout => ApiConfig.timeout;

  // HTTP client used for all requests with logging enabled
  static final http.Client _client = LoggingClient(http.Client());

  // Headers for all requests
  static Map<String, String> get _headers {
    final headers = _authToken != null
        ? ApiConfig.getAuthHeaders(_authToken!)
        : ApiConfig.defaultHeaders;

    // Debug logging for headers (excluding sensitive token details)
    if (ApiConfig.enableLogging) {
      final debugHeaders = Map<String, String>.from(headers);
      if (debugHeaders.containsKey('Authorization')) {
        final authValue = debugHeaders['Authorization']!;
        if (authValue.startsWith('Bearer ')) {
          final tokenPreview = authValue.length > 20
              ? '${authValue.substring(0, 20)}...'
              : authValue;
          debugHeaders['Authorization'] = tokenPreview;
        }
      }
    }

    return headers;
  }

  static String? _authToken;

  // Set authentication token
  static void setAuthToken(String? token) {
    _authToken = token;
    if (ApiConfig.enableLogging) {
      if (token != null) {
        final tokenPreview = token;
        debugPrint('🔑 Auth token set: $tokenPreview');
      } else {
        debugPrint('🔑 Auth token cleared');
      }
    }
  }

  // Get authentication token
  static String? get authToken => _authToken;

  // Clear authentication token
  static void clearAuthToken() {
    _authToken = null;
    if (ApiConfig.enableLogging) {
      debugPrint('🔑 Auth token cleared');
    }
  }

  // Check if we have a valid auth token
  static bool get hasAuthToken => _authToken != null && _authToken!.isNotEmpty;

  // Public getter for headers (for testing)
  static Map<String, String> get headers => _headers;

  // Generic GET request
  static Future<ApiResponse<T>> get<T>(String endpoint, {
    Map<String, String>? queryParams,
    T Function(dynamic)? fromJson,
    bool requiresAuth = true,
  }) async {
    // Check authentication if required
    if (requiresAuth && !hasAuthToken) {
      // Try to restore token from storage as a fallback
      await _restoreAuthTokenFromStorage();

      if (!hasAuthToken) {
        if (ApiConfig.enableLogging) {
          debugPrint(
              '🚫 GET request requires authentication but no token available: $endpoint');
        }
        return ApiResponse.error('Authentication required');
      }
    }

    try {
      debugPrint('GET: $endpoint');
      debugPrint('GET Query Params: $queryParams');
      debugPrint('GET Headers: $_headers');
      final uri = Uri.parse('$baseUrl$endpoint');
      final finalUri = queryParams != null
          ? uri.replace(queryParameters: queryParams)
          : uri;

      if (ApiConfig.enableLogging) {
        debugPrint('📤 GET: $finalUri');
        debugPrint('🔑 Auth token available: $hasAuthToken');
        if (hasAuthToken && _authToken != null) {
          final tokenPreview = _authToken!.length > 20
              ? '${_authToken!.substring(0, 20)}...'
              : _authToken!;
        }
      }

      final response = await _client.get(
        finalUri,
        headers: _headers,
      ).timeout(defaultTimeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ GET Error: $e');
      }
      return ApiResponse.error(_getErrorMessage(e));
    }
  }

  // Generic POST request
  static Future<ApiResponse<T>> post<T>(String endpoint, {
    Map<String, dynamic>? body,
    T Function(dynamic)? fromJson,
    bool requiresAuth = true,
  }) async {
    // Check authentication if required
    // Special handling for auth endpoints that require authentication
    final isAuthEndpoint = endpoint.contains('/auth/');
    final needsAuth = requiresAuth && (!isAuthEndpoint ||
        endpoint.contains('/auth/profile') ||
        endpoint.contains('/auth/phone') ||
        endpoint.contains('/auth/logout'));

    if (needsAuth && !hasAuthToken) {
      // Try to restore token from storage as a fallback
      await _restoreAuthTokenFromStorage();

      if (!hasAuthToken) {
        if (ApiConfig.enableLogging) {
          debugPrint(
              '🚫 POST request requires authentication but no token available: $endpoint');
        }
        return ApiResponse.error('Authentication required');
      }
    }

    try {
      final uri = Uri.parse('$baseUrl$endpoint');

      if (ApiConfig.enableLogging) {
        debugPrint('🚀 === API SERVICE POST REQUEST START ===');
        debugPrint('📤 POST URI: $uri');
        debugPrint('🔗 Endpoint: $endpoint');
        debugPrint('🔑 Has auth token: $hasAuthToken');
        debugPrint('🔑 Auth token length: ${_authToken?.length ?? 0}');
        debugPrint('📋 Headers: $_headers');
        if (body != null) {
          // Don't log sensitive auth data
          if (endpoint.contains('/auth/')) {
            debugPrint('📝 Body: [Auth request - body hidden for security]');
          } else {
            debugPrint('📝 Body: ${jsonEncode(body)}');
          }
        } else {
          debugPrint('📝 Body: null');
        }
      }

      debugPrint('🌐 Making HTTP POST request...');
      final response = await _client.post(
        uri,
        headers: _headers,
        body: body != null ? jsonEncode(body) : null,
      ).timeout(defaultTimeout);

      debugPrint('📡 HTTP POST response received');
      debugPrint('📊 Status code: ${response.statusCode}');
      debugPrint('📄 Response body length: ${response.body.length}');

      final result = _handleResponse<T>(response, fromJson);
      debugPrint('🏁 === API SERVICE POST REQUEST END ===');
      return result;
    } catch (e) {
      debugPrint('💥 === API SERVICE POST REQUEST EXCEPTION ===');
      debugPrint('❌ Exception: $e');
      debugPrint('❌ Exception type: ${e.runtimeType}');
      debugPrint('❌ Stack trace: ${StackTrace.current}');
      if (ApiConfig.enableLogging) {
        debugPrint('❌ POST Error: $e');
      }
      debugPrint('🏁 === API SERVICE POST REQUEST EXCEPTION END ===');
      return ApiResponse.error(_getErrorMessage(e));
    }
  }

  // Generic PUT request
  static Future<ApiResponse<T>> put<T>(String endpoint, {
    Map<String, dynamic>? body,
    T Function(dynamic)? fromJson,
    bool requiresAuth = true,
  }) async {
    // Check authentication if required
    if (requiresAuth && !hasAuthToken) {
      // Try to restore token from storage as a fallback
      await _restoreAuthTokenFromStorage();

      if (!hasAuthToken) {
        if (ApiConfig.enableLogging) {
          debugPrint(
              '🚫 PUT request requires authentication but no token available: $endpoint');
        }
        return ApiResponse.error('Authentication required');
      }
    }

    try {
      final uri = Uri.parse('$baseUrl$endpoint');

      if (ApiConfig.enableLogging) {
        debugPrint('📤 PUT: $uri');
        if (body != null) {
          debugPrint('📝 Body: ${jsonEncode(body)}');
        }
      }

      final response = await _client.put(
        uri,
        headers: _headers,
        body: body != null ? jsonEncode(body) : null,
      ).timeout(defaultTimeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ PUT Error: $e');
      }
      return ApiResponse.error(_getErrorMessage(e));
    }
  }

  // Generic PATCH request
  static Future<ApiResponse<T>> patch<T>(String endpoint, {
    Map<String, dynamic>? body,
    T Function(dynamic)? fromJson,
    bool requiresAuth = true,
  }) async {
    // Check authentication if required
    if (requiresAuth && !hasAuthToken) {
      debugPrint('🚫 === PATCH AUTH CHECK FAILED ===');
      debugPrint('🚫 Endpoint: $endpoint');
      debugPrint('🚫 Requires auth: $requiresAuth');
      debugPrint('🚫 Has auth token: $hasAuthToken');
      debugPrint('🚫 Auth token value: ${_authToken ?? 'NULL'}');
      debugPrint('🚫 Auth token length: ${_authToken?.length ?? 0}');

      // Try to restore token from storage as a fallback
      debugPrint('🔄 Attempting to restore auth token from storage...');
      await _restoreAuthTokenFromStorage();

      // Check again after restoration attempt
      if (!hasAuthToken) {
        if (ApiConfig.enableLogging) {
          debugPrint(
              '🚫 PATCH request requires authentication but no token available: $endpoint');
        }
        return ApiResponse.error('Authentication required');
      } else {
        debugPrint('✅ Auth token restored from storage successfully');
      }
    }

    try {
      final uri = Uri.parse('$baseUrl$endpoint');

      if (ApiConfig.enableLogging) {
        debugPrint('📤 PATCH: $uri');
        if (body != null) {
          debugPrint('📝 Body: ${jsonEncode(body)}');
        }
      }

      final response = await _client.patch(
        uri,
        headers: _headers,
        body: body != null ? jsonEncode(body) : null,
      ).timeout(defaultTimeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ PATCH Error: $e');
      }
      return ApiResponse.error(_getErrorMessage(e));
    }
  }

  // Generic DELETE request
  static Future<ApiResponse<T>> delete<T>(String endpoint, {
    Map<String, dynamic>? body,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');

      if (ApiConfig.enableLogging) {
        debugPrint('DELETE: $uri');
        if (body != null) {
          debugPrint('DELETE Body: $body');
        }
      }

      final response = await _client.delete(
        uri,
        headers: _headers,
        body: body != null ? jsonEncode(body) : null,
      ).timeout(defaultTimeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      debugPrint('DELETE Error: $e');
      return ApiResponse.error(_getErrorMessage(e));
    }
  }

  // Handle HTTP response
  static ApiResponse<T> _handleResponse<T>(http.Response response,
      T Function(dynamic)? fromJson,) {
    debugPrint('🔍 === API SERVICE RESPONSE HANDLING START ===');
    debugPrint('📥 Response Status: ${response.statusCode}');
    debugPrint('📥 Response Headers: ${response.headers}');
    debugPrint('📥 Response Body: ${response.body}');
    debugPrint('📥 Response Body Length: ${response.body.length}');
    debugPrint('📥 Response Content-Type: ${response.headers['content-type']}');

    if (ApiConfig.enableLogging) {
      debugPrint('📥 Response Status: ${response.statusCode}');
      // debugPrint('📥 Response Headers: ${response.headers}');
      // debugPrint('📥 Response Body: ${response.body}');
      // debugPrint('📥 Response Body Length: ${response.body.length}');
    }

    // Handle authentication errors
    if (response.statusCode == 401 || response.statusCode == 403) {
      if (ApiConfig.enableLogging) {
        debugPrint('🚫 Authentication error: ${response.statusCode}');
        debugPrint('🔑 Current token exists: ${_authToken != null}');
        debugPrint('📥 Response body: ${response.body}');
        if (_authToken != null) {
          final tokenPreview = _authToken!.length > 20
              ? '${_authToken!.substring(0, 20)}...'
              : _authToken!;
        }
      }

      // Only clear token on 401 (unauthorized), not 403 (forbidden)
      // 403 might be a permissions issue, not an invalid token
      if (response.statusCode == 401) {
        clearAuthToken();
      }

      // For 403 errors, check if the response contains specific validation errors
      // instead of generic permission errors
      if (response.statusCode == 403) {
        try {
          final Map<String, dynamic> jsonData = jsonDecode(response.body);

          // Check if this is a validation error with specific message
          if (jsonData['success'] == false &&
              jsonData['error'] != null &&
              _isValidationError(jsonData['error'].toString())) {

            if (ApiConfig.enableLogging) {
              debugPrint('🔍 403 contains validation error, preserving original message: ${jsonData['error']}');
            }

            // Return the validation error instead of generic permission error
            // Use the same error handling logic as the main response processing
            return ApiResponse.error(
              jsonData['error']?.toString() ?? 'Validation error',
              statusCode: response.statusCode,
            );
          }
        } catch (e) {
          // If JSON parsing fails, fall through to generic permission error
          if (ApiConfig.enableLogging) {
            debugPrint('🔍 Failed to parse 403 response body for validation errors: $e');
          }
        }
      }

      return ApiResponse.error(
        response.statusCode == 401
            ? 'Sesiunea a expirat. Vă rugăm să vă autentificați din nou.'
            : 'Nu aveți permisiunea să accesați această resursă.',
        statusCode: response.statusCode,
      );
    }

    try {
      if (ApiConfig.enableLogging) {
        // debugPrint('📋 Attempting to parse JSON response...');
      }

      final Map<String, dynamic> jsonData = jsonDecode(response.body);

      if (ApiConfig.enableLogging) {
        // debugPrint('📋 JSON parsed successfully');
        // debugPrint('📋 JSON keys: ${jsonData.keys.toList()}');
      }

      if (response.statusCode >= 200 && response.statusCode < 300) {
        // Success response
        debugPrint('✅ Success response detected');
        debugPrint('📄 JSON data keys: ${jsonData.keys.toList()}');
        debugPrint('📄 Has data field: ${jsonData.containsKey('data')}');
        debugPrint('📄 Data value: ${jsonData['data']}');
        debugPrint('📄 Message: ${jsonData['message']}');

        if (fromJson != null && jsonData['data'] != null) {
          debugPrint('🔄 Using fromJson to parse data');
          final data = fromJson(jsonData['data']);
          debugPrint('✅ Data parsed successfully: $data');
          debugPrint('🔍 === API SERVICE RESPONSE HANDLING END (SUCCESS WITH PARSER) ===');
          return ApiResponse.success(data, message: jsonData['message']);
        } else {
          debugPrint('📄 Returning raw data without parsing');
          debugPrint('🔍 === API SERVICE RESPONSE HANDLING END (SUCCESS RAW) ===');
          return ApiResponse.success(
              jsonData['data'], message: jsonData['message']);
        }
      } else {
        // Error response - handle multiple error formats
        String errorMessage = 'Unknown error';
        String? errorCode;
        Map<String, dynamic>? errorDetails;

        // Check for top-level errorCode and details (current backend format)
        if (jsonData.containsKey('errorCode')) {
          errorCode = jsonData['errorCode'];
          errorDetails = jsonData['details'];

          // Get error message from 'error' field or 'message' field
          if (jsonData['error'] is String) {
            errorMessage = jsonData['error'];
          } else if (jsonData['message'] is String) {
            errorMessage = jsonData['message'];
          }

          if (ApiConfig.enableLogging) {
            debugPrint('📋 Parsed top-level error format:');
            debugPrint('  - errorCode: $errorCode');
            debugPrint('  - errorMessage: $errorMessage');
            debugPrint('  - errorDetails keys: ${errorDetails?.keys.toList()}');
          }
        } else {
          // Handle nested error format: {error: {message: "...", code: "..."}}
          final errorData = jsonData['error'];

          if (errorData is Map<String, dynamic>) {
            errorMessage = errorData['message'] ?? 'Unknown error';
            errorCode = errorData['code'];
            errorDetails = errorData;
          } else if (errorData is String) {
            errorMessage = errorData;
          } else if (jsonData['message'] is String) {
            errorMessage = jsonData['message'];
          }

          if (ApiConfig.enableLogging) {
            debugPrint('📋 Parsed nested error format:');
            debugPrint('  - errorCode: $errorCode');
            debugPrint('  - errorMessage: $errorMessage');
            debugPrint('  - errorDetails: $errorDetails');
          }
        }

        debugPrint('❌ Returning error response');
        debugPrint('❌ Error message: $errorMessage');
        debugPrint('❌ Error code: $errorCode');
        debugPrint('❌ Status code: ${response.statusCode}');
        debugPrint('🔍 === API SERVICE RESPONSE HANDLING END (ERROR) ===');
        return ApiResponse.error(
          errorMessage,
          statusCode: response.statusCode,
          errorCode: errorCode,
          errorDetails: errorDetails,
        );
      }
    } catch (e) {
      debugPrint('💥 JSON Parse Exception in _handleResponse');
      debugPrint('❌ Exception: $e');
      debugPrint('❌ Exception type: ${e.runtimeType}');
      if (ApiConfig.enableLogging) {
        debugPrint('❌ JSON Parse Error: $e');
        debugPrint('❌ Response body that failed to parse: "${response.body}"');
        debugPrint('❌ Response body type: ${response.body.runtimeType}');
        debugPrint('❌ Response content-type: ${response.headers['content-type']}');
      }
      debugPrint('🔍 === API SERVICE RESPONSE HANDLING END (PARSE ERROR) ===');
      return ApiResponse.error(
        'Failed to parse response',
        statusCode: response.statusCode,
      );
    }
  }

  // Check if error message is a validation error that should be preserved
  static bool _isValidationError(String errorMessage) {
    return errorMessage.contains('Staff schedule violates business hours constraints') ||
           errorMessage.contains('Invalid time format') ||
           errorMessage.contains('Start time must be before end time') ||
           errorMessage.contains('Break time must be within working hours') ||
           errorMessage.contains('Staff cannot work on') ||
           errorMessage.contains('business end time') ||
           errorMessage.contains('business start time');
  }

  // Get user-friendly error message
  static String _getErrorMessage(dynamic error) {
    if (error is SocketException) {
      return 'Nu se poate conecta la server. Verificați conexiunea la internet.';
    } else if (error is HttpException) {
      return 'Eroare de comunicare cu serverul.';
    } else if (error.toString().contains('TimeoutException')) {
      return 'Timpul de așteptare a expirat. Încercați din nou.';
    } else {
      return 'A apărut o eroare neașteptată: ${error.toString()}';
    }
  }

  // Retry mechanism for failed requests
  static Future<ApiResponse<T>> retryRequest<T>(
      Future<ApiResponse<T>> Function() request, {
        int maxRetries = 3,
        Duration delay = const Duration(seconds: 1),
      }) async {
    ApiResponse<T>? lastResponse;

    for (int i = 0; i < maxRetries; i++) {
      lastResponse = await request();

      if (lastResponse.success) {
        return lastResponse;
      }

      if (i < maxRetries - 1) {
        await Future.delayed(delay * (i + 1)); // Exponential backoff
      }
    }

    return lastResponse!;
  }

  // Upload file (for future use)
  static Future<ApiResponse<T>> uploadFile<T>(String endpoint,
      String filePath, {
        Map<String, String>? fields,
        T Function(dynamic)? fromJson,
      }) async {
    try {
      final uri = Uri.parse('$baseUrl$endpoint');
      final request = http.MultipartRequest('POST', uri);

      // Add headers
      request.headers.addAll(_headers);

      // Add file
      request.files.add(await http.MultipartFile.fromPath('file', filePath));

      // Add fields
      if (fields != null) {
        request.fields.addAll(fields);
      }

      if (ApiConfig.enableLogging) {
        debugPrint('UPLOAD: $uri');
      }

      final streamedResponse = await _client.send(request).timeout(defaultTimeout);
      final response = await http.Response.fromStream(streamedResponse);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('UPLOAD Error: $e');
      }
      return ApiResponse.error(_getErrorMessage(e));
    }
  }

  // Helper method to restore auth token from storage
  static Future<void> _restoreAuthTokenFromStorage() async {
    try {
      // Import AuthService to access token storage
      final storedToken = await _getStoredAccessToken();
      if (storedToken != null && storedToken.isNotEmpty) {
        setAuthToken(storedToken);
        if (ApiConfig.enableLogging) {
          debugPrint('🔄 Auth token restored from storage');
        }
      } else {
        if (ApiConfig.enableLogging) {
          debugPrint('🚫 No stored auth token found');
        }
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error restoring auth token from storage: $e');
      }
    }
  }

  // Helper method to get stored access token (to avoid circular dependency)
  static Future<String?> _getStoredAccessToken() async {
    try {
      const storage = FlutterSecureStorage();
      return await storage.read(key: 'access_token');
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error reading stored access token: $e');
      }
      return null;
    }
  }

  // Validate current authentication state
  static Future<bool> validateAuthState() async {
    if (!hasAuthToken) {
      if (ApiConfig.enableLogging) {
        debugPrint('🔑 No auth token available, attempting to restore...');
      }
      await _restoreAuthTokenFromStorage();

      if (!hasAuthToken) {
        if (ApiConfig.enableLogging) {
          debugPrint('🔑 Still no auth token available after restore attempt');
        }
        return false;
      }
    }

    try {
      // Try a simple authenticated request to verify token validity
      // Use /api/auth/profile since it's a standard auth endpoint that should always exist
      final response = await get<Map<String, dynamic>>(
        '/api/auth/profile',
        fromJson: (data) => Map<String, dynamic>.from(data),
        requiresAuth: true,
      );

      final isValid = response.success;
      if (ApiConfig.enableLogging) {
        debugPrint('🔑 Auth token validation: ${isValid ? 'VALID' : 'INVALID'}');
        if (!isValid) {
          debugPrint('🔑 Auth validation error: ${response.error}');
          debugPrint('🔑 Auth validation status: ${response.statusCode}');
        }
      }

      return isValid;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('🔑 Auth validation failed: $e');
      }
      return false;
    }
  }
}