import 'package:flutter/foundation.dart';
import '../models/notification_settings.dart';
import '../services/notification_settings_service.dart';
import '../config/api_config.dart';

/// Helper service for triggering notifications throughout the app
/// This service respects user notification settings and provides easy integration
class NotificationHelper {
  
  /// Send notification for new appointment
  static Future<bool> sendNewAppointmentNotification({
    required String appointmentId,
    required String clientName,
    required String petName,
    required DateTime appointmentDate,
    required String groomerName,
  }) async {
    try {
      // Check if notifications should be sent
      final shouldSend = await NotificationSettingsService.shouldSendNotification(
        notificationType: 'new_appointment',
        priority: NotificationPriority.normal,
      );

      if (!shouldSend) {
        if (ApiConfig.enableLogging) {
          debugPrint('🔔 New appointment notification blocked by user settings');
        }
        return false;
      }

      // Get notification settings for customization
      final settingsResponse = await NotificationSettingsService.getNotificationSettings();
      final settings = settingsResponse.data;

      // Prepare notification data
      final notificationData = {
        'type': 'new_appointment',
        'appointmentId': appointmentId,
        'title': 'Programare nouă',
        'body': 'Programare nouă pentru $petName ($clientName) pe ${_formatDate(appointmentDate)}',
        'data': {
          'appointmentId': appointmentId,
          'clientName': clientName,
          'petName': petName,
          'appointmentDate': appointmentDate.toIso8601String(),
          'groomerName': groomerName,
        },
        'sound': settings?.soundPreference ?? 'default',
        'vibration': settings?.vibrationEnabled ?? true,
        'priority': settings?.notificationRules.defaultPriority.value ?? 'NORMAL',
      };

      // Send notification (integrate with your push notification service)
      final success = await _sendPushNotification(notificationData);

      if (ApiConfig.enableLogging) {
        debugPrint('🔔 New appointment notification sent: $success');
      }

      return success;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error sending new appointment notification: $e');
      }
      return false;
    }
  }

  /// Send notification for appointment cancellation
  static Future<bool> sendAppointmentCancellationNotification({
    required String appointmentId,
    required String clientName,
    required String petName,
    required DateTime appointmentDate,
    required String reason,
  }) async {
    try {
      final shouldSend = await NotificationSettingsService.shouldSendNotification(
        notificationType: 'appointment_cancellation',
        priority: NotificationPriority.normal,
      );

      if (!shouldSend) return false;

      final settingsResponse = await NotificationSettingsService.getNotificationSettings();
      final settings = settingsResponse.data;

      final notificationData = {
        'type': 'appointment_cancellation',
        'appointmentId': appointmentId,
        'title': 'Programare anulată',
        'body': 'Programarea pentru $petName ($clientName) pe ${_formatDate(appointmentDate)} a fost anulată',
        'data': {
          'appointmentId': appointmentId,
          'clientName': clientName,
          'petName': petName,
          'appointmentDate': appointmentDate.toIso8601String(),
          'reason': reason,
        },
        'sound': settings?.soundPreference ?? 'default',
        'vibration': settings?.vibrationEnabled ?? true,
        'priority': settings?.notificationRules.defaultPriority.value ?? 'NORMAL',
      };

      return await _sendPushNotification(notificationData);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error sending cancellation notification: $e');
      }
      return false;
    }
  }

  /// Send notification for payment confirmation
  static Future<bool> sendPaymentConfirmationNotification({
    required String paymentId,
    required String clientName,
    required double amount,
    required String paymentMethod,
  }) async {
    try {
      final shouldSend = await NotificationSettingsService.shouldSendNotification(
        notificationType: 'payment_confirmation',
        priority: NotificationPriority.normal,
      );

      if (!shouldSend) return false;

      final settingsResponse = await NotificationSettingsService.getNotificationSettings();
      final settings = settingsResponse.data;

      final notificationData = {
        'type': 'payment_confirmation',
        'paymentId': paymentId,
        'title': 'Plată confirmată',
        'body': 'Plată de ${amount.toStringAsFixed(2)} RON de la $clientName confirmată',
        'data': {
          'paymentId': paymentId,
          'clientName': clientName,
          'amount': amount,
          'paymentMethod': paymentMethod,
        },
        'sound': settings?.soundPreference ?? 'default',
        'vibration': settings?.vibrationEnabled ?? true,
        'priority': settings?.notificationRules.defaultPriority.value ?? 'NORMAL',
      };

      return await _sendPushNotification(notificationData);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error sending payment confirmation notification: $e');
      }
      return false;
    }
  }

  /// Send notification for team member updates
  static Future<bool> sendTeamMemberUpdateNotification({
    required String updateType, // 'joined', 'left', 'role_changed'
    required String memberName,
    required String? newRole,
  }) async {
    try {
      final shouldSend = await NotificationSettingsService.shouldSendNotification(
        notificationType: 'team_member_update',
        priority: NotificationPriority.normal,
      );

      if (!shouldSend) return false;

      final settingsResponse = await NotificationSettingsService.getNotificationSettings();
      final settings = settingsResponse.data;

      String title = 'Actualizare echipă';
      String body = '';

      switch (updateType) {
        case 'joined':
          body = '$memberName s-a alăturat echipei';
          break;
        case 'left':
          body = '$memberName a părăsit echipa';
          break;
        case 'role_changed':
          body = 'Rolul lui $memberName a fost schimbat în $newRole';
          break;
        default:
          body = 'Actualizare echipă: $memberName';
      }

      final notificationData = {
        'type': 'team_member_update',
        'title': title,
        'body': body,
        'data': {
          'updateType': updateType,
          'memberName': memberName,
          'newRole': newRole,
        },
        'sound': settings?.soundPreference ?? 'default',
        'vibration': settings?.vibrationEnabled ?? true,
        'priority': settings?.notificationRules.defaultPriority.value ?? 'NORMAL',
      };

      return await _sendPushNotification(notificationData);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error sending team update notification: $e');
      }
      return false;
    }
  }

  /// Send critical system maintenance notification
  static Future<bool> sendSystemMaintenanceNotification({
    required String maintenanceType,
    required DateTime scheduledTime,
    required Duration estimatedDuration,
    required String description,
  }) async {
    try {
      final shouldSend = await NotificationSettingsService.shouldSendNotification(
        notificationType: 'system_maintenance',
        priority: NotificationPriority.critical,
      );

      if (!shouldSend) return false;

      final settingsResponse = await NotificationSettingsService.getNotificationSettings();
      final settings = settingsResponse.data;

      final notificationData = {
        'type': 'system_maintenance',
        'title': 'Mentenanță sistem programată',
        'body': 'Mentenanță $maintenanceType programată pe ${_formatDate(scheduledTime)}',
        'data': {
          'maintenanceType': maintenanceType,
          'scheduledTime': scheduledTime.toIso8601String(),
          'estimatedDuration': estimatedDuration.inMinutes,
          'description': description,
        },
        'sound': settings?.soundPreference ?? 'default',
        'vibration': settings?.vibrationEnabled ?? true,
        'priority': 'CRITICAL', // Always critical for system maintenance
      };

      return await _sendPushNotification(notificationData);
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error sending maintenance notification: $e');
      }
      return false;
    }
  }

  /// Format date for display in notifications
  static String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')}.${date.year} la ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
  }

  /// Send push notification (integrate with your push notification service)
  /// This is a placeholder - replace with your actual push notification implementation
  static Future<bool> _sendPushNotification(Map<String, dynamic> notificationData) async {
    try {
      // Integrate with push notification service (Firebase, OneSignal, etc.)
      // Example implementation:
      
      if (ApiConfig.enableLogging) {
        debugPrint('🔔 Sending push notification:');
        debugPrint('   Type: ${notificationData['type']}');
        debugPrint('   Title: ${notificationData['title']}');
        debugPrint('   Body: ${notificationData['body']}');
        debugPrint('   Sound: ${notificationData['sound']}');
        debugPrint('   Vibration: ${notificationData['vibration']}');
        debugPrint('   Priority: ${notificationData['priority']}');
      }

      // Simulate successful notification sending
      await Future.delayed(const Duration(milliseconds: 100));
      return true;

      // Real implementation would look like:
      // final response = await FirebaseMessaging.instance.sendMessage(
      //   RemoteMessage(
      //     notification: RemoteNotification(
      //       title: notificationData['title'],
      //       body: notificationData['body'],
      //     ),
      //     data: Map<String, String>.from(notificationData['data']),
      //     android: AndroidNotification(
      //       sound: notificationData['sound'],
      //       priority: notificationData['priority'] == 'CRITICAL' 
      //         ? AndroidNotificationPriority.high 
      //         : AndroidNotificationPriority.normal,
      //     ),
      //   ),
      // );
      // return response.messageId != null;

    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error in _sendPushNotification: $e');
      }
      return false;
    }
  }

  /// Check if notifications are enabled for the current salon
  static Future<bool> areNotificationsEnabled() async {
    try {
      final response = await NotificationSettingsService.getNotificationSettings();
      return response.success && (response.data?.pushNotificationsEnabled ?? false);
    } catch (e) {
      return false;
    }
  }

  /// Get current notification settings for the salon
  static Future<NotificationSettings?> getCurrentSettings() async {
    try {
      final response = await NotificationSettingsService.getNotificationSettings();
      return response.success ? response.data : null;
    } catch (e) {
      return null;
    }
  }
}
