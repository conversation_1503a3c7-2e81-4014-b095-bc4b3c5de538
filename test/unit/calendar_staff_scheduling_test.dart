import 'package:flutter_test/flutter_test.dart';
import 'package:partykidsapp/providers/calendar_provider.dart';
import 'package:partykidsapp/utils/romanian_holidays.dart';

void main() {
  group('Calendar Staff Scheduling Tests', () {
    late CalendarProvider provider;

    setUp(() {
      provider = CalendarProvider();
    });

    group('Romanian Holidays Integration', () {
      test('should detect Romanian national holidays correctly', () {
        // Test fixed holidays
        expect(RomanianHolidays.isHoliday(DateTime(2024, 1, 1)), isTrue); // New Year
        expect(RomanianHolidays.isHoliday(DateTime(2024, 12, 25)), isTrue); // Christmas
        expect(RomanianHolidays.isHoliday(DateTime(2024, 12, 1)), isTrue); // National Day

        // Test non-holidays
        expect(RomanianHolidays.isHoliday(DateTime(2024, 6, 15)), isFalse); // Random date
      });

      test('should get holiday names correctly', () {
        final newYear = RomanianHolidays.getHolidayForDate(DateTime(2024, 1, 1));
        expect(newYear?.name, equals('Anul Nou'));

        final christmas = RomanianHolidays.getHolidayForDate(DateTime(2024, 12, 25));
        expect(christmas?.name, equals('Crăciunul'));

        final nationalDay = RomanianHolidays.getHolidayForDate(DateTime(2024, 12, 1));
        expect(nationalDay?.name, equals('Ziua Națională a României'));
      });

      test('should calculate Easter-dependent holidays correctly', () {
        // Test that Easter holidays are calculated for 2024
        final holidays2024 = RomanianHolidays.getHolidaysForYear(2024);

        // Should include Easter-dependent holidays
        final easterHolidays = holidays2024.where((h) =>
          h.name.contains('Paște') ||
          h.name.contains('Rusalii') ||
          h.name.contains('Vinerea Mare')
        ).toList();

        expect(easterHolidays.length, greaterThan(0));
      });

      test('should handle weekend detection correctly', () {
        // Test weekend detection using DateTime.weekday
        expect(DateTime(2024, 6, 15).weekday, equals(DateTime.saturday)); // Saturday
        expect(DateTime(2024, 6, 16).weekday, equals(DateTime.sunday)); // Sunday
        expect(DateTime(2024, 6, 17).weekday, equals(DateTime.monday)); // Monday
      });

      test('should provide holiday information correctly', () {
        final newYear = DateTime(2024, 1, 1);
        final workday = DateTime(2024, 6, 17);

        final newYearHoliday = RomanianHolidays.getHolidayForDate(newYear);
        expect(newYearHoliday?.name, equals('Anul Nou'));

        final workdayHoliday = RomanianHolidays.getHolidayForDate(workday);
        expect(workdayHoliday, isNull);
      });
    });

    group('Calendar Provider Integration', () {
      test('should handle schedule update methods without errors', () async {
        // Test that the schedule update methods exist and can be called
        expect(() => provider.onStaffScheduleChanged('staff-1', reason: 'Test'), returnsNormally);
        expect(() => provider.onSalonScheduleChanged(reason: 'Test'), returnsNormally);
        expect(() => provider.onHolidayClosureChanged(reason: 'Test'), returnsNormally);
      });

      test('should handle preload methods without errors', () async {
        // Test that preload methods exist and can be called
        expect(() => provider.preloadStaffWorkingHours(['staff-1', 'staff-2']), returnsNormally);
        expect(() => provider.getCachedStaffIds(), returnsNormally);
        expect(() => provider.getStaffCacheStatus(), returnsNormally);
      });
    });
  });
}
