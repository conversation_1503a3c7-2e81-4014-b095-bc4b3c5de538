import 'package:partykidsapp/utils/snack_bar_utils.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter/material.dart';
import '../core/constants/app_strings.dart';

class UrlLauncherService {
  // Launch phone dialer
  static Future<bool> makePhoneCall(String phoneNumber) async {
    // Clean the phone number (remove spaces, dashes, etc.)
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    final Uri phoneUri = Uri(scheme: 'tel', path: cleanNumber);
    
    try {
      if (await canLaunchUrl(phoneUri)) {
        return await launchUrl(phoneUri);
      } else {
        debugPrint('Could not launch phone dialer for: $cleanNumber');
        return false;
      }
    } catch (e) {
      debugPrint('Error launching phone dialer: $e');
      return false;
    }
  }

  // Launch SMS app
  static Future<bool> sendSMS(String phoneNumber, {String? message}) async {
    // Clean the phone number
    final cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    
    // Build SMS URI
    String smsUri = 'sms:$cleanNumber';
    if (message != null && message.isNotEmpty) {
      smsUri += '?body=${Uri.encodeComponent(message)}';
    }
    
    final Uri uri = Uri.parse(smsUri);
    
    try {
      if (await canLaunchUrl(uri)) {
        return await launchUrl(uri);
      } else {
        debugPrint('Could not launch SMS app for: $cleanNumber');
        return false;
      }
    } catch (e) {
      debugPrint('Error launching SMS app: $e');
      return false;
    }
  }

  // Launch WhatsApp
  static Future<bool> openWhatsApp(String phoneNumber, {String? message}) async {
    // Clean the phone number and ensure it starts with country code
    String cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d+]'), '');
    
    // If number starts with 0, replace with +40 (Romania)
    if (cleanNumber.startsWith('0')) {
      cleanNumber = '+40${cleanNumber.substring(1)}';
    }
    
    // Remove + for WhatsApp URL
    cleanNumber = cleanNumber.replaceAll('+', '');
    
    // Build WhatsApp URI
    String whatsappUri = 'https://wa.me/$cleanNumber';
    if (message != null && message.isNotEmpty) {
      whatsappUri += '?text=${Uri.encodeComponent(message)}';
    }
    
    final Uri uri = Uri.parse(whatsappUri);
    
    try {
      if (await canLaunchUrl(uri)) {
        return await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        debugPrint('Could not launch WhatsApp for: $cleanNumber');
        return false;
      }
    } catch (e) {
      debugPrint('Error launching WhatsApp: $e');
      return false;
    }
  }

  // Launch email app
  static Future<bool> sendEmail(String email, {String? subject, String? body}) async {
    String emailUri = 'mailto:$email';
    
    List<String> params = [];
    if (subject != null && subject.isNotEmpty) {
      params.add('subject=${Uri.encodeComponent(subject)}');
    }
    if (body != null && body.isNotEmpty) {
      params.add('body=${Uri.encodeComponent(body)}');
    }
    
    if (params.isNotEmpty) {
      emailUri += '?${params.join('&')}';
    }
    
    final Uri uri = Uri.parse(emailUri);
    
    try {
      if (await canLaunchUrl(uri)) {
        return await launchUrl(uri);
      } else {
        debugPrint('Could not launch email app for: $email');
        return false;
      }
    } catch (e) {
      debugPrint('Error launching email app: $e');
      return false;
    }
  }

  // Launch web URL
  static Future<bool> openWebUrl(String url) async {
    final Uri uri = Uri.parse(url);

    try {
      if (await canLaunchUrl(uri)) {
        return await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
      } else {
        debugPrint('Could not launch URL: $url');
        return false;
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
      return false;
    }
  }

  // Show error dialog when launch fails
  static void showLaunchError(BuildContext context, String action) {
    showTopSnackBar(context, 
      SnackBar(
        content: Text(AppStrings.launchError(action)),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
