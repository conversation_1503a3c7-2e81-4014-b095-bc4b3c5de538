import 'package:partykidsapp/utils/snack_bar_utils.dart';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:http/http.dart' as http;
import 'package:google_places_flutter/google_places_flutter.dart';
import 'package:google_places_flutter/model/prediction.dart';

import '../../config/environment.dart';
import '../../config/theme/app_theme.dart';

/// Full-screen map picker with Uber-like UX but forestGreen theme
class FullScreenMapPicker extends StatefulWidget {
  final LatLng? initialLocation;
  final String? initialAddress;
  final Function(LatLng location, String? address)? onLocationSelected;

  const FullScreenMapPicker({
    super.key,
    this.initialLocation,
    this.initialAddress,
    this.onLocationSelected,
  });

  @override
  State<FullScreenMapPicker> createState() => _FullScreenMapPickerState();
}

class _FullScreenMapPickerState extends State<FullScreenMapPicker>
    with TickerProviderStateMixin {
  late LatLng _currentLocation;
  GoogleMapController? _controller;
  bool _isLoading = true;
  bool _isMoving = false;
  String? _currentAddress;

  late TextEditingController _searchController;
  late FocusNode _searchFocusNode;
  
  late AnimationController _pinAnimationController;
  late Animation<double> _pinAnimation;

  @override
  void initState() {
    super.initState();
    _currentLocation = widget.initialLocation ?? const LatLng(44.4268, 26.1025);
    _currentAddress = widget.initialAddress;

    _searchController = TextEditingController(text: widget.initialAddress ?? '');
    _searchFocusNode = FocusNode();

    // Add listener to rebuild when focus changes
    _searchFocusNode.addListener(() {
      setState(() {});
    });

    // Add listener to rebuild when text changes
    _searchController.addListener(() {
      setState(() {});
    });

    _pinAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _pinAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pinAnimationController, curve: Curves.elasticOut),
    );

    _initLocation();
  }

  @override
  void dispose() {
    _pinAnimationController.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  Future<void> _initLocation() async {
    try {
      if (widget.initialLocation == null) {
        final permission = await Geolocator.requestPermission();
        if (permission != LocationPermission.denied &&
            permission != LocationPermission.deniedForever) {
          final position = await Geolocator.getCurrentPosition();
          if (mounted) {
            setState(() {
              _currentLocation = LatLng(position.latitude, position.longitude);
            });
            _controller?.animateCamera(
              CameraUpdate.newLatLngZoom(_currentLocation, 16),
            );
          }
        }
      }
    } catch (e) {
      // Keep default location if error
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
          if (_currentAddress == null || _currentAddress!.isEmpty) {
            await _updateAddressFromCoordinates();
          }
        }
      }
  }

  void _onCameraMove(CameraPosition position) {
    if (!_isMoving) {
      setState(() {
        _isMoving = true;
      });
      _pinAnimationController.forward();
    }
    _currentLocation = position.target;
  }

  void _onCameraIdle() {
    if (_isMoving) {
      setState(() {
        _isMoving = false;
      });
      _pinAnimationController.reverse();
      _updateAddressFromCoordinates();
    }
  }

  void _confirmLocation() {
    if (widget.onLocationSelected != null) {
      widget.onLocationSelected!(_currentLocation, _currentAddress);
    }
    Navigator.of(context).pop({
      'location': _currentLocation,
      'address': _currentAddress,
    });
  }

  Future<void> _updateAddressFromCoordinates() async {
    final url = Uri.parse(
        'https://maps.googleapis.com/maps/api/geocode/json?latlng=${_currentLocation.latitude},${_currentLocation.longitude}&key=${EnvironmentConfig.googleMapsApiKey}');
    try {
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final results = data['results'] as List<dynamic>?;
        if (results != null && results.isNotEmpty) {
          final formatted = results.first['formatted_address'] as String?;
          if (formatted != null) {
            if (mounted) {
              setState(() {
                _currentAddress = formatted;
                _searchController.text = formatted;
              });
            }
          }
        }
      }
    } catch (_) {
      // ignore errors silently
    }
  }

  void _goToCurrentLocation() async {
    try {
      // Show loading state
      setState(() {
        _isLoading = true;
      });

      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      final newLocation = LatLng(position.latitude, position.longitude);

      // Update both camera and pin location
      _controller?.animateCamera(
        CameraUpdate.newLatLngZoom(newLocation, 16),
      );

      setState(() {
        _currentLocation = newLocation;
        _isLoading = false;
      });

      // Update address from new coordinates
      await _updateAddressFromCoordinates();

      debugPrint('📍 Moved pin to current location: ${position.latitude}, ${position.longitude}');
    } catch (e) {
      // Handle error with user feedback
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        _showLocationPermissionDialog();
      }
      debugPrint('📍 Error getting current location: $e');
    }
  }

  void _showLocationPermissionDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.forestGreen.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.location_on,
                  color: AppColors.forestGreen,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Permisiune Locație',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.forestGreen,
                  ),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Pentru a vă afișa locația curentă pe hartă, aplicația are nevoie de acces la locația dvs.',
                style: TextStyle(
                  fontSize: 16,
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.blue.withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.blue.shade700,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Cum să activați locația:',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      '1. Deschideți Setările telefonului\n'
                      '2. Găsiți aplicația "Partykids"\n'
                      '3. Selectați "Locație"\n'
                      '4. Alegeți "În timpul utilizării aplicației"',
                      style: TextStyle(
                        fontSize: 14,
                        height: 1.3,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Anulează',
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                // Try to open app settings
                try {
                  await Geolocator.openAppSettings();
                } catch (e) {
                  // If opening settings fails, show a simple message
                  if (mounted) {
                    showTopSnackBar(context, 
                      const SnackBar(
                        content: Text('Deschideți manual setările aplicației pentru a activa locația'),
                        backgroundColor: AppColors.forestGreen,
                      ),
                    );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.forestGreen,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              ),
              child: const Text(
                'Deschide Setările',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildSearchField() {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _searchFocusNode.hasFocus
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.outline,
          width: _searchFocusNode.hasFocus ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: GooglePlaceAutoCompleteTextField(
        textEditingController: _searchController,
        focusNode: _searchFocusNode,
        googleAPIKey: EnvironmentConfig.googleMapsApiKey,
        inputDecoration: InputDecoration(
          hintText: 'Căutați locația...',
          hintStyle: TextStyle(
            color: Colors.grey.shade500,
            fontSize: 16,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
          prefixIcon: Container(
            padding: const EdgeInsets.all(12),
            child: Icon(
              Icons.search,
              color: _searchFocusNode.hasFocus
                  ? AppColors.forestGreen
                  : Colors.grey.shade500,
              size: 22,
            ),
          ),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                    setState(() {});
                  },
                  icon: Icon(
                    Icons.clear,
                    color: Colors.grey.shade500,
                    size: 20,
                  ),
                )
              : null,
        ),
        debounceTime: 600,
        countries: const ['ro'],
        isLatLngRequired: true,
        getPlaceDetailWithLatLng: (Prediction prediction) {
          final lat = double.tryParse(prediction.lat ?? '');
          final lng = double.tryParse(prediction.lng ?? '');
          if (lat != null && lng != null) {
            final loc = LatLng(lat, lng);
            _controller?.animateCamera(
              CameraUpdate.newLatLngZoom(loc, 16),
            );
            setState(() {
              _currentLocation = loc;
              _currentAddress = prediction.description ?? '';
              _searchController.text = prediction.description ?? '';
            });
          }
          Future.microtask(() => _searchFocusNode.unfocus());
        },
        itemClick: (Prediction prediction) {
          _searchController.text = prediction.description ?? '';
          _searchController.selection = TextSelection.fromPosition(
            TextPosition(offset: _searchController.text.length),
          );
          Future.microtask(() => _searchFocusNode.unfocus());
        },
        seperatedBuilder: Divider(
          height: 1,
          color: Colors.grey.shade200,
        ),
        containerHorizontalPadding: 0,
        itemBuilder: (context, index, Prediction prediction) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                bottom: BorderSide(
                  color: Colors.grey.shade100,
                  width: 0.5,
                ),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.forestGreen.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(
                    Icons.location_on,
                    color: AppColors.forestGreen,
                    size: 18,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        prediction.structuredFormatting?.mainText ?? '',
                        style: const TextStyle(
                          fontWeight: FontWeight.w600,
                          fontSize: 15,
                          color: Colors.black87,
                        ),
                      ),
                      if (prediction.structuredFormatting?.secondaryText != null)
                        ...[
                          const SizedBox(height: 3),
                          Text(
                            prediction.structuredFormatting!.secondaryText!,
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 13,
                            ),
                          ),
                        ],
                    ],
                  ),
                ),
                Icon(
                  Icons.north_west,
                  color: Colors.grey.shade400,
                  size: 16,
                ),
              ],
            ),
          );
        },
        isCrossBtnShown: false,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          // Map
          GoogleMap(
            initialCameraPosition: CameraPosition(
              target: _currentLocation,
              zoom: 15,
            ),
            onMapCreated: (controller) => _controller = controller,
            onCameraMove: _onCameraMove,
            onCameraIdle: _onCameraIdle,
            myLocationEnabled: true,
            myLocationButtonEnabled: false,
            zoomControlsEnabled: false,
            mapToolbarEnabled: false,
          ),

          // Loading overlay
          if (_isLoading)
            Container(
              color: Colors.white.withValues(alpha: 0.8),
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.forestGreen),
                ),
              ),
            ),

          // Center pin
          Center(
            child: AnimatedBuilder(
              animation: _pinAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _pinAnimation.value,
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: AppColors.forestGreen,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.location_on,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                );
              },
            ),
          ),

          // Top bar
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: SafeArea(
              child: Container(
                margin: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    // Back button with improved styling
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(
                          Icons.arrow_back,
                          color: AppColors.forestGreen,
                          size: 24,
                        ),
                        padding: EdgeInsets.zero,
                      ),
                    ),
                    const SizedBox(width: 12),
                    // Search field
                    Expanded(child: _buildSearchField()),
                  ],
                ),
              ),
            ),
          ),

          // Bottom address display and controls
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.15),
                    blurRadius: 20,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: SafeArea(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Drag handle
                    Container(
                      margin: const EdgeInsets.only(top: 12),
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey.shade300,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),

                    // Address display
                    Container(
                      padding: const EdgeInsets.all(20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: AppColors.forestGreen.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Icon(
                                  Icons.location_on,
                                  color: AppColors.forestGreen,
                                  size: 20,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text(
                                      'Locația selectată',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    const SizedBox(height: 2),
                                    Text(
                                      _currentAddress ?? 'Se încarcă adresa...',
                                      style: const TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,
                                        color: Colors.black87,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 20),

                          // Action buttons
                          Row(
                            children: [
                              // Current location button
                              Expanded(
                                child: OutlinedButton.icon(
                                  onPressed: _goToCurrentLocation,
                                  icon: const Icon(Icons.my_location, size: 18),
                                  label: const Text('Locația mea'),
                                  style: OutlinedButton.styleFrom(
                                    foregroundColor: AppColors.forestGreen,
                                    side: const BorderSide(color: AppColors.forestGreen),
                                    padding: const EdgeInsets.symmetric(vertical: 14),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),

                              // Confirm button
                              Expanded(
                                flex: 2,
                                child: ElevatedButton.icon(
                                  onPressed: _currentAddress != null ? _confirmLocation : null,
                                  icon: const Icon(Icons.check, size: 18),
                                  label: const Text('Confirmă locația'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: AppColors.forestGreen,
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(vertical: 14),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    elevation: 2,
                                    disabledBackgroundColor: Colors.grey.shade300,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

        ],
      ),
    );
  }
}
