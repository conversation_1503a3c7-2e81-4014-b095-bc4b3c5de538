import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:provider/provider.dart';

import 'package:partykidsapp/widgets/calendar_views/droppable_time_slot.dart';
import 'package:partykidsapp/widgets/calendar_views/draggable_appointment_block.dart';
import 'package:partykidsapp/models/appointment.dart';
import 'package:partykidsapp/providers/calendar_provider.dart';

// Generate mocks
@GenerateMocks([CalendarProvider])
import 'droppable_time_slot_test.mocks.dart';

void main() {
  group('DroppableTimeSlot', () {
    late MockCalendarProvider mockCalendarProvider;
    late DateTime testDateTime;
    late Appointment testAppointment;
    late AppointmentDragData testDragData;

    setUp(() {
      mockCalendarProvider = MockCalendarProvider();
      testDateTime = DateTime(2024, 1, 15, 10, 0);
      testAppointment = Appointment(
        id: 'test-id',
        clientId: 'client-1',
        clientName: '<PERSON>',
        clientPhone: '+40123456789',
        petId: 'pet-1',
        petName: 'Buddy',
        petSpecies: 'Dog',
        service: 'Grooming',
        startTime: DateTime(2024, 1, 15, 9, 0),
        endTime: DateTime(2024, 1, 15, 10, 0),
        status: 'SCHEDULED',
        isPaid: false,
        groomerId: 'groomer-1',
        assignedGroomer: 'Jane Smith',
      );
      testDragData = AppointmentDragData(
        appointment: testAppointment,
        originalDate: testAppointment.startTime,
        originalStaffId: 'groomer-1',
      );
    });

    Widget createTestWidget({
      bool isDragEnabled = true,
      String? staffId = 'groomer-1',
      VoidCallback? onTap,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: ChangeNotifierProvider<CalendarProvider>.value(
            value: mockCalendarProvider,
            child: DroppableTimeSlot(
              dateTime: testDateTime,
              staffId: staffId,
              isBusinessHour: true,
              isLunchBreak: false,
              isAvailable: true,
              isGreyedOut: false,
              height: 60,
              isDragEnabled: isDragEnabled,
              onTap: onTap,
            ),
          ),
        ),
      );
    }

    testWidgets('renders regular time slot when drag is disabled', (tester) async {
      await tester.pumpWidget(createTestWidget(isDragEnabled: false));

      expect(find.byType(DragTarget<AppointmentDragData>), findsNothing);
      expect(find.byType(DroppableTimeSlot), findsOneWidget);
    });

    testWidgets('renders drag target when drag is enabled', (tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.byType(DragTarget<AppointmentDragData>), findsOneWidget);
    });

    testWidgets('accepts drag data and shows visual feedback', (tester) async {
      when(mockCalendarProvider.getFilteredAppointmentsForDate(any))
          .thenReturn([]);

      await tester.pumpWidget(createTestWidget());

      final dragTarget = find.byType(DragTarget<AppointmentDragData>);
      expect(dragTarget, findsOneWidget);

      // Simulate drag over
      final dragTargetWidget = tester.widget<DragTarget<AppointmentDragData>>(dragTarget);
      final willAccept = dragTargetWidget.onWillAccept?.call(testDragData);

      expect(willAccept, isTrue);
    });

    testWidgets('shows green border for valid drop target', (tester) async {
      when(mockCalendarProvider.getFilteredAppointmentsForDate(any))
          .thenReturn([]);

      await tester.pumpWidget(createTestWidget());

      final dragTarget = find.byType(DragTarget<AppointmentDragData>);
      final dragTargetWidget = tester.widget<DragTarget<AppointmentDragData>>(dragTarget);

      // Simulate drag over
      dragTargetWidget.onWillAccept?.call(testDragData);
      await tester.pump();

      // The widget should show visual feedback for valid drop
      expect(find.byType(AnimatedContainer), findsOneWidget);
    });

    testWidgets('shows red border for invalid drop target', (tester) async {
      // Mock conflicting appointment
      final conflictingAppointment = Appointment(
        id: 'conflict-id',
        clientId: 'client-2',
        clientName: 'Jane Doe',
        clientPhone: '+40987654321',
        petId: 'pet-2',
        petName: 'Max',
        petSpecies: 'Cat',
        service: 'Grooming',
        startTime: testDateTime,
        endTime: testDateTime.add(Duration(hours: 1)),
        status: 'SCHEDULED',
        isPaid: false,
        groomerId: 'groomer-1',
      );

      when(mockCalendarProvider.getFilteredAppointmentsForDate(any))
          .thenReturn([conflictingAppointment]);

      await tester.pumpWidget(createTestWidget());

      final dragTarget = find.byType(DragTarget<AppointmentDragData>);
      final dragTargetWidget = tester.widget<DragTarget<AppointmentDragData>>(dragTarget);

      // Simulate drag over
      dragTargetWidget.onWillAccept?.call(testDragData);
      await tester.pump();

      // Should still accept for UI feedback, but validation will fail
      expect(find.byType(AnimatedContainer), findsOneWidget);
    });

    testWidgets('handles tap events correctly', (tester) async {
      bool tapped = false;
      
      await tester.pumpWidget(createTestWidget(
        onTap: () => tapped = true,
      ));

      await tester.tap(find.byType(DroppableTimeSlot));
      await tester.pump();

      expect(tapped, isTrue);
    });

    testWidgets('resets visual state when drag leaves', (tester) async {
      when(mockCalendarProvider.getFilteredAppointmentsForDate(any))
          .thenReturn([]);

      await tester.pumpWidget(createTestWidget());

      final dragTarget = find.byType(DragTarget<AppointmentDragData>);
      final dragTargetWidget = tester.widget<DragTarget<AppointmentDragData>>(dragTarget);

      // Simulate drag over then leave
      dragTargetWidget.onWillAccept?.call(testDragData);
      await tester.pump();
      
      dragTargetWidget.onLeave?.call(testDragData);
      await tester.pump();

      // Visual state should be reset
      expect(find.byType(AnimatedContainer), findsOneWidget);
    });

    testWidgets('handles null drag data gracefully', (tester) async {
      await tester.pumpWidget(createTestWidget());

      final dragTarget = find.byType(DragTarget<AppointmentDragData>);
      final dragTargetWidget = tester.widget<DragTarget<AppointmentDragData>>(dragTarget);

      // Simulate drag with null data
      final willAccept = dragTargetWidget.onWillAccept?.call(null);

      expect(willAccept, isFalse);
    });
  });

  group('DroppableStaffColumn', () {
    late MockCalendarProvider mockCalendarProvider;
    late Appointment testAppointment;
    late AppointmentDragData testDragData;

    setUp(() {
      mockCalendarProvider = MockCalendarProvider();
      testAppointment = Appointment(
        id: 'test-id',
        clientId: 'client-1',
        clientName: 'John Doe',
        clientPhone: '+40123456789',
        petId: 'pet-1',
        petName: 'Buddy',
        petSpecies: 'Dog',
        service: 'Grooming',
        startTime: DateTime(2024, 1, 15, 10, 0),
        endTime: DateTime(2024, 1, 15, 11, 0),
        status: 'SCHEDULED',
        isPaid: false,
        groomerId: 'groomer-1',
        assignedGroomer: 'Jane Smith',
      );
      testDragData = AppointmentDragData(
        appointment: testAppointment,
        originalDate: testAppointment.startTime,
        originalStaffId: 'groomer-1',
      );
    });

    Widget createTestWidget({
      bool isDragEnabled = true,
      String staffId = 'groomer-2',
      String staffName = 'John Smith',
    }) {
      return MaterialApp(
        home: Scaffold(
          body: ChangeNotifierProvider<CalendarProvider>.value(
            value: mockCalendarProvider,
            child: DroppableStaffColumn(
              staffId: staffId,
              staffName: staffName,
              isDragEnabled: isDragEnabled,
              child: Container(
                width: 100,
                height: 200,
                color: Colors.blue,
              ),
            ),
          ),
        ),
      );
    }

    testWidgets('renders child widget when drag is disabled', (tester) async {
      await tester.pumpWidget(createTestWidget(isDragEnabled: false));

      expect(find.byType(DragTarget<AppointmentDragData>), findsNothing);
      expect(find.byType(Container), findsOneWidget);
    });

    testWidgets('renders drag target when drag is enabled', (tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.byType(DragTarget<AppointmentDragData>), findsOneWidget);
      expect(find.byType(Container), findsOneWidget);
    });

    testWidgets('accepts drag data for different staff', (tester) async {
      when(mockCalendarProvider.getFilteredAppointmentsForDate(any))
          .thenReturn([]);

      await tester.pumpWidget(createTestWidget(staffId: 'groomer-2'));

      final dragTarget = find.byType(DragTarget<AppointmentDragData>);
      final dragTargetWidget = tester.widget<DragTarget<AppointmentDragData>>(dragTarget);

      // Should accept since it's a different staff member
      final willAccept = dragTargetWidget.onWillAccept?.call(testDragData);
      expect(willAccept, isTrue);
    });

    testWidgets('rejects drag data for same staff', (tester) async {
      await tester.pumpWidget(createTestWidget(staffId: 'groomer-1'));

      final dragTarget = find.byType(DragTarget<AppointmentDragData>);
      final dragTargetWidget = tester.widget<DragTarget<AppointmentDragData>>(dragTarget);

      // Should reject since it's the same staff member
      final willAccept = dragTargetWidget.onWillAccept?.call(testDragData);
      expect(willAccept, isFalse);
    });

    testWidgets('shows visual feedback during drag over', (tester) async {
      when(mockCalendarProvider.getFilteredAppointmentsForDate(any))
          .thenReturn([]);

      await tester.pumpWidget(createTestWidget());

      final dragTarget = find.byType(DragTarget<AppointmentDragData>);
      final dragTargetWidget = tester.widget<DragTarget<AppointmentDragData>>(dragTarget);

      // Simulate drag over
      dragTargetWidget.onWillAccept?.call(testDragData);
      await tester.pump();

      expect(find.byType(AnimatedContainer), findsOneWidget);
    });

    testWidgets('handles null drag data gracefully', (tester) async {
      await tester.pumpWidget(createTestWidget());

      final dragTarget = find.byType(DragTarget<AppointmentDragData>);
      final dragTargetWidget = tester.widget<DragTarget<AppointmentDragData>>(dragTarget);

      // Should reject null data
      final willAccept = dragTargetWidget.onWillAccept?.call(null);
      expect(willAccept, isFalse);
    });

    testWidgets('resets visual state when drag leaves', (tester) async {
      when(mockCalendarProvider.getFilteredAppointmentsForDate(any))
          .thenReturn([]);

      await tester.pumpWidget(createTestWidget());

      final dragTarget = find.byType(DragTarget<AppointmentDragData>);
      final dragTargetWidget = tester.widget<DragTarget<AppointmentDragData>>(dragTarget);

      // Simulate drag over then leave
      dragTargetWidget.onWillAccept?.call(testDragData);
      await tester.pump();
      
      dragTargetWidget.onLeave?.call(testDragData);
      await tester.pump();

      // Visual state should be reset
      expect(find.byType(AnimatedContainer), findsOneWidget);
    });
  });
}
