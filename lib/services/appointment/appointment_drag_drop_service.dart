import 'package:flutter/material.dart';
import '../../models/appointment.dart';
import '../../models/api_response.dart';
import '../../providers/calendar_provider.dart';
import '../../services/appointment/appointment_service.dart';

/// Service for handling appointment drag-and-drop operations
class AppointmentDragDropService {
  static const int _businessHourStart = 8;
  static const int _businessHourEnd = 20;
  static const int _lunchBreakStart = 13;
  static const int _lunchBreakEnd = 14;

  /// Validates if a time slot is available for dropping an appointment
  static Future<DropValidationResult> validateDropTarget({
    required Appointment appointment,
    required DateTime newStartTime,
    required DateTime newEndTime,
    required String? newStaffId,
    required CalendarProvider calendarProvider,
  }) async {
    try {
      final targetStaffId = newStaffId ?? appointment.groomerId;

      // Check if the new time is within staff working hours
      if (!isWithinStaffWorkingHours(
        startTime: newStartTime,
        endTime: newEndTime,
        staffId: targetStaffId,
        calendarProvider: calendarProvider,
      )) {
        return DropValidationResult(
          isValid: false,
          reason: 'În afara programului de lucru',
          conflictType: ConflictType.businessHours,
        );
      }

      // Check for appointment conflicts
      final conflictCheck = await _checkAppointmentConflicts(
        appointment: appointment,
        newStartTime: newStartTime,
        newEndTime: newEndTime,
        newStaffId: newStaffId,
        calendarProvider: calendarProvider,
      );

      if (!conflictCheck.isValid) {
        return conflictCheck;
      }

      // Check staff availability if staff is changing
      if (newStaffId != null && newStaffId != appointment.groomerId) {
        final staffAvailability = await _checkStaffAvailability(
          staffId: newStaffId,
          startTime: newStartTime,
          endTime: newEndTime,
          calendarProvider: calendarProvider,
        );

        if (!staffAvailability.isValid) {
          return staffAvailability;
        }
      }

      return DropValidationResult(
        isValid: true,
        reason: 'Slot disponibil',
        conflictType: ConflictType.none,
      );
    } catch (e) {
      debugPrint('Error validating drop target: $e');
      return DropValidationResult(
        isValid: false,
        reason: 'Eroare la validare',
        conflictType: ConflictType.error,
      );
    }
  }

  /// Fast validation for real-time feedback during drag operations
  /// This only checks basic constraints without API calls
  static DropValidationResult validateDropTargetFast({
    required Appointment appointment,
    required DateTime newStartTime,
    required DateTime newEndTime,
    required String? newStaffId,
    required CalendarProvider calendarProvider,
  }) {
    try {
      final targetStaffId = newStaffId ?? appointment.groomerId;

      // Check if the new time is within staff working hours
      if (!isWithinStaffWorkingHours(
        startTime: newStartTime,
        endTime: newEndTime,
        staffId: targetStaffId,
        calendarProvider: calendarProvider,
      )) {
        return DropValidationResult(
          isValid: false,
          reason: 'În afara programului de lucru',
          conflictType: ConflictType.businessHours,
        );
      }

      // Check for basic appointment conflicts using cached data
      final appointments = calendarProvider.getFilteredAppointmentsForDate(newStartTime);
      final staffAppointments = appointments
          .where((apt) => apt.groomerId == targetStaffId && apt.id != appointment.id)
          .toList();

      // Check for time conflicts
      for (final existingAppointment in staffAppointments) {
        if (_hasTimeConflict(newStartTime, newEndTime, existingAppointment)) {
          return DropValidationResult(
            isValid: false,
            reason: 'Conflict cu: ${existingAppointment.clientName}',
            conflictType: ConflictType.appointment,
            conflictingAppointment: existingAppointment,
          );
        }
      }

      return DropValidationResult(
        isValid: true,
        reason: 'Slot disponibil',
        conflictType: ConflictType.none,
      );
    } catch (e) {
      debugPrint('Error in fast validation: $e');
      return DropValidationResult(
        isValid: false,
        reason: 'Eroare la validare',
        conflictType: ConflictType.error,
      );
    }
  }

  /// Performs the actual appointment reschedule/reassignment
  static Future<DropOperationResult> performDrop({
    required Appointment appointment,
    required DateTime newStartTime,
    required DateTime newEndTime,
    required String? newStaffId,
    required CalendarProvider calendarProvider,
  }) async {
    try {
      // Validate the drop target first
      final validation = await validateDropTarget(
        appointment: appointment,
        newStartTime: newStartTime,
        newEndTime: newEndTime,
        newStaffId: newStaffId,
        calendarProvider: calendarProvider,
      );

      if (!validation.isValid) {
        return DropOperationResult(
          success: false,
          message: validation.reason,
          conflictType: validation.conflictType,
        );
      }

      // Determine the type of operation
      final isTimeChange = newStartTime != appointment.startTime || 
                          newEndTime != appointment.endTime;
      final isStaffChange = newStaffId != null && newStaffId != appointment.groomerId;

      ApiResponse<Appointment> response;

      if (isTimeChange || isStaffChange) {
        // Use reschedule endpoint for all changes (time, staff, or both)
        response = await AppointmentService.rescheduleAppointment(
          appointment.id,
          newStartTime: newStartTime,
          newEndTime: newEndTime,
          newStaffId: newStaffId,
          reason: 'Reprogramat prin drag-and-drop',
        );
      } else {
        // No actual change
        return DropOperationResult(
          success: true,
          message: 'Nicio modificare necesară',
          conflictType: ConflictType.none,
        );
      }

      if (response.success && response.data != null) {
        // Refresh calendar data
        await calendarProvider.fetchAppointmentsForDate(
          newStartTime,
          forceRefresh: true,
        );

        // If the date changed, also refresh the original date
        if (newStartTime.day != appointment.startTime.day ||
            newStartTime.month != appointment.startTime.month ||
            newStartTime.year != appointment.startTime.year) {
          await calendarProvider.fetchAppointmentsForDate(
            appointment.startTime,
            forceRefresh: true,
          );
        }

        String successMessage;
        if (isTimeChange && isStaffChange) {
          successMessage = 'Programarea a fost reprogramată și reasignată cu succes';
        } else if (isTimeChange) {
          successMessage = 'Programarea a fost reprogramată cu succes';
        } else {
          successMessage = 'Programarea a fost reasignată cu succes';
        }

        return DropOperationResult(
          success: true,
          message: successMessage,
          conflictType: ConflictType.none,
          updatedAppointment: response.data,
        );
      } else {
        return DropOperationResult(
          success: false,
          message: response.error ?? 'Eroare la actualizarea programării',
          conflictType: ConflictType.error,
        );
      }
    } catch (e) {
      debugPrint('Error performing drop operation: $e');
      return DropOperationResult(
        success: false,
        message: 'Eroare neașteptată: $e',
        conflictType: ConflictType.error,
      );
    }
  }

  static bool _isWithinBusinessHours(DateTime startTime, DateTime endTime) {
    final startHour = startTime.hour;
    final endHour = endTime.hour;

    return startHour >= _businessHourStart &&
           endHour <= _businessHourEnd &&
           startTime.isBefore(endTime);
  }

  /// Enhanced working hours validation using actual staff working hours
  static bool isWithinStaffWorkingHours({
    required DateTime startTime,
    required DateTime endTime,
    required String? staffId,
    required CalendarProvider calendarProvider,
  }) {
    if (staffId == null) return false;

    // Get staff working hours from calendar provider
    final staffWorkingHours = calendarProvider.getStaffWorkingHours(staffId);
    if (staffWorkingHours == null) {
      // Fallback to basic business hours check
      return _isWithinBusinessHours(startTime, endTime);
    }

    final dayOfWeek = startTime.weekday;
    final daySchedule = _getDayScheduleForWeekday(staffWorkingHours, dayOfWeek);

    if (daySchedule == null || !daySchedule.isWorkingDay) {
      return false;
    }

    final startTimeOfDay = TimeOfDay.fromDateTime(startTime);
    final endTimeOfDay = TimeOfDay.fromDateTime(endTime);

    // Check if appointment is within working hours
    if (daySchedule.startTime != null && daySchedule.endTime != null) {
      final workStart = _parseTimeString(daySchedule.startTime!);
      final workEnd = _parseTimeString(daySchedule.endTime!);

      if (workStart != null && workEnd != null) {
        if (_isTimeOfDayBefore(startTimeOfDay, workStart) ||
            _isTimeOfDayAfter(endTimeOfDay, workEnd)) {
          return false;
        }
      }
    }

    // Check if appointment conflicts with break time
    if (daySchedule.breakStart != null && daySchedule.breakEnd != null) {
      final breakStart = _parseTimeString(daySchedule.breakStart!);
      final breakEnd = _parseTimeString(daySchedule.breakEnd!);

      if (breakStart != null && breakEnd != null) {
        // Check if appointment overlaps with break
        if (!(_isTimeOfDayAfter(startTimeOfDay, breakEnd) ||
              _isTimeOfDayBefore(endTimeOfDay, breakStart))) {
          return false;
        }
      }
    }

    return true;
  }

  static dynamic _getDayScheduleForWeekday(dynamic staffWorkingHours, int weekday) {
    // Map weekday (1=Monday, 7=Sunday) to schedule key
    const dayKeys = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    if (weekday < 1 || weekday > 7) return null;

    final dayKey = dayKeys[weekday - 1];
    return staffWorkingHours.weeklySchedule?[dayKey];
  }

  static bool _isTimeOfDayBefore(TimeOfDay time1, TimeOfDay time2) {
    return time1.hour < time2.hour ||
           (time1.hour == time2.hour && time1.minute < time2.minute);
  }

  static bool _isTimeOfDayAfter(TimeOfDay time1, TimeOfDay time2) {
    return time1.hour > time2.hour ||
           (time1.hour == time2.hour && time1.minute > time2.minute);
  }

  /// Parse time string (e.g., "09:30") to TimeOfDay
  static TimeOfDay? _parseTimeString(String timeString) {
    try {
      final parts = timeString.split(':');
      if (parts.length == 2) {
        final hour = int.parse(parts[0]);
        final minute = int.parse(parts[1]);
        if (hour >= 0 && hour <= 23 && minute >= 0 && minute <= 59) {
          return TimeOfDay(hour: hour, minute: minute);
        }
      }
    } catch (e) {
      debugPrint('Error parsing time string "$timeString": $e');
    }
    return null;
  }

  static bool _isInLunchBreak(DateTime startTime, DateTime endTime) {
    final startHour = startTime.hour;
    final endHour = endTime.hour;
    
    return (startHour >= _lunchBreakStart && startHour < _lunchBreakEnd) ||
           (endHour > _lunchBreakStart && endHour <= _lunchBreakEnd) ||
           (startHour < _lunchBreakStart && endHour > _lunchBreakEnd);
  }

  static Future<DropValidationResult> _checkAppointmentConflicts({
    required Appointment appointment,
    required DateTime newStartTime,
    required DateTime newEndTime,
    required String? newStaffId,
    required CalendarProvider calendarProvider,
  }) async {
    final targetStaffId = newStaffId ?? appointment.groomerId;
    if (targetStaffId == null) {
      return DropValidationResult(
        isValid: false,
        reason: 'Staff ID necunoscut',
        conflictType: ConflictType.error,
      );
    }

    // Get appointments for the target date and staff
    final appointments = calendarProvider.getFilteredAppointmentsForDate(newStartTime);
    final staffAppointments = appointments
        .where((apt) => apt.groomerId == targetStaffId && apt.id != appointment.id)
        .toList();

    // Check for time conflicts
    for (final existingAppointment in staffAppointments) {
      if (_hasTimeConflict(newStartTime, newEndTime, existingAppointment)) {
        return DropValidationResult(
          isValid: false,
          reason: 'Conflict cu programarea existentă: ${existingAppointment.clientName}',
          conflictType: ConflictType.appointment,
          conflictingAppointment: existingAppointment,
        );
      }
    }

    return DropValidationResult(
      isValid: true,
      reason: 'Fără conflicte',
      conflictType: ConflictType.none,
    );
  }

  static bool _hasTimeConflict(
    DateTime newStart,
    DateTime newEnd,
    Appointment existingAppointment,
  ) {
    return newStart.isBefore(existingAppointment.endTime) &&
           newEnd.isAfter(existingAppointment.startTime);
  }

  static Future<DropValidationResult> _checkStaffAvailability({
    required String staffId,
    required DateTime startTime,
    required DateTime endTime,
    required CalendarProvider calendarProvider,
  }) async {
    // Check if staff exists and is available
    final staff = calendarProvider.getStaffById(staffId);
    if (staff == null) {
      return DropValidationResult(
        isValid: false,
        reason: 'Personal inexistent',
        conflictType: ConflictType.staffUnavailable,
      );
    }

    // Additional staff availability checks can be added here
    // For now, we assume staff is available if they exist

    return DropValidationResult(
      isValid: true,
      reason: 'Personal disponibil',
      conflictType: ConflictType.none,
    );
  }

  static Future<String> _getStaffName(String staffId, CalendarProvider calendarProvider) async {
    final staff = calendarProvider.getStaffById(staffId);
    return staff?.name ?? 'Personal necunoscut';
  }
}

/// Result of drop validation
class DropValidationResult {
  final bool isValid;
  final String reason;
  final ConflictType conflictType;
  final Appointment? conflictingAppointment;

  const DropValidationResult({
    required this.isValid,
    required this.reason,
    required this.conflictType,
    this.conflictingAppointment,
  });
}

/// Result of drop operation
class DropOperationResult {
  final bool success;
  final String message;
  final ConflictType conflictType;
  final Appointment? updatedAppointment;

  const DropOperationResult({
    required this.success,
    required this.message,
    required this.conflictType,
    this.updatedAppointment,
  });
}

/// Types of conflicts that can occur during drag-and-drop
enum ConflictType {
  none,
  businessHours,
  lunchBreak,
  appointment,
  staffUnavailable,
  error,
}
