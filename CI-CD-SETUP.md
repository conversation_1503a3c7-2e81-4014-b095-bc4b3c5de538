# CI/CD Setup Instructions

This document explains how to properly configure your CI/CD pipeline for iOS builds.

## Problem

The iOS build may fail with this error:
```
The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.
```

## Solution

The repository contains minimal CocoaPods configuration files that allow the project to compile in CI/CD environments, but the full configuration needs to be regenerated during the build process.

### For CI/CD Pipelines

Add this step to your CI/CD pipeline **before** building iOS:

```bash
# Setup iOS environment
./scripts/setup-ios-ci.sh
```

Or manually run these commands:

```bash
cd ios
pod install --repo-update
cd ..
```

### For Local Development

If you encounter the same error locally, run:

```bash
cd ios
pod install
cd ..
```

## What This Does

1. **Regenerates CocoaPods configuration** with actual framework references
2. **Updates Podfile.lock** to match the current environment
3. **Creates proper xcfilelist files** with actual framework paths
4. **Ensures PODS_ROOT** and other variables are correctly set

## Files Included for CI/CD Compatibility

The repository includes minimal versions of these files to prevent build failures:

- `ios/Flutter/Generated.xcconfig` - Basic Flutter configuration
- `ios/Flutter/Profile.xcconfig` - Profile build configuration
- `ios/Pods/Target Support Files/Pods-Runner/*.xcconfig` - Minimal CocoaPods configuration
- `ios/Pods/Target Support Files/Pods-Runner/*.xcfilelist` - Empty framework lists

These files are automatically regenerated with proper content when you run `pod install`.

## CI/CD Pipeline Example

```yaml
# Example GitHub Actions step
- name: Setup iOS
  run: |
    ./scripts/setup-ios-ci.sh

- name: Build iOS
  run: |
    flutter build ios --no-codesign
```

## Troubleshooting

If you still encounter issues:

1. **Clean and reinstall**:
   ```bash
   cd ios
   rm -rf Pods Podfile.lock
   pod install
   cd ..
   flutter clean
   flutter pub get
   ```

2. **Check CocoaPods version**:
   ```bash
   pod --version
   ```
   Ensure you're using CocoaPods 1.10.0 or later.

3. **Verify Xcode version**: Ensure your CI/CD environment uses a compatible Xcode version.
