class Service {
  final String id;
  final String name;
  final int duration; // in minutes (used when sizeDurations is null)
  final double price; // in RON (used when sizePrices is null)
  final double? minPrice; // optional minimum price for price ranges
  final double? maxPrice; // optional maximum price for price ranges
  final Map<String, double>? sizePrices; // optional prices by size (S/M/L)
  final Map<String, double>? sizeMinPrices; // optional min prices by size (S/M/L)
  final Map<String, double>? sizeMaxPrices; // optional max prices by size (S/M/L)
  final Map<String, int>? sizeDurations; // optional durations by size (S/M/L)
  final String description;
  final bool isActive;
  final int displayOrder;
  final List<String> requirements;
  final String category;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Service({
    required this.id,
    required this.name,
    required this.duration,
    required this.price,
    this.minPrice,
    this.maxPrice,
    this.sizePrices,
    this.sizeMinPrices,
    this.sizeMaxPrices,
    this.sizeDurations,
    required this.description,
    this.isActive = true,
    this.displayOrder = 0,
    this.requirements = const [],
    this.category = '',
    required this.createdAt,
    this.updatedAt,
  });

  // Computed properties for formatted display
  String get formattedPrice {
    if (sizePrices != null && sizePrices!.isNotEmpty) {
      final parts = <String>[];

      // Handle size-based pricing with potential ranges
      for (final size in ['S', 'M', 'L']) {
        if (sizePrices![size] != null) {
          final basePrice = sizePrices![size]!;
          final minPrice = sizeMinPrices?[size];
          final maxPrice = sizeMaxPrices?[size];

          String priceText;
          if (minPrice != null && maxPrice != null) {
            priceText = '$size: ${minPrice.toStringAsFixed(2)} - ${maxPrice.toStringAsFixed(2)} RON';
          } else {
            priceText = '$size: ${basePrice.toStringAsFixed(2)} RON';
          }
          parts.add(priceText);
        }
      }
      return parts.join(' | ');
    }

    // Handle fixed pricing with potential range
    if (minPrice != null && maxPrice != null) {
      return '${minPrice!.toStringAsFixed(2)} - ${maxPrice!.toStringAsFixed(2)} RON';
    }
    return '${price.toStringAsFixed(2)} RON';
  }

  /// Get price for specific pet size
  double getPriceForSize(String petSize) {
    if (sizePrices != null && sizePrices!.containsKey(petSize)) {
      return sizePrices![petSize]!;
    }
    return price;
  }

  /// Get price range for specific pet size
  String getPriceRangeForSize(String petSize) {
    if (sizePrices != null && sizePrices!.containsKey(petSize)) {
      final basePrice = sizePrices![petSize]!;
      final minPrice = sizeMinPrices?[petSize];
      final maxPrice = sizeMaxPrices?[petSize];

      if (minPrice != null && maxPrice != null) {
        return '${minPrice.toStringAsFixed(2)} - ${maxPrice.toStringAsFixed(2)} RON';
      }
      return '${basePrice.toStringAsFixed(2)} RON';
    }

    // Fixed pricing
    if (minPrice != null && maxPrice != null) {
      return '${minPrice!.toStringAsFixed(2)} - ${maxPrice!.toStringAsFixed(2)} RON';
    }
    return '${price.toStringAsFixed(2)} RON';
  }

  /// Check if service has price ranges
  bool get hasPriceRanges {
    return (minPrice != null && maxPrice != null) ||
           (sizeMinPrices != null && sizeMaxPrices != null);
  }

  /// Get formatted price for specific pet size
  String getFormattedPriceForSize(String petSize) {
    return getPriceRangeForSize(petSize);
  }

  String get formattedDuration {
    if (sizeDurations != null && sizeDurations!.isNotEmpty) {
      final parts = <String>[];
      if (sizeDurations!['S'] != null) {
        parts.add('S: ${_formatDuration(sizeDurations!['S']!)}');
      }
      if (sizeDurations!['M'] != null) {
        parts.add('M: ${_formatDuration(sizeDurations!['M']!)}');
      }
      if (sizeDurations!['L'] != null) {
        parts.add('L: ${_formatDuration(sizeDurations!['L']!)}');
      }
      return parts.join(' | ');
    }
    return _formatDuration(duration);
  }

  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '$minutes min';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      if (remainingMinutes == 0) {
        return '${hours}h';
      } else {
        return '${hours}h ${remainingMinutes}min';
      }
    }
  }

  Map<String, dynamic> toJson() {
    // Import the service management service for category conversion
    // We'll handle this in the service layer instead
    return {
      'id': id,
      'name': name,
      'duration': duration,
      'price': price,
      if (minPrice != null) 'minPrice': minPrice,
      if (maxPrice != null) 'maxPrice': maxPrice,
      if (sizePrices != null) 'sizePrices': sizePrices,
      if (sizeMinPrices != null) 'sizeMinPrices': sizeMinPrices,
      if (sizeMaxPrices != null) 'sizeMaxPrices': sizeMaxPrices,
      if (sizeDurations != null) 'sizeDurations': sizeDurations,
      'description': description,
      'isActive': isActive,
      'displayOrder': displayOrder,
      'requirements': requirements,
      'category': category, // This will be the enum value when sending to backend
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  factory Service.fromJson(Map<String, dynamic> json) {
    try {
      // Import ServiceManagementService for category conversion
      // We'll handle this conversion in the service layer to avoid circular imports
      return Service(
        id: json['id']?.toString() ?? '',
        name: json['name'] ?? '',
        duration: json['duration'] ?? 0,
        price: (json['price'] as num?)?.toDouble() ?? 0.0,
        minPrice: (json['minPrice'] as num?)?.toDouble(),
        maxPrice: (json['maxPrice'] as num?)?.toDouble(),
        sizePrices: json['sizePrices'] != null
            ? Map<String, double>.from((json['sizePrices'] as Map).map((key, value) => MapEntry(key.toString(), (value as num).toDouble())))
            : null,
        sizeMinPrices: json['sizeMinPrices'] != null
            ? Map<String, double>.from((json['sizeMinPrices'] as Map).map((key, value) => MapEntry(key.toString(), (value as num).toDouble())))
            : null,
        sizeMaxPrices: json['sizeMaxPrices'] != null
            ? Map<String, double>.from((json['sizeMaxPrices'] as Map).map((key, value) => MapEntry(key.toString(), (value as num).toDouble())))
            : null,
        sizeDurations: json['sizeDurations'] != null
            ? Map<String, int>.from((json['sizeDurations'] as Map).map((key, value) => MapEntry(key.toString(), (value as num).toInt())))
            : null,
        description: json['description'] ?? '',
        isActive: json['isActive'] ?? true,
        displayOrder: json['displayOrder'] ?? 0,
        requirements: List<String>.from(json['requirements'] ?? []),
        category: json['category'] ?? '', // Will be converted in service layer
        createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
        updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      );
    } catch (e) {
      rethrow;
    }
  }

  Service copyWith({
    String? id,
    String? name,
    int? duration,
    double? price,
    double? minPrice,
    double? maxPrice,
    Map<String, double>? sizePrices,
    Map<String, double>? sizeMinPrices,
    Map<String, double>? sizeMaxPrices,
    Map<String, int>? sizeDurations,
    String? description,
    bool? isActive,
    int? displayOrder,
    List<String>? requirements,
    String? category,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Service(
      id: id ?? this.id,
      name: name ?? this.name,
      duration: duration ?? this.duration,
      price: price ?? this.price,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      sizePrices: sizePrices ?? this.sizePrices,
      sizeMinPrices: sizeMinPrices ?? this.sizeMinPrices,
      sizeMaxPrices: sizeMaxPrices ?? this.sizeMaxPrices,
      sizeDurations: sizeDurations ?? this.sizeDurations,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      displayOrder: displayOrder ?? this.displayOrder,
      requirements: requirements ?? this.requirements,
      category: category ?? this.category,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Service && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'Service(id: $id, name: $name, duration: $duration, price: $price, sizePrices: $sizePrices, category: $category, isActive: $isActive)';
  }
}
