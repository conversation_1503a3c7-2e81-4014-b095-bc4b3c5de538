import 'package:flutter_test/flutter_test.dart';
import 'package:partykidsapp/models/service.dart';

void main() {
  group('Service Min-Max Price Tests', () {
    test('Service with fixed price range should format correctly', () {
      final service = Service(
        id: '1',
        name: 'Test Service',
        description: 'Test description',
        price: 50.0,
        minPrice: 40.0,
        maxPrice: 60.0,
        duration: 60,
        createdAt: DateTime.now(),
      );

      expect(service.formattedPrice, '40.00 - 60.00 RON');
    });

    test('Service with single fixed price should format correctly', () {
      final service = Service(
        id: '1',
        name: 'Test Service',
        description: 'Test description',
        price: 50.0,
        duration: 60,
        createdAt: DateTime.now(),
      );

      expect(service.formattedPrice, '50.00 RON');
    });

    test('Service with variable pricing and ranges should format correctly', () {
      final service = Service(
        id: '1',
        name: 'Test Service',
        description: 'Test description',
        price: 50.0,
        duration: 60,
        sizePrices: {
          'S': 30.0,
          'M': 50.0,
          'L': 70.0,
        },
        sizeMinPrices: {
          'S': 25.0,
          'M': 45.0,
          'L': 65.0,
        },
        sizeMaxPrices: {
          'S': 35.0,
          'M': 55.0,
          'L': 75.0,
        },
        createdAt: DateTime.now(),
      );

      expect(service.formattedPrice, 'S: 25.00 - 35.00 RON | M: 45.00 - 55.00 RON | L: 65.00 - 75.00 RON');
    });

    test('Service with variable pricing without ranges should format correctly', () {
      final service = Service(
        id: '1',
        name: 'Test Service',
        description: 'Test description',
        price: 50.0,
        duration: 60,
        sizePrices: {
          'S': 30.0,
          'M': 50.0,
          'L': 70.0,
        },
        createdAt: DateTime.now(),
      );

      expect(service.formattedPrice, 'S: 30.00 RON | M: 50.00 RON | L: 70.00 RON');
    });

    test('Service toJson should include min-max prices', () {
      final service = Service(
        id: '1',
        name: 'Test Service',
        description: 'Test description',
        price: 50.0,
        minPrice: 40.0,
        maxPrice: 60.0,
        duration: 60,
        createdAt: DateTime.now(),
      );

      final json = service.toJson();
      expect(json['minPrice'], 40.0);
      expect(json['maxPrice'], 60.0);
    });

    test('Service fromJson should parse min-max prices', () {
      final json = {
        'id': '1',
        'name': 'Test Service',
        'description': 'Test description',
        'price': 50.0,
        'minPrice': 40.0,
        'maxPrice': 60.0,
        'duration': 60,
        'isActive': true,
        'displayOrder': 0,
        'requirements': <String>[],
        'category': 'Test',
        'createdAt': DateTime.now().toIso8601String(),
      };

      final service = Service.fromJson(json);
      expect(service.minPrice, 40.0);
      expect(service.maxPrice, 60.0);
    });

    test('Service copyWith should handle min-max prices', () {
      final service = Service(
        id: '1',
        name: 'Test Service',
        description: 'Test description',
        price: 50.0,
        duration: 60,
        createdAt: DateTime.now(),
      );

      final updatedService = service.copyWith(
        minPrice: 40.0,
        maxPrice: 60.0,
      );

      expect(updatedService.minPrice, 40.0);
      expect(updatedService.maxPrice, 60.0);
      expect(updatedService.price, 50.0); // Original price should remain
    });

    test('Service getPriceForSize should return correct price', () {
      final service = Service(
        id: '1',
        name: 'Test Service',
        description: 'Test description',
        price: 50.0,
        duration: 60,
        sizePrices: {
          'S': 30.0,
          'M': 50.0,
          'L': 70.0,
        },
        createdAt: DateTime.now(),
      );

      expect(service.getPriceForSize('S'), 30.0);
      expect(service.getPriceForSize('M'), 50.0);
      expect(service.getPriceForSize('L'), 70.0);
      expect(service.getPriceForSize('XL'), 50.0); // Fallback to base price
    });

    test('Service getPriceRangeForSize should return correct range', () {
      final service = Service(
        id: '1',
        name: 'Test Service',
        description: 'Test description',
        price: 50.0,
        duration: 60,
        sizePrices: {
          'S': 30.0,
          'M': 50.0,
          'L': 70.0,
        },
        sizeMinPrices: {
          'S': 25.0,
          'M': 45.0,
          'L': 65.0,
        },
        sizeMaxPrices: {
          'S': 35.0,
          'M': 55.0,
          'L': 75.0,
        },
        createdAt: DateTime.now(),
      );

      expect(service.getPriceRangeForSize('S'), '25.00 - 35.00 RON');
      expect(service.getPriceRangeForSize('M'), '45.00 - 55.00 RON');
      expect(service.getPriceRangeForSize('L'), '65.00 - 75.00 RON');
    });

    test('Service hasPriceRanges should detect ranges correctly', () {
      final serviceWithFixedRange = Service(
        id: '1',
        name: 'Test Service',
        description: 'Test description',
        price: 50.0,
        minPrice: 40.0,
        maxPrice: 60.0,
        duration: 60,
        createdAt: DateTime.now(),
      );

      final serviceWithSizeRanges = Service(
        id: '2',
        name: 'Test Service 2',
        description: 'Test description',
        price: 50.0,
        duration: 60,
        sizeMinPrices: {'S': 25.0},
        sizeMaxPrices: {'S': 35.0},
        createdAt: DateTime.now(),
      );

      final serviceWithoutRanges = Service(
        id: '3',
        name: 'Test Service 3',
        description: 'Test description',
        price: 50.0,
        duration: 60,
        createdAt: DateTime.now(),
      );

      expect(serviceWithFixedRange.hasPriceRanges, true);
      expect(serviceWithSizeRanges.hasPriceRanges, true);
      expect(serviceWithoutRanges.hasPriceRanges, false);
    });
  });
}
