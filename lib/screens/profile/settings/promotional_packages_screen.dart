import 'package:partykidsapp/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';
import '../../../models/promotional_package.dart';
import '../../../services/promotional_package_service.dart';
import '../../../widgets/forms/promotional_package_form_dialog.dart';
import '../../../config/theme/app_theme.dart';

class PromotionalPackagesScreen extends StatefulWidget {
  const PromotionalPackagesScreen({super.key});

  @override
  State<PromotionalPackagesScreen> createState() => _PromotionalPackagesScreenState();
}

class _PromotionalPackagesScreenState extends State<PromotionalPackagesScreen> {
  List<PromotionalPackage> packages = [];
  bool isLoading = true;
  String? error;

  @override
  void initState() {
    super.initState();
    _loadPackages();
  }

  Future<void> _loadPackages() async {
    setState(() {
      isLoading = true;
      error = null;
    });

    try {
      final response = await PromotionalPackageService.getPackages();
      if (response.success && response.data != null) {
        setState(() {
          packages = response.data!;
          isLoading = false;
        });
      } else {
        setState(() {
          error = response.error ?? 'Failed to load promotional packages';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        error = 'Error loading promotional packages: $e';
        isLoading = false;
      });
    }
  }

  void _showAddPackageDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PromotionalPackageFormDialog(
        onPackageSaved: (_) => _loadPackages(),
      ),
    );
  }

  void _showEditPackageDialog(PromotionalPackage package) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PromotionalPackageFormDialog(
        package: package,
        onPackageSaved: (_) => _loadPackages(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Pachete Promoționale',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        elevation: 0,
      ),
      body: Column(
        children: [
          // Header with add button
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    '${packages.length} pachete disponibile',
                    style: TextStyle(
                      fontSize: 16,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed: () => _showAddPackageDialog(),
                  child: const Icon(Icons.add),
                ),
              ],
            ),
          ),
          // Packages list
          Expanded(
            child: _buildContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'Eroare la încărcarea pachetelor',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error!,
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadPackages,
              child: const Text('Încearcă din nou'),
            ),
          ],
        ),
      );
    }

    if (packages.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.card_giftcard_outlined,
              size: 64,
              color: Colors.grey.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'Nu ai pachete promoționale',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Creează primul tău pachet promoțional pentru a oferi reduceri clienților',
              textAlign: TextAlign.center,
              style: TextStyle(
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _showAddPackageDialog,
              child: const Text('Creează primul pachet'),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: packages.length,
      itemBuilder: (context, index) {
        final package = packages[index];
        return _buildPackageCard(package);
      },
    );
  }

  Widget _buildPackageCard(PromotionalPackage package) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _showEditPackageDialog(package),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with name and status
              Row(
                children: [
                  Expanded(
                    child: Text(
                      package.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  // Status indicator
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: package.isActive ? AppColors.forestGreen : Colors.grey,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      package.isActive ? 'Activ' : 'Inactiv',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Description
              if (package.description.isNotEmpty) ...[
                Text(
                  package.description,
                  style: TextStyle(
                    color: Colors.grey.shade600,
                    fontSize: 14,
                  ),
                ),
                const SizedBox(height: 8),
              ],
              // Package details
              Row(
                children: [
                  Icon(Icons.room_service, size: 16, color: Colors.grey.shade600),
                  const SizedBox(width: 4),
                  Text(
                    '${package.serviceCount} servicii',
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Icon(Icons.access_time, size: 16, color: Colors.grey.shade600),
                  const SizedBox(width: 4),
                  Text(
                    package.formattedDuration,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              // Price information
              Row(
                children: [
                  Text(
                    package.formattedPrice,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppColors.forestGreen,
                    ),
                  ),
                  if (package.hasDiscount) ...[
                    const SizedBox(width: 8),
                    Text(
                      package.formattedOriginalPrice,
                      style: TextStyle(
                        fontSize: 14,
                        decoration: TextDecoration.lineThrough,
                        color: Colors.grey.shade600,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        package.formattedDiscount,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    showTopSnackBar(
      context,
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.forestGreen,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    showTopSnackBar(
      context,
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
