# GitHub Secrets Setup for iOS CI/CD

## Required Secrets

Go to your GitHub repository → Settings → Secrets and variables → Actions → New repository secret

### 1. App Store Connect API Key
- **Name:** `APP_STORE_CONNECT_API_KEY`
- **Value:** Base64 encoded content of your `.p8` file
- **How to get:**
  ```bash
  # Encode your API key file
  base64 -i ~/Downloads/AuthKey_3JH9KG86RL.p8 | pbcopy
  ```

### 2. App Store Connect Key ID
- **Name:** `APP_STORE_CONNECT_KEY_ID`
- **Value:** `3JH9KG86RL` (your key ID)

### 3. App Store Connect Issuer ID
- **Name:** `APP_STORE_CONNECT_ISSUER_ID`
- **Value:** `bc1a1bf3-1f75-4702-b97f-dd90530f3a6e` (your issuer ID)

### 4. Match Password (Optional)
- **Name:** `MATCH_PASSWORD`
- **Value:** Password for Fastlane Match (if using)

## Alternative: Apple ID Credentials (Less Secure)

If you prefer using Apple ID instead of API key:

### Apple ID Username
- **Name:** `FASTLANE_USER`
- **Value:** `<EMAIL>`

### App-Specific Password
- **Name:** `FASTLANE_PASSWORD`
- **Value:** `wnwt-hnrh-suwd-qqec`

## Security Best Practices

1. **Use App Store Connect API Key** (recommended over Apple ID)
2. **Rotate secrets regularly**
3. **Use environment-specific secrets** for staging/production
4. **Enable branch protection rules**
5. **Require PR reviews** before merging to main

## Verification

After adding secrets, check:
- [ ] All secrets are added without typos
- [ ] API key file is properly base64 encoded
- [ ] Workflow has proper permissions
- [ ] Branch protection rules are configured
