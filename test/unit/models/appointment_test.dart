import 'package:flutter_test/flutter_test.dart';
import 'package:partykidsapp/models/appointment.dart';

void main() {
  group('Appointment Model Tests', () {
    group('Constructor Tests', () {
      test('should create appointment with required fields', () {
        final startTime = DateTime(2024, 3, 15, 10, 0);
        final endTime = DateTime(2024, 3, 15, 11, 0);
        
        final appointment = Appointment(
          id: '1',
          clientId: 'client1',
          clientName: '<PERSON>',
          clientPhone: '+40728626399',
          petId: 'pet1',
          petName: 'Buddy',
          petSpecies: 'dog',
          service: 'Grooming',
          startTime: startTime,
          endTime: endTime,
          status: 'confirmed',
          isPaid: false,
        );

        expect(appointment.id, equals('1'));
        expect(appointment.clientId, equals('client1'));
        expect(appointment.clientName, equals('<PERSON>'));
        expect(appointment.clientPhone, equals('+40728626399'));
        expect(appointment.petId, equals('pet1'));
        expect(appointment.petName, equals('Buddy'));
        expect(appointment.petSpecies, equals('dog'));
        expect(appointment.service, equals('Grooming'));
        expect(appointment.services, isEmpty);
        expect(appointment.startTime, equals(startTime));
        expect(appointment.endTime, equals(endTime));
        expect(appointment.status, equals('confirmed'));
        expect(appointment.isPaid, isFalse);
        expect(appointment.notes, equals(''));
        expect(appointment.assignedGroomer, equals(''));
        expect(appointment.totalPrice, equals(0.0));
        expect(appointment.totalDuration, equals(60));
        expect(appointment.repetitionFrequency, equals('none'));
      });

      test('should create appointment with all fields', () {
        final startTime = DateTime(2024, 3, 15, 14, 30);
        final endTime = DateTime(2024, 3, 15, 16, 0);
        
        final appointment = Appointment(
          id: '2',
          clientId: 'client2',
          clientName: 'Maria Popescu',
          clientPhone: '+40755123456',
          petId: 'pet2',
          petName: 'Whiskers',
          petSpecies: 'cat',
          service: 'Full Grooming',
          services: ['Bathing', 'Nail Trimming', 'Brushing'],
          startTime: startTime,
          endTime: endTime,
          status: 'completed',
          isPaid: true,
          notes: 'Cat is very nervous, handle with care',
          assignedGroomer: 'Ana Groomer',
          totalPrice: 150.0,
          totalDuration: 90,
          repetitionFrequency: 'monthly',
        );

        expect(appointment.id, equals('2'));
        expect(appointment.clientId, equals('client2'));
        expect(appointment.clientName, equals('Maria Popescu'));
        expect(appointment.clientPhone, equals('+40755123456'));
        expect(appointment.petId, equals('pet2'));
        expect(appointment.petName, equals('Whiskers'));
        expect(appointment.petSpecies, equals('cat'));
        expect(appointment.service, equals('Full Grooming'));
        expect(appointment.services, equals(['Bathing', 'Nail Trimming', 'Brushing']));
        expect(appointment.startTime, equals(startTime));
        expect(appointment.endTime, equals(endTime));
        expect(appointment.status, equals('completed'));
        expect(appointment.isPaid, isTrue);
        expect(appointment.notes, equals('Cat is very nervous, handle with care'));
        expect(appointment.assignedGroomer, equals('Ana Groomer'));
        expect(appointment.totalPrice, equals(150.0));
        expect(appointment.totalDuration, equals(90));
        expect(appointment.repetitionFrequency, equals('monthly'));
      });
    });

    group('Duration Calculation Tests', () {
      test('should calculate duration correctly', () {
        final startTime = DateTime(2024, 3, 15, 10, 0);
        final endTime = DateTime(2024, 3, 15, 11, 30);
        
        final appointment = Appointment(
          id: '1',
          clientId: 'client1',
          clientName: 'Test Client',
          clientPhone: '+40728626399',
          petId: 'pet1',
          petName: 'Test Pet',
          petSpecies: 'dog',
          service: 'Grooming',
          startTime: startTime,
          endTime: endTime,
          status: 'confirmed',
          isPaid: false,
        );

        final duration = appointment.durationInMinutes;
        expect(duration, equals(90)); // 1.5 hours = 90 minutes
      });

      test('should handle same start and end time', () {
        final time = DateTime(2024, 3, 15, 10, 0);
        
        final appointment = Appointment(
          id: '1',
          clientId: 'client1',
          clientName: 'Test Client',
          clientPhone: '+40728626399',
          petId: 'pet1',
          petName: 'Test Pet',
          petSpecies: 'dog',
          service: 'Quick Service',
          startTime: time,
          endTime: time,
          status: 'confirmed',
          isPaid: false,
        );

        final duration = appointment.durationInMinutes;
        expect(duration, equals(0));
      });
    });

    group('JSON Serialization Tests - New Backend Structure', () {
      test('should create appointment from new backend JSON structure', () {
        final json = {
          'id': '1',
          'client': {
            'id': 'client123',
            'name': 'John Doe',
            'phone': '+40728626399',
          },
          'pet': {
            'id': 'pet456',
            'name': 'Buddy',
            'breed': 'Golden Retriever',
          },
          'staff': {
            'id': 'staff789',
            'name': 'Maria Groomer',
          },
          'services': [
            {'name': 'Full Grooming', 'price': 100.0, 'duration': 60},
            {'name': 'Nail Trimming', 'price': 25.0, 'duration': 15},
          ],
          'appointmentDate': '2024-03-15',
          'startTime': '10:00',
          'endTime': '11:15',
          'status': 'SCHEDULED',
          'notes': 'First time client',
          'totalPrice': 125.0,
          'totalDuration': 75,
          'repetitionFrequency': 'none',
        };

        final appointment = Appointment.fromJson(json);

        expect(appointment.id, equals('1'));
        expect(appointment.clientId, equals('client123'));
        expect(appointment.clientName, equals('John Doe'));
        expect(appointment.clientPhone, equals('+40728626399'));
        expect(appointment.petId, equals('pet456'));
        expect(appointment.petName, equals('Buddy'));
        expect(appointment.petSpecies, equals('Golden Retriever'));
        expect(appointment.service, equals('Full Grooming'));
        expect(appointment.services, equals(['Full Grooming', 'Nail Trimming']));
        expect(appointment.status, equals('SCHEDULED'));
        expect(appointment.notes, equals('First time client'));
        expect(appointment.assignedGroomer, equals('Maria Groomer'));
        expect(appointment.totalPrice, equals(125.0));
        expect(appointment.totalDuration, equals(75));
        expect(appointment.repetitionFrequency, equals('none'));
      });

      test('should handle missing nested objects in new backend structure', () {
        final json = {
          'id': '2',
          'appointmentDate': '2024-03-16',
          'startTime': '2024-03-16T14:00:00.000Z', // Use full ISO format
          'endTime': '2024-03-16T15:00:00.000Z',   // Use full ISO format
          'status': 'PENDING',
        };

        final appointment = Appointment.fromJson(json);

        expect(appointment.id, equals('2'));
        expect(appointment.clientId, equals(''));
        expect(appointment.clientName, equals(''));
        expect(appointment.clientPhone, equals(''));
        expect(appointment.petId, equals(''));
        expect(appointment.petName, equals(''));
        expect(appointment.petSpecies, equals(''));
        expect(appointment.assignedGroomer, equals(''));
        expect(appointment.status, equals('PENDING'));
      });
    });

    group('JSON Serialization Tests - Legacy Structure', () {
      test('should create appointment from legacy JSON structure', () {
        final json = {
          'id': '3',
          'clientId': 'client789',
          'clientName': 'Ana Ionescu',
          'clientPhone': '+40755987654',
          'petId': 'pet321',
          'petName': 'Fluffy',
          'petSpecies': 'cat',
          'service': 'Basic Grooming',
          'services': ['Bathing', 'Brushing'],
          'startTime': '2024-03-17T09:00:00.000Z',
          'endTime': '2024-03-17T10:30:00.000Z',
          'status': 'confirmed',
          'isPaid': true,
          'notes': 'Regular client',
          'assignedGroomer': 'Petra Groomer',
          'totalPrice': 80.0,
          'totalDuration': 90,
          'repetitionFrequency': 'weekly',
        };

        final appointment = Appointment.fromJson(json);

        expect(appointment.id, equals('3'));
        expect(appointment.clientId, equals('client789'));
        expect(appointment.clientName, equals('Ana Ionescu'));
        expect(appointment.clientPhone, equals('+40755987654'));
        expect(appointment.petId, equals('pet321'));
        expect(appointment.petName, equals('Fluffy'));
        expect(appointment.petSpecies, equals('cat'));
        expect(appointment.service, equals('Basic Grooming'));
        expect(appointment.services, equals(['Bathing', 'Brushing']));
        expect(appointment.startTime, equals(DateTime.parse('2024-03-17T09:00:00.000Z')));
        expect(appointment.endTime, equals(DateTime.parse('2024-03-17T10:30:00.000Z')));
        expect(appointment.status, equals('confirmed'));
        expect(appointment.isPaid, isTrue);
        expect(appointment.notes, equals('Regular client'));
        expect(appointment.assignedGroomer, equals('Petra Groomer'));
        expect(appointment.totalPrice, equals(80.0));
        expect(appointment.totalDuration, equals(90));
        expect(appointment.repetitionFrequency, equals('weekly'));
      });

      test('should handle assignedCoworker fallback in legacy structure', () {
        final json = {
          'id': '4',
          'clientId': 'client456',
          'clientName': 'Test Client',
          'clientPhone': '+40728626399',
          'petId': 'pet123',
          'petName': 'Test Pet',
          'petSpecies': 'dog',
          'service': 'Test Service',
          'startTime': '2024-03-18T10:00:00.000Z',
          'endTime': '2024-03-18T11:00:00.000Z',
          'status': 'pending',
          'isPaid': false,
          'assignedCoworker': 'Legacy Groomer Name',
        };

        final appointment = Appointment.fromJson(json);

        expect(appointment.assignedGroomer, equals('Legacy Groomer Name'));
      });
    });

    group('JSON Conversion Tests', () {
      test('should convert appointment to JSON correctly', () {
        final startTime = DateTime(2024, 3, 15, 10, 0);
        final endTime = DateTime(2024, 3, 15, 11, 30);
        
        final appointment = Appointment(
          id: '1',
          clientId: 'client1',
          clientName: 'Test Client',
          clientPhone: '+40728626399',
          petId: 'pet1',
          petName: 'Test Pet',
          petSpecies: 'dog',
          service: 'Test Service',
          services: ['Service1', 'Service2'],
          startTime: startTime,
          endTime: endTime,
          status: 'confirmed',
          isPaid: true,
          notes: 'Test notes',
          assignedGroomer: 'Test Groomer',
          totalPrice: 100.0,
          totalDuration: 90,
          repetitionFrequency: 'monthly',
        );

        final json = appointment.toJson();

        expect(json['id'], equals('1'));
        expect(json['clientId'], equals('client1'));
        expect(json['clientName'], equals('Test Client'));
        expect(json['clientPhone'], equals('+40728626399'));
        expect(json['petId'], equals('pet1'));
        expect(json['petName'], equals('Test Pet'));
        expect(json['petSpecies'], equals('dog'));
        expect(json['service'], equals('Test Service'));
        expect(json['services'], equals(['Service1', 'Service2']));
        expect(json['startTime'], equals(startTime.toIso8601String()));
        expect(json['endTime'], equals(endTime.toIso8601String()));
        expect(json['status'], equals('confirmed'));
        expect(json['isPaid'], isTrue);
        expect(json['notes'], equals('Test notes'));
        expect(json['assignedGroomer'], equals('Test Groomer'));
        expect(json['assignedCoworker'], equals('Test Groomer')); // Backward compatibility
        expect(json['totalPrice'], equals(100.0));
        expect(json['totalDuration'], equals(90));
        expect(json['repetitionFrequency'], equals('monthly'));
      });
    });

    group('CopyWith Tests', () {
      test('should copy appointment with updated fields', () {
        final originalStart = DateTime(2024, 3, 15, 10, 0);
        final originalEnd = DateTime(2024, 3, 15, 11, 0);
        final newStart = DateTime(2024, 3, 15, 14, 0);
        final newEnd = DateTime(2024, 3, 15, 15, 30);
        
        final original = Appointment(
          id: '1',
          clientId: 'client1',
          clientName: 'Original Client',
          clientPhone: '+40728626399',
          petId: 'pet1',
          petName: 'Original Pet',
          petSpecies: 'dog',
          service: 'Original Service',
          startTime: originalStart,
          endTime: originalEnd,
          status: 'pending',
          isPaid: false,
          totalPrice: 50.0,
        );

        final updated = original.copyWith(
          clientName: 'Updated Client',
          petName: 'Updated Pet',
          service: 'Updated Service',
          startTime: newStart,
          endTime: newEnd,
          status: 'confirmed',
          isPaid: true,
          totalPrice: 100.0,
        );

        // Updated fields
        expect(updated.clientName, equals('Updated Client'));
        expect(updated.petName, equals('Updated Pet'));
        expect(updated.service, equals('Updated Service'));
        expect(updated.startTime, equals(newStart));
        expect(updated.endTime, equals(newEnd));
        expect(updated.status, equals('confirmed'));
        expect(updated.isPaid, isTrue);
        expect(updated.totalPrice, equals(100.0));

        // Unchanged fields
        expect(updated.id, equals('1'));
        expect(updated.clientId, equals('client1'));
        expect(updated.clientPhone, equals('+40728626399'));
        expect(updated.petId, equals('pet1'));
        expect(updated.petSpecies, equals('dog'));
      });
    });

    group('Edge Cases', () {
      test('should handle null services in legacy JSON', () {
        final json = {
          'id': '5',
          'clientId': 'client1',
          'clientName': 'Test Client',
          'clientPhone': '+40728626399',
          'petId': 'pet1',
          'petName': 'Test Pet',
          'petSpecies': 'dog',
          'service': 'Main Service',
          'services': null,
          'startTime': '2024-03-15T10:00:00.000Z',
          'endTime': '2024-03-15T11:00:00.000Z',
          'status': 'confirmed',
          'isPaid': false,
        };

        final appointment = Appointment.fromJson(json);

        expect(appointment.services, equals(['Main Service']));
      });

      test('should handle missing service field in legacy JSON', () {
        final json = {
          'id': '6',
          'clientId': 'client1',
          'clientName': 'Test Client',
          'clientPhone': '+40728626399',
          'petId': 'pet1',
          'petName': 'Test Pet',
          'petSpecies': 'dog',
          'startTime': '2024-03-15T10:00:00.000Z',
          'endTime': '2024-03-15T11:00:00.000Z',
          'status': 'confirmed',
          'isPaid': false,
        };

        final appointment = Appointment.fromJson(json);

        expect(appointment.service, equals(''));
        expect(appointment.services, equals(['']));
      });
    });
  });
}
