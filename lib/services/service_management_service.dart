
import '../models/service.dart';
import '../models/api_response.dart';
import 'api_service.dart';
import 'auth/auth_service.dart';

class ServiceManagementService {
  // Get all services for the current salon
  static Future<ApiResponse<List<Service>>> getServices({
    bool? isActive,
    String? category,
    String? search,
    double? minPrice,
    double? maxPrice,
    int? minDuration,
    int? maxDuration,
  }) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<List<Service>>.error('No salon selected');
      }

      final queryParams = <String, String>{};

      if (isActive != null) queryParams['isActive'] = isActive.toString();
      if (category != null && category.isNotEmpty) queryParams['category'] = category;
      if (search != null && search.isNotEmpty) queryParams['search'] = search;
      if (minPrice != null) queryParams['minPrice'] = minPrice.toString();
      if (maxPrice != null) queryParams['maxPrice'] = maxPrice.toString();
      if (minDuration != null) queryParams['minDuration'] = minDuration.toString();
      if (maxDuration != null) queryParams['maxDuration'] = maxDuration.toString();

      final response = await ApiService.get<List<Service>>(
        '/api/salons/$salonId/services',
        queryParams: queryParams.isNotEmpty ? queryParams : null,
        fromJson: (data) {
          // Handle the backend response format: {"services":[],"totalCount":0,"activeCount":0,"inactiveCount":0}
          if (data is Map<String, dynamic> && data.containsKey('services')) {
            final servicesList = data['services'] as List;
            return servicesList.map((item) {
              final service = Service.fromJson(item);
              // Convert backend enum category to display name
              return service.copyWith(
                category: getCategoryDisplayName(service.category),
              );
            }).toList();
          }
          // Fallback: assume it's a direct list
          return (data as List).map((item) {
            final service = Service.fromJson(item);
            // Convert backend enum category to display name
            return service.copyWith(
              category: getCategoryDisplayName(service.category),
            );
          }).toList();
        },
      );

      return response;
    } catch (e) {
      return ApiResponse<List<Service>>.error('Failed to load services: $e');
    }
  }

  // Get service by ID
  static Future<ApiResponse<Service>> getService(String id) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Service>.error('No salon selected');
      }

      final response = await ApiService.get<Service>(
        '/api/salons/$salonId/services/$id',
        fromJson: (data) {
          final service = Service.fromJson(data);
          // Convert backend enum category to display name
          return service.copyWith(
            category: getCategoryDisplayName(service.category),
          );
        },
      );

      return response;
    } catch (e) {
      return ApiResponse<Service>.error('Failed to load service: $e');
    }
  }

  // Create new service
  static Future<ApiResponse<Service>> createService(Service service) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Service>.error('No salon selected');
      }

      // Convert display name to enum value for backend
      final serviceForBackend = service.copyWith(
        category: getCategoryEnumValue(service.category),
      );

      final response = await ApiService.post<Service>(
        '/api/salons/$salonId/services',
        body: serviceForBackend.toJson(),
        fromJson: (data) {
          final createdService = Service.fromJson(data);
          // Convert backend enum category to display name
          return createdService.copyWith(
            category: getCategoryDisplayName(createdService.category),
          );
        },
      );

      return response;
    } catch (e) {
      return ApiResponse<Service>.error('Failed to create service: $e');
    }
  }

  // Update existing service
  static Future<ApiResponse<Service>> updateService(String id, Service service) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Service>.error('No salon selected');
      }

      // Convert display name to enum value for backend
      final serviceForBackend = service.copyWith(
        category: getCategoryEnumValue(service.category),
      );

      final response = await ApiService.put<Service>(
        '/api/salons/$salonId/services/$id',
        body: serviceForBackend.toJson(),
        fromJson: (data) {
          final updatedService = Service.fromJson(data);
          // Convert backend enum category to display name
          return updatedService.copyWith(
            category: getCategoryDisplayName(updatedService.category),
          );
        },
      );

      return response;
    } catch (e) {
      return ApiResponse<Service>.error('Failed to update service: $e');
    }
  }

  // Deactivate service (soft delete)
  static Future<ApiResponse<void>> deleteService(String id) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<void>.error('No salon selected');
      }

      final response = await ApiService.post<void>('/api/salons/$salonId/services/$id');
      return response;
    } catch (e) {
      return ApiResponse<void>.error('Failed to deactivate service: $e');
    }
  }

  // Permanently delete service (hard delete)
  static Future<ApiResponse<void>> permanentDeleteService(String id) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<void>.error('No salon selected');
      }

      final response = await ApiService.delete<void>('/api/salons/$salonId/services/$id');
      return response;
    } catch (e) {
      return ApiResponse<void>.error('Failed to permanently delete service: $e');
    }
  }

  // Get popular services (most booked)
  static Future<ApiResponse<List<Service>>> getPopularServices({int limit = 10}) async {
    final response = await ApiService.get<List<Service>>(
      '/api/services/popular',
      queryParams: {'limit': limit.toString()},
      fromJson: (data) => (data as List).map((item) => Service.fromJson(item)).toList(),
    );

    return response;
  }

  // Get service statistics
  static Future<ApiResponse<Map<String, dynamic>>> getServiceStats(String serviceId) async {
    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/services/$serviceId/stats',
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Validate service data
  static String? validateService(Service service) {
    if (service.name.trim().isEmpty) {
      return 'Numele serviciului este obligatoriu';
    }

    if (service.name.trim().length > 255) {
      return 'Numele serviciului nu poate depăși 255 de caractere';
    }

    if (service.description.trim().length > 1000) {
      return 'Descrierea nu poate depăși 1000 de caractere';
    }

    // Validate pricing
    if (service.sizePrices != null && service.sizePrices!.isNotEmpty) {
      for (final price in service.sizePrices!.values) {
        if (price < 0.01) {
          return 'Prețul trebuie să fie cel puțin 0.01 RON';
        }
      }

      // Validate size-based price ranges if they exist
      if (service.sizeMinPrices != null && service.sizeMaxPrices != null) {
        for (final size in ['S', 'M', 'L']) {
          final minPrice = service.sizeMinPrices![size];
          final maxPrice = service.sizeMaxPrices![size];
          if (minPrice != null && maxPrice != null) {
            if (minPrice < 0.01) {
              return 'Prețul minim trebuie să fie cel puțin 0.01 RON';
            }
            if (maxPrice < 0.01) {
              return 'Prețul maxim trebuie să fie cel puțin 0.01 RON';
            }
            if (minPrice >= maxPrice) {
              return 'Prețul minim trebuie să fie mai mic decât prețul maxim pentru mărimea $size';
            }
          }
        }
      }
    } else {
      if (service.price < 0.01) {
        return 'Prețul trebuie să fie cel puțin 0.01 RON';
      }

      // Validate fixed price range if it exists
      if (service.minPrice != null && service.maxPrice != null) {
        if (service.minPrice! < 0.01) {
          return 'Prețul minim trebuie să fie cel puțin 0.01 RON';
        }
        if (service.maxPrice! < 0.01) {
          return 'Prețul maxim trebuie să fie cel puțin 0.01 RON';
        }
        if (service.minPrice! >= service.maxPrice!) {
          return 'Prețul minim trebuie să fie mai mic decât prețul maxim';
        }
      }
    }

    if (service.duration < 1 || service.duration > 480) {
      return 'Durata trebuie să fie între 1 și 480 de minute (1-8 ore)';
    }

    if (service.category.trim().isEmpty) {
      return 'Categoria serviciului este obligatorie';
    }

    return null; // No validation errors
  }

  // Get service categories with backend enum mapping
  static Map<String, String> getServiceCategoriesMap() {
    return {
      'GROOMING': 'Tuns și Aranjat',
      'BATHING': 'Spălat și Uscat',
      'STYLING': 'Stilizare și Finisare',
      'NAIL_CARE': 'Îngrijire Unghii',
      'DENTAL_CARE': 'Îngrijire Dinți',
      'SPECIALTY': 'Tratamente Speciale',
      'PACKAGE': 'Pachete Complete',
    };
  }

  // Get service categories for UI display
  static List<String> getServiceCategories() {
    return getServiceCategoriesMap().values.toList();
  }

  // Get backend enum value from Romanian display name
  static String getCategoryEnumValue(String displayName) {
    final categoriesMap = getServiceCategoriesMap();
    for (final entry in categoriesMap.entries) {
      if (entry.value == displayName) {
        return entry.key;
      }
    }
    return 'GROOMING'; // Default fallback
  }

  // Get Romanian display name from backend enum value
  static String getCategoryDisplayName(String enumValue) {
    final categoriesMap = getServiceCategoriesMap();
    return categoriesMap[enumValue] ?? 'Tuns și Aranjat'; // Default fallback
  }

  // Get common service requirements
  static List<String> getCommonRequirements() {
    return [
      'Vaccinare la zi',
      'Deparazitare recentă',
      'Fără boli contagioase',
      'Animal sociabil',
      'Programare în avans',
      'Prezența proprietarului',
      'Certificat veterinar',
      'Istoric medical',
    ];
  }

  // Search services (convenience method)
  static Future<ApiResponse<List<Service>>> searchServices(String query) async {
    return getServices(search: query);
  }

  // Get services by category (convenience method)
  static Future<ApiResponse<List<Service>>> getServicesByCategory(String category) async {
    return getServices(category: category);
  }

  // Get active services only (convenience method)
  static Future<ApiResponse<List<Service>>> getActiveServices() async {
    return getServices(isActive: true);
  }

  // Get services by price range (convenience method)
  static Future<ApiResponse<List<Service>>> getServicesByPriceRange({
    double? minPrice,
    double? maxPrice,
  }) async {
    return getServices(minPrice: minPrice, maxPrice: maxPrice);
  }

  // Toggle service active status
  static Future<ApiResponse<Service>> toggleServiceStatus(String serviceId, bool isActive) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Service>.error('No salon selected');
      }

      final response = await ApiService.patch<Service>(
        '/api/salons/$salonId/services/$serviceId/toggle-status',
        body: {'isActive': isActive},
        fromJson: (data) {
          final service = Service.fromJson(data);
          // Convert backend enum category to display name
          return service.copyWith(
            category: getCategoryDisplayName(service.category),
          );
        },
      );

      return response;
    } catch (e) {
      return ApiResponse<Service>.error('Failed to toggle service status: $e');
    }
  }

  // Duplicate service
  static Future<ApiResponse<Service>> duplicateService(String serviceId, {String? newName}) async {
    try {
      final salonId = await AuthService.getCurrentSalonId();
      if (salonId == null) {
        return ApiResponse<Service>.error('No salon selected');
      }

      final body = newName != null ? {'newName': newName} : <String, dynamic>{};

      final response = await ApiService.post<Service>(
        '/api/salons/$salonId/services/$serviceId/duplicate',
        body: body,
        fromJson: (data) {
          final service = Service.fromJson(data);
          // Convert backend enum category to display name
          return service.copyWith(
            category: getCategoryDisplayName(service.category),
          );
        },
      );

      return response;
    } catch (e) {
      return ApiResponse<Service>.error('Failed to duplicate service: $e');
    }
  }
}
