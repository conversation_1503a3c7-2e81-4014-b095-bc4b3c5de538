import 'package:flutter_test/flutter_test.dart';

// Import all drag-and-drop related tests
import 'widgets/calendar_views/draggable_appointment_block_test.dart' as draggable_tests;
import 'services/appointment/appointment_drag_drop_service_test.dart' as service_tests;
import 'widgets/calendar_views/droppable_time_slot_test.dart' as droppable_tests;
import 'integration/appointment_drag_drop_integration_test.dart' as integration_tests;

/// Test runner for all drag-and-drop functionality tests
/// 
/// Run with: flutter test test/drag_drop_test_runner.dart
void main() {
  group('Drag and Drop Test Suite', () {
    group('Widget Tests', () {
      group('DraggableAppointmentBlock Tests', () {
        draggable_tests.main();
      });

      group('DroppableTimeSlot Tests', () {
        droppable_tests.main();
      });
    });

    group('Service Tests', () {
      group('AppointmentDragDropService Tests', () {
        service_tests.main();
      });
    });

    group('Integration Tests', () {
      group('Complete Drag and Drop Workflow Tests', () {
        integration_tests.main();
      });
    });
  });
}
