import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'dart:io' show Platform;
import '../config/environment.dart';

class FirebaseConfig {
  static bool isInitialized = false;

  /// Reset the initialization state (useful for testing or hot reload scenarios)
  static void resetInitializationState() {
    isInitialized = false;
    debugPrint('🔧 Firebase initialization state reset');
  }

  /// Check if Firebase is properly initialized
  static bool get isFirebaseReady {
    try {
      Firebase.app();
      return true;
    } catch (e) {
      return false;
    }
  }

  static Future<bool> initializeFirebase() async {
    // Check if Firebase is already initialized
    try {
      // Try to get the default Firebase app to see if it's already initialized
      Firebase.app();
      debugPrint('🔧 Firebase already initialized, skipping...');
      isInitialized = true;
      return true;
    } on FirebaseException catch (e) {
      // If we get a no-app error, Firebase is not initialized yet
      if (e.code == 'no-app') {
        debugPrint('🔧 Firebase not initialized yet, proceeding with initialization...');
      } else {
        // Some other Firebase error occurred
        debugPrint('🔧 Firebase error during check: ${e.code} - ${e.message}');
        return false;
      }
    } catch (e) {
      // Any other error
      debugPrint('🔧 Unexpected error checking Firebase state: $e');
    }

    // Double-check our internal flag
    if (isInitialized) {
      debugPrint('🔧 Firebase marked as initialized, skipping...');
      return true;
    }

    try {
      FirebaseOptions? options;

      // Get environment-specific Firebase configuration
      final firebaseConfig = EnvironmentConfig.firebaseConfig;
      final bundleId = EnvironmentConfig.bundleId;

      if (kIsWeb) {
        // Web configuration
        options = FirebaseOptions(
          apiKey: firebaseConfig['apiKey']!,
          appId: firebaseConfig['webAppId'] ?? firebaseConfig['appId']!,
          messagingSenderId: firebaseConfig['messagingSenderId']!,
          projectId: firebaseConfig['projectId']!,
          authDomain: firebaseConfig['authDomain']!,
        );
      } else if (Platform.isIOS) {
        // iOS configuration - Using environment-specific settings
        options = FirebaseOptions(
          apiKey: firebaseConfig['apiKey']!,
          appId: firebaseConfig['iosAppId'] ?? firebaseConfig['appId']!,
          messagingSenderId: firebaseConfig['messagingSenderId']!,
          projectId: firebaseConfig['projectId']!,
          authDomain: firebaseConfig['authDomain']!,
          iosBundleId: bundleId,
        );
      } else if (Platform.isAndroid) {
        // Android configuration - Using environment-specific settings
        options = FirebaseOptions(
          apiKey: firebaseConfig['apiKey']!,
          appId: firebaseConfig['androidAppId'] ?? firebaseConfig['appId']!,
          messagingSenderId: firebaseConfig['messagingSenderId']!,
          projectId: firebaseConfig['projectId']!,
          authDomain: firebaseConfig['authDomain']!,
        );
      }

      if (options != null) {
        debugPrint('🔧 Initializing Firebase with environment-specific options...');
        debugPrint('🌍 Environment: ${EnvironmentConfig.currentEnvironment.name}');
        debugPrint('📦 Bundle ID: ${EnvironmentConfig.bundleId}');
        await Firebase.initializeApp(options: options);
      } else {
        debugPrint('🔧 Using default Firebase initialization...');
        await Firebase.initializeApp();
      }
      debugPrint('✅ Firebase initialized successfully');
      debugPrint('🔍 FIREBASE CONFIG VERIFICATION:');
      debugPrint('- Environment: ${EnvironmentConfig.currentEnvironment.name}');
      debugPrint('- Project ID: ${Firebase.app().options.projectId}');
      debugPrint('- App ID: ${Firebase.app().options.appId}');
      debugPrint('- API Key: ${Firebase.app().options.apiKey}');
      debugPrint('- Auth Domain: ${Firebase.app().options.authDomain}');
      if (!kIsWeb && Platform.isIOS) {
        debugPrint('- iOS Bundle ID: ${Firebase.app().options.iosBundleId}');
      }
      isInitialized = true;
      debugPrint('✅ Firebase initialization completed successfully');
      return true;
    } on FirebaseException catch (e) {
      // Handle Firebase-specific errors
      if (e.code == 'duplicate-app') {
        debugPrint('🔧 Firebase app already exists, marking as initialized');
        isInitialized = true;
        return true;
      } else {
        debugPrint('❌ Firebase initialization failed with FirebaseException: ${e.code} - ${e.message}');
        isInitialized = false;
        return false;
      }
    } catch (e) {
      debugPrint('❌ Firebase initialization failed with error: $e');
      // Instead of silently falling back to mock mode, we'll show an error
      // This ensures the app doesn't proceed with mock authentication
      debugPrint('Firebase initialization failed - real authentication is required');
      isInitialized = false;
      return false;
    }
  }
}
