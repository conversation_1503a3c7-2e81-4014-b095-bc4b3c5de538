import 'package:flutter/material.dart';
import '../../config/theme/app_theme.dart';
import '../../models/working_hours_settings.dart';
import '../../services/staff_validation_error_service.dart';

/// Beautiful dialog for displaying staff schedule validation errors with business hours context
class ScheduleValidationDialog extends StatelessWidget {
  final String errorMessage;
  final WorkingHoursSettings? businessHours;
  final VoidCallback? onRetry;
  final VoidCallback? onCancel;

  const ScheduleValidationDialog({
    super.key,
    required this.errorMessage,
    this.businessHours,
    this.onRetry,
    this.onCancel,
  });

  /// Day name mappings for business hours display
  static const Map<String, String> _dayNames = {
    'monday': AppStrings.monday,
    'tuesday': AppStrings.tuesday,
    'wednesday': AppStrings.wednesday,
    'thursday': AppStrings.thursday,
    'friday': AppStrings.friday,
    'saturday': AppStrings.saturday,
    'sunday': AppStrings.sunday,
  };

  /// Show the validation dialog
  static Future<void> show({
    required BuildContext context,
    required String errorMessage,
    WorkingHoursSettings? businessHours,
    VoidCallback? onRetry,
    VoidCallback? onCancel,
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (context) => ScheduleValidationDialog(
        errorMessage: errorMessage,
        businessHours: businessHours,
        onRetry: onRetry,
        onCancel: onCancel,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final parsedError = StaffValidationErrorService.parseValidationError(errorMessage);
    final isValidationError = StaffValidationErrorService.shouldShowBusinessHoursContext(errorMessage);

    debugPrint('🔍 Dialog building with error: $errorMessage');
    debugPrint('🔍 Parsed error: $parsedError');
    debugPrint('🔍 Is validation error: $isValidationError');
    debugPrint('🔍 Business hours provided: ${businessHours != null}');

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 8,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: isValidationError ? Colors.orange.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    isValidationError ? Icons.schedule : Icons.error_outline,
                    color: isValidationError ? Colors.orange : Colors.red,
                    size: 28,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      StaffValidationErrorService.getErrorTitle(errorMessage),
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Error message
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: (isValidationError ? Colors.orange : Colors.red).withValues(alpha: 0.05),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: (isValidationError ? Colors.orange : Colors.red).withValues(alpha: 0.2),
                      ),
                    ),
                    child: Text(
                      parsedError,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black87,
                        height: 1.4,
                      ),
                    ),
                  ),

                  // Business hours context
                  if (isValidationError && businessHours != null) ...[
                    const SizedBox(height: 20),
                    _buildBusinessHoursSection(),
                  ],
                ],
              ),
            ),

            // Actions
            Padding(
              padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (onCancel != null) ...[
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        onCancel?.call();
                      },
                      child: Text(
                        AppStrings.cancel,
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                  ],
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                      onRetry?.call();
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.forestGreen,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(
                      onRetry != null ? AppStrings.retry : AppStrings.ok,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build business hours context section
  Widget _buildBusinessHoursSection() {
    if (businessHours == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.forestGreen.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.forestGreen.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.business,
                color: AppColors.forestGreen,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                AppStrings.businessHoursTitle,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: AppColors.forestGreen,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ..._buildBusinessHoursList(),
        ],
      ),
    );
  }

  /// Build business hours list
  List<Widget> _buildBusinessHoursList() {
    if (businessHours == null) return [];

    final weekDays = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    
    return weekDays.map((day) {
      final schedule = businessHours!.getScheduleForDay(day);
      final dayName = _dayNames[day] ?? day;
      
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 2),
        child: Row(
          children: [
            SizedBox(
              width: 80,
              child: Text(
                dayName,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '-',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade400,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                schedule?.isWorkingDay == true
                    ? '${schedule!.startTime} - ${schedule.endTime}'
                    : AppStrings.closed,
                style: TextStyle(
                  fontSize: 14,
                  color: schedule?.isWorkingDay == true ? Colors.black87 : Colors.grey.shade500,
                  fontWeight: schedule?.isWorkingDay == true ? FontWeight.w500 : FontWeight.normal,
                ),
              ),
            ),
          ],
        ),
      );
    }).toList();
  }
}
