import 'package:partykidsapp/utils/snack_bar_utils.dart';
import 'package:partykidsapp/screens/profile/settings/notification_settings_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import '../config/theme/app_theme.dart';
import '../services/invitation_service.dart';
import '../services/notification_service.dart';
import '../services/appointment/appointment_service.dart';
import '../providers/role_provider.dart';
import '../models/salon_invitation.dart';
import '../models/notification_history.dart';
import 'main_layout.dart';

class NotificationsScreen extends StatefulWidget {

  const NotificationsScreen({super.key, this.unreadCountNotifier});

  /// Global key for accessing state from outside the widget tree
  static final GlobalKey<_NotificationsScreenState> globalKey =
      GlobalKey<_NotificationsScreenState>();

  /// Notifier used to update the unread count badge on bottom navigation
  final ValueNotifier<int>? unreadCountNotifier;

  /// Convenience method to trigger a history reload from outside
  static void loadHistory() {
    globalKey.currentState?._loadNotificationHistory();
  }

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen> {
  List<SalonInvitation> _pendingInvitations = [];
  List<NotificationHistory> _notificationHistory = [];
  bool _isLoadingInvitations = false;
  bool _isLoadingNotifications = false;

  @override
  void initState() {
    super.initState();
    _loadPendingInvitations();
    _loadNotificationHistory();
  }

  Future<void> _loadPendingInvitations() async {
    setState(() {
      _isLoadingInvitations = true;
    });

    try {
      // Load real pending invitations from API
      final response = await InvitationService.getPendingInvitations();
      if (response.success && response.data != null) {
        _pendingInvitations = response.data!;
      } else {
        _pendingInvitations = [];
      }
    } catch (e) {
      _pendingInvitations = [];
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingInvitations = false;
        });
      }
    }
  }

  Future<void> _loadNotificationHistory() async {
    setState(() {
      _isLoadingNotifications = true;
    });

    try {
      // Try to load real notification history from paginated API
      final response = await NotificationService.getNotificationsPaginated(
        page: 0,
        pageSize: 50,
      );

      if (response.success && response.data != null) {
        final notifications =
            response.data!['notifications'] as List<NotificationHistory>;
        setState(() {
          _notificationHistory = notifications;
        });

        // Update unread count for badge
        final unread = notifications.where((n) => !n.read).length;
        widget.unreadCountNotifier?.value = unread;

        if (mounted) {
          debugPrint('📊 Loaded ${notifications.length} notifications');
          debugPrint('📊 Total count: ${response.data!['totalCount']}');
          debugPrint('📊 Unread count: ${response.data!['unreadCount']}');
        }
      } else {
        // If API fails or returns no data, use empty list
        setState(() {
          _notificationHistory = [];
        });
        widget.unreadCountNotifier?.value = 0;
      }
    } catch (e) {
      // If API is not available or returns wrong format, create sample notifications
      // This provides a graceful fallback while the backend is being developed
      debugPrint('⚠️ Notification API not available, using fallback: $e');
      setState(() {
        _notificationHistory = _createSampleNotifications();
      });
      widget.unreadCountNotifier?.value =
          _notificationHistory.where((n) => !n.read).length;
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingNotifications = false;
        });
      }
    }
  }

  /// Create sample notifications for fallback when API is not available
  List<NotificationHistory> _createSampleNotifications() {
    final now = DateTime.now();
    return [
      NotificationHistory(
        id: '1',
        title: 'Programare nouă',
        message: 'Maria Popescu a programat o întâlnire pentru Rex',
        type: 'appointment_created',
        read: false,
        timestamp: now.subtract(const Duration(minutes: 15)),
        metadata: {'appointmentId': 'app_123', 'clientId': 'client_456'},
      ),
      NotificationHistory(
        id: '2',
        title: 'Reminder programare',
        message: 'Programare în 30 de minute: Ion Marinescu - Bella',
        type: 'appointment_reminder',
        read: true,
        timestamp: now.subtract(const Duration(hours: 2)),
        metadata: {'appointmentId': 'app_124'},
      ),
      NotificationHistory(
        id: '3',
        title: 'Anulare programare',
        message: 'Elena Dumitrescu a anulat programarea pentru Max',
        type: 'appointment_cancelled',
        read: true,
        timestamp: now.subtract(const Duration(hours: 5)),
        metadata: {'appointmentId': 'app_125'},
      ),
      NotificationHistory(
        id: '4',
        title: 'Programare completată',
        message: 'Programarea pentru Andrei Stoica - Luna a fost finalizată',
        type: 'appointment_completed',
        read: true,
        timestamp: now.subtract(const Duration(days: 1, hours: 3)),
        metadata: {'appointmentId': 'app_126'},
      ),
    ];
  }

  Future<void> _refreshData() async {
    await Future.wait([
      _loadPendingInvitations(),
      _loadNotificationHistory(),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    final unreadCount = _notificationHistory.where((n) => !n.read).length;

    return Scaffold(
      appBar: AppBar(
        title: Text('Notificări'),
        actions: [
          IconButton(
            icon:  Icon(Icons.settings),
            onPressed: () => Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const NotificationSettingsScreen()),
            ),
            tooltip: 'Setări notificări',
          ),
        ],
      ),
      body: Column(
        children: [
          // Pending invitations section
          if (_isLoadingInvitations || _pendingInvitations.isNotEmpty) ...[
            _buildInvitationsSection(),
            const Divider(height: 1),
          ],
          // Header with statistics
          _buildNotificationHeader(unreadCount),
          // Notifications list
          Expanded(
            child: RefreshIndicator(
              onRefresh: _refreshData,
              color: Theme.of(context).colorScheme.onSurface,
              child: _buildNotificationsList(),
            ),
          ),
        ],
      ),
      // Removed debug FloatingActionButton previously used for test APIs
    );
  }

  Widget _buildNotificationHeader(int unreadCount) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.notifications,
                        color: Theme.of(context).colorScheme.onSurface,
                        size: 24,
                      ),
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${_notificationHistory.length} notificări',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            unreadCount > 0 ? '$unreadCount necitite' : 'Toate citite',
                            style: TextStyle(
                              fontSize: 14,
                              color: unreadCount > 0
                                  ? Theme.of(context).colorScheme.error
                                  : Theme.of(context).colorScheme.onSurfaceVariant,
                              fontWeight: unreadCount > 0 ? FontWeight.bold : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (unreadCount > 0)
                      TextButton(
                        onPressed: _markAllAsRead,
                        child: Text('Marchează toate ca citite'),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationsList() {
    if (_isLoadingNotifications) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_notificationHistory.isEmpty) {
      return ListView(
        children: [
          SizedBox(height: 100),
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.notifications_none,
                  size: 64,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                SizedBox(height: 16),
                Text(
                  'Nu există notificări',
                  style: TextStyle(
                    fontSize: 18,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 8),
                Text(
                  'Notificările vor apărea aici',
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _notificationHistory.length,
      itemBuilder: (context, index) {
        final notification = _notificationHistory[index];
        return _buildSwipeableNotificationItem(notification, index);
      },
    );
  }

  Widget _buildSwipeableNotificationItem(NotificationHistory notification, int index) {
    return Dismissible(
      key: Key(notification.id),
      direction: DismissDirection.endToStart,
      background: Container(
        alignment: Alignment.centerRight,
        padding: const EdgeInsets.only(right: 20.0),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.error,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.delete_outline,
              color: Theme.of(context).colorScheme.onError,
              size: 28,
            ),
            const SizedBox(height: 4),
            Text(
              'Șterge',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onError,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
      confirmDismiss: (direction) async {
        // Vibrate for haptic feedback
        HapticFeedback.mediumImpact();
        
        // Return true to confirm the dismiss
        return true;
      },
      onDismissed: (direction) {
        _deleteNotification(index);
      },
      child: _buildNotificationItem(notification, index),
    );
  }

  Widget _buildNotificationItem(NotificationHistory notification, int index) {
    final isUnread = !notification.read;
    IconData typeIcon;
    Color typeColor;

    switch (notification.type) {
      case 'appointment_created':
      case 'new_appointment':
        typeIcon = Icons.calendar_month_rounded;
        typeColor = Colors.green;
        break;
      case 'appointment_reminder':
      case 'reminder':
        typeIcon = Icons.access_time_rounded;
        typeColor = Colors.blue;
        break;
      case 'appointment_cancelled':
      case 'appointment_cancellation':
      case 'cancellation':
        typeIcon = Icons.event_busy_rounded;
        typeColor = Colors.red;
        break;
      case 'appointment_completed':
        typeIcon = Icons.task_alt_rounded;
        typeColor = Colors.green;
        break;
      case 'appointment_updated':
        typeIcon = Icons.edit_calendar_rounded;
        typeColor = Colors.orange;
        break;
      case 'system_maintenance':
        typeIcon = Icons.engineering_rounded;
        typeColor = Colors.purple;
        break;
      case 'report':
        typeIcon = Icons.insights_rounded;
        typeColor = Colors.purple;
        break;
      case 'payment_confirmation':
        typeIcon = Icons.payments_rounded;
        typeColor = Colors.green;
        break;
      case 'team_member_update':
        typeIcon = Icons.group_rounded;
        typeColor = Colors.blue;
        break;
      default:
        typeIcon = Icons.notifications_rounded;
        typeColor = Colors.grey;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: isUnread ? 3 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: isUnread
          ? BorderSide(color: Theme.of(context).colorScheme.onSurface, width: 1)
          : BorderSide.none,
      ),
      child: InkWell(
        onTap: () => _handleNotificationTap(notification, index),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: typeColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(typeIcon, color: typeColor, size: 24),
              ),
              SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      notification.title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: isUnread ? FontWeight.bold : FontWeight.w600,
                        color: isUnread
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    SizedBox(height: 6),
                    Text(
                      notification.message,
                      style: TextStyle(
                        fontSize: 14,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        height: 1.3,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (notification.appointmentTime != null) ...[
                      SizedBox(height: 4),
                      Text(
                        DateFormat('HH:mm').format(notification.appointmentTime!),
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                    SizedBox(height: 8),
                    Text(
                      _formatNotificationTime(notification.timestamp),
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
              Column(
                children: [
                  if (isUnread)
                    Container(
                      width: 10,
                      height: 10,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.onSurface,
                        shape: BoxShape.circle,
                      ),
                    ),
                  SizedBox(height: 8),
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      if (value == 'delete') {
                        _deleteNotification(index);
                      } else if (value == 'mark_read') {
                        _markAsRead(index);
                      }
                    },
                    itemBuilder: (context) => [
                      if (isUnread)
                        PopupMenuItem(
                          value: 'mark_read',
                          child: Row(
                            children: [
                              Icon(Icons.mark_email_read_rounded, size: 18),
                              SizedBox(width: 8),
                              Text('Marchează ca citită'),
                            ],
                          ),
                        ),
                      PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete_outline_rounded, size: 18, color: Theme.of(context).colorScheme.error),
                            SizedBox(width: 8),
                            Text('Șterge'),
                          ],
                        ),
                      ),
                    ],
                    child: Icon(
                      Icons.more_vert_rounded,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      size: 20,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatNotificationTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return 'Acum';
    } else if (difference.inMinutes < 60) {
      return 'Acum ${difference.inMinutes} min';
    } else if (difference.inHours < 24) {
      return 'Acum ${difference.inHours}h';
    } else if (difference.inDays == 1) {
      return 'Ieri la ${DateFormat('HH:mm').format(time)}';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} zile în urmă';
    } else {
      return DateFormat('dd MMM yyyy, HH:mm', 'ro').format(time);
    }
  }

  Future<void> _markAsRead(int index) async {
    final notification = _notificationHistory[index];
    if (notification.read) return;

    try {
      final response = await NotificationService.markAsRead(notification.id);
      if (response.success) {
        setState(() {
          _notificationHistory[index] = notification.copyWith(read: true);
        });
        widget.unreadCountNotifier?.value =
            _notificationHistory.where((n) => !n.read).length;
      }
    } catch (e) {
      // Fallback: update locally if API is not available
      debugPrint('⚠️ Mark as read API not available, updating locally: $e');
      setState(() {
        _notificationHistory[index] = notification.copyWith(read: true);
      });
      widget.unreadCountNotifier?.value =
          _notificationHistory.where((n) => !n.read).length;
    }
  }

  Future<void> _markAllAsRead() async {
    final unreadNotifications = _notificationHistory.where((n) => !n.read).toList();
    if (unreadNotifications.isEmpty) return;

    try {
      // Use the new bulk mark as read API
      final response = await NotificationService.markNotificationsAsRead(markAll: true);
      if (response.success) {
        setState(() {
          for (int i = 0; i < _notificationHistory.length; i++) {
            if (!_notificationHistory[i].read) {
              _notificationHistory[i] = _notificationHistory[i].copyWith(read: true);
            }
          }
        });
        widget.unreadCountNotifier?.value = 0;
      }
    } catch (e) {
      // Fallback: update locally if API is not available
      debugPrint('⚠️ Mark all as read API not available, updating locally: $e');
      setState(() {
        for (int i = 0; i < _notificationHistory.length; i++) {
          if (!_notificationHistory[i].read) {
            _notificationHistory[i] = _notificationHistory[i].copyWith(read: true);
          }
        }
      });
      widget.unreadCountNotifier?.value = 0;
    }
  }

  Future<void> _deleteNotification(int index) async {
    final notification = _notificationHistory[index];

    try {
      final response = await NotificationService.deleteNotification(notification.id);
      if (response.success) {
        setState(() {
          _notificationHistory.removeAt(index);
        });
        widget.unreadCountNotifier?.value =
            _notificationHistory.where((n) => !n.read).length;

      } else {
        showTopSnackBar(context, 
          SnackBar(
            content: Text('Eroare la ștergerea notificării: ${response.error}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } catch (e) {
      // Fallback: delete locally if API is not available
      debugPrint('⚠️ Delete notification API not available, deleting locally: $e');
      setState(() {
        _notificationHistory.removeAt(index);
      });
      widget.unreadCountNotifier?.value =
          _notificationHistory.where((n) => !n.read).length;

    }
  }

  Future<void> _handleNotificationTap(
      NotificationHistory notification, int index) async {
    await _markAsRead(index);

    final appointmentId = notification.appointmentId;
    if (appointmentId == null) return;

    try {
      final response = await AppointmentService.getAppointment(appointmentId);
      if (!mounted) return;

      if (response.success && response.data != null) {
        final appt = response.data!;
        MainLayout.openAppointmentInCalendar(appt.id);
      } else {
        showTopSnackBar(
          context,
          SnackBar(
            content: const Text('Programarea nu a putut fi găsită.'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } catch (e) {
      if (!mounted) return;
      showTopSnackBar(
        context,
        SnackBar(
          content: Text('Eroare la deschiderea programării: $e'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  Widget _buildInvitationsSection() {
    return Container(
      color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.mail_outline,
                    color: Colors.blue.shade700,
                    size: 20,
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Invitații în Așteptare',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onPrimary,
                        ),
                      ),
                      Text(
                        '${_pendingInvitations.length} invitații de la saloane',
                        style: TextStyle(
                          fontSize: 14,
                          color: Theme.of(context).colorScheme.onPrimary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Constrain invitations height to prevent overflow
          ConstrainedBox(
            constraints: const BoxConstraints(
              maxHeight: 300, // Limit height to prevent overflow
            ),
            child: SingleChildScrollView(
              child: Column(
                children: _pendingInvitations.map((invitation) => _buildInvitationCard(invitation)).toList(),
              ),
            ),
          ),
          SizedBox(height: 8),
        ],
      ),
    );
  }

  Widget _buildInvitationCard(SalonInvitation invitation) {
    final daysLeft = invitation.expiresAt.difference(DateTime.now()).inDays;
    final isExpiringSoon = daysLeft <= 2;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 3), // Reduced vertical margin
      child: Card(
        elevation: 2, // Reduced elevation
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10), // Slightly smaller radius
          side: BorderSide(
            color: isExpiringSoon ? Colors.orange : Colors.blue.shade200,
            width: 1,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(12), // Reduced padding
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          invitation.salonName,
                          style:  TextStyle(
                            fontSize: 16, // Reduced font size
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                        SizedBox(height: 2), // Reduced spacing
                        Text(
                          'Invitat de ${invitation.invitedByName}',
                          style: TextStyle(
                            fontSize: 12, // Reduced font size
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2), // Reduced padding
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      invitation.proposedRole.displayName,
                      style: TextStyle(
                        fontSize: 10, // Reduced font size
                        fontWeight: FontWeight.bold,
                        color: AppColors.forestGreen,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 8), // Reduced spacing

              // Message (truncated if too long)
              if (invitation.message != null) ...[
                Text(
                  invitation.message!.length > 80
                      ? '${invitation.message!.substring(0, 80)}...'
                      : invitation.message!,
                  style: TextStyle(
                    fontSize: 12, // Reduced font size
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    height: 1.2, // Reduced line height
                  ),
                ),
                SizedBox(height: 8), // Reduced spacing
              ],

              // Compact permissions and expiry info
              Row(
                children: [
                  // Permissions info (compact)
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4), // Reduced padding
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.security,
                            size: 12, // Reduced icon size
                            color: Colors.grey.shade600,
                          ),
                          SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              invitation.proposedClientDataPermission.displayName,
                              style: TextStyle(
                                fontSize: 10, // Reduced font size
                                  color: Theme.of(context).colorScheme.onSecondary,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Expiry warning (compact)
                  if (isExpiringSoon) ...[
                    SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade50,
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(color: Colors.orange.shade200),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            Icons.warning_amber,
                            size: 12,
                            color: Colors.orange.shade700,
                          ),
                          SizedBox(width: 4),
                          Text(
                            daysLeft == 0 ? 'Astăzi!' : '${daysLeft}z',
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                              color: Colors.orange.shade700,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
              SizedBox(height: 8), // Reduced spacing

              // Action buttons (compact)
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => _declineInvitation(invitation),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.red,
                        side: BorderSide(color: Colors.red),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 8), // Reduced padding
                      ),
                      child: Text('Refuză', style: TextStyle(fontSize: 12)),
                    ),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: () => _acceptInvitation(invitation),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                        padding: const EdgeInsets.symmetric(vertical: 8), // Reduced padding
                      ),
                      child: Text('Acceptă', style: TextStyle(fontSize: 12, color: Theme.of(context).colorScheme.onPrimary)),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _acceptInvitation(SalonInvitation invitation) async {
    try {
      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) =>  Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
          ),
        ),
      );

      // Call actual API
      final response = await InvitationService.acceptInvitation(invitation.id);

      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog

        if (response.success) {
          // Remove invitation from list
          setState(() {
            _pendingInvitations.removeWhere((inv) => inv.id == invitation.id);
          });

          // Refresh role provider to update permissions
          final roleProvider = context.read<RoleProvider>();
          await roleProvider.refresh();

          showTopSnackBar(context, 
            SnackBar(
              content: Text('Te-ai alăturat cu succes la ${invitation.salonName}!'),
              backgroundColor: Theme.of(context).colorScheme.primary,
              duration: const Duration(seconds: 4),
            ),
          );

          // Navigate to profile screen to see the new salon
          _navigateToProfile();
        } else {
          showTopSnackBar(context, 
            SnackBar(
              content: Text(response.error ?? 'Eroare la acceptarea invitației'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        Navigator.of(context).pop(); // Close loading dialog
        showTopSnackBar(context, 
          SnackBar(
            content: Text('Eroare la acceptarea invitației: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _declineInvitation(SalonInvitation invitation) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Refuzi invitația?'),
        content: Text(
          'Ești sigur că vrei să refuzi invitația de la ${invitation.salonName}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text('Anulează'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text('Refuză'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final response = await InvitationService.declineInvitation(invitation.id);

        if (response.success) {
          if (mounted) {
            // Remove invitation from list
            setState(() {
              _pendingInvitations.removeWhere((inv) => inv.id == invitation.id);
            });

            // Refresh role provider to ensure all state is updated
            final roleProvider = context.read<RoleProvider>();
            await roleProvider.refresh();

            showTopSnackBar(context, 
              SnackBar(
                content: Text('Invitația de la ${invitation.salonName} a fost refuzată'),
                backgroundColor: Colors.orange,
              ),
            );
          }
        } else {
          if (mounted) {
            showTopSnackBar(context, 
              SnackBar(
                content: Text(response.error ?? 'Nu s-a putut refuza invitația'),
                backgroundColor: Colors.red,
              ),
            );
          }
        }
      } catch (e) {
        if (mounted) {
          showTopSnackBar(context, 
            SnackBar(
              content: Text('Eroare la refuzarea invitației: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  /// Navigate to main layout with bottom navigation
  void _navigateToProfile() {
    // Navigate to main layout which will automatically show the calendar tab
    // since the user now has salon association after accepting the invitation
    Navigator.of(context).pushNamedAndRemoveUntil(
      '/',
      (route) => false,
    );
  }

}