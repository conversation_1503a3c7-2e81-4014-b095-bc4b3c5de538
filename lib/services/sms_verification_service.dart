import 'package:flutter/foundation.dart';
import '../models/sms_verification.dart';
import '../models/api_response.dart';
import '../config/api_config.dart';
import '../utils/formatters/phone_number_utils.dart';
import 'api_service.dart';

/// Service for SMS verification functionality
class SmsVerificationService {
  static final Map<String, DateTime> _lastSentTimes = {};
  static const Duration _resendCooldown = Duration(seconds: 60);

  /// Send SMS verification code to phone number
  static Future<ApiResponse<SendSmsVerificationResponse>> sendVerificationCode(
    String phoneNumber,
  ) async {
    try {
      // Validate phone number format
      final validationError = PhoneNumberUtils.getValidationError(phoneNumber);
      if (validationError != null) {
        return ApiResponse<SendSmsVerificationResponse>.error(validationError);
      }

      // Normalize phone number
      final normalizedPhone = PhoneNumberUtils.normalizePhoneNumber(phoneNumber);
      
      // Check resend cooldown
      if (_isInCooldown(normalizedPhone)) {
        final remainingTime = _getRemainingCooldownTime(normalizedPhone);
        return ApiResponse<SendSmsVerificationResponse>.error(
          'Puteți solicita un nou cod în $remainingTime secunde'
        );
      }

      if (ApiConfig.enableLogging) {
        debugPrint('📱 Sending SMS verification to: $normalizedPhone');
      }

      final request = SendSmsVerificationRequest(phoneNumber: normalizedPhone);
      
      final response = await ApiService.post<SendSmsVerificationResponse>(
        '/api/auth/send-sms-verification',
        body: request.toJson(),
        fromJson: (data) => SendSmsVerificationResponse.fromJson(data),
      );

      if (response.success) {
        // Record the send time for cooldown tracking
        _lastSentTimes[normalizedPhone] = DateTime.now();
        
        if (ApiConfig.enableLogging) {
          debugPrint('📱 SMS verification sent successfully');
          debugPrint('   Expires in: ${response.data?.expiresIn ?? 300} seconds');
        }
      }

      return response;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error sending SMS verification: $e');
      }
      return ApiResponse<SendSmsVerificationResponse>.error(
        'Eroare la trimiterea codului SMS: $e'
      );
    }
  }

  /// Verify SMS code
  static Future<ApiResponse<VerifySmsResponse>> verifyCode(
    String phoneNumber,
    String code,
  ) async {
    try {
      // Validate inputs
      if (code.length != 6 || !RegExp(r'^\d{6}$').hasMatch(code)) {
        return ApiResponse<VerifySmsResponse>.error(
          'Codul trebuie să conțină exact 6 cifre'
        );
      }

      // Normalize phone number
      final normalizedPhone = PhoneNumberUtils.normalizePhoneNumber(phoneNumber);

      if (ApiConfig.enableLogging) {
        debugPrint('📱 Verifying SMS code for: $normalizedPhone');
        debugPrint('   Code: $code');
      }

      final request = VerifySmsRequest(
        phoneNumber: normalizedPhone,
        code: code,
      );

      final response = await ApiService.post<VerifySmsResponse>(
        '/api/auth/verify-sms',
        body: request.toJson(),
        fromJson: (data) => VerifySmsResponse.fromJson(data),
      );

      if (response.success) {
        // Clear cooldown on successful verification
        _lastSentTimes.remove(normalizedPhone);
        
        if (ApiConfig.enableLogging) {
          debugPrint('✅ SMS verification successful');
        }
      } else {
        if (ApiConfig.enableLogging) {
          debugPrint('❌ SMS verification failed: ${response.error}');
        }
      }

      return response;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error verifying SMS code: $e');
      }
      return ApiResponse<VerifySmsResponse>.error(
        'Eroare la verificarea codului: $e'
      );
    }
  }

  /// Check if phone number is in resend cooldown
  static bool _isInCooldown(String phoneNumber) {
    final lastSent = _lastSentTimes[phoneNumber];
    if (lastSent == null) return false;
    
    return DateTime.now().difference(lastSent) < _resendCooldown;
  }

  /// Get remaining cooldown time in seconds
  static int _getRemainingCooldownTime(String phoneNumber) {
    final lastSent = _lastSentTimes[phoneNumber];
    if (lastSent == null) return 0;
    
    final elapsed = DateTime.now().difference(lastSent);
    final remaining = _resendCooldown - elapsed;
    
    return remaining.inSeconds > 0 ? remaining.inSeconds : 0;
  }

  /// Get remaining cooldown time for UI display
  static int getRemainingCooldownTime(String phoneNumber) {
    final normalizedPhone = PhoneNumberUtils.normalizePhoneNumber(phoneNumber);
    return _getRemainingCooldownTime(normalizedPhone);
  }

  /// Check if resend is available for phone number
  static bool canResend(String phoneNumber) {
    final normalizedPhone = PhoneNumberUtils.normalizePhoneNumber(phoneNumber);
    return !_isInCooldown(normalizedPhone);
  }

  /// Clear cooldown for phone number (for testing purposes)
  static void clearCooldown(String phoneNumber) {
    final normalizedPhone = PhoneNumberUtils.normalizePhoneNumber(phoneNumber);
    _lastSentTimes.remove(normalizedPhone);
    
    if (ApiConfig.enableLogging) {
      debugPrint('📱 Cleared SMS cooldown for: $normalizedPhone');
    }
  }

  /// Clear all cooldowns (for testing purposes)
  static void clearAllCooldowns() {
    _lastSentTimes.clear();
    
    if (ApiConfig.enableLogging) {
      debugPrint('📱 Cleared all SMS cooldowns');
    }
  }
}
