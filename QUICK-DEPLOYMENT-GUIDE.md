# 🚀 Quick Deployment Guide

## Automatic Deployment (Recommended)

### ✅ Push to Main Branch
```bash
# Make your changes
git add .
git commit -m "✨ Add new feature"
git push origin main
```

**What happens automatically:**
1. 🔍 GitHub detects changes in relevant files
2. 🚫 Tests skipped (for faster deployment)
3. 📱 Increments build number
4. 🏗️ Builds Flutter iOS app
5. 🚀 Uploads to TestFlight
6. 📋 Creates GitHub release

**Time:** ~10-15 minutes total (faster without tests)

---

## Manual Deployment Options

### 🎯 Option 1: Deployment Script (Interactive)
```bash
cd flutter-partykids
./scripts/deploy-ios.sh
```
Choose option 5 for automatic deployment.

### 🎯 Option 2: Deployment Script (Automatic)
```bash
cd flutter-partykids
./scripts/deploy-ios.sh --auto
```

### 🎯 Option 3: Fastlane Direct
```bash
cd flutter-partykids/ios
bundle exec fastlane deploy_main
```

### 🎯 Option 4: GitHub Actions Manual Trigger
1. Go to repository → Actions tab
2. Select "🚀 Auto Deploy to TestFlight (Main Branch)"
3. Click "Run workflow"
4. Choose options:
   - Skip tests: ☐ (for emergency deployments)
   - Force deploy: ☐ (deploy even without changes)

---

## 🔍 Monitoring Deployment

### GitHub Actions
- **URL:** `https://github.com/your-username/your-repo/actions`
- **Workflow:** "🚀 Auto Deploy to TestFlight (Main Branch)"
- **Duration:** ~15-20 minutes

### App Store Connect
- **URL:** `https://appstoreconnect.apple.com`
- **Path:** Your App → TestFlight
- **Processing Time:** 5-15 minutes after upload

### GitHub Releases
- **URL:** `https://github.com/your-username/your-repo/releases`
- **Format:** `v1.0.0+{build_number}`

---

## 🛠 Quick Troubleshooting

### ❌ "Missing required secrets"
```bash
# Add these to GitHub repository secrets:
APP_STORE_CONNECT_KEY_ID="2N84SQNMTJ"
APP_STORE_CONNECT_ISSUER_ID="bc1a1bf3-1f75-4702-b97f-dd90530f3a6e"
APP_STORE_CONNECT_API_KEY="<base64-encoded-p8-file>"
```

### ❌ "No changes detected"
```bash
# Force deployment:
git commit --allow-empty -m "🚀 Force deployment"
git push origin main
```

### ❌ "Build failed"
```bash
# Test locally first:
cd flutter-partykids
flutter clean
flutter pub get
flutter test
flutter analyze
flutter build ios --no-codesign
```

### ❌ "Fastlane error"
```bash
# Update Fastlane:
cd flutter-partykids/ios
bundle update fastlane
bundle exec fastlane --version
```

---

## 📋 Pre-Deployment Checklist

### Before Pushing to Main:
- [ ] ✅ Tests pass locally: `flutter test`
- [ ] ✅ Code analysis clean: `flutter analyze`
- [ ] ✅ iOS build works: `flutter build ios --no-codesign`
- [ ] ✅ Commit message is descriptive
- [ ] ✅ No sensitive data in code

### First-Time Setup:
- [ ] ✅ GitHub secrets configured
- [ ] ✅ App Store Connect API key valid
- [ ] ✅ Bundle ID matches: `ro.partykidsprogramari.partykids`
- [ ] ✅ Team ID correct: `6MB735DMZ5`
- [ ] ✅ Fastlane dependencies installed: `cd ios && bundle install`

---

## 🎯 Deployment Strategies

### 🟢 Regular Development
```bash
# Work on feature branch
git checkout -b feature/new-feature
# ... make changes ...
git commit -m "✨ Add new feature"
git push origin feature/new-feature

# Create PR, review, then merge to main
# Automatic deployment triggers on merge
```

### 🟡 Hotfix Deployment
```bash
# Quick fix on main branch
git checkout main
git pull origin main
# ... make urgent fix ...
git commit -m "🐛 Fix critical issue"
git push origin main
# Automatic deployment triggers immediately
```

### 🔴 Emergency Deployment
```bash
# Manual trigger with tests skipped
# Go to GitHub Actions → Run workflow
# Check "Skip tests" option
# Or use: ./scripts/deploy-ios.sh --auto
```

---

## 📊 Build Numbers

### Automatic Increment
- ✅ Build number auto-increments on each deployment
- ✅ Format: `1.0.0+123` (version+build)
- ✅ Committed back to repository
- ✅ Tagged in GitHub releases

### Manual Override
```bash
# Edit pubspec.yaml before pushing:
version: 1.0.0+124  # Increment manually
```

---

## 🔗 Quick Links

- **GitHub Actions:** [Repository Actions](https://github.com/your-username/your-repo/actions)
- **App Store Connect:** [TestFlight](https://appstoreconnect.apple.com)
- **Fastlane Docs:** [docs.fastlane.tools](https://docs.fastlane.tools)
- **Flutter Docs:** [flutter.dev](https://flutter.dev)

---

## 📞 Need Help?

1. **Check logs:** GitHub Actions → Workflow run → Job details
2. **Test locally:** `./scripts/test-auto-deploy.sh`
3. **Validate setup:** Review `AUTOMATIC-DEPLOYMENT-SETUP.md`
4. **Manual deployment:** `./scripts/deploy-ios.sh`

---

**Happy deploying! 🚀**
