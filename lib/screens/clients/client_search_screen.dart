import 'package:flutter/material.dart';
import '../../models/client.dart';

class ClientSearchScreen extends StatefulWidget {
  final List<Client> availableClients;

  const ClientSearchScreen({
    super.key,
    required this.availableClients,
  });

  @override
  State<ClientSearchScreen> createState() => _ClientSearchScreenState();
}

class _ClientSearchScreenState extends State<ClientSearchScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Client> _filteredClients = [];
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _filteredClients = widget.availableClients;
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    setState(() {
      _searchQuery = _searchController.text.toLowerCase();
      _filteredClients = widget.availableClients.where((client) {
        return client.name.toLowerCase().contains(_searchQuery) ||
               client.phone.toLowerCase().contains(_searchQuery);
      }).toList();
    });
  }

  void _selectClient(Client client) {
    Navigator.of(context).pop(client);
  }

  void _addNewClient() {
    Navigator.of(context).pop('new_client');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Selectează client'),
      ),
      body: Container(
        child: Column(
          children: [
            // Search field
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  labelText: 'Caută client',
                  hintText: 'Nume sau telefon...',
                  prefixIcon:  Icon(Icons.search, color: Theme.of(context).colorScheme.onSurface),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon:  Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                          },
                        )
                      : null,
                  border: const OutlineInputBorder(),
                  focusedBorder:  OutlineInputBorder(
                    borderSide: BorderSide(color: Theme.of(context).colorScheme.onSurface),
                  ),
                ),
              ),
            ),
            
            // Add new client button
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _addNewClient,
                  icon:  Icon(Icons.person_add),
                  label: Text('Adaugă client nou'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ),
            
            SizedBox(height: 16),
            
            // Results header
            if (_filteredClients.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  children: [
                    Text(
                      'Clienți găsiți (${_filteredClients.length})',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ],
                ),
              ),
            
            SizedBox(height: 8),
            
            // Clients list
            Expanded(
              child: _buildClientsList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildClientsList() {
    if (_filteredClients.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SizedBox(height: 16),
            Text(
              _searchQuery.isEmpty 
                  ? 'Nu există clienți disponibili'
                  : 'Nu s-au găsit clienți pentru "$_searchQuery"',
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _filteredClients.length,
      itemBuilder: (context, index) {
        final client = _filteredClients[index];
        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: Text(
                client.name.isNotEmpty ? client.name[0].toUpperCase() : '?',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            title: Text(
              client.name,
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(client.phone),
                if (client.email.isNotEmpty)
                  Text(
                    client.email,
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
              ],
            ),
            trailing: Icon(
              Icons.arrow_forward_ios,
              color: Theme.of(context).colorScheme.onSurface,
              size: 16,
            ),
            onTap: () => _selectClient(client),
          ),
        );
      },
    );
  }
}
