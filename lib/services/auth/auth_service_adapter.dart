import '../../models/api_response.dart';
import 'auth_service.dart';

abstract class AuthServiceAdapter {
  Future<ApiResponse<Map<String, dynamic>>> login({required String email, required String password});
  Future<ApiResponse<Map<String, dynamic>>> register({required String name, required String email, required String password});
  Future<ApiResponse<Map<String, dynamic>>> firebaseLogin(String firebaseToken);
}

class DefaultAuthServiceAdapter implements AuthServiceAdapter {
  const DefaultAuthServiceAdapter();

  @override
  Future<ApiResponse<Map<String, dynamic>>> login({required String email, required String password}) {
    return AuthService.login(email: email, password: password);
  }

  @override
  Future<ApiResponse<Map<String, dynamic>>> register({required String name, required String email, required String password}) {
    return AuthService.register(name: name, email: email, password: password);
  }

  @override
  Future<ApiResponse<Map<String, dynamic>>> firebaseLogin(String firebaseToken) {
    return AuthService.firebaseLogin(firebaseToken);
  }
}
