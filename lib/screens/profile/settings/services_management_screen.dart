import 'package:partykidsapp/utils/snack_bar_utils.dart';

import 'package:flutter/material.dart';

import '../../../models/service.dart';
import '../../../services/service_management_service.dart';
import '../../../widgets/forms/service_form_dialog.dart';

class ServicesManagementScreen extends StatefulWidget {
  const ServicesManagementScreen({super.key});

  @override
  State<ServicesManagementScreen> createState() => _ServicesManagementScreenState();
}

class _ServicesManagementScreenState extends State<ServicesManagementScreen> {
  List<Service> services = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadServices();
  }

  Future<void> _loadServices() async {
    setState(() {
      isLoading = true;
    });

    try {
      final response = await ServiceManagementService.getServices();
      if (response.success && response.data != null) {
        setState(() {
          services = response.data!.cast<Service>();
          isLoading = false;
        });
      } else {
        setState(() {
          isLoading = false;
        });
        _showErrorSnackBar('Eroare la încărcarea serviciilor: ${response.error}');
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      _showErrorSnackBar('Eroare la încărcarea serviciilor: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    showTopSnackBar(context, 
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Gestionare Servicii',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        elevation: 0,
        actions: [
          IconButton(
            icon:  Icon(Icons.refresh),
            onPressed: _loadServices,
          ),
        ],
      ),
      body: Column(
        children: [
          // Header with add button
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    '${services.length} servicii disponibile',
                    style: TextStyle(
                      fontSize: 16,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed: () => _showAddServiceDialog(),
                  child:  Icon(Icons.add),
                ),
              ],
            ),
          ),
          // Services list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: services.length,
              itemBuilder: (context, index) {
                final service = services[index];
                return _buildServiceCard(service);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildServiceCard(Service service) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _showEditServiceDialog(service),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              service.name,
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: service.isActive 
                                  ? Theme.of(context).colorScheme.primary 
                                  : Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                              ),
                            ),
                          ),
                          if (!service.isActive)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.surfaceContainerHighest,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                'INACTIV',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ),
                        ],
                      ),
                      SizedBox(height: 4),
                      if (service.category.isNotEmpty)
                        Text(
                          service.category,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                      if (service.description.isNotEmpty) ...[
                        SizedBox(height: 4),
                        Text(
                          service.description,
                          style: TextStyle(
                            fontSize: 14,
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  onSelected: (value) {
                    if (value == 'edit') {
                      _showEditServiceDialog(service);
                    } else if (value == 'toggle') {
                      _toggleServiceStatus(service);
                    } else if (value == 'duplicate') {
                      _duplicateService(service);
                    } else if (value == 'delete_permanent') {
                      _showPermanentDeleteConfirmation(service);
                    }
                  },
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, color: Theme.of(context).colorScheme.onSurface),
                          const SizedBox(width: 8),
                          const Text('Editează'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'toggle',
                      child: Row(
                        children: [
                          Icon(
                            service.isActive ? Icons.visibility_off : Icons.visibility,
                            color: service.isActive ? Theme.of(context).colorScheme.secondary : Theme.of(context).colorScheme.primary,
                          ),
                          SizedBox(width: 8),
                          Text(service.isActive ? 'Dezactivează' : 'Activează'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'duplicate',
                      child: Row(
                        children: [
                          Icon(Icons.copy, color: Theme.of(context).colorScheme.secondary),
                          const SizedBox(width: 8),
                          const Text('Duplică'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'delete_permanent',
                      child: Row(
                        children: [
                          Icon(Icons.delete_forever, color: Theme.of(context).colorScheme.error),
                          const SizedBox(width: 8),
                          Text('Șterge definitiv', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 12),
            // Use Wrap instead of Row to prevent overflow with long price text
            Wrap(
              spacing: 12,
              runSpacing: 8,
              children: [
                _buildInfoChip(
                  icon: Icons.access_time,
                  label: service.formattedDuration,
                  color: Theme.of(context).colorScheme.secondary,
                ),
                _buildInfoChip(
                  icon: Icons.attach_money,
                  label: service.formattedPrice,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                if (service.requirements.isNotEmpty)
                  _buildInfoChip(
                    icon: Icons.rule,
                    label: '${service.requirements.length} cerințe',
                    color: Theme.of(context).colorScheme.tertiary,
                  ),
              ],
            ),
            if (service.requirements.isNotEmpty) ...[
              SizedBox(height: 8),
              Wrap(
                spacing: 4,
                runSpacing: 4,
                children: service.requirements.take(3).map((requirement) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceContainerHighest,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      requirement,
                      style: TextStyle(
                        fontSize: 10,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
    ));
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          SizedBox(width: 4),
          Flexible(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  void _showAddServiceDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => ServiceFormDialog(
        onServiceSaved: (_) => _loadServices(),
      ),
    );
  }

  void _showEditServiceDialog(Service service) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => ServiceFormDialog(
        service: service,
        onServiceSaved: (_) => _loadServices(),
      ),
    );
  }

  Future<void> _toggleServiceStatus(Service service) async {
    try {
      final response = await ServiceManagementService.toggleServiceStatus(
        service.id,
        !service.isActive,
      );

      if (response.success) {
        _loadServices();
        _showSuccessSnackBar(
          service.isActive
            ? 'Serviciul "${service.name}" a fost dezactivat'
            : 'Serviciul "${service.name}" a fost activat',
        );
      } else {
        _showErrorSnackBar('Eroare: ${response.error}');
      }
    } catch (e) {
      _showErrorSnackBar('Eroare la modificarea statusului: $e');
    }
  }

  Future<void> _duplicateService(Service service) async {
    try {
      final response = await ServiceManagementService.duplicateService(service.id);

      if (response.success && response.data != null) {
        _loadServices();
        _showSuccessSnackBar('Serviciul "${response.data!.name}" a fost duplicat cu succes');
      } else {
        _showErrorSnackBar('Eroare: ${response.error}');
      }
    } catch (e) {
      _showErrorSnackBar('Eroare la duplicarea serviciului: $e');
    }
  }

  void _showSuccessSnackBar(String message) {
    showTopSnackBar(context, 
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle, color: Theme.of(context).colorScheme.onPrimary),
            SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  void _showDeleteConfirmation(Service service) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Confirmare dezactivare'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Ești sigur că vrei să dezactivezi serviciul "${service.name}"?'),
            SizedBox(height: 8),
            Text(
              'Serviciul va fi dezactivat și nu va mai fi disponibil pentru programări noi, dar va rămâne în sistem.',
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Anulează'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _deleteService(service);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.secondary,
              
            ),
            child: Text('Dezactivează'),
          ),
        ],
      ),
    );
  }

  void _showPermanentDeleteConfirmation(Service service) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Confirmare ștergere definitivă'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Ești sigur că vrei să ștergi definitiv serviciul "${service.name}"?'),
            SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.error.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Theme.of(context).colorScheme.error.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.warning, color: Theme.of(context).colorScheme.error, size: 20),
                      SizedBox(width: 8),
                      Text(
                        'ATENȚIE!',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.error,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Această acțiune va șterge definitiv serviciul din sistem. Toate datele asociate vor fi pierdute permanent.',
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Anulează'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _permanentDeleteService(service);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: Text('Șterge definitiv'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteService(Service service) async {
    try {
      final response = await ServiceManagementService.deleteService(service.id);

      if (response.success) {
        _loadServices();
        _showSuccessSnackBar('Serviciul "${service.name}" a fost dezactivat cu succes');
      } else {
        _showErrorSnackBar('Eroare: ${response.error}');
      }
    } catch (e) {
      _showErrorSnackBar('Eroare la dezactivarea serviciului: $e');
    }
  }

  Future<void> _permanentDeleteService(Service service) async {
    try {
      final response = await ServiceManagementService.permanentDeleteService(service.id);

      if (response.success) {
        _loadServices();
        _showSuccessSnackBar('Serviciul "${service.name}" a fost șters definitiv');
      } else {
        _showErrorSnackBar('Eroare: ${response.error}');
      }
    } catch (e) {
      _showErrorSnackBar('Eroare la ștergerea definitivă: $e');
    }
  }
}
