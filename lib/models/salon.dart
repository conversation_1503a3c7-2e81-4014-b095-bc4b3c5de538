import '../utils/formatters/phone_number_utils.dart';

/// Salon model for grooming salons
class Salon {
  final String id;
  final String name;
  final String? description;
  final String address;
  final String city;
  final String? phone;
  final String? email;
  final String ownerId;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  Salon({
    required this.id,
    required this.name,
    this.description,
    required this.address,
    required this.city,
    this.phone,
    this.email,
    required this.ownerId,
    required this.isActive,
    required this.createdAt,
    this.updatedAt,
  });

  /// Create from JSON
  factory Salon.fromJson(Map<String, dynamic> json) {
    return Salon(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'],
      address: json['address'] ?? '',
      city: json['city'] ?? '', // Backend might not return this yet
      phone: json['phone'],
      email: json['email'],
      ownerId: json['ownerId'] ?? '', // Backend might not return this yet
      isActive: json['isActive'] ?? true,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'address': address,
      'city': city,
      'phone': phone,
      'email': email,
      'ownerId': ownerId,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  /// Get formatted phone number for display
  String? get formattedPhone => phone != null ? PhoneNumberUtils.formatForDisplay(phone!) : null;

  /// Get normalized phone number for API calls
  String? get normalizedPhone => phone != null ? PhoneNumberUtils.normalizeForApi(phone!) : null;

  /// Copy with method for immutable updates
  Salon copyWith({
    String? id,
    String? name,
    String? description,
    String? address,
    String? city,
    String? phone,
    String? email,
    String? ownerId,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Salon(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      address: address ?? this.address,
      city: city ?? this.city,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      ownerId: ownerId ?? this.ownerId,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// Request model for creating a new salon
class CreateSalonRequest {
  final String name;
  final String? description;
  final String address;
  final String? city;
  final String? phone;
  final String? email;

  CreateSalonRequest({
    required this.name,
    this.description,
    required this.address,
    this.city,
    String? phone,
    this.email,
  }) : phone = phone != null ? PhoneNumberUtils.normalizeForApi(phone) : null;

  /// Convert to JSON for API request
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'address': address,
      'city': city,
      'phone': phone,
      'email': email,
    };
  }

  /// Validate the salon creation request
  List<String> validate() {
    final errors = <String>[];

    if (name.trim().isEmpty) {
      errors.add('Numele salonului este obligatoriu');
    }

    if (name.trim().length < 3) {
      errors.add('Numele salonului trebuie să aibă cel puțin 3 caractere');
    }

    if (address.trim().isEmpty) {
      errors.add('Adresa este obligatorie');
    }



    if (phone != null && phone!.isNotEmpty) {
      // Use phone number utility for validation
      final phoneError = PhoneNumberUtils.getValidationError(phone!);
      if (phoneError != null) {
        errors.add(phoneError);
      }
    }

    if (email != null && email!.isNotEmpty) {
      // Basic email validation
      final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
      if (!emailRegex.hasMatch(email!)) {
        errors.add('Adresa de email nu este validă');
      }
    }

    return errors;
  }

  /// Check if the request is valid
  bool get isValid => validate().isEmpty;
}

/// Response model for salon creation
class CreateSalonResponse {
  final Salon salon;
  final String message;

  CreateSalonResponse({
    required this.salon,
    required this.message,
  });

  /// Create from JSON
  factory CreateSalonResponse.fromJson(Map<String, dynamic> json) {
    return CreateSalonResponse(
      salon: Salon.fromJson(json['salon']),
      message: json['message'] ?? 'Salon creat cu succes',
    );
  }
}
