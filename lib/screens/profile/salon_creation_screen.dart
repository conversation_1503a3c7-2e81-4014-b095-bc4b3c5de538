import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:geolocator/geolocator.dart';
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../../config/theme/app_theme.dart';
import '../../services/salon_service.dart';
import '../../services/auth/auth_service.dart';
import '../../providers/role_provider.dart';
import '../../models/salon.dart';
import '../../utils/formatters/phone_number_utils.dart';
import '../../config/environment.dart';


import '../../widgets/address_selection/location_selection_button.dart';


import 'package:google_maps_flutter/google_maps_flutter.dart';

class SalonCreationScreen extends StatefulWidget {
  final Salon? salon;
  const SalonCreationScreen({super.key, this.salon});

  bool get isEdit => salon != null;

  @override
  State<SalonCreationScreen> createState() => _SalonCreationScreenState();
}

class _SalonCreationScreenState extends State<SalonCreationScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _addressController = TextEditingController();
  final _addressFocusNode = FocusNode();
  final _addressDetailsController = TextEditingController();
  final _descriptionController = TextEditingController();


  // Animation controllers
  late AnimationController _fadeAnimationController;
  late AnimationController _slideAnimationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;



  LatLng _selectedLocation = const LatLng(
      44.4268, 26.1025); // Default Bucharest (fallback)
  bool _isLoadingLocation = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.isEdit && widget.salon != null) {
      final salon = widget.salon!;
      _nameController.text = salon.name;
      _phoneController.text = salon.phone ?? '';
      _emailController.text = salon.email ?? '';

      // Parse address to separate main address from details
      _parseAndPopulateAddress(salon.address);
      // Load coordinates for existing address
      _loadLocationFromAddress(salon.address);

      _descriptionController.text = salon.description ?? '';
    } else {
      // For new salon creation, try to get user's current location
      _initializeUserLocation();
    }
    _setupAnimations();
    _loadUserPhoneNumber();
    _startAnimations();
  }

  /// Parse the combined address and populate address and address details fields
  void _parseAndPopulateAddress(String fullAddress) {
    final parts = fullAddress.split(',');
    switch (parts.length) {
      case 1:
        _addressController.text = parts[0];
        return;
      case 2:
        _addressController.text = parts[0] + parts[1];
        return;
      case 3:
        _addressController.text = parts[0] + parts[1] + parts[2];
        return;
      case 4:
        _addressController.text = parts[0] + parts[1] + parts[2];
        _addressController.text += parts[3];
    }
  }

  void _setupAnimations() {
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeAnimationController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideAnimationController,
      curve: Curves.elasticOut,
    ));

    // Add listeners to ensure values stay within bounds
    _fadeAnimationController.addListener(() {
      if (_fadeAnimationController.value < 0.0) {
        _fadeAnimationController.value = 0.0;
      } else if (_fadeAnimationController.value > 1.0) {
        _fadeAnimationController.value = 1.0;
      }
    });

    _slideAnimationController.addListener(() {
      if (_slideAnimationController.value < 0.0) {
        _slideAnimationController.value = 0.0;
      } else if (_slideAnimationController.value > 1.0) {
        _slideAnimationController.value = 1.0;
      }
    });
  }

  void _startAnimations() {
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _fadeAnimationController.forward();
        _slideAnimationController.forward();
      }
    });
  }

  @override
  void dispose() {
    _fadeAnimationController.dispose();
    _slideAnimationController.dispose();
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _addressController.dispose();
    _addressFocusNode.dispose();
    _addressDetailsController.dispose();
    _descriptionController.dispose();

    super.dispose();
  }

  /// Load and pre-fill user's phone number for salon
  Future<void> _loadUserPhoneNumber() async {
    try {
      if (_phoneController.text.isNotEmpty) return;
      final userPhone = await AuthService.getCurrentUserPhone();
      if (userPhone != null &&
          userPhone.isNotEmpty &&
          userPhone.replaceAll(' ', '') != '+4000000000' &&
          mounted) {
        setState(() {
          _phoneController.text =
              PhoneNumberUtils.formatForDisplay(userPhone);
        });
      }
    } catch (e) {
      // If we can't get user phone, just leave the field empty
      debugPrint('Could not load user phone number: $e');
    }
  }

  /// Initialize user's current location for new salon creation
  Future<void> _initializeUserLocation() async {
    setState(() {
      _isLoadingLocation = true;
    });

    try {
      // Check location permission
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }

      if (permission == LocationPermission.denied ||
          permission == LocationPermission.deniedForever) {
        // Permission denied, keep Bucharest default
        debugPrint('📍 Location permission denied, using Bucharest default');
        return;
      }

      // Get current position
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      if (mounted) {
        setState(() {
          _selectedLocation = LatLng(position.latitude, position.longitude);
        });
        debugPrint('📍 Initial location set to user position: ${position.latitude}, ${position.longitude}');
      }
    } catch (e) {
      // Error getting location, keep Bucharest default
      debugPrint('📍 Error getting user location: $e, using Bucharest default');
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingLocation = false;
        });
      }
    }
  }

  /// Geocode an address to obtain coordinates when editing a salon
  Future<void> _loadLocationFromAddress(String address) async {
    final url = Uri.parse(
        'https://maps.googleapis.com/maps/api/geocode/json?address=${Uri.encodeComponent(address)}&key=${EnvironmentConfig.googleMapsApiKey}');
    try {
      final response = await http.get(url);
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final results = data['results'] as List<dynamic>?;
        if (results != null && results.isNotEmpty) {
          final loc = results.first['geometry']['location'];
          final lat = loc['lat'] as num?;
          final lng = loc['lng'] as num?;
          if (lat != null && lng != null && mounted) {
            setState(() {
              _selectedLocation = LatLng(lat.toDouble(), lng.toDouble());
            });
          }
        }
      }
    } catch (e) {
      debugPrint('📍 Error geocoding address: $e');
    }
  }



  Future<void> _submitSalon() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Combine address and address details
      String fullAddress = _addressController.text.trim();
      if (_addressDetailsController.text.trim().isNotEmpty) {
        fullAddress += ', ${_addressDetailsController.text.trim()}';
      }

      final request = CreateSalonRequest(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? null
            : _descriptionController.text.trim(),
        address: fullAddress,
        phone: _phoneController.text.trim().isEmpty
            ? null
            : _phoneController.text.trim(),
        email: _emailController.text.trim().isEmpty
            ? null
            : _emailController.text.trim(),
      );

      final response = widget.isEdit
          ? await SalonService.updateSalon(widget.salon!.id, request)
          : await SalonService.createSalon(request);
      debugPrint('🏢 Salon creation response: success=${response.success}');

      if (response.success && response.data != null) {
        final salonId = widget.isEdit
            ? (response.data as Salon).id
            : (response.data as CreateSalonResponse).salon.id;
        debugPrint(
            '🏢 Salon ${widget.isEdit ? 'updated' : 'created'} successfully: $salonId');

        // Refresh role provider to update salon association
        if (mounted) {
          final roleProvider = context.read<RoleProvider>();
          debugPrint('🏢 Refreshing role provider after salon creation...');
          await roleProvider.refresh();
          debugPrint('🏢 Role provider refresh completed');

          // Navigate back immediately and let the parent handle the success message
          if (mounted) {
            Navigator.of(context).pop(true); // Return success
          }
        }
      } else {
        debugPrint('❌ Salon submit failed: ${response.error}');
        throw Exception(response.error ?? 'Failed to save salon');
      }
    } catch (e) {
      if (mounted) {
        debugPrint('❌ Error saving salon: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.isEdit ? 'Editează Salon' : 'Creează Salon Nou',
          style: const TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        elevation: 0,
      ),
      body: AnimatedBuilder(
        animation: _fadeAnimation,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header Card with enhanced animation
                      TweenAnimationBuilder<double>(
                        duration: const Duration(milliseconds: 1200),
                        tween: Tween(begin: 0.0, end: 1.0),
                        curve: Curves.elasticOut,
                        builder: (context, value, child) {
                          return SizedBox(
                            width: double.infinity,
                            child: Transform.scale(
                              scale: 0.8 + (0.2 * value),
                              child: Opacity(
                                opacity: value.clamp(0.0, 1.0),
                                child: _buildHeaderCard(),
                              ),
                            ),
                          );
                        },
                      ),
                      SizedBox(height: 24),

                      // Form Fields with staggered animation
                      TweenAnimationBuilder<double>(
                        duration: const Duration(milliseconds: 1000),
                        tween: Tween(begin: 0.0, end: 1.0),
                        curve: Curves.easeOutCubic,
                        builder: (context, value, child) {
                          return SizedBox(
                            width: double.infinity,
                            child: Transform.translate(
                              offset: Offset(0, 30 * (1 - value)),
                              child: Opacity(
                                opacity: value.clamp(0.0, 1.0),
                                child: _buildFormSection(),
                              ),
                            ),
                          );
                        },
                      ),
                      SizedBox(height: 32),

                      // Create Button with bounce animation
                      TweenAnimationBuilder<double>(
                        duration: const Duration(milliseconds: 1400),
                        tween: Tween(begin: 0.0, end: 1.0),
                        curve: Curves.elasticOut,
                        builder: (context, value, child) {
                          return SizedBox(
                            width: double.infinity,
                            child: Transform.scale(
                              scale: value.clamp(0.0, 1.0),
                              child: _buildCreateButton(),
                            ),
                          );
                        },
                      ),
                      SizedBox(height: 16),

                      // Info Card with fade animation
                      TweenAnimationBuilder<double>(
                        duration: const Duration(milliseconds: 1600),
                        tween: Tween(begin: 0.0, end: 1.0),
                        curve: Curves.easeOut,
                        builder: (context, value, child) {
                          return Opacity(
                            opacity: value.clamp(0.0, 1.0),
                            child: _buildInfoCard(),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeaderCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          children: [
            TweenAnimationBuilder<double>(
              duration: const Duration(milliseconds: 2000),
              tween: Tween(begin: 0.0, end: 1.0),
              curve: Curves.elasticOut,
              builder: (context, value, child) {
                return SizedBox(
                  width: 80,
                  height: 80,
                  child: Transform.rotate(
                    angle: value * 0.1,
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(50),
                      ),
                      child: Icon(
                        Icons.pets,
                        color: Theme.of(context).colorScheme.onSurface,
                        size: 48,
                      ),
                    ),
                  ),
                );
              },
            ),
            SizedBox(height: 16),
            Text(
              'Creează-ți propriul salon',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 8),
            Text(
              'Completează informațiile de mai jos pentru a-ți crea salonul de toaletaj',
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCreateButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _isLoading ? null : _submitSalon,
        style: ElevatedButton.styleFrom(

          
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child: _isLoading
            ? SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.onPrimary),
                ),
              )
            : Text(
                widget.isEdit ? 'Salvează' : 'Creează Salon',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  Widget _buildInfoCard() {
    return Card(
      elevation: 1,
      color: Theme.of(context).colorScheme.surfaceContainerHighest,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: Theme.of(context).colorScheme.onSurface,
              size: 24,
            ),
            SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Informații importante',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'După crearea salonului, vei deveni proprietarul și groomer șef. Vei putea invita alți groomeri să se alăture echipei tale.',
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Informații Salon',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        SizedBox(height: 16),

        // Salon Name
        _buildTextField(
          controller: _nameController,
          label: 'Nume Salon *',
          hint: 'ex. Salon Premium Pet Grooming',
          icon: Icons.pets,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Numele salonului este obligatoriu';
            }
            if (value.trim().length < 3) {
              return 'Numele trebuie să aibă cel puțin 3 caractere';
            }
            return null;
          },
        ),
        SizedBox(height: 16),

        // Phone
        _buildTextField(
          controller: _phoneController,
          label: 'Telefon *',
          hint: '+40 721 123 456',
          icon: Icons.phone,
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Numărul de telefon este obligatoriu';
            }
            final phoneError = PhoneNumberUtils.getValidationError(value);
            if (phoneError != null) {
              return phoneError;
            }
            return null;
          },
        ),
        // Phone number explanation
        Padding(
          padding: const EdgeInsets.only(left: 12, top: 4),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                size: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              SizedBox(width: 4),
              Expanded(
                child: Text(
                  'Acest număr va fi folosit pentru Groomer Șef (proprietarul salonului)',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 16),

        // Email
        _buildTextField(
          controller: _emailController,
          label: 'Email',
          hint: '<EMAIL>',
          icon: Icons.email,
          keyboardType: TextInputType.emailAddress,
          validator: (value) {
            if (value != null && value.trim().isNotEmpty) {
              if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                return 'Adresa de email nu este validă';
              }
            }
            return null;
          },
        ),
        SizedBox(height: 16),

        // Simplified location selection
        LocationSelectionButton(
          selectedAddress: _addressController.text.isNotEmpty ? _addressController.text : null,
          selectedLocation: _selectedLocation,
          label: 'Locația salonului',
          hint: _isLoadingLocation ? 'Se obține locația curentă...' : 'Selectați locația pe hartă',
          isRequired: true,
          showReminderDisclaimer: true,
          onLocationSelected: (location, address) {
            setState(() {
              _selectedLocation = location;
              if (address != null && address.isNotEmpty) {
                _addressController.text = address;
              }
            });
          },
        ),
        SizedBox(height: 16),

        // Address Details
        _buildTextField(
          controller: _addressDetailsController,
          label: 'Detalii adresă (opțional)',
          hint: 'ex. Etajul 2, colțul din dreapta, apartament 15',
          icon: Icons.info_outline,
          maxLines: 2,
          validator: null,
        ),
        // Address details explanation
        Padding(
          padding: const EdgeInsets.only(left: 12, top: 4),
          child: Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                size: 14,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              SizedBox(width: 4),
              Expanded(
                child: Text(
                  'Adaugă detalii suplimentare pentru a ajuta clienții să te găsească mai ușor',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(height: 16),



        // Description
        _buildTextField(
          controller: _descriptionController,
          label: 'Descriere (opțional)',
          hint: 'Scurtă descriere a salonului...',
          icon: Icons.description,
          maxLines: 3,
        ),
      ],
    );
  }



  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    int maxLines = 1,
    String? Function(String?)? validator,
  }) {
    return StandardFormField(
      controller: controller,
      labelText: label,
      hintText: hint,
      prefixIcon: icon,
      keyboardType: keyboardType,
      maxLines: maxLines,
      validator: validator,

    );
  }
}
