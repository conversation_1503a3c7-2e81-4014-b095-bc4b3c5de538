import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';

class DateFormatter {
  // Romanian date formatters
  static final DateFormat _dateFormat = DateFormat('dd.MM.yyyy', 'ro_RO');
  static final DateFormat _timeFormat = DateFormat('HH:mm', 'ro_RO');
  static final DateFormat _dateTimeFormat = DateFormat('dd.MM.yyyy HH:mm', 'ro_RO');
  static final DateFormat _dayMonthFormat = DateFormat('dd MMM', 'ro_RO');
  static final DateFormat _fullDateFormat = DateFormat('EEEE, dd MMMM yyyy', 'ro_RO');
  static final DateFormat _monthYearFormat = DateFormat('MMMM yyyy', 'ro_RO');
  static final DateFormat _shortDateFormat = DateFormat('dd/MM/yy', 'ro_RO');

  // Fallback English formatters (in case Romanian fails)
  static final DateFormat _dateFormatEN = DateFormat('dd.MM.yyyy', 'en_US');
  static final DateFormat _timeFormatEN = DateFormat('HH:mm', 'en_US');
  static final DateFormat _dateTimeFormatEN = DateFormat('dd.MM.yyyy HH:mm', 'en_US');

  /// Format date as "25.12.2024"
  static String formatDate(DateTime date) {
    try {
      return _dateFormat.format(date);
    } catch (e) {
      return _dateFormatEN.format(date);
    }
  }

  /// Format time as "14:30"
  static String formatTime(DateTime time) {
    try {
      return _timeFormat.format(time);
    } catch (e) {
      return _timeFormatEN.format(time);
    }
  }

  /// Format date and time as "25.12.2024 14:30"
  static String formatDateTime(DateTime dateTime) {
    try {
      return _dateTimeFormat.format(dateTime);
    } catch (e) {
      return _dateTimeFormatEN.format(dateTime);
    }
  }

  /// Format as "25 Dec"
  static String formatDayMonth(DateTime date) {
    try {
      return _dayMonthFormat.format(date);
    } catch (e) {
      return DateFormat('dd MMM', 'en_US').format(date);
    }
  }

  /// Format as "Luni, 25 decembrie 2024"
  static String formatFullDate(DateTime date) {
    try {
      return _fullDateFormat.format(date);
    } catch (e) {
      return DateFormat('EEEE, dd MMMM yyyy', 'en_US').format(date);
    }
  }

  /// Format as "decembrie 2024"
  static String formatMonthYear(DateTime date) {
    try {
      return _monthYearFormat.format(date);
    } catch (e) {
      return DateFormat('MMMM yyyy', 'en_US').format(date);
    }
  }

  /// Format as "25/12/24"
  static String formatShortDate(DateTime date) {
    try {
      return _shortDateFormat.format(date);
    } catch (e) {
      return DateFormat('dd/MM/yy', 'en_US').format(date);
    }
  }

  /// Get relative time like "acum 2 ore", "ieri", "săptămâna trecută"
  static String formatRelativeTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'acum';
    } else if (difference.inMinutes < 60) {
      return 'acum ${difference.inMinutes} ${difference.inMinutes == 1 ? 'minut' : 'minute'}';
    } else if (difference.inHours < 24) {
      return 'acum ${difference.inHours} ${difference.inHours == 1 ? 'oră' : 'ore'}';
    } else if (difference.inDays == 1) {
      return 'ieri';
    } else if (difference.inDays < 7) {
      return 'acum ${difference.inDays} zile';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return 'acum $weeks ${weeks == 1 ? 'săptămână' : 'săptămâni'}';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return 'acum $months ${months == 1 ? 'lună' : 'luni'}';
    } else {
      final years = (difference.inDays / 365).floor();
      return 'acum $years ${years == 1 ? 'an' : 'ani'}';
    }
  }

  /// Format appointment time range like "14:30 - 16:00"
  static String formatTimeRange(DateTime start, DateTime end) {
    try {
      return '${formatTime(start)} - ${formatTime(end)}';
    } catch (e) {
      return '${_timeFormatEN.format(start)} - ${_timeFormatEN.format(end)}';
    }
  }

  /// Format duration in minutes to readable format like "2h 30min"
  static String formatDuration(int minutes) {
    if (minutes < 60) {
      return '${minutes}min';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      if (remainingMinutes == 0) {
        return '${hours}h';
      } else {
        return '${hours}h ${remainingMinutes}min';
      }
    }
  }

  /// Get day name in Romanian
  static String getDayName(DateTime date) {
    try {
      return DateFormat('EEEE', 'ro_RO').format(date);
    } catch (e) {
      // Fallback to manual mapping
      const dayNames = [
        'luni', 'marți', 'miercuri', 'joi', 'vineri', 'sâmbătă', 'duminică'
      ];
      return dayNames[date.weekday - 1];
    }
  }

  /// Get month name in Romanian
  static String getMonthName(DateTime date) {
    try {
      return DateFormat('MMMM', 'ro_RO').format(date);
    } catch (e) {
      // Fallback to manual mapping
      const monthNames = [
        'ianuarie', 'februarie', 'martie', 'aprilie', 'mai', 'iunie',
        'iulie', 'august', 'septembrie', 'octombrie', 'noiembrie', 'decembrie'
      ];
      return monthNames[date.month - 1];
    }
  }

  /// Parse date string in various formats
  static DateTime? parseDate(String dateString) {
    try {
      // Try different formats
      final formats = [
        'yyyy-MM-dd',
        'dd.MM.yyyy',
        'dd/MM/yyyy',
        'yyyy-MM-ddTHH:mm:ss',
        'yyyy-MM-ddTHH:mm:ssZ',
      ];

      for (final format in formats) {
        try {
          return DateFormat(format).parse(dateString);
        } catch (e) {
          continue;
        }
      }

      // Try ISO 8601 parsing
      return DateTime.parse(dateString);
    } catch (e) {
      debugPrint('Error parsing date: $dateString - $e');
      return null;
    }
  }

  /// Check if date is today
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month && date.day == now.day;
  }

  /// Check if date is tomorrow
  static bool isTomorrow(DateTime date) {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return date.year == tomorrow.year && date.month == tomorrow.month && date.day == tomorrow.day;
  }

  /// Check if date is yesterday
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return date.year == yesterday.year && date.month == yesterday.month && date.day == yesterday.day;
  }

  /// Get smart date format (Today, Tomorrow, Yesterday, or date)
  static String formatSmartDate(DateTime date) {
    if (isToday(date)) {
      return 'Astăzi';
    } else if (isTomorrow(date)) {
      return 'Mâine';
    } else if (isYesterday(date)) {
      return 'Ieri';
    } else {
      return formatDate(date);
    }
  }

  /// Format for appointment cards: "Astăzi, 14:30" or "25 Dec, 14:30"
  static String formatAppointmentTime(DateTime dateTime) {
    final dateStr = formatSmartDate(dateTime);
    final timeStr = formatTime(dateTime);
    return '$dateStr, $timeStr';
  }
}
