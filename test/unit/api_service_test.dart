import 'package:flutter_test/flutter_test.dart';

import 'package:partykidsapp/services/api_service.dart';
import 'package:partykidsapp/config/environment.dart';

void main() {
  group('ApiService Tests', () {
    setUp(() {
      // Reset ApiService state before each test
      ApiService.clearAuthToken();
    });

    tearDown(() {
      // Clean up after each test
      ApiService.clearAuthToken();
    });

    group('Authentication Token Management', () {
      test('should set and get auth token correctly', () {
        const testToken = 'test-auth-token-123';
        
        ApiService.setAuthToken(testToken);
        
        expect(ApiService.authToken, equals(testToken));
        expect(ApiService.hasAuthToken, isTrue);
      });

      test('should clear auth token correctly', () {
        const testToken = 'test-auth-token-123';
        
        ApiService.setAuthToken(testToken);
        expect(ApiService.hasAuthToken, isTrue);
        
        ApiService.clearAuthToken();
        
        expect(ApiService.authToken, isNull);
        expect(ApiService.hasAuthToken, isFalse);
      });

      test('should return correct headers with auth token', () {
        const testToken = 'test-auth-token-123';
        ApiService.setAuthToken(testToken);
        
        final headers = ApiService.headers;
        
        expect(headers['Authorization'], equals('Bearer $testToken'));
        expect(headers['Content-Type'], equals('application/json'));
        expect(headers['Accept'], equals('application/json'));
      });

      test('should return headers without auth when no token set', () {
        ApiService.clearAuthToken();
        
        final headers = ApiService.headers;
        
        expect(headers.containsKey('Authorization'), isFalse);
        expect(headers['Content-Type'], equals('application/json'));
        expect(headers['Accept'], equals('application/json'));
      });
    });

    group('GET Requests', () {
      test('should handle auth token in headers correctly', () {
        // Arrange
        const testToken = 'test-token';
        ApiService.setAuthToken(testToken);

        // Act
        final headers = ApiService.headers;

        // Assert
        expect(headers['Authorization'], equals('Bearer $testToken'));
        expect(headers['Content-Type'], equals('application/json'));
        expect(headers['Accept'], equals('application/json'));
      });

      test('should detect when auth is required but no token available', () {
        // Arrange
        ApiService.clearAuthToken();

        // Act & Assert
        expect(ApiService.hasAuthToken, isFalse);
        expect(ApiService.authToken, isNull);
      });

      test('should handle headers without auth when not required', () {
        // Arrange
        ApiService.clearAuthToken();

        // Act
        final headers = ApiService.headers;

        // Assert
        expect(headers.containsKey('Authorization'), isFalse);
        expect(headers['Content-Type'], equals('application/json'));
        expect(headers['Accept'], equals('application/json'));
      });
    });

    group('POST Requests', () {
      test('should prepare headers correctly for POST with auth', () {
        // Arrange
        const testToken = 'test-token';
        ApiService.setAuthToken(testToken);

        // Act
        final headers = ApiService.headers;

        // Assert
        expect(headers['Authorization'], equals('Bearer $testToken'));
        expect(headers['Content-Type'], equals('application/json'));
        expect(headers['Accept'], equals('application/json'));
      });

      test('should prepare headers correctly for POST without auth', () {
        // Arrange
        ApiService.clearAuthToken();

        // Act
        final headers = ApiService.headers;

        // Assert
        expect(headers.containsKey('Authorization'), isFalse);
        expect(headers['Content-Type'], equals('application/json'));
        expect(headers['Accept'], equals('application/json'));
      });
    });

    group('Error Handling', () {
      test('should handle missing auth token correctly', () {
        // Arrange
        ApiService.clearAuthToken();

        // Act & Assert
        expect(ApiService.hasAuthToken, isFalse);
        expect(ApiService.authToken, isNull);
      });

      test('should handle auth token validation', () {
        // Arrange & Act
        const validToken = 'valid-token-123';
        ApiService.setAuthToken(validToken);

        // Assert
        expect(ApiService.hasAuthToken, isTrue);
        expect(ApiService.authToken, equals(validToken));

        // Clear and verify
        ApiService.clearAuthToken();
        expect(ApiService.hasAuthToken, isFalse);
        expect(ApiService.authToken, isNull);
      });

      test('should handle empty token correctly', () {
        // Arrange & Act
        ApiService.setAuthToken('');

        // Assert - empty token should be considered as no token
        expect(ApiService.hasAuthToken, isFalse);
        expect(ApiService.authToken, equals(''));
      });
    });

    group('Configuration', () {
      test('should use correct base URL from environment', () {
        expect(ApiService.baseUrl, equals(EnvironmentConfig.apiBaseUrl));
      });

      test('should use correct timeout from environment', () {
        expect(ApiService.defaultTimeout, equals(EnvironmentConfig.apiTimeout));
      });
    });
  });
}
