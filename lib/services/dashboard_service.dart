import '../models/salon_settings.dart';
import '../models/api_response.dart';
import 'api_service.dart';

class DashboardService {
  // Get dashboard statistics
  static Future<ApiResponse<DashboardStats>> getDashboardStats() async {
    final response = await ApiService.get<DashboardStats>(
      '/api/reports/dashboard',
      fromJson: (data) => DashboardStats.fromJson(data),
    );

    return response;
  }

  // Get today's overview
  static Future<ApiResponse<Map<String, dynamic>>> getTodayOverview() async {
    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/reports/today',
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Get weekly overview
  static Future<ApiResponse<Map<String, dynamic>>> getWeeklyOverview() async {
    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/reports/weekly',
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Get monthly overview
  static Future<ApiResponse<Map<String, dynamic>>> getMonthlyOverview() async {
    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/reports/monthly',
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Get revenue statistics
  static Future<ApiResponse<Map<String, dynamic>>> getRevenueStats({
    DateTime? startDate,
    DateTime? endDate,
    String period = 'monthly', // daily, weekly, monthly, yearly
  }) async {
    final queryParams = <String, String>{
      'period': period,
    };
    
    if (startDate != null) {
      queryParams['startDate'] = startDate.toIso8601String().split('T')[0];
    }
    if (endDate != null) {
      queryParams['endDate'] = endDate.toIso8601String().split('T')[0];
    }

    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/reports/revenue',
      queryParams: queryParams,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Get appointment statistics
  static Future<ApiResponse<Map<String, dynamic>>> getAppointmentStats({
    DateTime? startDate,
    DateTime? endDate,
    String period = 'monthly',
  }) async {
    final queryParams = <String, String>{
      'period': period,
    };
    
    if (startDate != null) {
      queryParams['startDate'] = startDate.toIso8601String().split('T')[0];
    }
    if (endDate != null) {
      queryParams['endDate'] = endDate.toIso8601String().split('T')[0];
    }

    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/reports/appointments',
      queryParams: queryParams,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Get client statistics
  static Future<ApiResponse<Map<String, dynamic>>> getClientStats({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final queryParams = <String, String>{};
    
    if (startDate != null) {
      queryParams['startDate'] = startDate.toIso8601String().split('T')[0];
    }
    if (endDate != null) {
      queryParams['endDate'] = endDate.toIso8601String().split('T')[0];
    }

    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/reports/clients',
      queryParams: queryParams.isNotEmpty ? queryParams : null,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Get service performance
  static Future<ApiResponse<Map<String, dynamic>>> getServicePerformance({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final queryParams = <String, String>{};
    
    if (startDate != null) {
      queryParams['startDate'] = startDate.toIso8601String().split('T')[0];
    }
    if (endDate != null) {
      queryParams['endDate'] = endDate.toIso8601String().split('T')[0];
    }

    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/reports/services',
      queryParams: queryParams.isNotEmpty ? queryParams : null,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Get groomer performance
  static Future<ApiResponse<Map<String, dynamic>>> getGroomerPerformance({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final queryParams = <String, String>{};
    
    if (startDate != null) {
      queryParams['startDate'] = startDate.toIso8601String().split('T')[0];
    }
    if (endDate != null) {
      queryParams['endDate'] = endDate.toIso8601String().split('T')[0];
    }

    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/reports/groomers',
      queryParams: queryParams.isNotEmpty ? queryParams : null,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Get financial summary
  static Future<ApiResponse<Map<String, dynamic>>> getFinancialSummary({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final queryParams = <String, String>{};
    
    if (startDate != null) {
      queryParams['startDate'] = startDate.toIso8601String().split('T')[0];
    }
    if (endDate != null) {
      queryParams['endDate'] = endDate.toIso8601String().split('T')[0];
    }

    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/reports/financial',
      queryParams: queryParams.isNotEmpty ? queryParams : null,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Get business insights
  static Future<ApiResponse<Map<String, dynamic>>> getBusinessInsights() async {
    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/reports/insights',
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Get trending data
  static Future<ApiResponse<Map<String, dynamic>>> getTrendingData({
    String period = 'monthly',
    int months = 6,
  }) async {
    final queryParams = <String, String>{
      'period': period,
      'months': months.toString(),
    };

    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/reports/trends',
      queryParams: queryParams,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Get comparison data (current vs previous period)
  static Future<ApiResponse<Map<String, dynamic>>> getComparisonData({
    DateTime? startDate,
    DateTime? endDate,
    String period = 'monthly',
  }) async {
    final queryParams = <String, String>{
      'period': period,
    };
    
    if (startDate != null) {
      queryParams['startDate'] = startDate.toIso8601String().split('T')[0];
    }
    if (endDate != null) {
      queryParams['endDate'] = endDate.toIso8601String().split('T')[0];
    }

    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/reports/comparison',
      queryParams: queryParams,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Get key performance indicators (KPIs)
  static Future<ApiResponse<Map<String, dynamic>>> getKPIs() async {
    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/reports/kpis',
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Get real-time metrics
  static Future<ApiResponse<Map<String, dynamic>>> getRealTimeMetrics() async {
    final response = await ApiService.get<Map<String, dynamic>>(
      '/api/reports/realtime',
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Export dashboard data
  static Future<ApiResponse<String>> exportDashboardData({
    DateTime? startDate,
    DateTime? endDate,
    String format = 'pdf',
    List<String>? sections,
  }) async {
    final body = <String, dynamic>{
      'format': format,
      if (startDate != null) 'startDate': startDate.toIso8601String().split('T')[0],
      if (endDate != null) 'endDate': endDate.toIso8601String().split('T')[0],
      if (sections != null) 'sections': sections,
    };

    final response = await ApiService.post<String>(
      '/api/reports/export',
      body: body,
      fromJson: (data) => data.toString(),
    );

    return response;
  }

  // Schedule automated reports
  static Future<ApiResponse<void>> scheduleReport({
    required String reportType,
    required String frequency, // daily, weekly, monthly
    required List<String> recipients,
    String format = 'pdf',
  }) async {
    final body = <String, dynamic>{
      'reportType': reportType,
      'frequency': frequency,
      'recipients': recipients,
      'format': format,
    };

    final response = await ApiService.post<void>(
      '/api/reports/schedule',
      body: body,
    );

    return response;
  }

  // Get scheduled reports
  static Future<ApiResponse<List<Map<String, dynamic>>>> getScheduledReports() async {
    final response = await ApiService.get<List<Map<String, dynamic>>>(
      '/api/reports/scheduled',
      fromJson: (data) => (data as List).map((item) => Map<String, dynamic>.from(item)).toList(),
    );

    return response;
  }

  // Cancel scheduled report
  static Future<ApiResponse<void>> cancelScheduledReport(String reportId) async {
    final response = await ApiService.delete<void>('/api/reports/scheduled/$reportId');
    return response;
  }

  // Get report history
  static Future<ApiResponse<List<Map<String, dynamic>>>> getReportHistory({
    int? limit,
    String? type,
  }) async {
    final queryParams = <String, String>{};
    
    if (limit != null) queryParams['limit'] = limit.toString();
    if (type != null) queryParams['type'] = type;

    final response = await ApiService.get<List<Map<String, dynamic>>>(
      '/api/reports/history',
      queryParams: queryParams.isNotEmpty ? queryParams : null,
      fromJson: (data) => (data as List).map((item) => Map<String, dynamic>.from(item)).toList(),
    );

    return response;
  }
}
