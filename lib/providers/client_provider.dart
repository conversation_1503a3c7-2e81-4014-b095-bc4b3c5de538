import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../models/client.dart';
import '../services/client/client_service.dart';
import '../services/auth/auth_service.dart';
import 'base_provider.dart';

/// Provider for managing client data with salon-specific isolation
class ClientProvider extends BaseProvider {
  List<Client> _clients = [];
  List<Client> _filteredClients = [];
  String _searchQuery = '';
  String? _currentSalonId;

  // Getters
  List<Client> get clients => _clients;
  List<Client> get filteredClients => _filteredClients;
  String get searchQuery => _searchQuery;
  String? get currentSalonId => _currentSalonId;
  bool get hasClients => _clients.isNotEmpty;

  @override
  Future<void> initialize() async {
    debugPrint('🔄 ClientProvider: Initializing...');
    await executeVoidAsync(
      () async {
        await _loadCurrentSalonId();
        await _loadClients();
        setInitialized(true);
        debugPrint('✅ ClientProvider: Initialization completed');
      },
      errorMessage: 'Failed to initialize client provider',
    );
  }

  @override
  Future<void> refresh() async {
    debugPrint('🔄 ClientProvider: Refreshing client data...');
    await executeVoidAsync(
      () async {
        await _loadCurrentSalonId();
        await _loadClients();
        debugPrint('✅ ClientProvider: Refresh completed');
      },
      errorMessage: 'Failed to refresh client data',
    );
  }

  @override
  void clear() {
    debugPrint('🧹 ClientProvider: Clearing all client data...');
    _clients.clear();
    _filteredClients.clear();
    _searchQuery = '';
    _currentSalonId = null;
    super.clear();
    debugPrint('✅ ClientProvider: All client data cleared');
  }

  /// Clear client data for salon switching (with comprehensive logging)
  Future<void> clearForSalonSwitch(String newSalonId) async {
    debugPrint('🔄 ClientProvider: Salon switch initiated');
    debugPrint('📍 ClientProvider: Previous salon ID: $_currentSalonId');
    debugPrint('📍 ClientProvider: New salon ID: $newSalonId');
    debugPrint('🗑️ ClientProvider: Clearing cached client data from previous salon...');

    // Clear all cached data
    final previousClientCount = _clients.length;
    _clients.clear();
    _filteredClients.clear();
    _searchQuery = '';
    _currentSalonId = newSalonId;

    // Notify listeners immediately after clearing data
    notifyListeners();
    debugPrint('🔔 ClientProvider: UI notified of data clearing');

    debugPrint('✅ ClientProvider: Cleared $previousClientCount clients from cache');
    debugPrint('🔄 ClientProvider: Loading clients for new salon...');

    // Load fresh data for new salon
    await _loadClients();

    debugPrint('✅ ClientProvider: Salon switch completed');
    debugPrint('📊 ClientProvider: Loaded ${_clients.length} clients for new salon');
  }

  /// Load current salon ID
  Future<void> _loadCurrentSalonId() async {
    _currentSalonId = await AuthService.getCurrentSalonId();
    debugPrint('📍 ClientProvider: Current salon ID: $_currentSalonId');
  }

  /// Load clients for current salon
  Future<void> _loadClients() async {
    if (_currentSalonId == null) {
      debugPrint('⚠️ ClientProvider: No salon ID available, skipping client load');
      return;
    }

    debugPrint('🔄 ClientProvider: Loading clients for salon: $_currentSalonId');

    final response = await ClientService.getClients();

    if (response.success && response.data != null) {
      _clients = response.data!;
      _filteredClients = response.data!;
      debugPrint('✅ ClientProvider: Successfully loaded ${_clients.length} clients');
      debugPrint('📊 ClientProvider: API response: ${_clients.map((c) => '${c.name} (${c.id})').join(', ')}');
    } else {
      _clients = [];
      _filteredClients = [];
      final errorMsg = response.error ?? 'Unknown error loading clients';
      debugPrint('❌ ClientProvider: Failed to load clients: $errorMsg');
      setError('Failed to load clients: $errorMsg');
    }

    // Notify listeners that the data has changed
    notifyListeners();
    debugPrint('🔔 ClientProvider: UI notified of data changes');
  }

  /// Search clients by query
  void searchClients(String query) {
    debugPrint('🔍 ClientProvider: Searching clients with query: "$query"');
    _searchQuery = query;
    
    if (query.isEmpty) {
      _filteredClients = List.from(_clients);
      debugPrint('📊 ClientProvider: Search cleared, showing all ${_filteredClients.length} clients');
    } else {
      _filteredClients = _clients.where((client) {
        final searchLower = query.toLowerCase();
        return client.name.toLowerCase().contains(searchLower) ||
               client.phone.toLowerCase().contains(searchLower) ||
               client.email.toLowerCase().contains(searchLower);
      }).toList();
      debugPrint('📊 ClientProvider: Search found ${_filteredClients.length} matching clients');
    }
    
    notifyListeners();
  }

  /// Add a new client
  Future<bool> addClient(Client client) async {
    debugPrint('➕ ClientProvider: Adding new client: ${client.name}');
    
    final response = await ClientService.createClient(client);
    
    if (response.success && response.data != null) {
      _clients.add(response.data!);
      searchClients(_searchQuery); // Refresh filtered list
      debugPrint('✅ ClientProvider: Successfully added client: ${response.data!.name} (${response.data!.id})');
      return true;
    } else {
      final errorMsg = response.error ?? 'Failed to add client';
      debugPrint('❌ ClientProvider: Failed to add client: $errorMsg');
      setError(errorMsg);
      return false;
    }
  }

  /// Update an existing client
  Future<bool> updateClient(Client client) async {
    debugPrint('✏️ ClientProvider: Updating client: ${client.name} (${client.id})');
    
    final response = await ClientService.updateClient(client.id, client);
    
    if (response.success && response.data != null) {
      final index = _clients.indexWhere((c) => c.id == client.id);
      if (index != -1) {
        _clients[index] = response.data!;
        searchClients(_searchQuery); // Refresh filtered list
        debugPrint('✅ ClientProvider: Successfully updated client: ${response.data!.name}');
        return true;
      }
    }
    
    final errorMsg = response.error ?? 'Failed to update client';
    debugPrint('❌ ClientProvider: Failed to update client: $errorMsg');
    setError(errorMsg);
    return false;
  }

  /// Delete a client
  Future<bool> deleteClient(String clientId) async {
    debugPrint('🗑️ ClientProvider: Deleting client: $clientId');
    
    final response = await ClientService.deleteClient(clientId);
    
    if (response.success) {
      _clients.removeWhere((c) => c.id == clientId);
      searchClients(_searchQuery); // Refresh filtered list
      debugPrint('✅ ClientProvider: Successfully deleted client: $clientId');
      return true;
    } else {
      final errorMsg = response.error ?? 'Failed to delete client';
      debugPrint('❌ ClientProvider: Failed to delete client: $errorMsg');
      setError(errorMsg);
      return false;
    }
  }

  /// Get client by ID
  Client? getClientById(String clientId) {
    try {
      return _clients.firstWhere((client) => client.id == clientId);
    } catch (e) {
      debugPrint('⚠️ ClientProvider: Client not found: $clientId');
      return null;
    }
  }
}
