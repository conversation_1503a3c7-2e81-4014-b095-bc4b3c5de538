#!/bin/bash

# Integration Testing Script for Partykids Project
# Run this before TestFlight submissions or major releases

echo "🧪 Starting Integration Tests for Partykids Project..."
echo "====================================================="

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Start timer
start_time=$(date +%s)

# Check if simulator is running
echo -e "${BLUE}📱 Checking iOS Simulator...${NC}"
if xcrun simctl list devices | grep -q "Booted"; then
    echo -e "${GREEN}✅ iOS Simulator is running${NC}"
else
    echo -e "${YELLOW}⚠️  Starting iOS Simulator...${NC}"
    open -a Simulator
    echo "Waiting 10 seconds for simulator to boot..."
    sleep 10
fi

echo ""
echo -e "${YELLOW}🧪 Running Basic Integration Test...${NC}"
echo "This may take 2-5 minutes..."

if flutter test integration_test/app_test.dart --timeout=300s; then
    echo -e "${GREEN}✅ Integration Tests: PASSED${NC}"
    integration_tests_passed=true
else
    echo -e "${RED}❌ Integration Tests: FAILED${NC}"
    integration_tests_passed=false
fi

echo ""
echo -e "${YELLOW}🌐 Running API Integration Test...${NC}"
echo "Note: Connection errors are expected if backend isn't running"

if flutter test integration_test/api_integration_test.dart --timeout=120s; then
    echo -e "${GREEN}✅ API Tests: PASSED${NC}"
    api_tests_passed=true
else
    echo -e "${RED}❌ API Tests: FAILED${NC}"
    api_tests_passed=false
fi

# End timer
end_time=$(date +%s)
duration=$((end_time - start_time))
minutes=$((duration / 60))
seconds=$((duration % 60))

echo ""
echo "====================================================="
echo -e "${YELLOW}📊 Integration Test Summary${NC}"
echo "====================================================="

if [ "$integration_tests_passed" = true ] && [ "$api_tests_passed" = true ]; then
    echo -e "${GREEN}🎉 ALL INTEGRATION TESTS PASSED!${NC}"
    echo -e "${GREEN}✅ App Integration: PASSED${NC}"
    echo -e "${GREEN}✅ API Integration: PASSED${NC}"
    echo ""
    echo -e "${GREEN}🚀 Your app is ready for TestFlight!${NC}"
    exit_code=0
else
    echo -e "${YELLOW}⚠️  SOME INTEGRATION TESTS HAD ISSUES${NC}"
    if [ "$integration_tests_passed" = false ]; then
        echo -e "${RED}❌ App Integration: FAILED${NC}"
    else
        echo -e "${GREEN}✅ App Integration: PASSED${NC}"
    fi
    
    if [ "$api_tests_passed" = false ]; then
        echo -e "${YELLOW}⚠️  API Integration: FAILED (may be expected)${NC}"
    else
        echo -e "${GREEN}✅ API Integration: PASSED${NC}"
    fi
    echo ""
    echo -e "${YELLOW}💡 Note: API test failures are normal without backend server${NC}"
    exit_code=0  # Don't fail on API tests
fi

echo ""
echo -e "${YELLOW}⏱️  Total time: ${minutes}m ${seconds}s${NC}"
echo ""
echo -e "${YELLOW}💡 Tips:${NC}"
echo "   • Run fast tests first: ./test_fast.sh"
echo "   • API errors are expected without backend server"
echo "   • For daily development, stick to fast tests"

exit $exit_code
