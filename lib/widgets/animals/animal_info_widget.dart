import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../models/pet.dart';
import '../../config/theme/app_theme.dart';

class AnimalInfoWidget extends StatelessWidget {
  final Pet pet;

  const AnimalInfoWidget({
    super.key,
    required this.pet,
  });

  String _getGenderText(String gender) {
    switch (gender.toLowerCase()) {
      case 'male':
      case 'masculin':
      case 'm':
        return 'Masculin';
      case 'female':
      case 'feminin':
      case 'f':
        return 'Feminin';
      default:
        return 'Necunoscut';
    }
  }

  String _calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    final difference = now.difference(birthDate);
    final years = (difference.inDays / 365).floor();
    final months = ((difference.inDays % 365) / 30).floor();

    if (years > 0) {
      if (months > 0) {
        return '$years ani, $months luni';
      } else {
        return '$years ${years == 1 ? 'an' : 'ani'}';
      }
    } else if (months > 0) {
      return '$months ${months == 1 ? 'lună' : 'luni'}';
    } else {
      final weeks = (difference.inDays / 7).floor();
      if (weeks > 0) {
        return '$weeks ${weeks == 1 ? 'săptămână' : 'săptămâni'}';
      } else {
        return '${difference.inDays} ${difference.inDays == 1 ? 'zi' : 'zile'}';
      }
    }
  }

  Widget _buildInfoCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  color: color,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              color: AppColors.forestGreen,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Informații de bază',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.forestGreen,
          ),
        ),
        const SizedBox(height: 16),

        // Basic info grid
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          childAspectRatio: 1.8,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          children: [
            _buildInfoCard(
              'Rasă',
              pet.breed,
              Icons.pets,
              AppColors.forestGreen,
            ),
            _buildInfoCard(
              'Gen',
              _getGenderText(pet.gender),
              pet.gender.toLowerCase() == 'male' || pet.gender.toLowerCase() == 'masculin'
                  ? Icons.male
                  : Icons.female,
              pet.gender.toLowerCase() == 'male' || pet.gender.toLowerCase() == 'masculin'
                  ? Colors.blue
                  : Colors.pink,
            ),
            _buildInfoCard(
              'Vârstă',
              _calculateAge(pet.birthDate),
              Icons.cake,
              AppColors.logoBrown,
            ),
            _buildInfoCard(
              'Greutate',
              '${pet.weight.toStringAsFixed(1)} kg',
              Icons.monitor_weight,
              Colors.orange,
            ),
          ],
        ),

        const SizedBox(height: 16),

        // Birth date card
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                spreadRadius: 1,
                blurRadius: 3,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.calendar_today, color: Colors.purple, size: 20),
                  const SizedBox(width: 8),
                  const Text(
                    'Data nașterii',
                    style: TextStyle(
                      color: Colors.purple,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                DateFormat('dd MMMM yyyy', 'ro').format(pet.birthDate),
                style: const TextStyle(
                  color: AppColors.forestGreen,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),

        // Behavioral notes if any
        if (pet.notes.isNotEmpty) ...[
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.1),
                  spreadRadius: 1,
                  blurRadius: 3,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Row(
                  children: [
                    Icon(Icons.note, color: AppColors.logoBrown, size: 20),
                    SizedBox(width: 8),
                    Text(
                      'Note comportamentale',
                      style: TextStyle(
                        color: AppColors.logoBrown,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  pet.notes,
                  style: const TextStyle(
                    color: AppColors.forestGreen,
                    fontSize: 14,
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }
}
