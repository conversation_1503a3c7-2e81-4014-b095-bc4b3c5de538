import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../config/theme/app_theme.dart';
import '../../models/appointment_alternative.dart';

class ConflictResolutionDialog extends StatefulWidget {
  final List<AppointmentAlternative> alternatives;
  final VoidCallback onTryDifferentTime;
  final VoidCallback onContactSalon;
  final VoidCallback onAddToWaitingList;
  final void Function(AppointmentAlternative) onSelectAlternative;

  const ConflictResolutionDialog({
    super.key,
    required this.alternatives,
    required this.onTryDifferentTime,
    required this.onContactSalon,
    required this.onAddToWaitingList,
    required this.onSelectAlternative,
  });

  @override
  State<ConflictResolutionDialog> createState() => _ConflictResolutionDialogState();
}

class _ConflictResolutionDialogState extends State<ConflictResolutionDialog>
    with TickerProviderStateMixin {
  late AnimationController _dialogController;
  late AnimationController _cardsController;
  late Animation<double> _dialogScaleAnimation;
  late Animation<double> _dialogOpacityAnimation;
  late List<Animation<Offset>> _cardSlideAnimations;
  late List<Animation<double>> _cardOpacityAnimations;

  @override
  void initState() {
    super.initState();
    _setupAnimations();
    _startAnimations();
  }

  void _setupAnimations() {
    // Dialog entrance animation
    _dialogController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _dialogScaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _dialogController,
      curve: Curves.elasticOut,
    ));

    _dialogOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _dialogController,
      curve: const Interval(0.0, 0.5, curve: Curves.easeOut),
    ));

    // Cards staggered animation
    _cardsController = AnimationController(
      duration: Duration(milliseconds: 300 + (widget.alternatives.length * 150)),
      vsync: this,
    );

    _cardSlideAnimations = [];
    _cardOpacityAnimations = [];

    for (int i = 0; i < widget.alternatives.length; i++) {
      final startTime = i * 0.15;
      final endTime = startTime + 0.4;

      _cardSlideAnimations.add(
        Tween<Offset>(
          begin: const Offset(1.0, 0.0),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _cardsController,
          curve: Interval(startTime, endTime, curve: Curves.elasticOut),
        )),
      );

      _cardOpacityAnimations.add(
        Tween<double>(
          begin: 0.0,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: _cardsController,
          curve: Interval(startTime, endTime, curve: Curves.easeOut),
        )),
      );
    }
  }

  void _startAnimations() {
    _dialogController.forward();
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _cardsController.forward();
      }
    });
  }

  @override
  void dispose() {
    _dialogController.dispose();
    _cardsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _dialogController,
      builder: (context, child) {
        return Transform.scale(
          scale: _dialogScaleAnimation.value,
          child: Opacity(
            opacity: _dialogOpacityAnimation.value,
            child: Dialog(
              backgroundColor: Colors.transparent,
              insetPadding: const EdgeInsets.all(16),
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: 400,
                  maxHeight: MediaQuery.of(context).size.height * 0.8,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Theme.of(context).colorScheme.surface,
                      Theme.of(context).colorScheme.surface.withValues(alpha: 0.95),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                    BoxShadow(
                      color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildHeader(),
                    _buildAlternativesList(),
                    _buildActionButtons(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
      ),
      child: Column(
        children: [
          // Animated conflict icon
          TweenAnimationBuilder<double>(
            duration: const Duration(milliseconds: 1000),
            tween: Tween(begin: 0.0, end: 1.0),
            builder: (context, value, child) {
              return Transform.rotate(
                angle: value * 0.1,
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.schedule_outlined,
                    size: 48,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 16),
          Text(
            'Ups! Există un conflict de programare',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Am găsit ${widget.alternatives.length} alternative pentru tine',
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.9),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAlternativesList() {
    return Expanded(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Selectează o alternativă:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 12),
            Expanded(
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: widget.alternatives.length,
                itemBuilder: (context, index) {
                  return AnimatedBuilder(
                    animation: _cardsController,
                    builder: (context, child) {
                      return SlideTransition(
                        position: _cardSlideAnimations[index],
                        child: FadeTransition(
                          opacity: _cardOpacityAnimations[index],
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 12),
                            child: _ModernAlternativeCard(
                              alternative: widget.alternatives[index],
                              index: index,
                              onSelect: () => widget.onSelectAlternative(widget.alternatives[index]),
                            ),
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
        borderRadius: const BorderRadius.vertical(bottom: Radius.circular(24)),
      ),
      child: Column(
        children: [
          // Primary action - Try different time
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: widget.onTryDifferentTime,
              icon: const Icon(Icons.access_time),
              label: const Text('Alege altă oră'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
            ),
          ),
          const SizedBox(height: 12),
          // Secondary actions
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: widget.onContactSalon,
                  icon: const Icon(Icons.phone, size: 18),
                  label: const Text('Contactează'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.primary,
                    side: BorderSide(color: Theme.of(context).colorScheme.onSurface),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: widget.onAddToWaitingList,
                  icon: const Icon(Icons.list_alt, size: 18),
                  label: const Text('Listă așteptare'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Theme.of(context).colorScheme.primary,
                    side: BorderSide(color: Theme.of(context).colorScheme.onSurface),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _ModernAlternativeCard extends StatefulWidget {
  final AppointmentAlternative alternative;
  final int index;
  final VoidCallback onSelect;

  const _ModernAlternativeCard({
    required this.alternative,
    required this.index,
    required this.onSelect,
  });

  @override
  State<_ModernAlternativeCard> createState() => _ModernAlternativeCardState();
}

class _ModernAlternativeCardState extends State<_ModernAlternativeCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _hoverController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _elevationAnimation;
  bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    _hoverController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeOut,
    ));

    _elevationAnimation = Tween<double>(
      begin: 2.0,
      end: 8.0,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: Curves.easeOut,
    ));
  }

  @override
  void dispose() {
    _hoverController.dispose();
    super.dispose();
  }

  void _onHoverChanged(bool isHovered) {
    setState(() {
      _isHovered = isHovered;
    });
    if (isHovered) {
      _hoverController.forward();
    } else {
      _hoverController.reverse();
    }
  }

  String _formatDate(DateTime date) {
    return DateFormat('dd MMMM yyyy', 'ro').format(date);
  }

  String _formatTime(DateTime start, DateTime end) {
    final startTime = DateFormat('HH:mm').format(start);
    final endTime = DateFormat('HH:mm').format(end);
    return '$startTime - $endTime';
  }

  IconData _getTypeIcon(String? reason) {
    if (reason?.contains('TIME_ADJUSTMENT') == true || reason?.contains('oră') == true) {
      return Icons.schedule;
    } else if (reason?.contains('DAY_ADJUSTMENT') == true || reason?.contains('zile') == true) {
      return Icons.calendar_today;
    }
    return Icons.lightbulb_outline;
  }

  Color _getTypeColor(String? reason) {
    if (reason?.contains('TIME_ADJUSTMENT') == true || reason?.contains('oră') == true) {
      return AppTheme.getStatusColor(context, 'info');
    } else if (reason?.contains('DAY_ADJUSTMENT') == true || reason?.contains('zile') == true) {
      return AppTheme.getStatusColor(context, 'warning');
    }
    return Theme.of(context).colorScheme.primary;
  }

  String _getTypeLabel(String? reason) {
    if (reason?.contains('TIME_ADJUSTMENT') == true || reason?.contains('oră') == true) {
      return 'Aceeași zi';
    } else if (reason?.contains('DAY_ADJUSTMENT') == true || reason?.contains('zile') == true) {
      return 'Altă zi';
    }
    return 'Sugestie';
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _hoverController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: MouseRegion(
            onEnter: (_) => _onHoverChanged(true),
            onExit: (_) => _onHoverChanged(false),
            child: GestureDetector(
              onTapDown: (_) => _onHoverChanged(true),
              onTapUp: (_) => _onHoverChanged(false),
              onTapCancel: () => _onHoverChanged(false),
              onTap: widget.onSelect,
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: _isHovered
                        ? [
                            Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
                            Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                          ]
                        : [
                            Theme.of(context).colorScheme.surface,
                            Theme.of(context).colorScheme.surface.withValues(alpha: 0.95),
                          ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: _isHovered
                        ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
                        : Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
                    width: _isHovered ? 2 : 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: _isHovered ? 0.2 : 0.1),
                      blurRadius: _elevationAnimation.value,
                      offset: Offset(0, _elevationAnimation.value / 2),
                    ),
                  ],
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header with type and priority
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _getTypeColor(widget.alternative.reason),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  _getTypeIcon(widget.alternative.reason),
                                  size: 14,
                                  color: Theme.of(context).colorScheme.onPrimary,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  _getTypeLabel(widget.alternative.reason),
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w600,
                                    color: Theme.of(context).colorScheme.onPrimary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const Spacer(),
                          if (widget.alternative.priority != null)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                'Prioritate: ${widget.alternative.priority}',
                                style: TextStyle(
                                  fontSize: 11,
                                  fontWeight: FontWeight.w500,
                                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 12),

                      // Date and time - prominently displayed
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _formatDate(widget.alternative.startTime),
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _formatTime(widget.alternative.startTime, widget.alternative.endTime),
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.w600,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 12),

                      // Staff and reason
                      Row(
                        children: [
                          Icon(
                            Icons.person,
                            size: 16,
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                          const SizedBox(width: 6),
                          Expanded(
                            child: Text(
                              widget.alternative.staffName,
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            ),
                          ),
                        ],
                      ),
                      if (widget.alternative.reason != null) ...[
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              size: 16,
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                            const SizedBox(width: 6),
                            Expanded(
                              child: Text(
                                widget.alternative.reason!,
                                style: TextStyle(
                                  fontSize: 13,
                                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],

                      // Confidence indicator (if available)
                      if (widget.alternative.confidence != null) ...[
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Text(
                              'Compatibilitate:',
                              style: TextStyle(
                                fontSize: 12,
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: LinearProgressIndicator(
                                value: widget.alternative.confidence!,
                                backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  widget.alternative.confidence! > 0.7
                                      ? AppTheme.getStatusColor(context, 'success')
                                      : widget.alternative.confidence! > 0.5
                                          ? AppTheme.getStatusColor(context, 'warning')
                                          : AppTheme.getStatusColor(context, 'error'),
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '${(widget.alternative.confidence! * 100).toStringAsFixed(0)}%',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
