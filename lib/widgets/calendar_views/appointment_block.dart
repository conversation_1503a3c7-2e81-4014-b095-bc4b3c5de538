import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/appointment.dart';
import '../../providers/calendar_provider.dart';
import '../../config/theme/app_theme.dart';

class AppointmentBlock extends StatefulWidget {
  final Appointment appointment;
  final double height;
  final VoidCallback? onTap;
  final bool isCompact;

  const AppointmentBlock({
    super.key,
    required this.appointment,
    required this.height,
    this.onTap,
    this.isCompact = false,
  });

  @override
  State<AppointmentBlock> createState() => _AppointmentBlockState();
}

class _AppointmentBlockState extends State<AppointmentBlock> {
  bool _hovered = false;

  TextDecoration _getCancellationDecoration() {
    // Apply strike-through to all cancelled appointments regardless of theme
    return _isAppointmentCanceled()
        ? TextDecoration.lineThrough
        : TextDecoration.none;
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        final appointmentColor = _getAppointmentColor(provider);
        final bool darkCancelled =
            _isAppointmentCanceled() && Theme.of(context).brightness == Brightness.dark;
        final staffDisplayName = _getStaffDisplayName(provider);

        return MouseRegion(
          onEnter: (_) => setState(() => _hovered = true),
          onExit: (_) => setState(() => _hovered = false),
          child: GestureDetector(
            onTap: widget.onTap,
            child: Stack(
              children: [
                Container(
                  height: widget.height,
                  margin: const EdgeInsets.symmetric(horizontal: 2, vertical: 1),
                  decoration: BoxDecoration(
                    color: appointmentColor,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: _hovered
                          ? Colors.white
                          : appointmentColor.withAlpha(
                              (appointmentColor.alpha * 0.8).round()),
                      width: _hovered ? 2 : 1,
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(4),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Ultra-compact display for very small blocks
                        if (widget.height <= 25) ...[
                          Flexible(
                            child: _buildUltraCompactInfo(
                                context, staffDisplayName),
                          ),
                        ] else ...[
                          // Time range - show only in non-compact mode with sufficient space
                          if (!widget.isCompact && widget.height > 60) ...[
                            Text(
                              widget.appointment.timeRange,
                              style: TextStyle(
                                color: _getCancelledAppointmentTextColor(context),
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                                decoration: _getCancellationDecoration(),
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 2),
                          ],

                          if (widget.isCompact) ...[
                            Flexible(child: _buildClientPetInfo(true)),
                            if (widget.height > 35) ...[
                              const SizedBox(height: 2),
                              Flexible(child: _buildServiceInfo(true)),
                            ],
                          ] else ...[
                            Flexible(
                              child: _buildDayViewInfo(
                                  context, staffDisplayName),
                            ),
                          ],

                          // Show "Anulată" for canceled appointments
                          if (_isAppointmentCanceled() && widget.height > 45) ...[
                            const SizedBox(height: 2),
                            Text(
                              'ANULATĂ',
                              style: TextStyle(
                                color: _getCancelledTextColor(context),
                                fontSize: 9,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 0.5,
                                decoration: TextDecoration.lineThrough,
                              ),
                            ),
                          ],
                        ],
                        if (!widget.isCompact && widget.appointment.isPaid) ...[
                          const SizedBox(height: 2),
                          const Icon(
                            Icons.check_circle,
                            color: Colors.white,
                            size: 12,
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
                if (darkCancelled)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.black.withAlpha(128),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.outline.withAlpha(
                              (Theme.of(context).colorScheme.outline.alpha * 0.3).round()),
                          width: 1,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Color _getAppointmentColor(CalendarProvider provider) {
    // Enhanced elegant color coding system
    final baseColor = _getBaseStaffColor(provider);

    // Apply subtle status-based modifications to the base color
    switch (widget.appointment.status.toLowerCase()) {
      case 'canceled':
      case 'anulat':
      case 'cancelled':
        final isDark = Theme.of(context).brightness == Brightness.dark;
        return isDark
            ? AppColors.appointmentCancelledGrayDark
            : AppColors.appointmentCancelledGrayLight;
      case 'pending':
      case 'in asteptare':
        return baseColor.withAlpha((baseColor.alpha * 0.6).round());
      case 'confirmed':
      case 'confirmat':
        return baseColor;
      case 'finalizat':
      case 'completed':
        return _darkenColor(baseColor, 0.1);
      case 'rescheduled':
        return _lightenColor(baseColor, 0.2);
      default:
        return baseColor;
    }
  }

  Color _getBaseStaffColor(CalendarProvider provider) {
    // Get staff-based color with elegant fallbacks
    if (widget.appointment.groomerId != null) {
      try {
        final staff = provider.availableStaff.firstWhere(
          (s) => s.id == widget.appointment.groomerId,
        );
        return _getElegantStaffColor(provider.getStaffColor(staff.id));
      } catch (e) {
        // Fallback to name-based lookup
        try {
          final staff = provider.availableStaff.firstWhere(
            (s) => s.name == widget.appointment.assignedGroomer,
          );
          return _getElegantStaffColor(provider.getStaffColor(staff.id));
        } catch (e) {
          return _getElegantStaffColor(AppColors.logoBrown);
        }
      }
    } else {
      // Name-based lookup for backward compatibility
      try {
        final staff = provider.availableStaff.firstWhere(
          (s) => s.name == widget.appointment.assignedGroomer,
        );
        return _getElegantStaffColor(provider.getStaffColor(staff.id));
      } catch (e) {
        return _getElegantStaffColor(Theme.of(context).colorScheme.primary);
      }
    }
  }

  Color _getElegantStaffColor(Color originalColor) {
    // Slight transparency for a modern look
    return originalColor.withAlpha((originalColor.alpha * 0.9).round());
  }

  Color _darkenColor(Color color, double amount) {
    final hsl = HSLColor.fromColor(color);
    return hsl.withLightness((hsl.lightness - amount).clamp(0.0, 1.0)).toColor();
  }

  Color _lightenColor(Color color, double amount) {
    final hsl = HSLColor.fromColor(color);
    return hsl.withLightness((hsl.lightness + amount).clamp(0.0, 1.0)).toColor();
  }

  // Helper method to check if appointment is canceled
  bool _isAppointmentCanceled() {
    final status = widget.appointment.status.toLowerCase();
    return status == 'canceled' || status == 'anulat' || status == 'cancelled';
  }

  // Helper method to get theme-aware cancelled text color
  Color _getCancelledTextColor(BuildContext context) {
    if (Theme.of(context).brightness == Brightness.dark) {
      return Theme.of(context).colorScheme.error.withAlpha(
          (Theme.of(context).colorScheme.error.alpha * 0.9).round());
    } else {
      return Colors.grey.shade700;
    }
  }

  // Helper method to get theme-aware cancelled appointment text color
  Color _getCancelledAppointmentTextColor(BuildContext context, {bool isSecondary = false}) {
    if (_isAppointmentCanceled()) {
      if (Theme.of(context).brightness == Brightness.dark) {
        return isSecondary
            ? Theme.of(context).colorScheme.onSurfaceVariant.withAlpha(
                (Theme.of(context).colorScheme.onSurfaceVariant.alpha * 0.7).round())
            : Theme.of(context).colorScheme.onSurfaceVariant;
      } else {
        return isSecondary ? Colors.grey.shade500 : Colors.grey.shade600;
      }
    }
    return isSecondary ? Colors.white70 : Colors.white;
  }

  /// Build client and pet information display
  Widget _buildClientPetInfo(bool isCompact) {
    final clientName = widget.appointment.clientName.isNotEmpty ? widget.appointment.clientName : 'Client necunoscut';
    final petName = widget.appointment.petName.isNotEmpty ? widget.appointment.petName : 'Animal necunoscut';

    if (isCompact) {
      // Compact format: "Client - Pet" on single line
      return Text(
        '$clientName - $petName',
        style: TextStyle(
          color: _getCancelledAppointmentTextColor(context),
          fontSize: 10,
          fontWeight: FontWeight.w600,
          decoration: _getCancellationDecoration(),
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      );
    } else {
      // Non-compact format: Better spacing and readability
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Client name (primary)
          Text(
            clientName,
            style: TextStyle(
              color: _getCancelledAppointmentTextColor(context),
              fontSize: 12,
              fontWeight: FontWeight.bold,
              decoration: _getCancellationDecoration(),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          // Pet name (secondary)
          if (petName.isNotEmpty) ...[
            const SizedBox(height: 1),
            Text(
              '🐾 $petName',
              style: TextStyle(
                color: _getCancelledAppointmentTextColor(context, isSecondary: true),
                fontSize: 10,
                fontWeight: FontWeight.w500,
                decoration: _getCancellationDecoration(),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      );
    }
  }

  /// Build ultra-compact display for very small appointment blocks
  Widget _buildUltraCompactInfo(BuildContext context, String staffName) {
    final petName = widget.appointment.petName.isNotEmpty ? widget.appointment.petName : 'Pet';
    final breed = widget.appointment.petSpecies.isNotEmpty ? widget.appointment.petSpecies : '';
    final serviceName =
        widget.appointment.service.isNotEmpty ? widget.appointment.service : 'Serviciu';
    final staffShort = staffName.isNotEmpty
        ? staffName.split(' ').map((e) => e.isNotEmpty ? e[0] : '').join()
        : '';

    final petLine = breed.isNotEmpty ? '$petName-$breed' : petName;

    // Truncate names if too long
    final shortPet = petLine.length > 10 ? '${petLine.substring(0, 10)}...' : petLine;
    final shortService = serviceName.length > 10 ? '${serviceName.substring(0, 10)}...' : serviceName;
    final shortStaff = staffShort.length > 3 ? staffShort.substring(0, 3) : staffShort;

    return Tooltip(
      message:
          '${widget.appointment.petName} $breed\n${widget.appointment.service}\n$staffName\n${widget.appointment.timeRange}',
      child: FittedBox(
        fit: BoxFit.scaleDown,
        alignment: Alignment.centerLeft,
        child: Text(
          '$shortPet | $shortService ${shortStaff.isNotEmpty ? '| $shortStaff' : ''}',
          style: TextStyle(
            color: _getCancelledAppointmentTextColor(context),
            fontSize: 9,
            fontWeight: FontWeight.w600,
            decoration: _getCancellationDecoration(),
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  /// Build service information display
  Widget _buildServiceInfo(bool isCompact) {
    final serviceName =
        widget.appointment.service.isNotEmpty ? widget.appointment.service : 'Serviciu general';

    return Tooltip(
      message: serviceName,
      child: Text(
        serviceName,
        style: TextStyle(
          color: _getCancelledAppointmentTextColor(context, isSecondary: true),
          fontSize: isCompact ? 9 : 10,
          fontWeight: FontWeight.w500,
          fontStyle: FontStyle.italic,
          decoration: _getCancellationDecoration(),
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  String _getStaffDisplayName(CalendarProvider provider) {
    final staff = widget.appointment.groomerId != null
        ? provider.availableStaff
            .where((s) => s.id == widget.appointment.groomerId)
            .firstOrNull
        : provider.availableStaff
            .where((s) => s.name == widget.appointment.assignedGroomer)
            .firstOrNull;
    return staff?.displayName ?? widget.appointment.assignedGroomer;
  }

  Widget _buildDayViewInfo(BuildContext context, String staffName) {
    final breed = widget.appointment.petSpecies.isNotEmpty
        ? widget.appointment.petSpecies
        : '';
    final petLine = breed.isNotEmpty
        ? '${widget.appointment.petName} - $breed'
        : widget.appointment.petName;

    // Adjust displayed lines based on height
    if (widget.height <= 40) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            petLine,
            style: TextStyle(
              color: _isAppointmentCanceled()
                  ? Colors.grey.shade600
                  : Colors.white,
              fontSize: 11,
              fontWeight: FontWeight.bold,
              decoration: _getCancellationDecoration(),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 1),
          Text(
            widget.appointment.service,
            style: TextStyle(
              color: _isAppointmentCanceled()
                  ? Colors.grey.shade500
                  : Colors.white70,
              fontSize: 9,
              fontWeight: FontWeight.w500,
              fontStyle: FontStyle.italic,
              decoration: _getCancellationDecoration(),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          if (widget.height > 35 && staffName.isNotEmpty) ...[
            const SizedBox(height: 1),
            Text(
              '👤 $staffName',
              style: TextStyle(
                color: _isAppointmentCanceled()
                    ? Colors.grey.shade500
                    : Colors.white60,
                fontSize: 8,
                fontStyle: FontStyle.italic,
                decoration: _getCancellationDecoration(),
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          petLine,
          style: TextStyle(
            color: _getCancelledAppointmentTextColor(context),
            fontSize: 12,
            fontWeight: FontWeight.bold,
            decoration: _getCancellationDecoration(),
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 1),
        Text(
          widget.appointment.service,
          style: TextStyle(
            color: _getCancelledAppointmentTextColor(context, isSecondary: true),
            fontSize: 10,
            fontWeight: FontWeight.w500,
            fontStyle: FontStyle.italic,
            decoration: _getCancellationDecoration(),
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        const SizedBox(height: 1),
        Text(
          widget.appointment.clientName,
          style: TextStyle(
            color: _getCancelledAppointmentTextColor(context),
            fontSize: 10,
            fontWeight: FontWeight.w600,
            decoration: _getCancellationDecoration(),
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        if (staffName.isNotEmpty) ...[
          const SizedBox(height: 1),
          Text(
            '👤 $staffName',
            style: TextStyle(
              color: _getCancelledAppointmentTextColor(context, isSecondary: true),
              fontSize: 8,
              fontStyle: FontStyle.italic,
              decoration: _getCancellationDecoration(),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }
}


