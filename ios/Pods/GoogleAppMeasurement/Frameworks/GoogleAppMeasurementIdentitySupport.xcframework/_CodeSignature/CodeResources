<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ios-arm64/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<data>
		KXJZtjKaR8+YFsmFIJgmCBAi61k=
		</data>
		<key>ios-arm64/GoogleAppMeasurementIdentitySupport.framework/Info.plist</key>
		<data>
		syq9BjzDDxiI3ANzzIcjVxYELeM=
		</data>
		<key>ios-arm64/GoogleAppMeasurementIdentitySupport.framework/Modules/module.modulemap</key>
		<data>
		ZCuUZzVZ47W3gsydCwK/7TEMjVw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurementIdentitySupport.framework/Versions/A/GoogleAppMeasurementIdentitySupport</key>
		<data>
		8LK7yWTCrL8xL7ehTP2rdQnOxPQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurementIdentitySupport.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		ZCuUZzVZ47W3gsydCwK/7TEMjVw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurementIdentitySupport.framework/Versions/A/Resources/Info.plist</key>
		<data>
		8fxpHUcSuQ1lYZFpQ2I3m+Sm5tg=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<data>
		E6M5bmkQiowZkCsRec4C1zWzEgc=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/Info.plist</key>
		<data>
		1947MyOb7QTDpsmzjYtgMSN1PlY=
		</data>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/Modules/module.modulemap</key>
		<data>
		ZCuUZzVZ47W3gsydCwK/7TEMjVw=
		</data>
		<key>macos-arm64_x86_64/GoogleAppMeasurementIdentitySupport.framework/Versions/A/GoogleAppMeasurementIdentitySupport</key>
		<data>
		GvRKXHhTUOcRC6q4lCtHfaealfw=
		</data>
		<key>macos-arm64_x86_64/GoogleAppMeasurementIdentitySupport.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		ZCuUZzVZ47W3gsydCwK/7TEMjVw=
		</data>
		<key>macos-arm64_x86_64/GoogleAppMeasurementIdentitySupport.framework/Versions/A/Resources/Info.plist</key>
		<data>
		Zp6CpsWPPMTqgRCG5k3rw9QUKHs=
		</data>
		<key>tvos-arm64/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<data>
		faJon13KEa7exKXSm0k3ONIu1BM=
		</data>
		<key>tvos-arm64/GoogleAppMeasurementIdentitySupport.framework/Info.plist</key>
		<data>
		M1GjbthTGJo8mJgzCCHKKqNLJvo=
		</data>
		<key>tvos-arm64/GoogleAppMeasurementIdentitySupport.framework/Modules/module.modulemap</key>
		<data>
		ZCuUZzVZ47W3gsydCwK/7TEMjVw=
		</data>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<data>
		/RYEtrcsu0tlFqjaGM21gMT6PS4=
		</data>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/Info.plist</key>
		<data>
		EfWWBhR38ghwgW+MoEc8WgyC8cM=
		</data>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/Modules/module.modulemap</key>
		<data>
		ZCuUZzVZ47W3gsydCwK/7TEMjVw=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ios-arm64/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<dict>
			<key>hash</key>
			<data>
			KXJZtjKaR8+YFsmFIJgmCBAi61k=
			</data>
			<key>hash2</key>
			<data>
			SSBwUA5LG1rznKuV90TjTTBKvsrPOTlLIXroVyKCCso=
			</data>
		</dict>
		<key>ios-arm64/GoogleAppMeasurementIdentitySupport.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			syq9BjzDDxiI3ANzzIcjVxYELeM=
			</data>
			<key>hash2</key>
			<data>
			imlAGPyyE0oa9IFcouvs0jB+FiuHVO/l7zq9v6AHrbE=
			</data>
		</dict>
		<key>ios-arm64/GoogleAppMeasurementIdentitySupport.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			ZCuUZzVZ47W3gsydCwK/7TEMjVw=
			</data>
			<key>hash2</key>
			<data>
			UGhX/JwInh1Ja0+DYSnYvdaBKLCWcOy/3nH/ZRnTP7I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/GoogleAppMeasurementIdentitySupport</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurementIdentitySupport.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurementIdentitySupport.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurementIdentitySupport.framework/Versions/A/GoogleAppMeasurementIdentitySupport</key>
		<dict>
			<key>hash</key>
			<data>
			8LK7yWTCrL8xL7ehTP2rdQnOxPQ=
			</data>
			<key>hash2</key>
			<data>
			xBXXa2e2tfEDUa607SCV3flFLc7t0sKfcFgz5yFZgWk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurementIdentitySupport.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			ZCuUZzVZ47W3gsydCwK/7TEMjVw=
			</data>
			<key>hash2</key>
			<data>
			UGhX/JwInh1Ja0+DYSnYvdaBKLCWcOy/3nH/ZRnTP7I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurementIdentitySupport.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			8fxpHUcSuQ1lYZFpQ2I3m+Sm5tg=
			</data>
			<key>hash2</key>
			<data>
			oLgMO74XI44Yo9oXhGSzrVFLIqHzESxVdZZ4t2NiKKQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/GoogleAppMeasurementIdentitySupport.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<dict>
			<key>hash</key>
			<data>
			E6M5bmkQiowZkCsRec4C1zWzEgc=
			</data>
			<key>hash2</key>
			<data>
			1difrfomcgkhggQfel5m/lDxLmpc1rzMMyYq6+ffLkw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			1947MyOb7QTDpsmzjYtgMSN1PlY=
			</data>
			<key>hash2</key>
			<data>
			nJ0oTCm9VIzeKOMrnVJPU/dHSMlN5nbWqfvFWUYuwnI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			ZCuUZzVZ47W3gsydCwK/7TEMjVw=
			</data>
			<key>hash2</key>
			<data>
			UGhX/JwInh1Ja0+DYSnYvdaBKLCWcOy/3nH/ZRnTP7I=
			</data>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/GoogleAppMeasurementIdentitySupport</string>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurementIdentitySupport.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurementIdentitySupport.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurementIdentitySupport.framework/Versions/A/GoogleAppMeasurementIdentitySupport</key>
		<dict>
			<key>hash</key>
			<data>
			GvRKXHhTUOcRC6q4lCtHfaealfw=
			</data>
			<key>hash2</key>
			<data>
			wqapKra2CjCKEJfjBSQwcUHcZs/eVq3X6vmwdwWQN3Q=
			</data>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurementIdentitySupport.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			ZCuUZzVZ47W3gsydCwK/7TEMjVw=
			</data>
			<key>hash2</key>
			<data>
			UGhX/JwInh1Ja0+DYSnYvdaBKLCWcOy/3nH/ZRnTP7I=
			</data>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurementIdentitySupport.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			Zp6CpsWPPMTqgRCG5k3rw9QUKHs=
			</data>
			<key>hash2</key>
			<data>
			oRDMqGQEDc8q3eFCj/p1MKZfe1YURnomDElGXUOSbDI=
			</data>
		</dict>
		<key>macos-arm64_x86_64/GoogleAppMeasurementIdentitySupport.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
		<key>tvos-arm64/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<dict>
			<key>hash</key>
			<data>
			faJon13KEa7exKXSm0k3ONIu1BM=
			</data>
			<key>hash2</key>
			<data>
			pZRyALHO66+MYJ/QmPT1mnN13JC1vKURRe/N0KZ/yNs=
			</data>
		</dict>
		<key>tvos-arm64/GoogleAppMeasurementIdentitySupport.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			M1GjbthTGJo8mJgzCCHKKqNLJvo=
			</data>
			<key>hash2</key>
			<data>
			fYJ7ebbIsyYqTWJPgW8hsF79xE3c2LPt856+k01dlcE=
			</data>
		</dict>
		<key>tvos-arm64/GoogleAppMeasurementIdentitySupport.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			ZCuUZzVZ47W3gsydCwK/7TEMjVw=
			</data>
			<key>hash2</key>
			<data>
			UGhX/JwInh1Ja0+DYSnYvdaBKLCWcOy/3nH/ZRnTP7I=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/GoogleAppMeasurementIdentitySupport</key>
		<dict>
			<key>hash</key>
			<data>
			/RYEtrcsu0tlFqjaGM21gMT6PS4=
			</data>
			<key>hash2</key>
			<data>
			2QLBZ1zWcCXEWDr4ePqWMzKjzy5AK3tpQlnihcpmKeI=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			EfWWBhR38ghwgW+MoEc8WgyC8cM=
			</data>
			<key>hash2</key>
			<data>
			uMe1GDQ+6MuS+cecKkWncCx7AAvM+vyTAJMUjjSVEYU=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/GoogleAppMeasurementIdentitySupport.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			ZCuUZzVZ47W3gsydCwK/7TEMjVw=
			</data>
			<key>hash2</key>
			<data>
			UGhX/JwInh1Ja0+DYSnYvdaBKLCWcOy/3nH/ZRnTP7I=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
