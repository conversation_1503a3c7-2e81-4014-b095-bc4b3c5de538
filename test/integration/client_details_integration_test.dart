import 'package:flutter_test/flutter_test.dart';
import 'package:partykidsapp/providers/client_provider.dart';
import 'package:partykidsapp/models/client.dart';

void main() {
  group('Client Details Integration Tests', () {
    late ClientProvider clientProvider;

    setUp(() {
      clientProvider = ClientProvider();
    });

    group('Data Refresh After Editing', () {
      test('should refresh client data when client is updated', () async {
        // Arrange
        final originalClient = Client(
          id: 'test-client-1',
          name: '<PERSON>',
          phone: '+40123456789',
          email: '<EMAIL>',
          registrationDate: DateTime.now(),
        );

        final updatedClient = originalClient.copyWith(
          name: '<PERSON>',
          email: '<EMAIL>',
          updatedAt: DateTime.now(),
        );

        // Simulate having the original client in the provider
        clientProvider.clients.add(originalClient);
        clientProvider.filteredClients.add(originalClient);

        // Act - Simulate updating the client
        final index = clientProvider.clients.indexWhere((c) => c.id == originalClient.id);
        if (index != -1) {
          clientProvider.clients[index] = updatedClient;
          clientProvider.filteredClients[index] = updatedClient;
        }

        // Assert
        final retrievedClient = clientProvider.getClientById('test-client-1');
        expect(retrievedClient, isNotNull);
        expect(retrievedClient!.name, equals('John Smith'));
        expect(retrievedClient.email, equals('<EMAIL>'));
        expect(retrievedClient.updatedAt, isNotNull);
      });

      test('should handle client not found gracefully', () {
        // Act
        final client = clientProvider.getClientById('non-existent-id');

        // Assert
        expect(client, isNull);
      });
    });

    group('Client Deletion', () {
      test('should remove client from cache when deleted', () {
        // Arrange
        final client1 = Client(
          id: 'client-1',
          name: 'Client 1',
          phone: '+40123456789',
          registrationDate: DateTime.now(),
        );

        final client2 = Client(
          id: 'client-2',
          name: 'Client 2',
          phone: '+40123456790',
          registrationDate: DateTime.now(),
        );

        clientProvider.clients.addAll([client1, client2]);
        clientProvider.filteredClients.addAll([client1, client2]);

        // Act - Simulate deletion
        clientProvider.clients.removeWhere((c) => c.id == 'client-1');
        clientProvider.filteredClients.removeWhere((c) => c.id == 'client-1');

        // Assert
        expect(clientProvider.clients.length, equals(1));
        expect(clientProvider.filteredClients.length, equals(1));
        expect(clientProvider.getClientById('client-1'), isNull);
        expect(clientProvider.getClientById('client-2'), isNotNull);
      });

      test('should handle deletion of non-existent client', () {
        // Arrange
        final client = Client(
          id: 'existing-client',
          name: 'Existing Client',
          phone: '+40123456789',
          registrationDate: DateTime.now(),
        );

        clientProvider.clients.add(client);
        clientProvider.filteredClients.add(client);

        // Act - Try to delete non-existent client
        final initialCount = clientProvider.clients.length;
        clientProvider.clients.removeWhere((c) => c.id == 'non-existent-id');
        clientProvider.filteredClients.removeWhere((c) => c.id == 'non-existent-id');

        // Assert - No change should occur
        expect(clientProvider.clients.length, equals(initialCount));
        expect(clientProvider.getClientById('existing-client'), isNotNull);
      });
    });

    group('Search Functionality', () {
      test('should maintain search state correctly', () {
        // Arrange
        final clients = [
          Client(
            id: '1',
            name: 'John Doe',
            phone: '+40123456789',
            email: '<EMAIL>',
            registrationDate: DateTime.now(),
          ),
          Client(
            id: '2',
            name: 'Jane Smith',
            phone: '+40987654321',
            email: '<EMAIL>',
            registrationDate: DateTime.now(),
          ),
        ];

        clientProvider.clients.addAll(clients);
        clientProvider.filteredClients.addAll(clients);

        // Act & Assert - Search by name
        clientProvider.searchClients('John');
        expect(clientProvider.searchQuery, equals('John'));
        expect(clientProvider.filteredClients.length, equals(1));
        expect(clientProvider.filteredClients[0].name, equals('John Doe'));

        // Act & Assert - Clear search
        clientProvider.searchClients('');
        expect(clientProvider.searchQuery, isEmpty);
        expect(clientProvider.filteredClients.length, equals(2));
      });
    });

    group('Data Isolation', () {
      test('should clear all data when switching salons', () async {
        // Arrange
        final clients = [
          Client(
            id: '1',
            name: 'Client 1',
            phone: '+40123456789',
            registrationDate: DateTime.now(),
          ),
          Client(
            id: '2',
            name: 'Client 2',
            phone: '+40123456790',
            registrationDate: DateTime.now(),
          ),
        ];

        clientProvider.clients.addAll(clients);
        clientProvider.filteredClients.addAll(clients);
        clientProvider.searchClients('Client');

        // Verify initial state
        expect(clientProvider.clients.length, equals(2));
        expect(clientProvider.searchQuery, equals('Client'));

        // Act - Clear for salon switch
        await clientProvider.clearForSalonSwitch('new-salon-id');

        // Assert - All data should be cleared
        expect(clientProvider.clients, isEmpty);
        expect(clientProvider.filteredClients, isEmpty);
        expect(clientProvider.searchQuery, isEmpty);
        expect(clientProvider.currentSalonId, equals('new-salon-id'));
      });
    });

    group('Error Handling', () {
      test('should handle errors gracefully', () {
        // Arrange
        clientProvider.setError('Test error message');

        // Assert
        expect(clientProvider.hasError, isTrue);
        expect(clientProvider.error, equals('Test error message'));

        // Act - Clear error
        clientProvider.clearError();

        // Assert
        expect(clientProvider.hasError, isFalse);
        expect(clientProvider.error, isNull);
      });
    });
  });
}
