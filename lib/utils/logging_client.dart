import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';

/// A simple HTTP client that logs every request and response.
class LoggingClient extends http.BaseClient {
  final http.Client _inner;

  LoggingClient(this._inner);

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) async {
    // Log the outgoing request
    if (ApiConfig.enableLogging) {
      debugPrint('➡️ ${request.method} ${request.url}');
      if (request is http.Request && request.body.isNotEmpty) {
        debugPrint('📝 Request body: ${request.body}');
      }
    }

    final streamedResponse = await _inner.send(request);

    // Read the response so we can log the body
    final response = await http.Response.fromStream(streamedResponse);
    if (ApiConfig.enableLogging) {
      debugPrint('⬅️ ${response.statusCode} ${request.method} ${request.url}');
      debugPrint('⬅️ Response body: ${response.body}');
    }

    // Return a new StreamedResponse with the same data so callers can read it
    return http.StreamedResponse(
      Stream.fromIterable([response.bodyBytes]),
      response.statusCode,
      request: request,
      headers: response.headers,
      reasonPhrase: response.reasonPhrase,
      isRedirect: response.isRedirect,
      persistentConnection: response.persistentConnection,
    );
  }

  @override
  void close() {
    _inner.close();
    super.close();
  }
}
