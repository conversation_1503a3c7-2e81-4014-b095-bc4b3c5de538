import 'package:partykidsapp/config/theme/app_dimensions.dart';

import '../models/working_hours_settings.dart';
import '../models/staff_working_hours_settings.dart';

/// Romanian holidays utility class
class RomanianHolidays {

  /// Fixed legal holiday data (month, day, name)
  static const List<({int month, int day, String name})> _legalHolidayData = [
    (month: 1, day: 1, name: '<PERSON><PERSON> No<PERSON>'),
    (month: 1, day: 2, name: 'A doua zi de Anul Nou'),
    (month: 1, day: 24, name: 'Unirea Principatelor Române'),
    (month: 5, day: 1, name: '<PERSON><PERSON><PERSON>'),
    (month: 6, day: 1, name: '<PERSON><PERSON><PERSON>pi<PERSON>'),
    (month: 8, day: 15, name: 'Adormirea Maicii Domnului'),
    (month: 11, day: 30, name: '<PERSON><PERSON><PERSON><PERSON><PERSON>'),
    (month: 12, day: 1, name: '<PERSON><PERSON><PERSON> Națională a României'),
    (month: 12, day: 25, name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>'),
    (month: 12, day: 26, name: 'A doua <PERSON> Cră<PERSON>'),
  ];

  /// Get all Romanian legal holidays for a specific year
  static List<Holiday> getHolidaysForYear(int year) {
    final holidays = <Holiday>[];

    // Fixed holidays
    holidays.addAll(_getFixedHolidays(year));

    // Easter-dependent holidays
    holidays.addAll(_getEasterDependentHolidays(year));

    return holidays;
  }

  /// Get fixed holidays that occur on the same date every year
  static List<Holiday> _getFixedHolidays(int year) {
    final fixed = <Holiday>[];

    for (final data in _legalHolidayData) {
      fixed.add(Holiday(
        name: data.name,
        date: DateTime(year, data.month, data.day),
        isWorkingDay: false,
        type: HolidayType.legal,
      ));
    }

    // Additional traditional/religious holidays
    fixed.addAll([
      Holiday(
        name: 'Boboteaza',
        date: DateTime(year, 1, 6),
        isWorkingDay: false,
        type: HolidayType.religious,
      ),
      Holiday(
        name: 'Adormirea Maicii Domnului',
        date: DateTime(year, 8, 15),
        isWorkingDay: false,
        type: HolidayType.religious,
      ),
      Holiday(
        name: 'Sfântul Andrei',
        date: DateTime(year, 11, 30),
        isWorkingDay: false,
        type: HolidayType.religious,
      ),
    ]);

    return fixed;
  }

  /// Get holidays that depend on Easter date
  static List<Holiday> _getEasterDependentHolidays(int year) {
    final easterDate = _calculateEasterDate(year);

    return [
      // Good Friday
      Holiday(
        name: 'Vinerea Mare',
        date: easterDate.subtract(const Duration(days: 2)),
        isWorkingDay: false,
        type: HolidayType.religious,
      ),

      // Easter Sunday
      Holiday(
        name: 'Paștele',
        date: easterDate,
        isWorkingDay: false,
        type: HolidayType.religious,
      ),

      // Easter Monday
      Holiday(
        name: 'A doua zi de Paște',
        date: easterDate.add(const Duration(days: 1)),
        isWorkingDay: false,
        type: HolidayType.religious,
      ),

      // Pentecost (Whit Sunday)
      Holiday(
        name: 'Rusaliile',
        date: easterDate.add(const Duration(days: 49)),
        isWorkingDay: false,
        type: HolidayType.religious,
      ),

      // Whit Monday
      Holiday(
        name: 'A doua zi de Rusalii',
        date: easterDate.add(const Duration(days: 50)),
        isWorkingDay: false,
        type: HolidayType.religious,
      ),
    ];
  }

  /// Calculate Orthodox Easter date for a given year
  /// Uses the Orthodox Easter calculation algorithm
  static DateTime _calculateEasterDate(int year) {
    // Orthodox Easter calculation (Julian calendar based)
    int a = year % 4;
    int b = year % 7;
    int c = year % 19;
    int d = (19 * c + 15) % 30;
    int e = (2 * a + 4 * b - d + 34) % 7;
    int month = ((d + e + 114) / 31).floor();
    int day = ((d + e + 114) % 31) + 1;

    // Convert from Julian to Gregorian calendar
    DateTime julianEaster = DateTime(year, month, day);

    // Add 13 days for 20th-21st century (Gregorian correction)
    int gregorianCorrection = 13;
    if (year >= 1900 && year < 2100) {
      gregorianCorrection = 13;
    } else if (year >= 2100) {
      gregorianCorrection = 14;
    }

    return julianEaster.add(Duration(days: gregorianCorrection));
  }

  /// Check if a specific date is a Romanian holiday
  static bool isHoliday(DateTime date) {
    final holidays = getHolidaysForYear(date.year);
    return holidays.any((holiday) =>
      holiday.date.year == date.year &&
      holiday.date.month == date.month &&
      holiday.date.day == date.day
    );
  }

  /// Check if a specific date is a legal Romanian holiday
  static bool isLegalHoliday(DateTime date) {
    final fixedMatch = _legalHolidayData.any((h) =>
        h.month == date.month && h.day == date.day);
    if (fixedMatch) return true;

    final easterHolidays = _getEasterDependentHolidays(date.year);
    return easterHolidays.any((h) =>
        h.date.month == date.month && h.date.day == date.day);
  }

  /// Get holiday information for a specific date
  static Holiday? getHolidayForDate(DateTime date) {
    final holidays = getHolidaysForYear(date.year);
    try {
      return holidays.firstWhere((holiday) =>
        holiday.date.year == date.year &&
        holiday.date.month == date.month &&
        holiday.date.day == date.day
      );
    } catch (e) {
      return null;
    }
  }

  /// Get upcoming holidays from a specific date
  static List<Holiday> getUpcomingHolidays(DateTime fromDate, {int daysAhead = AppDimensions.daysAhead}) {
    final endDate = fromDate.add(Duration(days: daysAhead));
    final holidays = <Holiday>[];

    // Get holidays for current year
    holidays.addAll(getHolidaysForYear(fromDate.year));

    // If the range spans into next year, get next year's holidays too
    if (endDate.year > fromDate.year) {
      holidays.addAll(getHolidaysForYear(endDate.year));
    }

    // Filter holidays within the date range
    return holidays.where((holiday) =>
      holiday.date.isAfter(fromDate.subtract(const Duration(days: 1))) &&
      holiday.date.isBefore(endDate.add(const Duration(days: 1)))
    ).toList()..sort((a, b) => a.date.compareTo(b.date));
  }

  /// Get default working hours settings with Romanian holidays
  static WorkingHoursSettings getDefaultWorkingHours(String salonId) {
    final currentYear = DateTime.now().year;
    final holidays = getHolidaysForYear(currentYear);

    // Add next year's holidays as well
    holidays.addAll(getHolidaysForYear(currentYear + 1));

    return WorkingHoursSettings(
      salonId: salonId,
      weeklySchedule: {
        'monday': const DaySchedule(
          startTime: '09:00',
          endTime: '17:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'tuesday': const DaySchedule(
          startTime: '09:00',
          endTime: '17:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'wednesday': const DaySchedule(
          startTime: '09:00',
          endTime: '17:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'thursday': const DaySchedule(
          startTime: '09:00',
          endTime: '17:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'friday': const DaySchedule(
          startTime: '09:00',
          endTime: '17:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'saturday': const DaySchedule(
          startTime: '10:00',
          endTime: '15:00',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'sunday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
      },
      holidays: holidays,
      customClosures: [],
      updatedAt: DateTime.now(),
    );
  }

  /// Get default working hours settings for a staff member
  static StaffWorkingHoursSettings getDefaultStaffWorkingHours(String staffId, String salonId) {
    final currentYear = DateTime.now().year;
    final holidays = getHolidaysForYear(currentYear);

    // Add next year's holidays as well
    holidays.addAll(getHolidaysForYear(currentYear + 1));

    return StaffWorkingHoursSettings(
      staffId: staffId,
      salonId: salonId,
      weeklySchedule: {
        'monday': const DaySchedule(
          startTime: '09:00',
          endTime: '17:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'tuesday': const DaySchedule(
          startTime: '09:00',
          endTime: '17:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'wednesday': const DaySchedule(
          startTime: '09:00',
          endTime: '17:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'thursday': const DaySchedule(
          startTime: '09:00',
          endTime: '17:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'friday': const DaySchedule(
          startTime: '09:00',
          endTime: '17:00',
          isWorkingDay: true,
          breakStart: '12:00',
          breakEnd: '13:00',
        ),
        'saturday': const DaySchedule(
          startTime: '10:00',
          endTime: '15:00',
          isWorkingDay: true,
          breakStart: null,
          breakEnd: null,
        ),
        'sunday': const DaySchedule(
          startTime: null,
          endTime: null,
          isWorkingDay: false,
          breakStart: null,
          breakEnd: null,
        ),
      },
      holidays: holidays,
      customClosures: [],
      updatedAt: DateTime.now(),
    );
  }

  /// Validate time format (HH:MM)
  static bool isValidTimeFormat(String time) {
    final timeRegex = RegExp(r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$');
    return timeRegex.hasMatch(time);
  }

  /// Convert time string to minutes since midnight
  static int timeToMinutes(String time) {
    final parts = time.split(':');
    return int.parse(parts[0]) * 60 + int.parse(parts[1]);
  }

  /// Convert minutes since midnight to time string
  static String minutesToTime(int minutes) {
    final hours = (minutes / 60).floor();
    final mins = minutes % 60;
    return '${hours.toString().padLeft(2, '0')}:${mins.toString().padLeft(2, '0')}';
  }

  /// Get day name in Romanian
  static String getDayNameInRomanian(String dayOfWeek) {
    switch (dayOfWeek.toLowerCase()) {
      case 'monday':
        return 'Luni';
      case 'tuesday':
        return 'Marți';
      case 'wednesday':
        return 'Miercuri';
      case 'thursday':
        return 'Joi';
      case 'friday':
        return 'Vineri';
      case 'saturday':
        return 'Sâmbătă';
      case 'sunday':
        return 'Duminică';
      default:
        return dayOfWeek;
    }
  }

  /// Get short day name in Romanian
  static String getShortDayNameInRomanian(String dayOfWeek) {
    switch (dayOfWeek.toLowerCase()) {
      case 'monday':
        return 'Lun';
      case 'tuesday':
        return 'Mar';
      case 'wednesday':
        return 'Mie';
      case 'thursday':
        return 'Joi';
      case 'friday':
        return 'Vin';
      case 'saturday':
        return 'Sâm';
      case 'sunday':
        return 'Dum';
      default:
        return dayOfWeek.substring(0, 3);
    }
  }
}
