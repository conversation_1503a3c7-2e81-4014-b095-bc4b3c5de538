import 'package:flutter_test/flutter_test.dart';
import 'package:partykidsapp/models/user_salon_association.dart';
import 'package:partykidsapp/models/user_role.dart';

void main() {
  group('UserSalonAssociation JSON Parsing', () {
    test('should parse backend response with StaffRole and ClientDataAccess enums', () {
      // Backend response format
      final backendJson = {
        'id': 'assoc-123',
        'userId': 'user-456',
        'salonId': 'salon-789',
        'salon': {
          'id': 'salon-789',
          'name': 'Test Salon',
          'address': 'Test Address',
          'phone': '+40123456789',
          'email': '<EMAIL>',
          'description': 'Test Description',
          'isActive': true,
          'createdAt': '2024-01-01T10:00:00Z',
          'updatedAt': '2024-01-01T10:00:00Z'
        },
        'groomerRole': 'CHIEF_GROOMER', // Backend StaffRole enum
        'clientDataPermission': 'FULL', // Backend ClientDataAccess enum
        'isCurrentSalon': true,
        'clientCount': 25,
        'createdAt': '2024-01-01T10:00:00Z',
        'updatedAt': '2024-01-01T10:00:00Z'
      };

      // Parse the JSON
      final association = UserSalonAssociation.fromJson(backendJson);

      // Verify parsing
      expect(association.id, 'assoc-123');
      expect(association.userId, 'user-456');
      expect(association.salonId, 'salon-789');
      expect(association.salon.name, 'Test Salon');
      expect(association.groomerRole, GroomerRole.chiefGroomer);
      expect(association.clientDataPermission, ClientDataPermission.fullAccess);
      expect(association.isCurrentSalon, true);
      expect(association.clientCount, 25);
    });

    test('should parse different StaffRole values correctly', () {
      final testCases = [
        {'role': 'CHIEF_GROOMER', 'expected': GroomerRole.chiefGroomer},
        {'role': 'GROOMER', 'expected': GroomerRole.groomer},
        {'role': 'ASSISTANT', 'expected': GroomerRole.assistant},
        {'role': 'SENIOR_GROOMER', 'expected': GroomerRole.seniorGroomer},
      ];

      for (final testCase in testCases) {
        final json = {
          'id': 'test-id',
          'userId': 'user-id',
          'salonId': 'salon-id',
          'salon': {
            'id': 'salon-id',
            'name': 'Test Salon',
            'address': 'Test Address',
            'phone': '+40123456789',
            'email': '<EMAIL>',
            'description': 'Test Description',
            'isActive': true,
            'createdAt': '2024-01-01T10:00:00Z',
            'updatedAt': '2024-01-01T10:00:00Z'
          },
          'groomerRole': testCase['role'],
          'clientDataPermission': 'LIMITED',
          'isCurrentSalon': false,
          'clientCount': 0,
          'createdAt': '2024-01-01T10:00:00Z',
          'updatedAt': '2024-01-01T10:00:00Z'
        };

        final association = UserSalonAssociation.fromJson(json);
        expect(association.groomerRole, testCase['expected'],
            reason: 'Failed to parse role: ${testCase['role']}');
      }
    });

    test('should parse different ClientDataAccess values correctly', () {
      final testCases = [
        {'permission': 'NONE', 'expected': ClientDataPermission.noAccess},
        {'permission': 'LIMITED', 'expected': ClientDataPermission.limitedAccess},
        {'permission': 'FULL', 'expected': ClientDataPermission.fullAccess},
      ];

      for (final testCase in testCases) {
        final json = {
          'id': 'test-id',
          'userId': 'user-id',
          'salonId': 'salon-id',
          'salon': {
            'id': 'salon-id',
            'name': 'Test Salon',
            'address': 'Test Address',
            'phone': '+40123456789',
            'email': '<EMAIL>',
            'description': 'Test Description',
            'isActive': true,
            'createdAt': '2024-01-01T10:00:00Z',
            'updatedAt': '2024-01-01T10:00:00Z'
          },
          'groomerRole': 'GROOMER',
          'clientDataPermission': testCase['permission'],
          'isCurrentSalon': false,
          'clientCount': 0,
          'createdAt': '2024-01-01T10:00:00Z',
          'updatedAt': '2024-01-01T10:00:00Z'
        };

        final association = UserSalonAssociation.fromJson(json);
        expect(association.clientDataPermission, testCase['expected'],
            reason: 'Failed to parse permission: ${testCase['permission']}');
      }
    });

    test('should handle legacy frontend enum values for backward compatibility', () {
      final legacyJson = {
        'id': 'test-id',
        'userId': 'user-id',
        'salonId': 'salon-id',
        'salon': {
          'id': 'salon-id',
          'name': 'Test Salon',
          'address': 'Test Address',
          'phone': '+40123456789',
          'email': '<EMAIL>',
          'description': 'Test Description',
          'isActive': true,
          'createdAt': '2024-01-01T10:00:00Z',
          'updatedAt': '2024-01-01T10:00:00Z'
        },
        'groomerRole': 'REGULAR_GROOMER', // Legacy frontend enum
        'clientDataPermission': 'NO_ACCESS', // Legacy frontend enum
        'isCurrentSalon': false,
        'clientCount': 0,
        'createdAt': '2024-01-01T10:00:00Z',
        'updatedAt': '2024-01-01T10:00:00Z'
      };

      final association = UserSalonAssociation.fromJson(legacyJson);
      expect(association.groomerRole, GroomerRole.groomer); // Maps to new enum
      expect(association.clientDataPermission, ClientDataPermission.noAccess);
    });
  });
}
