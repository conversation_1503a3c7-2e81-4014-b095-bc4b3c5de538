import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../providers/calendar_provider.dart';
import '../../config/theme/app_theme.dart';
import '../../services/calendar_preferences_service.dart';
import '../../services/staff_service.dart';
import '../calendar_views/google_calendar_view.dart';

class CalendarSettingsDrawer extends StatefulWidget {
  final CalendarViewMode currentViewMode;
  final ValueChanged<CalendarViewMode> onViewModeChanged;
  final bool monthlyViewEnabled;

  const CalendarSettingsDrawer({
    super.key,
    required this.currentViewMode,
    required this.onViewModeChanged,
    required this.monthlyViewEnabled,
  });

  @override
  State<CalendarSettingsDrawer> createState() => _CalendarSettingsDrawerState();
}

class _CalendarSettingsDrawerState extends State<CalendarSettingsDrawer> {

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            _buildHeader(),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildHourViewModeSection(),
                    const SizedBox(height: 24),
                    _buildZoomSection(),
                    const SizedBox(height: 24),
                    _buildStaffFilterSection(),
                    const SizedBox(height: 24),
                    _buildDisplayOptionsSection(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Text(
        'Setări Calendar',
        style: TextStyle(
          color: Theme.of(context).colorScheme.onSurface,
          fontWeight: FontWeight.bold,
          fontSize: 18,
        ),
      ),
    );
  }

  Widget _buildHourViewModeSection() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Afișare ore',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            ToggleButtons(
              isSelected: [
                provider.hourViewMode == CalendarHourViewMode.businessHours,
                provider.hourViewMode == CalendarHourViewMode.fullDay,
              ],
              onPressed: (index) {
                final mode = index == 0
                    ? CalendarHourViewMode.businessHours
                    : CalendarHourViewMode.fullDay;
                provider.setHourViewMode(mode);
              },
              borderRadius: BorderRadius.circular(8),
              selectedColor: Theme.of(context).colorScheme.onPrimary,
              selectedBorderColor: Theme.of(context).colorScheme.primary,
              fillColor: Theme.of(context).colorScheme.primary,
              color: Theme.of(context).colorScheme.onSurface,
              constraints: const BoxConstraints(minWidth: 120, minHeight: 40),
              children: const [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 12),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.business, size: 18),
                      SizedBox(width: 8),
                      Text('Program lucru'),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 12),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.schedule, size: 18),
                      SizedBox(width: 8),
                      Text('24 ore'),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              provider.hourViewMode == CalendarHourViewMode.businessHours
                  ? 'Afișează doar orele de program ale salonului'
                  : 'Afișează toate cele 24 de ore din zi',
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildStaffFilterSection() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        final staff = provider.availableStaff;
        final selectedStaff = provider.selectedStaff;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Filtrare personal',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {
                    if (selectedStaff.length == staff.length) {
                      provider.clearStaffSelection();
                    } else {
                      provider.selectAllStaff();
                    }
                  },
                  child: Text(
                    selectedStaff.length == staff.length
                        ? 'Deselectează tot'
                        : 'Selectează tot',
                    style: const TextStyle(fontSize: 12),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            if (staff.isEmpty)
              Text(
                'Nu este personal disponibil',
                style: TextStyle(color: Theme.of(context).colorScheme.onSurfaceVariant),
              )
            else
              ...staff.map((staffMember) => _buildStaffOption(staffMember, provider)),
            const SizedBox(height: 8),
            _buildStaffLegend(staff),
          ],
        );
      },
    );
  }

  Widget _buildStaffOption(StaffResponse staffMember, CalendarProvider provider) {
    final isSelected = provider.selectedStaff.contains(staffMember.id);
    final staffColor = provider.getStaffColor(staffMember.id);

    return CheckboxListTile(
      value: isSelected,
      onChanged: (value) {
        if (value == true) {
          provider.selectStaff(staffMember.id);
        } else {
          provider.deselectStaff(staffMember.id);
        }
      },
      title: Row(
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: staffColor,
              shape: BoxShape.circle,
              border: Border.all(color: Theme.of(context).colorScheme.outline),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(staffMember.displayName),
                if (staffMember.nickname != null &&
                    staffMember.nickname!.isNotEmpty &&
                    staffMember.nickname != staffMember.name) ...[
                  Text(
                    '(${staffMember.name})',
                    style: TextStyle(
                      fontSize: 12,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
      activeColor: Theme.of(context).colorScheme.primary,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildStaffLegend(List<StaffResponse> staff) {
    if (staff.isEmpty) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Theme.of(context).colorScheme.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Legendă culori:',
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 12,
            runSpacing: 4,
            children: staff
                .map((staffMember) => Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: AppTheme.getStaffColor(staffMember.id),
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          staffMember.displayName,
                          style: TextStyle(
                            fontSize: 11,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                      ],
                    ))
                .toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildZoomSection() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Zoom și Accesibilitate',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 12),

            // Time slot height slider
            Row(
              children: [
                Icon(Icons.zoom_out, color: Theme.of(context).colorScheme.onSurface, size: 20),
                Expanded(
                  child: Slider(
                    value: provider.timeSlotHeight,
                    min: provider.minTimeSlotHeight,
                    max: provider.maxTimeSlotHeight,
                    divisions: 8,
                    activeColor: Theme.of(context).colorScheme.primary,
                    inactiveColor: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                    onChanged: (value) {
                      provider.setTimeSlotHeight(value);
                    },
                  ),
                ),
                Icon(Icons.zoom_in, color: Theme.of(context).colorScheme.onSurface, size: 20),
              ],
            ),

            // Height indicator and quick actions
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Înălțime: ${provider.timeSlotHeight.round()}px',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                Row(
                  children: [
                    TextButton(
                      onPressed: provider.zoomOut,
                      style: TextButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.primary,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        minimumSize: const Size(40, 32),
                      ),
                      child: const Text('−', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    ),
                    TextButton(
                      onPressed: provider.resetZoom,
                      style: TextButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.primary,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        minimumSize: const Size(50, 32),
                      ),
                      child: const Text('Reset', style: TextStyle(fontSize: 12)),
                    ),
                    TextButton(
                      onPressed: provider.zoomIn,
                      style: TextButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.primary,
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        minimumSize: const Size(40, 32),
                      ),
                      child: const Text('+', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                    ),
                  ],
                ),
              ],
            ),

            const SizedBox(height: 8),
            Text(
              'Ajustează înălțimea intervalelor de timp pentru o vizibilitate mai bună',
              style: TextStyle(
                fontSize: 11,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildDisplayOptionsSection() {
    return Consumer<CalendarProvider>(
      builder: (context, provider, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Opțiuni afișare',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            CheckboxListTile(
              title: Text('Afișează programări anulate'),
              value: provider.showCanceledAppointments,
              onChanged: (value) {
                provider.setShowCanceledAppointments(value ?? false);
              },
              activeColor: Theme.of(context).colorScheme.primary,
              contentPadding: EdgeInsets.zero,
            ),
            const SizedBox(height: 16),
            Center(
              // child: TextButton(
              //   onPressed: () async {
              //     await provider.selectCurrentUserOnly();
              //
              //     // Ensure UI updates to reflect the selection
              //     setState(() {
              //     });
              //   },
              //   style: ElevatedButton.styleFrom(
              //     backgroundColor: Theme.of(context).colorScheme.primary,
              //     foregroundColor: Theme.of(context).colorScheme.onPrimary,
              //     padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
              //     minimumSize: const Size(160, 40),
              //     shape: RoundedRectangleBorder(
              //       borderRadius: BorderRadius.circular(8),
              //     ),
              //   ),
              //   // child: const Row(
              //   //   mainAxisSize: MainAxisSize.min,
              //   //   // children: [
              //   //   //   Icon(Icons.person, size: 18),
              //   //   //   SizedBox(width: 8),
              //   //   //   Text('Doar eu', style: TextStyle(fontWeight: FontWeight.bold)),
              //   //   // ],
              //   // ),
              // ),
            ),
            const SizedBox(height: 16),
          ],
        );
      },
    );
  }
}
