import 'package:flutter/material.dart';
import 'analytics_service.dart';
import '../config/api_config.dart';

/// Service for tracking screen time and user session analytics
class ScreenTimeService {
  static final Map<String, DateTime> _screenStartTimes = {};
  static final Map<String, int> _screenVisitCounts = {};
  static DateTime? _sessionStartTime;
  static DateTime? _lastActivityTime;
  static bool _isInitialized = false;

  /// Initialize screen time tracking
  static void initialize() {
    if (_isInitialized) return;
    
    _sessionStartTime = DateTime.now();
    _lastActivityTime = DateTime.now();
    _isInitialized = true;
    
    if (ApiConfig.enableLogging) {
      debugPrint('📊 Screen time tracking initialized');
    }
  }

  /// Start tracking time for a specific screen
  static void startScreenTime(String screenName) {
    if (!_isInitialized) initialize();
    
    final now = DateTime.now();
    _screenStartTimes[screenName] = now;
    _lastActivityTime = now;
    
    // Increment visit count
    _screenVisitCounts[screenName] = (_screenVisitCounts[screenName] ?? 0) + 1;
    
    // Track screen view in analytics
    AnalyticsService.trackScreenView(screenName);
    
    if (ApiConfig.enableLogging) {
      debugPrint('📊 Started tracking screen time: $screenName');
    }
  }

  /// End tracking time for a specific screen and log the duration
  static void endScreenTime(String screenName) {
    if (!_isInitialized || !_screenStartTimes.containsKey(screenName)) return;
    
    final startTime = _screenStartTimes[screenName]!;
    final endTime = DateTime.now();
    final durationMs = endTime.difference(startTime).inMilliseconds;
    
    // Track screen time in analytics
    AnalyticsService.trackPerformance(
      metricName: 'screen_time',
      durationMs: durationMs,
      parameters: {
        'screen_name': screenName,
        'visit_count': _screenVisitCounts[screenName] ?? 1,
        'session_duration_ms': _getSessionDuration(),
      },
    );
    
    // Remove from tracking
    _screenStartTimes.remove(screenName);
    
    if (ApiConfig.enableLogging) {
      debugPrint('📊 Screen time tracked: $screenName (${durationMs}ms)');
    }
  }

  /// Track user activity (call this on any user interaction)
  static void recordActivity() {
    _lastActivityTime = DateTime.now();
  }

  /// Get current session duration in milliseconds
  static int _getSessionDuration() {
    if (_sessionStartTime == null) return 0;
    return DateTime.now().difference(_sessionStartTime!).inMilliseconds;
  }

  /// Get time since last activity in milliseconds
  static int getTimeSinceLastActivity() {
    if (_lastActivityTime == null) return 0;
    return DateTime.now().difference(_lastActivityTime!).inMilliseconds;
  }

  /// Check if user session is considered active (less than 5 minutes since last activity)
  static bool isSessionActive() {
    return getTimeSinceLastActivity() < 300000; // 5 minutes
  }

  /// End current session and track session analytics
  static void endSession({String? reason}) {
    if (_sessionStartTime == null) return;
    
    final sessionDuration = _getSessionDuration();
    final screensVisited = _screenVisitCounts.length;
    final totalScreenViews = _screenVisitCounts.values.fold(0, (sum, count) => sum + count);
    
    // Track session end in analytics
    AnalyticsService.trackFeatureUsed(
      featureName: 'session_end',
      parameters: {
        'session_duration_ms': sessionDuration,
        'screens_visited': screensVisited,
        'total_screen_views': totalScreenViews,
        'end_reason': reason ?? 'unknown',
        'was_active': isSessionActive(),
      },
    );
    
    // Reset session data
    _sessionStartTime = null;
    _lastActivityTime = null;
    _screenStartTimes.clear();
    _screenVisitCounts.clear();
    
    if (ApiConfig.enableLogging) {
      debugPrint('📊 Session ended: ${sessionDuration}ms, $screensVisited screens');
    }
  }

  /// Get current session statistics
  static Map<String, dynamic> getSessionStats() {
    return {
      'session_duration_ms': _getSessionDuration(),
      'screens_visited': _screenVisitCounts.length,
      'total_screen_views': _screenVisitCounts.values.fold(0, (sum, count) => sum + count),
      'is_active': isSessionActive(),
      'time_since_last_activity_ms': getTimeSinceLastActivity(),
      'current_screens': _screenStartTimes.keys.toList(),
    };
  }

  /// Track screen transition (when navigating between screens)
  static void trackScreenTransition({
    required String fromScreen,
    required String toScreen,
    String? transitionType, // 'navigation', 'back', 'deep_link', etc.
  }) {
    AnalyticsService.trackFeatureUsed(
      featureName: 'screen_transition',
      parameters: {
        'from_screen': fromScreen,
        'to_screen': toScreen,
        'transition_type': transitionType ?? 'navigation',
        'session_duration_ms': _getSessionDuration(),
      },
    );
    
    if (ApiConfig.enableLogging) {
      debugPrint('📊 Screen transition: $fromScreen -> $toScreen');
    }
  }
}

/// Mixin to automatically track screen time for StatefulWidgets
mixin ScreenTimeMixin<T extends StatefulWidget> on State<T> {
  String get screenName;
  
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ScreenTimeService.startScreenTime(screenName);
    });
  }
  
  @override
  void dispose() {
    ScreenTimeService.endScreenTime(screenName);
    super.dispose();
  }
}

/// Widget to automatically track screen time
class ScreenTimeTracker extends StatefulWidget {
  final String screenName;
  final Widget child;
  
  const ScreenTimeTracker({
    super.key,
    required this.screenName,
    required this.child,
  });
  
  @override
  State<ScreenTimeTracker> createState() => _ScreenTimeTrackerState();
}

class _ScreenTimeTrackerState extends State<ScreenTimeTracker> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ScreenTimeService.startScreenTime(widget.screenName);
    });
  }
  
  @override
  void dispose() {
    ScreenTimeService.endScreenTime(widget.screenName);
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
