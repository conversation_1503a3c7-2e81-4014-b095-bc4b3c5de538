import 'package:flutter_test/flutter_test.dart';
import 'package:partykidsapp/models/phone_verification_info.dart';

void main() {
  group('PhoneVerificationInfo Model Tests', () {
    test('fromJson should parse correctly', () {
      final json = {
        'message': 'Codul de verificare a fost trimis cu succes',
        'phoneNumber': '+40712345678',
        'expiresIn': 300,
        'canResendAfter': 60,
        'remainingAttempts': 2,
      };

      final info = PhoneVerificationInfo.fromJson(json);

      expect(info.message, 'Codul de verificare a fost trimis cu succes');
      expect(info.phoneNumber, '+40712345678');
      expect(info.expiresIn, 300);
      expect(info.canResendAfter, 60);
      expect(info.remainingAttempts, 2);
    });

    test('toJson should convert correctly', () {
      final info = PhoneVerificationInfo(
        message: 'ok',
        phoneNumber: '+40111222333',
        expiresIn: 200,
        canResendAfter: 30,
        remainingAttempts: 1,
      );

      final json = info.toJson();

      expect(json['message'], 'ok');
      expect(json['phoneNumber'], '+40111222333');
      expect(json['expiresIn'], 200);
      expect(json['canResendAfter'], 30);
      expect(json['remainingAttempts'], 1);
    });
  });
}
