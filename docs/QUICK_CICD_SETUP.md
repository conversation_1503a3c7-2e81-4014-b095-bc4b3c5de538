# Quick CI/CD Setup Guide

## ⚡ **5-Minute Setup**

### Step 1: Add GitHub Secrets
1. Go to your repo → Settings → Secrets and variables → Actions
2. Add these secrets:
   ```
   APP_STORE_CONNECT_KEY_ID: 3JH9KG86RL
   APP_STORE_CONNECT_ISSUER_ID: bc1a1bf3-1f75-4702-b97f-dd90530f3a6e
   APP_STORE_CONNECT_API_KEY: [base64 encoded .p8 file]
   ```

### Step 2: Encode Your API Key
```bash
# In Terminal:
base64 -i ~/Downloads/AuthKey_3JH9KG86RL.p8 | pbcopy
# Paste the result as APP_STORE_CONNECT_API_KEY secret
```

### Step 3: Test the Workflow
1. Make a small change to your code
2. Commit and push to main branch:
   ```bash
   git add .
   git commit -m "🚀 Test CI/CD deployment"
   git push origin main
   ```
3. Check GitHub Actions tab for progress

## 🎯 **What Happens Automatically**

### On Every Push to Main:
1. ✅ **Tests run** - Flutter test suite
2. ✅ **Code analysis** - Flutter analyze
3. ✅ **Build increment** - Auto-increment build number
4. ✅ **iOS build** - Create signed IPA
5. ✅ **TestFlight upload** - Deploy to TestFlight
6. ✅ **Git commit** - Update build number in repo
7. ✅ **GitHub release** - Create release tag

### Notifications:
- ✅ **GitHub notifications** - Build status
- ✅ **Email alerts** - Success/failure
- ✅ **TestFlight ready** - App available for testing

## 🔧 **Customization Options**

### Change Trigger Branches
```yaml
# In .github/workflows/ios-deploy.yml
on:
  push:
    branches: [ main, develop, release/* ]  # Add your branches
```

### Skip CI for Specific Commits
```bash
git commit -m "Update README [skip ci]"
```

### Manual Deployment
1. Go to GitHub Actions tab
2. Select "iOS TestFlight Deployment"
3. Click "Run workflow"
4. Choose branch and optional build number

## 🚨 **Troubleshooting**

### Common Issues:

#### 1. **"Secrets not found"**
- ✅ Check secret names match exactly
- ✅ Verify secrets are added to correct repo
- ✅ Ensure no extra spaces in secret values

#### 2. **"Build failed - signing"**
- ✅ Check certificates are valid
- ✅ Verify provisioning profiles
- ✅ Ensure team ID is correct

#### 3. **"Upload failed - authentication"**
- ✅ Verify API key is properly base64 encoded
- ✅ Check key ID and issuer ID
- ✅ Ensure API key has proper permissions

#### 4. **"Build number conflict"**
- ✅ Manually increment build number
- ✅ Check TestFlight for existing builds
- ✅ Verify git commits are working

## 📊 **Monitoring Your Deployments**

### GitHub Actions Dashboard
- View build logs and status
- Download build artifacts
- Monitor deployment history

### TestFlight Dashboard
- Check build processing status
- Manage beta testers
- View crash reports

### App Store Connect
- Monitor app review status
- Check analytics and metrics
- Manage app metadata

## 🎉 **Success Indicators**

You'll know it's working when:
- ✅ GitHub Actions shows green checkmarks
- ✅ Build number auto-increments in pubspec.yaml
- ✅ New builds appear in TestFlight
- ✅ GitHub releases are created automatically
- ✅ Team gets notified of new builds

## 🚀 **Next Steps**

1. **Add team members** to TestFlight for testing
2. **Set up staging environment** for pre-production testing
3. **Configure notifications** for Slack/email alerts
4. **Add more quality gates** (code coverage, security scans)
5. **Set up App Store submission** workflow for releases
