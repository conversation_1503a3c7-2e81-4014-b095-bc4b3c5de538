#!/bin/bash

# Script to build Flutter app for specific environment
# Usage: ./scripts/build_for_environment.sh [environment] [platform] [build_type]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

show_usage() {
    echo "Usage: $0 [environment] [platform] [build_type]"
    echo ""
    echo "Arguments:"
    echo "  environment: development, staging, production (default: auto-detect from branch)"
    echo "  platform:    ios, android, both (default: both)"
    echo "  build_type:  debug, release (default: release for production, debug for others)"
    echo ""
    echo "Examples:"
    echo "  $0                           # Auto-detect environment, build both platforms"
    echo "  $0 production ios release    # Build production iOS release"
    echo "  $0 development android debug # Build development Android debug"
}

detect_environment() {
    local branch_name
    branch_name=$(git branch --show-current 2>/dev/null || git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")
    
    case "$branch_name" in
        "main"|"master")
            echo "production"
            ;;
        "staging"|"stage"|"develop")
            echo "staging"
            ;;
        *)
            echo "development"
            ;;
    esac
}

validate_environment() {
    case "$1" in
        "development"|"staging"|"production")
            echo "$1"
            ;;
        *)
            print_error "Invalid environment: $1"
            show_usage
            exit 1
            ;;
    esac
}

validate_platform() {
    case "$1" in
        "ios"|"android"|"both")
            echo "$1"
            ;;
        *)
            print_error "Invalid platform: $1"
            show_usage
            exit 1
            ;;
    esac
}

validate_build_type() {
    case "$1" in
        "debug"|"release")
            echo "$1"
            ;;
        *)
            print_error "Invalid build type: $1"
            show_usage
            exit 1
            ;;
    esac
}

clean_build() {
    print_info "Cleaning previous builds..."
    flutter clean
    flutter pub get
}

build_ios() {
    local env=$1
    local build_type=$2
    
    print_info "Building iOS for $env environment ($build_type)..."
    
    cd ios
    pod install
    cd ..
    
    if [ "$build_type" = "release" ]; then
        flutter build ios --release --dart-define=FLUTTER_ENV=$env
    else
        flutter build ios --debug --dart-define=FLUTTER_ENV=$env
    fi
    
    print_success "iOS build completed"
}

build_android() {
    local env=$1
    local build_type=$2
    
    print_info "Building Android for $env environment ($build_type)..."
    
    if [ "$build_type" = "release" ]; then
        flutter build apk --release --dart-define=FLUTTER_ENV=$env
        if [ "$env" = "production" ]; then
            print_info "Building App Bundle for production..."
            flutter build appbundle --release --dart-define=FLUTTER_ENV=$env
        fi
    else
        flutter build apk --debug --dart-define=FLUTTER_ENV=$env
    fi
    
    print_success "Android build completed"
}

main() {
    print_info "🚀 Flutter Environment Build Script"
    echo
    
    # Parse arguments
    local environment=${1:-$(detect_environment)}
    local platform=${2:-"both"}
    local build_type=${3:-""}
    
    # Validate inputs
    environment=$(validate_environment "$environment")
    platform=$(validate_platform "$platform")
    
    # Set default build type based on environment
    if [ -z "$build_type" ]; then
        if [ "$environment" = "production" ]; then
            build_type="release"
        else
            build_type="debug"
        fi
    else
        build_type=$(validate_build_type "$build_type")
    fi
    
    print_info "Configuration:"
    echo "  Environment: $environment"
    echo "  Platform: $platform"
    echo "  Build Type: $build_type"
    echo
    
    # Set environment variable
    export FLUTTER_ENV=$environment
    
    # Clean build
    clean_build
    
    # Build for specified platforms
    case "$platform" in
        "ios")
            build_ios "$environment" "$build_type"
            ;;
        "android")
            build_android "$environment" "$build_type"
            ;;
        "both")
            build_ios "$environment" "$build_type"
            build_android "$environment" "$build_type"
            ;;
    esac
    
    echo
    print_success "Build completed successfully!"
    
    if [ "$environment" = "production" ]; then
        echo
        print_warning "PRODUCTION BUILD COMPLETED"
        print_warning "Make sure to test thoroughly before release!"
    fi
    
    echo
    print_info "Build artifacts location:"
    if [ "$platform" = "ios" ] || [ "$platform" = "both" ]; then
        echo "  iOS: build/ios/iphoneos/Runner.app"
    fi
    if [ "$platform" = "android" ] || [ "$platform" = "both" ]; then
        echo "  Android APK: build/app/outputs/flutter-apk/"
        if [ "$environment" = "production" ] && [ "$build_type" = "release" ]; then
            echo "  Android Bundle: build/app/outputs/bundle/"
        fi
    fi
}

# Check if help is requested
if [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_usage
    exit 0
fi

# Run main function
main "$@"
