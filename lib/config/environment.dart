/// Environment configuration for the Partykids app
/// Handles different deployment environments and their specific configurations
library;

import 'package:flutter/foundation.dart';

enum Environment {
  development,
  staging,
  production
}

class EnvironmentConfig {
  // Current environment - automatically detected based on build configuration
  static Environment? _currentEnvironment;

  /// Automatically detect environment based on build configuration
  static Environment _detectEnvironment() {
    // Check for compile-time environment variables first
    String? envFromBuild = String.fromEnvironment('FLUTTER_ENV');
    switch (envFromBuild.toLowerCase()) {
      case 'production':
      case 'prod':
        return Environment.production;
      case 'staging':
      case 'stage':
        return Environment.staging;
      case 'development':
      case 'dev':
      default:
        return Environment.development;
    }
  }

  /// Get the current environment
  static Environment get currentEnvironment {
    _currentEnvironment ??= _detectEnvironment();
    return _currentEnvironment!;
  }

  /// Force set environment (for testing purposes)
  static void setEnvironment(Environment env) {
    _currentEnvironment = env;
  }

  /// Get the API base URL for the current environment
  static String get apiBaseUrl {
    switch (currentEnvironment) {
      case Environment.development:
        return 'http://192.168.1.162:8080';
      case Environment.staging:
        return 'https://staging-api.partykids.ro';
      case Environment.production:
        return 'https://www.api.partykids-partykidsapp.ro';
    }
  }

  /// Get the Firebase configuration for the current environment
  static Map<String, String> get firebaseConfig {
    switch (currentEnvironment) {
      case Environment.development:
        return {
          'projectId': 'partykidsapp',
          'appId': '1:159713565362:android:371de7c613640d521ec7ab',
          'iosAppId': '1:159713565362:ios:371de7c613640d521ec7ab',
          'androidAppId': '1:159713565362:android:371de7c613640d521ec7ab',
          'webAppId': '1:159713565362:web:371de7c613640d521ec7ab',
          'apiKey': 'AIzaSyCO8pf-O0ptpalZEjpuKmP0S4rRkLkOdQE',
          'messagingSenderId': '159713565362',
          'authDomain': 'partykidsapp.firebaseapp.com',
          'googleClientId': '159713565362-l0tqek5gvgdsleq2ur4ijiqtl7i812o6.apps.googleusercontent.com',
        };
      case Environment.staging:
        throw UnimplementedError('Production Firebase configuration not implemented yet');
      case Environment.production:
        throw UnimplementedError('Production Firebase configuration not implemented yet');
    }
  }

  /// Google Maps & Places API key used for location features
  static String get googleMapsApiKey {
    switch (currentEnvironment) {
      case Environment.development:
        return 'AIzaSyCO77ldStnRCjfZ3EThONj8F8X6d3EVWvI';
      case Environment.staging:
        return 'AIzaSyCO77ldStnRCjfZ3EThONj8F8X6d3EVWvI';
      case Environment.production:
        return 'AIzaSyCO77ldStnRCjfZ3EThONj8F8X6d3EVWvI';
    }
  }

  /// Get the app name for the current environment
  static String get appName {
    switch (currentEnvironment) {
      case Environment.development:
        return 'Partykids (Dev)';
      case Environment.staging:
        return 'Partykids (Staging)';
      case Environment.production:
        return 'Partykids';
    }
  }

  /// Get the bundle ID for the current environment
  static String get bundleId {
    switch (currentEnvironment) {
      case Environment.development:
        return 'ro.partykidsprogramari.partykids.dev';
      case Environment.staging:
        return 'ro.partykidsprogramari.partykids.staging';
      case Environment.production:
        return 'ro.partykidsprogramari.partykids';
    }
  }

  /// Check if the current environment is development
  static bool get isDevelopment => currentEnvironment == Environment.development;

  /// Check if the current environment is staging
  static bool get isStaging => currentEnvironment == Environment.staging;

  /// Check if the current environment is production
  static bool get isProduction => currentEnvironment == Environment.production;

  /// Get debug mode status
  static bool get isDebugMode => isDevelopment || isStaging;

  /// Get API timeout duration
  static Duration get apiTimeout {
    switch (currentEnvironment) {
      case Environment.development:
        return const Duration(seconds: 30);
      case Environment.staging:
        return const Duration(seconds: 20);
      case Environment.production:
        return const Duration(seconds: 15);
    }
  }

  /// Get logging level
  static String get logLevel {
    switch (currentEnvironment) {
      case Environment.development:
        return 'DEBUG';
      case Environment.staging:
        return 'INFO';
      case Environment.production:
        return 'ERROR';
    }
  }

  /// Print current environment configuration (debug mode only)
  static void printConfig() {
    if (isDebugMode) {
      debugPrint('🌍 Environment: ${currentEnvironment.name}');
      debugPrint('🔗 API Base URL: $apiBaseUrl');
      debugPrint('📱 App Name: $appName');
      debugPrint('📦 Bundle ID: $bundleId');
      debugPrint('⏱️ API Timeout: ${apiTimeout.inSeconds}s');
      debugPrint('📝 Log Level: $logLevel');
    }
  }
}
