#!/bin/bash
# Test Flutter version compatibility before deployment
# This script verifies that the app builds with the current Flutter version

set -e

echo "🧪 Testing Flutter Version Compatibility"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    print_error "Not in Flutter project root. Please run from flutter-partykids directory."
    exit 1
fi

# Check Flutter version
print_info "Checking Flutter version..."
FLUTTER_VERSION=$(flutter --version | head -n 1)
print_info "Current Flutter version: $FLUTTER_VERSION"

# Check Dart version
DART_VERSION=$(dart --version)
print_info "Current Dart version: $DART_VERSION"

# Check pubspec.yaml requirements
print_info "Checking pubspec.yaml requirements..."
SDK_REQUIREMENT=$(grep "sdk:" pubspec.yaml | head -n 1)
print_info "SDK requirement: $SDK_REQUIREMENT"

# Clean previous builds
print_info "Cleaning previous builds..."
flutter clean

# Get dependencies
print_info "Getting Flutter dependencies..."
if flutter pub get; then
    print_success "Dependencies resolved successfully"
else
    print_error "Failed to resolve dependencies"
    print_info "This usually means version conflicts. Check the error above."
    exit 1
fi

# Run Flutter doctor
print_info "Running Flutter doctor..."
flutter doctor -v

# Analyze code
print_info "Analyzing code..."
if flutter analyze; then
    print_success "Code analysis passed"
else
    print_warning "Code analysis found issues (may not be critical)"
fi

# Test build for iOS (no codesign)
print_info "Testing iOS build..."
if flutter build ios --no-codesign; then
    print_success "iOS build successful"
else
    print_error "iOS build failed"
    exit 1
fi

# Test build for Android (if needed)
print_info "Testing Android build..."
if flutter build apk --debug; then
    print_success "Android build successful"
else
    print_warning "Android build failed (may not be critical for iOS-only deployment)"
fi

# Check for any dependency conflicts
print_info "Checking for dependency conflicts..."
if flutter pub deps | grep -i "conflict\|error"; then
    print_warning "Potential dependency conflicts detected"
else
    print_success "No dependency conflicts detected"
fi

print_success "Flutter version compatibility test completed!"
echo ""
print_info "Summary:"
echo "- Flutter version: Compatible ✅"
echo "- Dependencies: Resolved ✅"
echo "- iOS build: Working ✅"
echo "- Code analysis: Passed ✅"
echo ""
print_info "You can now safely push to main branch for automatic deployment."
echo ""
print_info "To deploy manually:"
echo "  ./scripts/deploy-ios.sh --auto"
echo ""
print_info "To push for automatic deployment:"
echo "  git add ."
echo "  git commit -m '🚀 Update Flutter version to 3.32.4'"
echo "  git push origin main"
