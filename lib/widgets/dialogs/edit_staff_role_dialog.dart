import 'package:partykidsapp/utils/snack_bar_utils.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../models/user_role.dart';
import '../../providers/role_provider.dart';
import '../../services/staff_service.dart';
import '../../config/theme/app_theme.dart';

class EditStaffRoleDialog extends StatefulWidget {
  final StaffResponse staff;
  final VoidCallback? onSuccess;

  const EditStaffRoleDialog({
    super.key,
    required this.staff,
    this.onSuccess,
  });

  @override
  State<EditStaffRoleDialog> createState() => _EditStaffRoleDialogState();
}

class _EditStaffRoleDialogState extends State<EditStaffRoleDialog> {
  final _formKey = GlobalKey<FormState>();
  final _notesController = TextEditingController();

  late GroomerRole _selectedRole;
  late ClientDataPermission _selectedPermission;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedRole = widget.staff.groomerRole;
    _selectedPermission = widget.staff.clientDataPermission;
    _notesController.text = widget.staff.notes ?? '';
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                const Icon(
                  Icons.edit,
                  color: AppColors.forestGreen,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Editează Rol Membru',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        widget.staff.name,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            const SizedBox(height: 24),
            // Form
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildStaffInfoSection(),
                      const SizedBox(height: 24),
                      _buildRoleSection(),
                      const SizedBox(height: 24),
                      _buildPermissionsSection(),
                      const SizedBox(height: 24),
                      _buildNotesSection(),
                    ],
                  ),
                ),
              ),
            ),
            // Footer
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
                    child: const Text('Anulează'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _handleSave,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.forestGreen,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : const Text('Salvează'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStaffInfoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Informații membru',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.forestGreen,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: widget.staff.isActive ? AppColors.forestGreen : Colors.grey,
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    widget.staff.name.split(' ').map((n) => n.isNotEmpty ? n[0] : '').join(''),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.staff.name,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (widget.staff.phone != null) ...[
                      const SizedBox(height: 2),
                      Text(
                        widget.staff.phone!,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: widget.staff.isActive ? Colors.green : Colors.red,
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 6),
                        Text(
                          widget.staff.isActive ? 'Activ' : 'Inactiv',
                          style: TextStyle(
                            fontSize: 12,
                            color: widget.staff.isActive ? Colors.green : Colors.red,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRoleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Rol în echipă',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.forestGreen,
          ),
        ),
        const SizedBox(height: 12),
        ...GroomerRole.values
            .where((role) => role != GroomerRole.regularGroomer) // Exclude deprecated role
            .where((role) => _canAssignRole(role)) // Exclude roles that current user cannot assign
            .map((role) => RadioListTile<GroomerRole>(
          title: Text(role.displayName),
          subtitle: Text(role.description),
          value: role,
          groupValue: _selectedRole,
          activeColor: AppColors.forestGreen,
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedRole = value;
                // Auto-adjust permissions based on role
                if (value.hasManagementAccess) {
                  _selectedPermission = ClientDataPermission.fullAccess;
                }
              });
            }
          },
        )),
      ],
    );
  }

  Widget _buildPermissionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Permisiuni acces date clienți',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.forestGreen,
          ),
        ),
        const SizedBox(height: 12),
        ...ClientDataPermission.values.map((permission) => RadioListTile<ClientDataPermission>(
          title: Text(permission.displayName),
          subtitle: Text(permission.description),
          value: permission,
          groupValue: _selectedPermission,
          activeColor: AppColors.forestGreen,
          onChanged: _selectedRole.hasManagementAccess 
              ? null // Chief groomers always have full access
              : (value) {
                  if (value != null) {
                    setState(() {
                      _selectedPermission = value;
                    });
                  }
                },
        )),
        if (_selectedRole.hasManagementAccess) ...[
          const SizedBox(height: 8),
          Text(
            'Groomer Șef are automat acces complet la datele clienților.',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey.shade600,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Note (opțional)',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: AppColors.forestGreen,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: _notesController,
          maxLines: 3,
          decoration: const InputDecoration(
            hintText: 'Note despre acest membru al echipei...',
            border: OutlineInputBorder(),
            focusedBorder: OutlineInputBorder(
              borderSide: BorderSide(color: AppColors.forestGreen),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _handleSave() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final request = UpdateStaffRoleRequest(
        groomerRole: _selectedRole,
        clientDataPermission: _selectedPermission,
        notes: _notesController.text.trim().isNotEmpty 
            ? _notesController.text.trim() 
            : null,
      );

      final response = await StaffService.updateStaffRoleInCurrentSalon(
        widget.staff.id,
        request,
      );

      if (response.success) {
        if (mounted) {
          Navigator.of(context).pop();
          showTopSnackBar(context, 
            SnackBar(
              content: Text('Rolul pentru ${widget.staff.name} a fost actualizat cu succes'),
              backgroundColor: AppColors.forestGreen,
            ),
          );
          widget.onSuccess?.call();
        }
      } else {
        if (mounted) {
          showTopSnackBar(context, 
            SnackBar(
              content: Text(response.error ?? 'Nu s-a putut actualiza rolul'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        showTopSnackBar(context, 
          SnackBar(
            content: Text('Eroare: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Check if the current user can assign the given role
  bool _canAssignRole(GroomerRole role) {
    // If the role is not chief groomer, it can always be assigned
    if (role != GroomerRole.chiefGroomer) {
      return true;
    }

    // Only admins can assign chief groomer roles
    final roleProvider = Provider.of<RoleProvider>(context, listen: false);
    return roleProvider.isAdmin;
  }
}
