// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCO8pf-O0ptpalZEjpuKmP0S4rRkLkOdQE',
    appId: '1:159713565362:web:371de7c613640d521ec7ab',
    messagingSenderId: '159713565362',
    projectId: 'partykidsapp',
    authDomain: 'partykidsapp.firebaseapp.com',
    storageBucket: 'partykidsapp.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCO8pf-O0ptpalZEjpuKmP0S4rRkLkOdQE',
    appId: '1:159713565362:android:371de7c613640d521ec7ab',
    messagingSenderId: '159713565362',
    projectId: 'partykidsapp',
    storageBucket: 'partykidsapp.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCO8pf-O0ptpalZEjpuKmP0S4rRkLkOdQE',
    appId: '1:159713565362:ios:371de7c613640d521ec7ab',
    messagingSenderId: '159713565362',
    projectId: 'partykidsapp',
    storageBucket: 'partykidsapp.firebasestorage.app',
    iosBundleId: 'com.partykids.partykidsapp',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCO8pf-O0ptpalZEjpuKmP0S4rRkLkOdQE',
    appId: '1:159713565362:ios:371de7c613640d521ec7ab',
    messagingSenderId: '159713565362',
    projectId: 'partykidsapp',
    storageBucket: 'partykidsapp.firebasestorage.app',
    iosBundleId: 'com.partykids.partykidsapp',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCO8pf-O0ptpalZEjpuKmP0S4rRkLkOdQE',
    appId: '1:159713565362:web:371de7c613640d521ec7ab',
    messagingSenderId: '159713565362',
    projectId: 'partykidsapp',
    authDomain: 'partykidsapp.firebaseapp.com',
    storageBucket: 'partykidsapp.firebasestorage.app',
  );
}
