/// Working hours settings for a salon
class WorkingHoursSettings {
  final String salonId;
  final Map<String, DaySchedule> weeklySchedule;
  final List<Holiday> holidays;
  final List<CustomClosure> customClosures;
  final DateTime updatedAt;

  const WorkingHoursSettings({
    required this.salonId,
    required this.weeklySchedule,
    required this.holidays,
    required this.customClosures,
    required this.updatedAt,
  });

  /// Create WorkingHoursSettings from JSON
  factory WorkingHoursSettings.fromJson(Map<String, dynamic> json) {
    final weeklyScheduleMap = <String, DaySchedule>{};
    final weeklyScheduleJson = json['weeklySchedule'] as Map<String, dynamic>? ?? {};

    for (final entry in weeklyScheduleJson.entries) {
      weeklyScheduleMap[entry.key] = DaySchedule.fromJson(entry.value as Map<String, dynamic>);
    }

    return WorkingHoursSettings(
      salonId: json['salonId'] as String,
      weeklySchedule: weeklyScheduleMap,
      holidays: (json['holidays'] as List<dynamic>? ?? [])
          .map((h) => Holiday.fromJson(h as Map<String, dynamic>))
          .toList(),
      customClosures: (json['customClosures'] as List<dynamic>? ?? [])
          .map((c) => CustomClosure.fromJson(c as Map<String, dynamic>))
          .toList(),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// Convert WorkingHoursSettings to JSON
  Map<String, dynamic> toJson() {
    final weeklyScheduleJson = <String, dynamic>{};
    for (final entry in weeklySchedule.entries) {
      weeklyScheduleJson[entry.key] = entry.value.toJson();
    }

    return {
      'salonId': salonId,
      'weeklySchedule': weeklyScheduleJson,
      'holidays': holidays.map((h) => h.toJson()).toList(),
      'customClosures': customClosures.map((c) => c.toJson()).toList(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// Create a copy with updated values
  WorkingHoursSettings copyWith({
    String? salonId,
    Map<String, DaySchedule>? weeklySchedule,
    List<Holiday>? holidays,
    List<CustomClosure>? customClosures,
    DateTime? updatedAt,
  }) {
    return WorkingHoursSettings(
      salonId: salonId ?? this.salonId,
      weeklySchedule: weeklySchedule ?? this.weeklySchedule,
      holidays: holidays ?? this.holidays,
      customClosures: customClosures ?? this.customClosures,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get schedule for a specific day
  DaySchedule? getScheduleForDay(String dayOfWeek) {
    return weeklySchedule[dayOfWeek.toLowerCase()];
  }

  /// Check if salon is open on a specific date
  bool isOpenOnDate(DateTime date) {
    final dayOfWeek = _getDayOfWeekString(date);
    final daySchedule = getScheduleForDay(dayOfWeek);

    if (daySchedule == null || daySchedule.isDayOff) {
      return false;
    }

    // Check for holidays
    if (holidays.any((h) => h.date.year == date.year &&
                           h.date.month == date.month &&
                           h.date.day == date.day &&
                           !h.isWorkingDay)) {
      return false;
    }

    // Check for custom closures
    if (customClosures.any((c) =>
        !date.isBefore(c.startDate) && !date.isAfter(c.endDate))) {
      return false;
    }

    return true;
  }

  /// Get working hours for a specific date if the salon is open
  ({String? startTime, String? endTime, String? breakStart, String? breakEnd})?
      getWorkingHoursForDate(DateTime date) {
    if (!isOpenOnDate(date)) {
      return null;
    }

    final dayOfWeek = _getDayOfWeekString(date);
    final schedule = getScheduleForDay(dayOfWeek);
    if (schedule == null) return null;

    return (
      startTime: schedule.startTime,
      endTime: schedule.endTime,
      breakStart: schedule.breakStart,
      breakEnd: schedule.breakEnd,
    );
  }

  /// Check if a time range is completely within the working hours of that day
  bool isTimeRangeWithinWorkingHours(DateTime start, DateTime end) {
    if (start.year != end.year ||
        start.month != end.month ||
        start.day != end.day) {
      return false;
    }

    if (!isOpenOnDate(start)) {
      return false;
    }

    final schedule = getScheduleForDay(_getDayOfWeekString(start));
    if (schedule == null ||
        schedule.startTime == null ||
        schedule.endTime == null) {
      return false;
    }

    final startMinutes = _timeToMinutes(schedule.startTime!);
    final endMinutes = _timeToMinutes(schedule.endTime!);
    final appointmentStart = start.hour * 60 + start.minute;
    final appointmentEnd = end.hour * 60 + end.minute;

    return appointmentStart >= startMinutes && appointmentEnd <= endMinutes;
  }

  int _timeToMinutes(String time) {
    final parts = time.split(':');
    return int.parse(parts[0]) * 60 + int.parse(parts[1]);
  }

  /// Get day of week string from DateTime
  String _getDayOfWeekString(DateTime date) {
    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    return days[date.weekday - 1];
  }
}

/// Schedule for a single day
class DaySchedule {
  final String? startTime;
  final String? endTime;
  final bool isWorkingDay;
  final String? breakStart;
  final String? breakEnd;

  const DaySchedule({
    this.startTime,
    this.endTime,
    required this.isWorkingDay,
    this.breakStart,
    this.breakEnd,
  });

  factory DaySchedule.fromJson(Map<String, dynamic> json) {
    return DaySchedule(
      startTime: json['startTime'] as String?,
      endTime: json['endTime'] as String?,
      isWorkingDay: json['isWorkingDay'] as bool? ?? false,
      breakStart: json['breakStart'] as String?,
      breakEnd: json['breakEnd'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'startTime': startTime,
      'endTime': endTime,
      'isWorkingDay': isWorkingDay,
      'breakStart': breakStart,
      'breakEnd': breakEnd,
    };
  }

  DaySchedule copyWith({
    String? startTime,
    String? endTime,
    bool? isWorkingDay,
    String? breakStart,
    String? breakEnd,
    bool clearBreak = false,
  }) {
    return DaySchedule(
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      isWorkingDay: isWorkingDay ?? this.isWorkingDay,
      breakStart: clearBreak ? null : (breakStart ?? this.breakStart),
      breakEnd: clearBreak ? null : (breakEnd ?? this.breakEnd),
    );
  }

  /// Validate that break is within working hours
  bool isBreakValid() {
    if (breakStart == null || breakEnd == null || !isWorkingDay) return true;
    if (startTime == null || endTime == null) return true;

    final start = _timeToMinutes(startTime!);
    final end = _timeToMinutes(endTime!);
    final bStart = _timeToMinutes(breakStart!);
    final bEnd = _timeToMinutes(breakEnd!);

    return bStart >= start && bEnd <= end && bStart < bEnd;
  }

  int _timeToMinutes(String time) {
    final parts = time.split(':');
    return int.parse(parts[0]) * 60 + int.parse(parts[1]);
  }

  /// Check if day is off (not working)
  bool get isDayOff => !isWorkingDay;

  /// Check if break is enabled
  bool get hasBreak => breakStart != null && breakEnd != null;
}



/// Romanian legal holiday
class Holiday {
  final String name;
  final DateTime date;
  final bool isWorkingDay;
  final HolidayType type;

  const Holiday({
    required this.name,
    required this.date,
    required this.isWorkingDay,
    required this.type,
  });

  factory Holiday.fromJson(Map<String, dynamic> json) {
    return Holiday(
      name: json['name'] as String,
      date: DateTime.parse(json['date'] as String),
      isWorkingDay: json['isWorkingDay'] as bool? ?? false,
      type: HolidayType.fromString(json['type'] as String? ?? 'LEGAL'),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'date': date.toIso8601String(),
      'isWorkingDay': isWorkingDay,
      'type': type.value,
    };
  }

  Holiday copyWith({
    String? name,
    DateTime? date,
    bool? isWorkingDay,
    HolidayType? type,
  }) {
    return Holiday(
      name: name ?? this.name,
      date: date ?? this.date,
      isWorkingDay: isWorkingDay ?? this.isWorkingDay,
      type: type ?? this.type,
    );
  }
}

/// Custom salon closure
class CustomClosure {
  final String reason;
  final DateTime startDate;
  final DateTime endDate;
  final String? description;

  const CustomClosure({
    required this.reason,
    required this.startDate,
    required this.endDate,
    this.description,
  });

  /// Backwards compatible factory supporting both single `date` and
  /// new `startDate`/`endDate` fields.
  factory CustomClosure.fromJson(Map<String, dynamic> json) {
    if (json.containsKey('startDate') && json.containsKey('endDate')) {
      return CustomClosure(
        reason: json['reason'] as String,
        startDate: DateTime.parse(json['startDate'] as String),
        endDate: DateTime.parse(json['endDate'] as String),
        description: json['description'] as String?,
      );
    }

    final singleDate = DateTime.parse(json['date'] as String);
    return CustomClosure(
      reason: json['reason'] as String,
      startDate: singleDate,
      endDate: singleDate,
      description: json['description'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    if (startDate.isAtSameMomentAs(endDate)) {
      return {
        'reason': reason,
        'date': startDate.toIso8601String(),
        'description': description,
      };
    }
    return {
      'reason': reason,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'description': description,
    };
  }

  /// Convenience getter for old API usage where only a single date was used.
  DateTime get date => startDate;

  CustomClosure copyWith({
    String? reason,
    DateTime? startDate,
    DateTime? endDate,
    String? description,
  }) {
    return CustomClosure(
      reason: reason ?? this.reason,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      description: description ?? this.description,
    );
  }
}

/// Holiday types
enum HolidayType {
  legal('LEGAL'),
  religious('RELIGIOUS'),
  national('NATIONAL');

  const HolidayType(this.value);
  final String value;

  static HolidayType fromString(String value) {
    switch (value.toUpperCase()) {
      case 'LEGAL':
        return HolidayType.legal;
      case 'RELIGIOUS':
        return HolidayType.religious;
      case 'NATIONAL':
        return HolidayType.national;
      default:
        return HolidayType.legal;
    }
  }

  String get displayName {
    switch (this) {
      case HolidayType.legal:
        return 'Sărbătoare legală';
      case HolidayType.religious:
        return 'Sărbătoare religioasă';
      case HolidayType.national:
        return 'Sărbătoare națională';
    }
  }
}

/// Request model for updating working hours
class UpdateWorkingHoursRequest {
  final Map<String, DaySchedule> weeklySchedule;
  final List<Holiday> holidays;
  final List<CustomClosure> customClosures;

  const UpdateWorkingHoursRequest({
    required this.weeklySchedule,
    required this.holidays,
    required this.customClosures,
  });

  /// Convert to JSON for API request
  Map<String, dynamic> toJson() {
    final weeklyScheduleJson = <String, dynamic>{};
    for (final entry in weeklySchedule.entries) {
      weeklyScheduleJson[entry.key] = entry.value.toJson();
    }

    return {
      'weeklySchedule': weeklyScheduleJson,
      'holidays': holidays.map((h) => h.toJson()).toList(),
      'customClosures': customClosures.map((c) => c.toJson()).toList(),
    };
  }

  /// Create from WorkingHoursSettings
  factory UpdateWorkingHoursRequest.fromWorkingHoursSettings(WorkingHoursSettings settings) {
    return UpdateWorkingHoursRequest(
      weeklySchedule: settings.weeklySchedule,
      holidays: settings.holidays,
      customClosures: settings.customClosures,
    );
  }
}
