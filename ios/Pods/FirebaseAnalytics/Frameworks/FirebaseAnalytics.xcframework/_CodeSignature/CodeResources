<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ios-arm64/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<data>
		plvZyPQu9b6IDPmuktiAI8xiEfo=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<data>
		JxSy4BVIpZB3s+tbI3EgcIVsvN0=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<data>
		IN4riVkldIj3MlPpvlUuU1i+I8w=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<data>
		58LqQ/Y9jqPGz3JttzK0z7jPLLc=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<data>
		B+YLaEHhiOu6tTPZaB5wWy4n95U=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<data>
		HrzLufMtB3E5zAfPXZTkYQdGyHI=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<data>
		SztBbCRZ5QE523pdMx4HZt0AYKE=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<data>
		33ogzLW88kbc3XVXJp9Opq9Znmc=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<data>
		0nWXABx/HRqcUMkqMxRdJuHxqQU=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<data>
		wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<data>
		6tM+QmAiCFyFHMaFXgWsH/uYosM=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Info.plist</key>
		<data>
		IddYklCmJJsa7Thme5ifeOf9rv4=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo</key>
		<data>
		vkVoHhy3kd/rHaHNaPC5tJHZzbQ=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		OIyKZwUs8zVioE8HQOB78/abijw=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		NAvpCrv1M1YGI6Vin6eL42cx/3s=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		BHIT/FyHupjfAkf+upImM8fRLqU=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		NAvpCrv1M1YGI6Vin6eL42cx/3s=
		</data>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<data>
		uLysK0T5K1GSoqJmcXx9AnZ9lqY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/FirebaseAnalytics</key>
		<data>
		mhR3Ug9lUl1yUQTCW4m7YkyAmZg=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+AppDelegate.h</key>
		<data>
		JxSy4BVIpZB3s+tbI3EgcIVsvN0=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+Consent.h</key>
		<data>
		IN4riVkldIj3MlPpvlUuU1i+I8w=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+OnDevice.h</key>
		<data>
		58LqQ/Y9jqPGz3JttzK0z7jPLLc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics.h</key>
		<data>
		B+YLaEHhiOu6tTPZaB5wWy4n95U=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIREventNames.h</key>
		<data>
		HrzLufMtB3E5zAfPXZTkYQdGyHI=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRParameterNames.h</key>
		<data>
		SztBbCRZ5QE523pdMx4HZt0AYKE=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRUserPropertyNames.h</key>
		<data>
		33ogzLW88kbc3XVXJp9Opq9Znmc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-Swift.h</key>
		<data>
		cu0mXcU7RrT9nKAMSamYxQcZ8MA=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-umbrella.h</key>
		<data>
		wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics.h</key>
		<data>
		6tM+QmAiCFyFHMaFXgWsH/uYosM=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-ios-macabi.swiftsourceinfo</key>
		<data>
		mNZgmu3wt/RqzubmpQ07j2eNxwY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-ios-macabi.swiftsourceinfo</key>
		<data>
		mtSWw+snhbHzDlzsJrgUl1JBfss=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.abi.json</key>
		<data>
		OIyKZwUs8zVioE8HQOB78/abijw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.private.swiftinterface</key>
		<data>
		4gR6vGh9NGCOc8NqbJitBi4PYMc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.swiftdoc</key>
		<data>
		qhoelsD3haNmZEE9NM8xvtK9mEY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.swiftinterface</key>
		<data>
		4gR6vGh9NGCOc8NqbJitBi4PYMc=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.abi.json</key>
		<data>
		OIyKZwUs8zVioE8HQOB78/abijw=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.private.swiftinterface</key>
		<data>
		R1LVVji18GwfW9nhhjO5aFGB1+k=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.swiftdoc</key>
		<data>
		/8RdTbctZKCA1/OIwmZLl2oWnCk=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.swiftinterface</key>
		<data>
		R1LVVji18GwfW9nhhjO5aFGB1+k=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		uLysK0T5K1GSoqJmcXx9AnZ9lqY=
		</data>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Resources/Info.plist</key>
		<data>
		8RUX3m/IgwBKv9KKIWbL+mRQYKw=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<data>
		OtH6T8rge2N1pRsDXQMMqzn/F7s=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<data>
		JxSy4BVIpZB3s+tbI3EgcIVsvN0=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<data>
		IN4riVkldIj3MlPpvlUuU1i+I8w=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<data>
		58LqQ/Y9jqPGz3JttzK0z7jPLLc=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<data>
		B+YLaEHhiOu6tTPZaB5wWy4n95U=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<data>
		HrzLufMtB3E5zAfPXZTkYQdGyHI=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<data>
		SztBbCRZ5QE523pdMx4HZt0AYKE=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<data>
		33ogzLW88kbc3XVXJp9Opq9Znmc=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<data>
		cu0mXcU7RrT9nKAMSamYxQcZ8MA=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<data>
		wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<data>
		6tM+QmAiCFyFHMaFXgWsH/uYosM=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Info.plist</key>
		<data>
		5Bge6neCAIYbZVhWSY/LfWDswDg=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		cmRNV3GWY/miJwMluqgGlpyH04s=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		gvELGHmDeVShTp4Sw2Ehi0iV2vk=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		OIyKZwUs8zVioE8HQOB78/abijw=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		hZUm+6Yon0B4K7L8GD1ScdCY3t0=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		JeXYTVdQMygRSZdbbP8kc/pgjAI=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		hZUm+6Yon0B4K7L8GD1ScdCY3t0=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		OIyKZwUs8zVioE8HQOB78/abijw=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		eO9rriExhXQgyXhchyORV3h91L4=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		mjATLkNPULjHjLv3ARUXQ7doOSY=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		eO9rriExhXQgyXhchyORV3h91L4=
		</data>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<data>
		uLysK0T5K1GSoqJmcXx9AnZ9lqY=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/FirebaseAnalytics</key>
		<data>
		yEyKBxNXcqMLLi5WobKSSqsYXyU=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+AppDelegate.h</key>
		<data>
		JxSy4BVIpZB3s+tbI3EgcIVsvN0=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+Consent.h</key>
		<data>
		IN4riVkldIj3MlPpvlUuU1i+I8w=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+OnDevice.h</key>
		<data>
		58LqQ/Y9jqPGz3JttzK0z7jPLLc=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics.h</key>
		<data>
		B+YLaEHhiOu6tTPZaB5wWy4n95U=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIREventNames.h</key>
		<data>
		HrzLufMtB3E5zAfPXZTkYQdGyHI=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRParameterNames.h</key>
		<data>
		SztBbCRZ5QE523pdMx4HZt0AYKE=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRUserPropertyNames.h</key>
		<data>
		33ogzLW88kbc3XVXJp9Opq9Znmc=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-Swift.h</key>
		<data>
		cu0mXcU7RrT9nKAMSamYxQcZ8MA=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-umbrella.h</key>
		<data>
		sXk7jWhcfsvb8eJb7/NDl2Dy2RQ=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics.h</key>
		<data>
		6tM+QmAiCFyFHMaFXgWsH/uYosM=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo</key>
		<data>
		TwLAZ1XhIXo1LtZoHvkyQ/AqMYk=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo</key>
		<data>
		u836CufTwd0ayLaNU9l9cSIU7f0=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.abi.json</key>
		<data>
		OIyKZwUs8zVioE8HQOB78/abijw=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.private.swiftinterface</key>
		<data>
		ZAKuYvVr4sfCX7usXZP7Kk9oW7k=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.swiftdoc</key>
		<data>
		1zu68JlIn8zDFnfRoKMDRMs6j5I=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.swiftinterface</key>
		<data>
		ZAKuYvVr4sfCX7usXZP7Kk9oW7k=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.abi.json</key>
		<data>
		OIyKZwUs8zVioE8HQOB78/abijw=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.private.swiftinterface</key>
		<data>
		TztLGK3InUf3F4NjuC+lzdaWf50=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.swiftdoc</key>
		<data>
		ByKNCJ6aB6LHj+qNk+ZX3YQ3LWM=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.swiftinterface</key>
		<data>
		TztLGK3InUf3F4NjuC+lzdaWf50=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/module.modulemap</key>
		<data>
		jWZ+azdgXQdH9z56z25NptmWlq4=
		</data>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Resources/Info.plist</key>
		<data>
		XO8ZVD6xxygs8PKKekccN7K1Mm8=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<data>
		kQVeU0AWpYljHPUmmqH2sesHS5Q=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<data>
		JxSy4BVIpZB3s+tbI3EgcIVsvN0=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<data>
		IN4riVkldIj3MlPpvlUuU1i+I8w=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<data>
		58LqQ/Y9jqPGz3JttzK0z7jPLLc=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<data>
		B+YLaEHhiOu6tTPZaB5wWy4n95U=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<data>
		HrzLufMtB3E5zAfPXZTkYQdGyHI=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<data>
		SztBbCRZ5QE523pdMx4HZt0AYKE=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<data>
		33ogzLW88kbc3XVXJp9Opq9Znmc=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<data>
		0nWXABx/HRqcUMkqMxRdJuHxqQU=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<data>
		wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<data>
		6tM+QmAiCFyFHMaFXgWsH/uYosM=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Info.plist</key>
		<data>
		eAO71p2PZKQp0RRknfaMlvCsRpo=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-tvos.swiftsourceinfo</key>
		<data>
		UksYdiig4lIj09vq3L114PcSoso=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.abi.json</key>
		<data>
		OIyKZwUs8zVioE8HQOB78/abijw=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.private.swiftinterface</key>
		<data>
		Yg0lxt4eDVJ4zEDgxRvyl2hwR5Q=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.swiftdoc</key>
		<data>
		ICm3QgbA9GCrU8RrR6izKGRnrLA=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.swiftinterface</key>
		<data>
		Yg0lxt4eDVJ4zEDgxRvyl2hwR5Q=
		</data>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<data>
		uLysK0T5K1GSoqJmcXx9AnZ9lqY=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<data>
		4aJ+MSoC43Mrzc1uYiLAb8hoeTY=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<data>
		JxSy4BVIpZB3s+tbI3EgcIVsvN0=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<data>
		IN4riVkldIj3MlPpvlUuU1i+I8w=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<data>
		58LqQ/Y9jqPGz3JttzK0z7jPLLc=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<data>
		B+YLaEHhiOu6tTPZaB5wWy4n95U=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<data>
		HrzLufMtB3E5zAfPXZTkYQdGyHI=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<data>
		SztBbCRZ5QE523pdMx4HZt0AYKE=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<data>
		33ogzLW88kbc3XVXJp9Opq9Znmc=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<data>
		cu0mXcU7RrT9nKAMSamYxQcZ8MA=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<data>
		wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<data>
		6tM+QmAiCFyFHMaFXgWsH/uYosM=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Info.plist</key>
		<data>
		SDDli5tmSXv7DxwywbAalNO2lvs=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-tvos-simulator.swiftsourceinfo</key>
		<data>
		RN6IMZcVp4IBzOxNjZkG48sz/Zs=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-tvos-simulator.swiftsourceinfo</key>
		<data>
		yuI+a5OwrwKXx46hJattbh49Z4c=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.abi.json</key>
		<data>
		OIyKZwUs8zVioE8HQOB78/abijw=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.private.swiftinterface</key>
		<data>
		+EoKjIoaiJH4HuBxFOAR+N6qm/A=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.swiftdoc</key>
		<data>
		kEpmMJ+EwdoypGxC9M4fHrfAYic=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.swiftinterface</key>
		<data>
		+EoKjIoaiJH4HuBxFOAR+N6qm/A=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.abi.json</key>
		<data>
		OIyKZwUs8zVioE8HQOB78/abijw=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.private.swiftinterface</key>
		<data>
		KthVIak11hjuXvIlm095GJUwL1M=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.swiftdoc</key>
		<data>
		65HbWdHXydClg5RHbdZeeWhc8us=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.swiftinterface</key>
		<data>
		KthVIak11hjuXvIlm095GJUwL1M=
		</data>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<data>
		uLysK0T5K1GSoqJmcXx9AnZ9lqY=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ios-arm64/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<dict>
			<key>hash</key>
			<data>
			plvZyPQu9b6IDPmuktiAI8xiEfo=
			</data>
			<key>hash2</key>
			<data>
			RKScnZNX+stbxagElML/nwHt/rIntd/ObP6K97nyCzU=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			JxSy4BVIpZB3s+tbI3EgcIVsvN0=
			</data>
			<key>hash2</key>
			<data>
			HSPmaeLSu5PqNTtlwUWfRT4cyVX65JmeB/oed8f0pdU=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<dict>
			<key>hash</key>
			<data>
			IN4riVkldIj3MlPpvlUuU1i+I8w=
			</data>
			<key>hash2</key>
			<data>
			ebGwpP2JZ0Rp6BdXhKiLUYOq6nJG7la5O0y/wEKfyak=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			58LqQ/Y9jqPGz3JttzK0z7jPLLc=
			</data>
			<key>hash2</key>
			<data>
			67S/czxwflT8GxF7bVp32FLgu2W97zH06W6zv8/VILU=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			B+YLaEHhiOu6tTPZaB5wWy4n95U=
			</data>
			<key>hash2</key>
			<data>
			wmpxZuP80odiQUX5Ts7GaPkhE7UeuzmDMrvTcOS0vLQ=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			HrzLufMtB3E5zAfPXZTkYQdGyHI=
			</data>
			<key>hash2</key>
			<data>
			mNgPvQJ0O5ZQ/EINmxz+zoQHoxj/O70iH5crkbhGU3g=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			SztBbCRZ5QE523pdMx4HZt0AYKE=
			</data>
			<key>hash2</key>
			<data>
			hwLb6dR4Q5L+j2vYcsXcc8sr2yoS1X9vyl7T/PjcHow=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			33ogzLW88kbc3XVXJp9Opq9Znmc=
			</data>
			<key>hash2</key>
			<data>
			z0lD0Agt0NzOZdG+xd6QpXvGS+06dK4A3RYK0Wu1OKw=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			0nWXABx/HRqcUMkqMxRdJuHxqQU=
			</data>
			<key>hash2</key>
			<data>
			DmmG7v+JGyWpcKQl1BeGd8rqz4HV3b+PToUt+aM45bY=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
			</data>
			<key>hash2</key>
			<data>
			LOiywMHEh60MswGEzs9lM8P6m3oohYp+IWaiZtbwVVM=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			6tM+QmAiCFyFHMaFXgWsH/uYosM=
			</data>
			<key>hash2</key>
			<data>
			VS1dxfDwCZeRcJYgkkEUhmdXx6ch9X/E8YjKBX367WY=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			IddYklCmJJsa7Thme5ifeOf9rv4=
			</data>
			<key>hash2</key>
			<data>
			Frw+X2DLjri8RjNkQhUXmZSwrsd8KOKNkRhSMm26PGM=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			vkVoHhy3kd/rHaHNaPC5tJHZzbQ=
			</data>
			<key>hash2</key>
			<data>
			bon8nr/VzbWDXAs5Ehhxc7lAAsD75HhRQpYgxMtf3aU=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			OIyKZwUs8zVioE8HQOB78/abijw=
			</data>
			<key>hash2</key>
			<data>
			oOpbVxIsFqhvPD+IJYfAhNDsUZ7JWINxaoPnpuJgUmc=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			NAvpCrv1M1YGI6Vin6eL42cx/3s=
			</data>
			<key>hash2</key>
			<data>
			w4kvFgyfxWYtCjCy0BYHS05P0r5XAws8V9Mc4rKyOaQ=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			BHIT/FyHupjfAkf+upImM8fRLqU=
			</data>
			<key>hash2</key>
			<data>
			VD+vnhQT/tzGLf1nYQU+/onj2SvCzxwQtgst449XRlc=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			NAvpCrv1M1YGI6Vin6eL42cx/3s=
			</data>
			<key>hash2</key>
			<data>
			w4kvFgyfxWYtCjCy0BYHS05P0r5XAws8V9Mc4rKyOaQ=
			</data>
		</dict>
		<key>ios-arm64/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			uLysK0T5K1GSoqJmcXx9AnZ9lqY=
			</data>
			<key>hash2</key>
			<data>
			vxNgOuI61t45Sed09vILAKePFm9riTp4aZ48hjDRPIQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/FirebaseAnalytics</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Headers</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Headers</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/FirebaseAnalytics</key>
		<dict>
			<key>hash</key>
			<data>
			mhR3Ug9lUl1yUQTCW4m7YkyAmZg=
			</data>
			<key>hash2</key>
			<data>
			B9xh0PgSO6hLRm7yr7n+VOeIGk7WfJUCrBgXgrICjQE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+AppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			JxSy4BVIpZB3s+tbI3EgcIVsvN0=
			</data>
			<key>hash2</key>
			<data>
			HSPmaeLSu5PqNTtlwUWfRT4cyVX65JmeB/oed8f0pdU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+Consent.h</key>
		<dict>
			<key>hash</key>
			<data>
			IN4riVkldIj3MlPpvlUuU1i+I8w=
			</data>
			<key>hash2</key>
			<data>
			ebGwpP2JZ0Rp6BdXhKiLUYOq6nJG7la5O0y/wEKfyak=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+OnDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			58LqQ/Y9jqPGz3JttzK0z7jPLLc=
			</data>
			<key>hash2</key>
			<data>
			67S/czxwflT8GxF7bVp32FLgu2W97zH06W6zv8/VILU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			B+YLaEHhiOu6tTPZaB5wWy4n95U=
			</data>
			<key>hash2</key>
			<data>
			wmpxZuP80odiQUX5Ts7GaPkhE7UeuzmDMrvTcOS0vLQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIREventNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			HrzLufMtB3E5zAfPXZTkYQdGyHI=
			</data>
			<key>hash2</key>
			<data>
			mNgPvQJ0O5ZQ/EINmxz+zoQHoxj/O70iH5crkbhGU3g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRParameterNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			SztBbCRZ5QE523pdMx4HZt0AYKE=
			</data>
			<key>hash2</key>
			<data>
			hwLb6dR4Q5L+j2vYcsXcc8sr2yoS1X9vyl7T/PjcHow=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FIRUserPropertyNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			33ogzLW88kbc3XVXJp9Opq9Znmc=
			</data>
			<key>hash2</key>
			<data>
			z0lD0Agt0NzOZdG+xd6QpXvGS+06dK4A3RYK0Wu1OKw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			cu0mXcU7RrT9nKAMSamYxQcZ8MA=
			</data>
			<key>hash2</key>
			<data>
			4mnxjtoRMyo5TIjTieqzJLBQ4m0noIZqWhmI5a2a3QE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
			</data>
			<key>hash2</key>
			<data>
			LOiywMHEh60MswGEzs9lM8P6m3oohYp+IWaiZtbwVVM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			6tM+QmAiCFyFHMaFXgWsH/uYosM=
			</data>
			<key>hash2</key>
			<data>
			VS1dxfDwCZeRcJYgkkEUhmdXx6ch9X/E8YjKBX367WY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-ios-macabi.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			mNZgmu3wt/RqzubmpQ07j2eNxwY=
			</data>
			<key>hash2</key>
			<data>
			QCNo0TnT6rvNMmyEBJvCIX7np3yIBZDgdCUMQUAfb/Y=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-ios-macabi.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			mtSWw+snhbHzDlzsJrgUl1JBfss=
			</data>
			<key>hash2</key>
			<data>
			uINeDM2cFcHchCBRpej6VdoOdLE+Ny1W6/1inVSCs2Y=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			OIyKZwUs8zVioE8HQOB78/abijw=
			</data>
			<key>hash2</key>
			<data>
			oOpbVxIsFqhvPD+IJYfAhNDsUZ7JWINxaoPnpuJgUmc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			4gR6vGh9NGCOc8NqbJitBi4PYMc=
			</data>
			<key>hash2</key>
			<data>
			sBsGCsKt/qVMlUX2JkA5hB1ko0klhw7cNGdqgUlwGW4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			qhoelsD3haNmZEE9NM8xvtK9mEY=
			</data>
			<key>hash2</key>
			<data>
			ORG2lGLtqMk9O/g5fSRsgM0vkRuCeApJ2rKBJUOAyQY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-macabi.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			4gR6vGh9NGCOc8NqbJitBi4PYMc=
			</data>
			<key>hash2</key>
			<data>
			sBsGCsKt/qVMlUX2JkA5hB1ko0klhw7cNGdqgUlwGW4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			OIyKZwUs8zVioE8HQOB78/abijw=
			</data>
			<key>hash2</key>
			<data>
			oOpbVxIsFqhvPD+IJYfAhNDsUZ7JWINxaoPnpuJgUmc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			R1LVVji18GwfW9nhhjO5aFGB1+k=
			</data>
			<key>hash2</key>
			<data>
			8sleA8PWyFsmq4UfIdcmC5GkHeUcPvp/kAytxpjR9N0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			/8RdTbctZKCA1/OIwmZLl2oWnCk=
			</data>
			<key>hash2</key>
			<data>
			zgcFlEjAeUx/3gKqZtb0j32k5wK3ScunLgYWrpCZ8YY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-macabi.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			R1LVVji18GwfW9nhhjO5aFGB1+k=
			</data>
			<key>hash2</key>
			<data>
			8sleA8PWyFsmq4UfIdcmC5GkHeUcPvp/kAytxpjR9N0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			uLysK0T5K1GSoqJmcXx9AnZ9lqY=
			</data>
			<key>hash2</key>
			<data>
			vxNgOuI61t45Sed09vILAKePFm9riTp4aZ48hjDRPIQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			8RUX3m/IgwBKv9KKIWbL+mRQYKw=
			</data>
			<key>hash2</key>
			<data>
			RUtAV8HvzBUyztPZxL/PPj5JymOYSNb5BAwSdo0RuPQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-maccatalyst/FirebaseAnalytics.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<dict>
			<key>hash</key>
			<data>
			OtH6T8rge2N1pRsDXQMMqzn/F7s=
			</data>
			<key>hash2</key>
			<data>
			5oSUn67iJ82iFmREfMS1X9qy5ZiRuy/LSQo7AcJl1KM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			JxSy4BVIpZB3s+tbI3EgcIVsvN0=
			</data>
			<key>hash2</key>
			<data>
			HSPmaeLSu5PqNTtlwUWfRT4cyVX65JmeB/oed8f0pdU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<dict>
			<key>hash</key>
			<data>
			IN4riVkldIj3MlPpvlUuU1i+I8w=
			</data>
			<key>hash2</key>
			<data>
			ebGwpP2JZ0Rp6BdXhKiLUYOq6nJG7la5O0y/wEKfyak=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			58LqQ/Y9jqPGz3JttzK0z7jPLLc=
			</data>
			<key>hash2</key>
			<data>
			67S/czxwflT8GxF7bVp32FLgu2W97zH06W6zv8/VILU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			B+YLaEHhiOu6tTPZaB5wWy4n95U=
			</data>
			<key>hash2</key>
			<data>
			wmpxZuP80odiQUX5Ts7GaPkhE7UeuzmDMrvTcOS0vLQ=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			HrzLufMtB3E5zAfPXZTkYQdGyHI=
			</data>
			<key>hash2</key>
			<data>
			mNgPvQJ0O5ZQ/EINmxz+zoQHoxj/O70iH5crkbhGU3g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			SztBbCRZ5QE523pdMx4HZt0AYKE=
			</data>
			<key>hash2</key>
			<data>
			hwLb6dR4Q5L+j2vYcsXcc8sr2yoS1X9vyl7T/PjcHow=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			33ogzLW88kbc3XVXJp9Opq9Znmc=
			</data>
			<key>hash2</key>
			<data>
			z0lD0Agt0NzOZdG+xd6QpXvGS+06dK4A3RYK0Wu1OKw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			cu0mXcU7RrT9nKAMSamYxQcZ8MA=
			</data>
			<key>hash2</key>
			<data>
			4mnxjtoRMyo5TIjTieqzJLBQ4m0noIZqWhmI5a2a3QE=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
			</data>
			<key>hash2</key>
			<data>
			LOiywMHEh60MswGEzs9lM8P6m3oohYp+IWaiZtbwVVM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			6tM+QmAiCFyFHMaFXgWsH/uYosM=
			</data>
			<key>hash2</key>
			<data>
			VS1dxfDwCZeRcJYgkkEUhmdXx6ch9X/E8YjKBX367WY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			5Bge6neCAIYbZVhWSY/LfWDswDg=
			</data>
			<key>hash2</key>
			<data>
			bLtLJsNLgJCqojAYx6/kvxJXAbcvIZjFphYTKJVfeFU=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			cmRNV3GWY/miJwMluqgGlpyH04s=
			</data>
			<key>hash2</key>
			<data>
			Yw26mVeSHdOnMF7KRXzKYx4iBJhztmHQMBdX0nWndUg=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			gvELGHmDeVShTp4Sw2Ehi0iV2vk=
			</data>
			<key>hash2</key>
			<data>
			IXTFX1dNgCKuHo5VN1bVzBQLDI3X1xjp6ZzM/phbbb0=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			OIyKZwUs8zVioE8HQOB78/abijw=
			</data>
			<key>hash2</key>
			<data>
			oOpbVxIsFqhvPD+IJYfAhNDsUZ7JWINxaoPnpuJgUmc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			hZUm+6Yon0B4K7L8GD1ScdCY3t0=
			</data>
			<key>hash2</key>
			<data>
			QzIaVFFMO8tYIzl4o3nbVBhW2fuNtgOmkSNeDMOfOfA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			JeXYTVdQMygRSZdbbP8kc/pgjAI=
			</data>
			<key>hash2</key>
			<data>
			R2Pt3A/l7SQ4n2I93xw+Ffz7yGbF0Q7HJd4fqxMgV10=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			hZUm+6Yon0B4K7L8GD1ScdCY3t0=
			</data>
			<key>hash2</key>
			<data>
			QzIaVFFMO8tYIzl4o3nbVBhW2fuNtgOmkSNeDMOfOfA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			OIyKZwUs8zVioE8HQOB78/abijw=
			</data>
			<key>hash2</key>
			<data>
			oOpbVxIsFqhvPD+IJYfAhNDsUZ7JWINxaoPnpuJgUmc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			eO9rriExhXQgyXhchyORV3h91L4=
			</data>
			<key>hash2</key>
			<data>
			lt9Vh+N0wzNo3JZuavlytTxaN9X/rwKiQlcn7Xt6DU4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			mjATLkNPULjHjLv3ARUXQ7doOSY=
			</data>
			<key>hash2</key>
			<data>
			gcmxRgiXlWBZMSGlxd3/aPJfzJWomCwGizfkHXKvofA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			eO9rriExhXQgyXhchyORV3h91L4=
			</data>
			<key>hash2</key>
			<data>
			lt9Vh+N0wzNo3JZuavlytTxaN9X/rwKiQlcn7Xt6DU4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			uLysK0T5K1GSoqJmcXx9AnZ9lqY=
			</data>
			<key>hash2</key>
			<data>
			vxNgOuI61t45Sed09vILAKePFm9riTp4aZ48hjDRPIQ=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/FirebaseAnalytics</string>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Headers</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Headers</string>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Modules</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Modules</string>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Resources</key>
		<dict>
			<key>symlink</key>
			<string>Versions/Current/Resources</string>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/FirebaseAnalytics</key>
		<dict>
			<key>hash</key>
			<data>
			yEyKBxNXcqMLLi5WobKSSqsYXyU=
			</data>
			<key>hash2</key>
			<data>
			3TidqTkK2nsDZCZxbWFa953kxb6dCUao2Q3e9opN6eY=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+AppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			JxSy4BVIpZB3s+tbI3EgcIVsvN0=
			</data>
			<key>hash2</key>
			<data>
			HSPmaeLSu5PqNTtlwUWfRT4cyVX65JmeB/oed8f0pdU=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+Consent.h</key>
		<dict>
			<key>hash</key>
			<data>
			IN4riVkldIj3MlPpvlUuU1i+I8w=
			</data>
			<key>hash2</key>
			<data>
			ebGwpP2JZ0Rp6BdXhKiLUYOq6nJG7la5O0y/wEKfyak=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics+OnDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			58LqQ/Y9jqPGz3JttzK0z7jPLLc=
			</data>
			<key>hash2</key>
			<data>
			67S/czxwflT8GxF7bVp32FLgu2W97zH06W6zv8/VILU=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			B+YLaEHhiOu6tTPZaB5wWy4n95U=
			</data>
			<key>hash2</key>
			<data>
			wmpxZuP80odiQUX5Ts7GaPkhE7UeuzmDMrvTcOS0vLQ=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIREventNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			HrzLufMtB3E5zAfPXZTkYQdGyHI=
			</data>
			<key>hash2</key>
			<data>
			mNgPvQJ0O5ZQ/EINmxz+zoQHoxj/O70iH5crkbhGU3g=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRParameterNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			SztBbCRZ5QE523pdMx4HZt0AYKE=
			</data>
			<key>hash2</key>
			<data>
			hwLb6dR4Q5L+j2vYcsXcc8sr2yoS1X9vyl7T/PjcHow=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FIRUserPropertyNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			33ogzLW88kbc3XVXJp9Opq9Znmc=
			</data>
			<key>hash2</key>
			<data>
			z0lD0Agt0NzOZdG+xd6QpXvGS+06dK4A3RYK0Wu1OKw=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			cu0mXcU7RrT9nKAMSamYxQcZ8MA=
			</data>
			<key>hash2</key>
			<data>
			4mnxjtoRMyo5TIjTieqzJLBQ4m0noIZqWhmI5a2a3QE=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			sXk7jWhcfsvb8eJb7/NDl2Dy2RQ=
			</data>
			<key>hash2</key>
			<data>
			E6EdPyZp5cQbOObE0CPO9/R+QvTIYlDIOHvuzam/9H8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Headers/FirebaseAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			6tM+QmAiCFyFHMaFXgWsH/uYosM=
			</data>
			<key>hash2</key>
			<data>
			VS1dxfDwCZeRcJYgkkEUhmdXx6ch9X/E8YjKBX367WY=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-macos.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			TwLAZ1XhIXo1LtZoHvkyQ/AqMYk=
			</data>
			<key>hash2</key>
			<data>
			XWIjo4B0mWjxlex7amdciBVzZyw4OjL9fW2cffCBmYA=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-macos.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			u836CufTwd0ayLaNU9l9cSIU7f0=
			</data>
			<key>hash2</key>
			<data>
			jP/lR2FqJFo5szcNXTgrKw05DAD8WDS6tJwVfx8QcHY=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			OIyKZwUs8zVioE8HQOB78/abijw=
			</data>
			<key>hash2</key>
			<data>
			oOpbVxIsFqhvPD+IJYfAhNDsUZ7JWINxaoPnpuJgUmc=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			ZAKuYvVr4sfCX7usXZP7Kk9oW7k=
			</data>
			<key>hash2</key>
			<data>
			65tpMWBKS1d0GpEhBByvoQ0+KStlRWp8GF8K7dzViE8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			1zu68JlIn8zDFnfRoKMDRMs6j5I=
			</data>
			<key>hash2</key>
			<data>
			5q8dF0bgCDYfD0KnwHIw+wuj/GogPeqMVp2cx9uulKg=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-macos.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			ZAKuYvVr4sfCX7usXZP7Kk9oW7k=
			</data>
			<key>hash2</key>
			<data>
			65tpMWBKS1d0GpEhBByvoQ0+KStlRWp8GF8K7dzViE8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			OIyKZwUs8zVioE8HQOB78/abijw=
			</data>
			<key>hash2</key>
			<data>
			oOpbVxIsFqhvPD+IJYfAhNDsUZ7JWINxaoPnpuJgUmc=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			TztLGK3InUf3F4NjuC+lzdaWf50=
			</data>
			<key>hash2</key>
			<data>
			nSnXCeosOPuMgd2b41zKfQBUM22ZloRdAj9hyRYK8wk=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			ByKNCJ6aB6LHj+qNk+ZX3YQ3LWM=
			</data>
			<key>hash2</key>
			<data>
			7L1iy8hHgpzEAPYgxmkdsz/w6JO8FzKST9eY6Hz9nQU=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-macos.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			TztLGK3InUf3F4NjuC+lzdaWf50=
			</data>
			<key>hash2</key>
			<data>
			nSnXCeosOPuMgd2b41zKfQBUM22ZloRdAj9hyRYK8wk=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			jWZ+azdgXQdH9z56z25NptmWlq4=
			</data>
			<key>hash2</key>
			<data>
			e4a41Axjw7BAywMms/GuOcqrdwPIVYuMRsPznqQS0X8=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/A/Resources/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			XO8ZVD6xxygs8PKKekccN7K1Mm8=
			</data>
			<key>hash2</key>
			<data>
			FVQbqinZ2QYs83wrBVWxL+k6y4nJtcSPonBdRHe1icY=
			</data>
		</dict>
		<key>macos-arm64_x86_64/FirebaseAnalytics.framework/Versions/Current</key>
		<dict>
			<key>symlink</key>
			<string>A</string>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<dict>
			<key>hash</key>
			<data>
			kQVeU0AWpYljHPUmmqH2sesHS5Q=
			</data>
			<key>hash2</key>
			<data>
			ruLoqjQcri5U9V/Fbo9DEgKdDVMFgfK5FV+yeuIu2/0=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			JxSy4BVIpZB3s+tbI3EgcIVsvN0=
			</data>
			<key>hash2</key>
			<data>
			HSPmaeLSu5PqNTtlwUWfRT4cyVX65JmeB/oed8f0pdU=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<dict>
			<key>hash</key>
			<data>
			IN4riVkldIj3MlPpvlUuU1i+I8w=
			</data>
			<key>hash2</key>
			<data>
			ebGwpP2JZ0Rp6BdXhKiLUYOq6nJG7la5O0y/wEKfyak=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			58LqQ/Y9jqPGz3JttzK0z7jPLLc=
			</data>
			<key>hash2</key>
			<data>
			67S/czxwflT8GxF7bVp32FLgu2W97zH06W6zv8/VILU=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			B+YLaEHhiOu6tTPZaB5wWy4n95U=
			</data>
			<key>hash2</key>
			<data>
			wmpxZuP80odiQUX5Ts7GaPkhE7UeuzmDMrvTcOS0vLQ=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			HrzLufMtB3E5zAfPXZTkYQdGyHI=
			</data>
			<key>hash2</key>
			<data>
			mNgPvQJ0O5ZQ/EINmxz+zoQHoxj/O70iH5crkbhGU3g=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			SztBbCRZ5QE523pdMx4HZt0AYKE=
			</data>
			<key>hash2</key>
			<data>
			hwLb6dR4Q5L+j2vYcsXcc8sr2yoS1X9vyl7T/PjcHow=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			33ogzLW88kbc3XVXJp9Opq9Znmc=
			</data>
			<key>hash2</key>
			<data>
			z0lD0Agt0NzOZdG+xd6QpXvGS+06dK4A3RYK0Wu1OKw=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			0nWXABx/HRqcUMkqMxRdJuHxqQU=
			</data>
			<key>hash2</key>
			<data>
			DmmG7v+JGyWpcKQl1BeGd8rqz4HV3b+PToUt+aM45bY=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
			</data>
			<key>hash2</key>
			<data>
			LOiywMHEh60MswGEzs9lM8P6m3oohYp+IWaiZtbwVVM=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			6tM+QmAiCFyFHMaFXgWsH/uYosM=
			</data>
			<key>hash2</key>
			<data>
			VS1dxfDwCZeRcJYgkkEUhmdXx6ch9X/E8YjKBX367WY=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			eAO71p2PZKQp0RRknfaMlvCsRpo=
			</data>
			<key>hash2</key>
			<data>
			AlojXQunXaAElTvRDg8RzXSsdtOqpYxXQMbGs8u36L4=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-tvos.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			UksYdiig4lIj09vq3L114PcSoso=
			</data>
			<key>hash2</key>
			<data>
			6an+mprih+MHnw6BiNxOBGzbT5o2ONxp2CU+j1ecwug=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			OIyKZwUs8zVioE8HQOB78/abijw=
			</data>
			<key>hash2</key>
			<data>
			oOpbVxIsFqhvPD+IJYfAhNDsUZ7JWINxaoPnpuJgUmc=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			Yg0lxt4eDVJ4zEDgxRvyl2hwR5Q=
			</data>
			<key>hash2</key>
			<data>
			EsiBmwD4KG8UOQJ9SEWfB/O1mnTXHmVIEyAeH2JVApE=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			ICm3QgbA9GCrU8RrR6izKGRnrLA=
			</data>
			<key>hash2</key>
			<data>
			Wd9mR8J/cQBEqDuyZ1x32/TWxOvg35osz6P9hEE4nck=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			Yg0lxt4eDVJ4zEDgxRvyl2hwR5Q=
			</data>
			<key>hash2</key>
			<data>
			EsiBmwD4KG8UOQJ9SEWfB/O1mnTXHmVIEyAeH2JVApE=
			</data>
		</dict>
		<key>tvos-arm64/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			uLysK0T5K1GSoqJmcXx9AnZ9lqY=
			</data>
			<key>hash2</key>
			<data>
			vxNgOuI61t45Sed09vILAKePFm9riTp4aZ48hjDRPIQ=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/FirebaseAnalytics</key>
		<dict>
			<key>hash</key>
			<data>
			4aJ+MSoC43Mrzc1uYiLAb8hoeTY=
			</data>
			<key>hash2</key>
			<data>
			11GOBNjS9s48oLUAMpasG/Ti4k/k6fi7LlfW41ePQQs=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+AppDelegate.h</key>
		<dict>
			<key>hash</key>
			<data>
			JxSy4BVIpZB3s+tbI3EgcIVsvN0=
			</data>
			<key>hash2</key>
			<data>
			HSPmaeLSu5PqNTtlwUWfRT4cyVX65JmeB/oed8f0pdU=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+Consent.h</key>
		<dict>
			<key>hash</key>
			<data>
			IN4riVkldIj3MlPpvlUuU1i+I8w=
			</data>
			<key>hash2</key>
			<data>
			ebGwpP2JZ0Rp6BdXhKiLUYOq6nJG7la5O0y/wEKfyak=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics+OnDevice.h</key>
		<dict>
			<key>hash</key>
			<data>
			58LqQ/Y9jqPGz3JttzK0z7jPLLc=
			</data>
			<key>hash2</key>
			<data>
			67S/czxwflT8GxF7bVp32FLgu2W97zH06W6zv8/VILU=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			B+YLaEHhiOu6tTPZaB5wWy4n95U=
			</data>
			<key>hash2</key>
			<data>
			wmpxZuP80odiQUX5Ts7GaPkhE7UeuzmDMrvTcOS0vLQ=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIREventNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			HrzLufMtB3E5zAfPXZTkYQdGyHI=
			</data>
			<key>hash2</key>
			<data>
			mNgPvQJ0O5ZQ/EINmxz+zoQHoxj/O70iH5crkbhGU3g=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRParameterNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			SztBbCRZ5QE523pdMx4HZt0AYKE=
			</data>
			<key>hash2</key>
			<data>
			hwLb6dR4Q5L+j2vYcsXcc8sr2yoS1X9vyl7T/PjcHow=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FIRUserPropertyNames.h</key>
		<dict>
			<key>hash</key>
			<data>
			33ogzLW88kbc3XVXJp9Opq9Znmc=
			</data>
			<key>hash2</key>
			<data>
			z0lD0Agt0NzOZdG+xd6QpXvGS+06dK4A3RYK0Wu1OKw=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			cu0mXcU7RrT9nKAMSamYxQcZ8MA=
			</data>
			<key>hash2</key>
			<data>
			4mnxjtoRMyo5TIjTieqzJLBQ4m0noIZqWhmI5a2a3QE=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics-umbrella.h</key>
		<dict>
			<key>hash</key>
			<data>
			wE8Bjq1o5wGq1TFJb0gCJUmRelQ=
			</data>
			<key>hash2</key>
			<data>
			LOiywMHEh60MswGEzs9lM8P6m3oohYp+IWaiZtbwVVM=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Headers/FirebaseAnalytics.h</key>
		<dict>
			<key>hash</key>
			<data>
			6tM+QmAiCFyFHMaFXgWsH/uYosM=
			</data>
			<key>hash2</key>
			<data>
			VS1dxfDwCZeRcJYgkkEUhmdXx6ch9X/E8YjKBX367WY=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			SDDli5tmSXv7DxwywbAalNO2lvs=
			</data>
			<key>hash2</key>
			<data>
			14e4GDBp8kzO15zYVAeOpSf8F52ry76UynsJmieb2cw=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/arm64-apple-tvos-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			RN6IMZcVp4IBzOxNjZkG48sz/Zs=
			</data>
			<key>hash2</key>
			<data>
			yDVr4JVYWFL0nKGp/8c80f8Rw/n7XeZ89M0gE78ZCaE=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/Project/x86_64-apple-tvos-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			yuI+a5OwrwKXx46hJattbh49Z4c=
			</data>
			<key>hash2</key>
			<data>
			MgDH/a5X1BnsLhQ8X7m7llqq6w6OpkKtDB0ALDV3iFg=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			OIyKZwUs8zVioE8HQOB78/abijw=
			</data>
			<key>hash2</key>
			<data>
			oOpbVxIsFqhvPD+IJYfAhNDsUZ7JWINxaoPnpuJgUmc=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			+EoKjIoaiJH4HuBxFOAR+N6qm/A=
			</data>
			<key>hash2</key>
			<data>
			DoD1OSJHno0bNy0osPn/laVPoBAxHGWB789dQSNEMkI=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			kEpmMJ+EwdoypGxC9M4fHrfAYic=
			</data>
			<key>hash2</key>
			<data>
			kgbprKMKWtu9nlXnOTVjHVY0TAgVDbK5pK/G+QpaymE=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/arm64-apple-tvos-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			+EoKjIoaiJH4HuBxFOAR+N6qm/A=
			</data>
			<key>hash2</key>
			<data>
			DoD1OSJHno0bNy0osPn/laVPoBAxHGWB789dQSNEMkI=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			OIyKZwUs8zVioE8HQOB78/abijw=
			</data>
			<key>hash2</key>
			<data>
			oOpbVxIsFqhvPD+IJYfAhNDsUZ7JWINxaoPnpuJgUmc=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			KthVIak11hjuXvIlm095GJUwL1M=
			</data>
			<key>hash2</key>
			<data>
			MwVZoM3SRxYLhYEsI316k3d6aaaJ31Xne1MXyoX0sFs=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			65HbWdHXydClg5RHbdZeeWhc8us=
			</data>
			<key>hash2</key>
			<data>
			HXWYfxPhG1Lkni6+8MhUCQGNPmfg72yH2N6CVQbWuuI=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/FirebaseAnalytics.swiftmodule/x86_64-apple-tvos-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			KthVIak11hjuXvIlm095GJUwL1M=
			</data>
			<key>hash2</key>
			<data>
			MwVZoM3SRxYLhYEsI316k3d6aaaJ31Xne1MXyoX0sFs=
			</data>
		</dict>
		<key>tvos-arm64_x86_64-simulator/FirebaseAnalytics.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			uLysK0T5K1GSoqJmcXx9AnZ9lqY=
			</data>
			<key>hash2</key>
			<data>
			vxNgOuI61t45Sed09vILAKePFm9riTp4aZ48hjDRPIQ=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
