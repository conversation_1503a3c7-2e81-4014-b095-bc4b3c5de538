import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'analytics_service.dart';
import '../config/api_config.dart';

/// Service for tracking user retention metrics and engagement patterns
class RetentionAnalyticsService {
  static const String _keyFirstLaunch = 'first_launch_date';
  static const String _keyLastLaunch = 'last_launch_date';
  static const String _keyLaunchCount = 'launch_count';
  static const String _keyDailyActiveUser = 'daily_active_user';
  static const String _keyWeeklyActiveUser = 'weekly_active_user';
  static const String _keyMonthlyActiveUser = 'monthly_active_user';
  static const String _keyConsecutiveDays = 'consecutive_days';
  static const String _keyLastActiveDate = 'last_active_date';

  static bool _isInitialized = false;

  /// Initialize retention tracking
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now();
      final today = _getDateString(now);

      // Track first launch
      if (!prefs.containsKey(_keyFirstLaunch)) {
        await prefs.setString(_keyFirstLaunch, today);
        await _trackRetentionEvent('first_launch', {
          'first_launch_date': today,
        });
      }

      // Update launch count
      final launchCount = prefs.getInt(_keyLaunchCount) ?? 0;
      await prefs.setInt(_keyLaunchCount, launchCount + 1);

      // Track daily active user
      await _trackDailyActiveUser(prefs, today);

      // Track weekly active user
      await _trackWeeklyActiveUser(prefs, now);

      // Track monthly active user
      await _trackMonthlyActiveUser(prefs, now);

      // Track consecutive days
      await _trackConsecutiveDays(prefs, today);

      // Update last launch date
      await prefs.setString(_keyLastLaunch, today);

      _isInitialized = true;

      if (ApiConfig.enableLogging) {
        debugPrint('📊 Retention analytics initialized');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to initialize retention analytics: $e');
      }
    }
  }

  /// Track daily active user
  static Future<void> _trackDailyActiveUser(SharedPreferences prefs, String today) async {
    final lastDailyActive = prefs.getString(_keyDailyActiveUser);
    
    if (lastDailyActive != today) {
      await prefs.setString(_keyDailyActiveUser, today);
      await _trackRetentionEvent('daily_active_user', {
        'date': today,
        'is_returning_user': lastDailyActive != null,
      });
    }
  }

  /// Track weekly active user
  static Future<void> _trackWeeklyActiveUser(SharedPreferences prefs, DateTime now) async {
    final weekStart = _getWeekStart(now);
    final weekKey = '${_keyWeeklyActiveUser}_$weekStart';
    
    if (!prefs.containsKey(weekKey)) {
      await prefs.setBool(weekKey, true);
      await _trackRetentionEvent('weekly_active_user', {
        'week_start': weekStart,
        'year': now.year,
        'week_number': _getWeekNumber(now),
      });
    }
  }

  /// Track monthly active user
  static Future<void> _trackMonthlyActiveUser(SharedPreferences prefs, DateTime now) async {
    final monthKey = '${_keyMonthlyActiveUser}_${now.year}_${now.month}';
    
    if (!prefs.containsKey(monthKey)) {
      await prefs.setBool(monthKey, true);
      await _trackRetentionEvent('monthly_active_user', {
        'year': now.year,
        'month': now.month,
        'month_name': _getMonthName(now.month),
      });
    }
  }

  /// Track consecutive days of usage
  static Future<void> _trackConsecutiveDays(SharedPreferences prefs, String today) async {
    final lastActiveDate = prefs.getString(_keyLastActiveDate);
    final consecutiveDays = prefs.getInt(_keyConsecutiveDays) ?? 0;
    
    if (lastActiveDate == null) {
      // First time user
      await prefs.setInt(_keyConsecutiveDays, 1);
    } else {
      final lastDate = DateTime.parse(lastActiveDate);
      final todayDate = DateTime.parse(today);
      final daysDifference = todayDate.difference(lastDate).inDays;
      
      if (daysDifference == 1) {
        // Consecutive day
        final newConsecutiveDays = consecutiveDays + 1;
        await prefs.setInt(_keyConsecutiveDays, newConsecutiveDays);
        
        // Track streak milestones
        if (newConsecutiveDays % 7 == 0) {
          await _trackRetentionEvent('streak_milestone', {
            'consecutive_days': newConsecutiveDays,
            'milestone_type': 'weekly',
          });
        } else if (newConsecutiveDays % 30 == 0) {
          await _trackRetentionEvent('streak_milestone', {
            'consecutive_days': newConsecutiveDays,
            'milestone_type': 'monthly',
          });
        }
      } else if (daysDifference > 1) {
        // Streak broken
        await _trackRetentionEvent('streak_broken', {
          'previous_streak': consecutiveDays,
          'days_away': daysDifference,
        });
        await prefs.setInt(_keyConsecutiveDays, 1);
      }
      // If daysDifference == 0, it's the same day, no change needed
    }
    
    await prefs.setString(_keyLastActiveDate, today);
  }

  /// Track user churn risk
  static Future<void> trackChurnRisk() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastLaunch = prefs.getString(_keyLastLaunch);
      
      if (lastLaunch != null) {
        final lastLaunchDate = DateTime.parse(lastLaunch);
        final daysSinceLastLaunch = DateTime.now().difference(lastLaunchDate).inDays;
        
        String riskLevel = 'low';
        if (daysSinceLastLaunch >= 7) {
          riskLevel = 'high';
        } else if (daysSinceLastLaunch >= 3) {
          riskLevel = 'medium';
        }
        
        await _trackRetentionEvent('churn_risk_assessment', {
          'days_since_last_launch': daysSinceLastLaunch,
          'risk_level': riskLevel,
        });
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to track churn risk: $e');
      }
    }
  }

  /// Get user retention statistics
  static Future<Map<String, dynamic>> getRetentionStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now();
      
      final firstLaunch = prefs.getString(_keyFirstLaunch);
      final launchCount = prefs.getInt(_keyLaunchCount) ?? 0;
      final consecutiveDays = prefs.getInt(_keyConsecutiveDays) ?? 0;
      
      int daysSinceFirstLaunch = 0;
      if (firstLaunch != null) {
        final firstLaunchDate = DateTime.parse(firstLaunch);
        daysSinceFirstLaunch = now.difference(firstLaunchDate).inDays;
      }
      
      return {
        'days_since_first_launch': daysSinceFirstLaunch,
        'total_launches': launchCount,
        'consecutive_days': consecutiveDays,
        'average_launches_per_day': daysSinceFirstLaunch > 0 ? launchCount / daysSinceFirstLaunch : 0,
        'is_new_user': daysSinceFirstLaunch <= 7,
        'is_power_user': consecutiveDays >= 7 && launchCount >= 20,
      };
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Failed to get retention stats: $e');
      }
      return {};
    }
  }

  /// Track retention-related events
  static Future<void> _trackRetentionEvent(String eventName, Map<String, dynamic> parameters) async {
    await AnalyticsService.trackFeatureUsed(
      featureName: 'retention_$eventName',
      parameters: parameters,
    );
  }

  /// Helper methods
  static String _getDateString(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  static String _getWeekStart(DateTime date) {
    final weekday = date.weekday;
    final weekStart = date.subtract(Duration(days: weekday - 1));
    return _getDateString(weekStart);
  }

  static int _getWeekNumber(DateTime date) {
    final startOfYear = DateTime(date.year, 1, 1);
    final daysSinceStartOfYear = date.difference(startOfYear).inDays;
    return (daysSinceStartOfYear / 7).ceil();
  }

  static String _getMonthName(int month) {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[month - 1];
  }
}
