import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:partykidsapp/screens/auth/login_screen.dart';
import 'package:partykidsapp/widgets/social_login_buttons.dart';
import 'package:partykidsapp/providers/auth_provider.dart';
import 'package:provider/provider.dart';

void main() {
  group('Authentication UI Cleanup Tests', () {
    group('Login Screen UI Elements', () {
      testWidgets('should not display "Autentificare admin" label', (WidgetTester tester) async {
        // Create the login screen with necessary providers
        await tester.pumpWidget(
          MaterialApp(
            home: ChangeNotifierProvider(
              create: (_) => AuthProvider(),
              child: const LoginScreen(),
            ),
          ),
        );

        // Look for the problematic admin label
        final adminLabelFinder = find.text('Autentificare admin');
        
        expect(adminLabelFinder, findsNothing,
          reason: 'Admin authentication label should be removed from login screen');
      });

      testWidgets('should not display Facebook login button', (WidgetTester tester) async {
        // Create the social login buttons widget
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChangeNotifierProvider(
                create: (_) => AuthProvider(),
                child: const SocialLoginButtons(),
              ),
            ),
          ),
        );

        // Look for Facebook-related text
        final facebookButtonFinder = find.text('Continue with Facebook');
        final facebookTextFinder = find.textContaining('Facebook');
        
        expect(facebookButtonFinder, findsNothing,
          reason: 'Facebook login button should be removed as it is non-functional');
        expect(facebookTextFinder, findsNothing,
          reason: 'No Facebook-related text should be present');
      });

      testWidgets('should display Google and Phone login options only', (WidgetTester tester) async {
        // Create the social login buttons widget
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChangeNotifierProvider(
                create: (_) => AuthProvider(),
                child: const SocialLoginButtons(),
              ),
            ),
          ),
        );

        // Should find Google login
        final googleButtonFinder = find.text('Continue with Google');
        expect(googleButtonFinder, findsOneWidget,
          reason: 'Google login should be available');

        // Should find Phone login
        final phoneButtonFinder = find.text('Continue with Phone');
        expect(phoneButtonFinder, findsOneWidget,
          reason: 'Phone login should be available');

        // Should NOT find Facebook login
        final facebookButtonFinder = find.text('Continue with Facebook');
        expect(facebookButtonFinder, findsNothing,
          reason: 'Facebook login should be removed');
      });

      testWidgets('should have proper Romanian labels for authentication', (WidgetTester tester) async {
        // Create the login screen
        await tester.pumpWidget(
          MaterialApp(
            home: ChangeNotifierProvider(
              create: (_) => AuthProvider(),
              child: const LoginScreen(),
            ),
          ),
        );

        // Should have appropriate Romanian text instead of admin label
        final chooseMethodFinder = find.text('Alegeți o metodă de autentificare');
        expect(chooseMethodFinder, findsOneWidget,
          reason: 'Should have user-friendly Romanian sign in instruction');

        // Should NOT have admin-specific text
        final adminFinder = find.textContaining('admin');
        expect(adminFinder, findsNothing,
          reason: 'Should not contain admin-specific text');
      });
    });

    group('Google Login Error Handling', () {
      testWidgets('should handle Google login cancellation gracefully', (WidgetTester tester) async {
        // This test verifies that when Google login is cancelled,
        // the user gets appropriate feedback instead of silent failure
        
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChangeNotifierProvider(
                create: (_) => AuthProvider(),
                child: const SocialLoginButtons(),
              ),
            ),
          ),
        );

        // Find and tap Google login button
        final googleButton = find.text('Continue with Google');
        expect(googleButton, findsOneWidget);

        // Note: We can't easily test the actual Google sign-in flow in unit tests
        // as it requires real Google services. This test mainly verifies the UI exists.
        // The actual error handling would be tested in integration tests.
      });
    });

    group('UI Consistency Tests', () {
      testWidgets('should have consistent button styling', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChangeNotifierProvider(
                create: (_) => AuthProvider(),
                child: const SocialLoginButtons(),
              ),
            ),
          ),
        );

        // Find all ElevatedButton widgets
        final buttonFinder = find.byType(ElevatedButton);
        
        // Should have login buttons (Google, Phone, and possibly others)
        expect(buttonFinder, findsWidgets,
          reason: 'Should have login buttons available');
      });

      testWidgets('should maintain proper spacing between login options', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: ChangeNotifierProvider(
                create: (_) => AuthProvider(),
                child: const SocialLoginButtons(),
              ),
            ),
          ),
        );

        // Find SizedBox widgets that provide spacing
        final spacingFinder = find.byType(SizedBox);
        
        // Should have proper spacing elements
        expect(spacingFinder, findsWidgets,
          reason: 'Should have proper spacing between login options');
      });
    });
  });
}
