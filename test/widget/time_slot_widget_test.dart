import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:partykidsapp/widgets/calendar_views/time_slot.dart';

void main() {
  testWidgets('TimeSlot calls long press callback',
      (WidgetTester tester) async {
    bool pressed = false;
    await tester.pumpWidget(
      MaterialApp(
        home: Material(
          child: TimeSlot(
            dateTime: DateTime.now(),
            isBusinessHour: true,
            isLunchBreak: false,
            isAvailable: true,
            isGreyedOut: false,
            onLongPress: () => pressed = true,
          ),
        ),
      ),
    );

    await tester.longPress(find.byType(TimeSlot));
    expect(pressed, isTrue);
  });
}
