import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';

class NetworkTest {
  // Test basic connectivity to the mock server
  static Future<bool> testServerConnection() async {
    try {
      debugPrint('🔍 Testing connection to: ${ApiConfig.baseUrl}');

      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/api/settings/salon'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        debugPrint('✅ Server connection successful!');
        debugPrint('📊 Response: ${response.statusCode}');
        return true;
      } else {
        debugPrint('⚠️ Server responded with status: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      debugPrint('❌ Connection failed: $e');
      return false;
    }
  }

  // Test all main endpoints
  static Future<Map<String, bool>> testAllEndpoints() async {
    final results = <String, bool>{};

    final endpoints = [
      '/api/settings/salon',
      '/api/clients',
      '/api/services',
      '/api/groomers',
      '/api/appointments',
      '/api/notifications',
      '/api/reports/dashboard',
    ];

    debugPrint('\n🧪 Testing all endpoints...');

    for (final endpoint in endpoints) {
      try {
        final response = await http.get(
          Uri.parse('${ApiConfig.baseUrl}$endpoint'),
          headers: {'Content-Type': 'application/json'},
        ).timeout(const Duration(seconds: 5));

        final success = response.statusCode >= 200 && response.statusCode < 300;
        results[endpoint] = success;

        debugPrint('${success ? '✅' : '❌'} $endpoint: ${response.statusCode}');
      } catch (e) {
        results[endpoint] = false;
        debugPrint('❌ $endpoint: Failed ($e)');
      }
    }

    return results;
  }

  // Test authentication endpoint
  static Future<bool> testAuthentication() async {
    try {
      debugPrint('\n🔐 Testing authentication...');

      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/api/auth/login'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': '<EMAIL>',
          'password': 'password123',
        }),
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['success'] == true) {
          debugPrint('✅ Authentication test successful!');
          debugPrint('🔑 Token received: ${data['data']['token']?.substring(0, 20)}...');
          return true;
        }
      }

      debugPrint('⚠️ Authentication test failed: ${response.statusCode}');
      return false;
    } catch (e) {
      debugPrint('❌ Authentication test error: $e');
      return false;
    }
  }

  // Run comprehensive network tests
  static Future<void> runFullTest() async {
    debugPrint('\n🚀 Starting Network Connectivity Tests');
    debugPrint('=' * 50);

    // Test basic connection
    final basicConnection = await testServerConnection();

    if (!basicConnection) {
      debugPrint('\n❌ Basic connection failed. Check:');
      debugPrint('1. Mock server is running (dart lib/main_server.dart)');
      debugPrint('2. API config has correct IP address');
      debugPrint('3. Mobile device is on same WiFi network');
      debugPrint('4. Firewall is not blocking port 8080');
      return;
    }

    // Test all endpoints
    final endpointResults = await testAllEndpoints();
    final successfulEndpoints = endpointResults.values.where((v) => v).length;
    final totalEndpoints = endpointResults.length;

    debugPrint('\n📊 Endpoint Test Results: $successfulEndpoints/$totalEndpoints successful');

    // Test authentication
    await testAuthentication();

    // Summary
    debugPrint('\n📋 Test Summary:');
    debugPrint('   • Server URL: ${ApiConfig.baseUrl}');
    debugPrint('   • Basic Connection: ${basicConnection ? '✅' : '❌'}');
    debugPrint('   • Endpoints Working: $successfulEndpoints/$totalEndpoints');
    debugPrint('   • Configuration Valid: ${ApiConfig.isValidServerConfig() ? '✅' : '❌'}');

    if (basicConnection && successfulEndpoints == totalEndpoints) {
      debugPrint('\n🎉 All tests passed! Your app is ready for mobile testing.');
    } else {
      debugPrint('\n⚠️ Some tests failed. Check server configuration.');
    }

    debugPrint('=' * 50);
  }

  // Quick connectivity check for app startup
  static Future<bool> quickConnectivityCheck() async {
    try {
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/settings/salon'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 3));

      return response.statusCode == 200;
    } catch (e) {
      return false;
    }
  }
}
