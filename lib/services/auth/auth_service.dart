import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../../config/api_config.dart';
import '../../models/api_response.dart';
import '../../models/phone_verification_info.dart';
import '../api_service.dart';
import '../notification_handler.dart';

class AuthService {
  static const FlutterSecureStorage _storage = FlutterSecureStorage();

  // Storage keys
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userIdKey = 'user_id';
  static const String _userPhoneKey = 'user_phone';
  static const String _userRoleKey = 'user_role';
  static const String _salonIdKey = 'salon_id';
  static const String _groomerPermissionsKey = 'groomer_permissions';
  static const String _firebaseTokenKey = 'firebase_token';

  /// Exchange Firebase token for backend JWT token
  static Future<ApiResponse<Map<String, dynamic>>> firebaseLogin(String firebaseToken) async {
    try {
      if (ApiConfig.enableLogging) {
        debugPrint('🔑 Starting Firebase login process...');
        debugPrint('🔑 Target URL: ${ApiConfig.baseUrl}/api/auth/firebase-login');
        final tokenPreview = firebaseToken.length > 20
            ? '${firebaseToken.substring(0, 20)}...'
            : firebaseToken;
        debugPrint('🔑 Firebase token preview: $tokenPreview');
      }

      final response = await ApiService.post<Map<String, dynamic>>(
        '/api/auth/firebase-login',
        body: {
          'firebaseToken': firebaseToken,
          'platform': 'ios',
          'appVersion': '1.0.0',
        },
        fromJson: (data) => Map<String, dynamic>.from(data),
        requiresAuth: false, // This is an auth endpoint
      );

      if (ApiConfig.enableLogging) {
        debugPrint('🔑 Firebase login response received:');
        debugPrint('🔑 Success: ${response.success}');
        debugPrint('🔑 Error: ${response.error}');
        debugPrint('🔑 Status Code: ${response.statusCode}');
        if (response.data != null) {
          debugPrint('🔑 Response data keys: ${response.data!.keys.toList()}');
        }
      }

      if (response.success && response.data != null) {
        // ApiService already extracted the 'data' field from the backend response
        // So response.data contains the authentication data directly
        final authData = response.data!;

        if (ApiConfig.enableLogging) {
          debugPrint('🔑 Firebase login successful, storing tokens...');
          debugPrint('🔑 Full auth response: $authData');
          debugPrint('🔑 User ID: ${authData['userId']}');
          debugPrint('🔑 User Phone: ${authData['userPhone']}');
          debugPrint('🔑 User Role: ${authData['userRole']}');
          debugPrint('🔑 Salon ID: ${authData['salonId']}');
        }

        // 🔧 BUSINESS RULE: All authenticated users are groomers by default
        // This ensures simplified onboarding - users can create/join salons from profile screen
        debugPrint('🔍 Backend returned user role: ${authData['userRole']}');

        // Force all users to be GROOMER role regardless of what backend returns
        // This matches the user's requirement that backend sets everything to USER but app should use GROOMER
        if (authData['userRole'] == 'USER' || authData['userRole'] == 'ADMIN' || authData['userRole'] == null) {
          debugPrint('🔧 Converting ${authData['userRole']} to GROOMER (default for all users)');
          authData['userRole'] = 'GROOMER';
        }

        // Store tokens securely using the correct field names from your backend
        await _storeTokens(
          accessToken: authData['accessToken'] as String,
          refreshToken: authData['accessToken'] as String, // Your backend doesn't return refreshToken in this response
          userId: authData['userId'] as String,
          userPhone: authData['userPhone'] as String?,
          userRole: authData['userRole'] as String?,
          salonId: authData['salonId'] as String?,
        );
        if (ApiConfig.enableLogging) {
          debugPrint('🔑 Tokens stored successfully');
          debugPrint('user role: ${authData['userRole']}');
        }

        // Store Firebase token for future use
        await _storage.write(key: _firebaseTokenKey, value: firebaseToken);

        // Set the token in ApiService for future requests
        ApiService.setAuthToken(authData['accessToken'] as String);

        if (ApiConfig.enableLogging) {
          debugPrint('✅ Firebase login successful, registering FCM token...');
        }

        // Register FCM token now that user is authenticated
        try {
          await NotificationHandler.registerTokenAfterLogin();
        } catch (e) {
          if (ApiConfig.enableLogging) {
            debugPrint('⚠️ Failed to register FCM token after login: $e');
          }
          // Don't fail login if FCM registration fails
        }

        // Return the auth data for the provider
        return ApiResponse.success(authData);
      } else {
        if (ApiConfig.enableLogging) {
          debugPrint('❌ Firebase login failed: ${response.error}');
        }
      }

      return response;
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Firebase authentication error: $e');
      }
      return ApiResponse<Map<String, dynamic>>.error('Firebase authentication failed: $e');
    }
  }
  // Login with email and password
  static Future<ApiResponse<Map<String, dynamic>>> login({
    required String email,
    required String password,
  }) async {
    final body = <String, dynamic>{
      'email': email,
      'password': password,
    };

    final response = await ApiService.post<Map<String, dynamic>>(
      '/api/auth/login',
      body: body,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    // If login successful, store the token and register FCM
    if (response.success && response.data != null) {
      final token = response.data!['token'] as String?;
      if (token != null) {
        ApiService.setAuthToken(token);
      }

      // Some endpoints may also return user information
      final userId = response.data!['userId'] as String?;
      final userPhone = response.data!['userPhone'] as String?;
      final userRole = response.data!['userRole'] as String?;
      final salonId = response.data!['salonId'] as String?;

      if (token != null && userId != null) {
        await _storeTokens(
          accessToken: token,
          refreshToken: token,
          userId: userId,
          userPhone: userPhone,
          userRole: userRole,
          salonId: salonId,
        );

        try {
          await NotificationHandler.registerTokenAfterLogin();
        } catch (_) {
          // Ignore FCM errors during login
        }
      }
    }

    return response;
  }

  // Logout
  static Future<ApiResponse<void>> logout() async {
    try {
      final accessToken = await _storage.read(key: _accessTokenKey);

      if (accessToken != null) {
        // Notify backend about logout
        await ApiService.post<void>('/api/auth/logout', body: {'token': accessToken}, requiresAuth: false);
      }

      // Unregister FCM token
      try {
        await NotificationHandler.unregisterToken();
      } catch (e) {
        if (ApiConfig.enableLogging) {
          debugPrint('⚠️ Failed to unregister FCM token during logout: $e');
        }
        // Don't fail logout if FCM unregistration fails
      }

      // Clear all stored data
      await _clearStoredData();

      // Clear token from ApiService
      ApiService.clearAuthToken();

      return ApiResponse<void>.success(null);
    } catch (e) {
      // Even if backend call fails, clear local data
      await _clearStoredData();
      ApiService.clearAuthToken();
      return ApiResponse<void>.success(null);
    }
  }

  // Register new user
  static Future<ApiResponse<Map<String, dynamic>>> register({
    required String name,
    required String email,
    required String password,
    String? role,
  }) async {
    final body = <String, dynamic>{
      'name': name,
      'email': email,
      'password': password,
      if (role != null) 'role': role,
    };

    final response = await ApiService.post<Map<String, dynamic>>(
      '/api/auth/register',
      body: body,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Forgot password
  static Future<ApiResponse<void>> forgotPassword(String email) async {
    final body = <String, dynamic>{
      'email': email,
    };

    final response = await ApiService.post<void>(
      '/api/auth/forgot-password',
      body: body,
    );

    return response;
  }

  // Reset password
  static Future<ApiResponse<void>> resetPassword({
    required String token,
    required String newPassword,
  }) async {
    final body = <String, dynamic>{
      'token': token,
      'newPassword': newPassword,
    };

    final response = await ApiService.post<void>(
      '/api/auth/reset-password',
      body: body,
    );

    return response;
  }

  // Change password
  static Future<ApiResponse<void>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    final body = <String, dynamic>{
      'currentPassword': currentPassword,
      'newPassword': newPassword,
    };

    final response = await ApiService.put<void>(
      '/api/auth/change-password',
      body: body,
    );

    return response;
  }

  // Delete account
  static Future<ApiResponse<void>> deleteAccount(String confirmationText) async {
    try {
      debugPrint('🗑️ Attempting to delete account with confirmation: $confirmationText');

      final response = await ApiService.delete<void>(
        '/api/auth/delete-account',
        body: {
          'confirmationText': confirmationText,
        },
      );

      if (response.success) {
        debugPrint('✅ Account deleted successfully');

        // Unregister FCM token
        try {
          await NotificationHandler.unregisterToken();
        } catch (e) {
          if (ApiConfig.enableLogging) {
            debugPrint('⚠️ Failed to unregister FCM token during account deletion: $e');
          }
          // Don't fail account deletion if FCM unregistration fails
        }

        // Clear all local data after successful account deletion
        await _clearStoredData();
        ApiService.clearAuthToken();
        return ApiResponse.success(null);
      } else {
        debugPrint('❌ Account deletion failed: ${response.error}');
        return response;
      }
    } catch (e) {
      debugPrint('❌ Error deleting account: $e');
      return ApiResponse.error('Network error occurred');
    }
  }

  // Update user profile
  static Future<ApiResponse<Map<String, dynamic>>> updateProfile({
    String? name,
    String? email,
    String? phone,
    Map<String, dynamic>? preferences,
  }) async {
    final body = <String, dynamic>{};

    if (name != null) body['name'] = name;
    if (email != null) body['email'] = email;
    if (phone != null) body['phone'] = phone;
    if (preferences != null) body['preferences'] = preferences;

    final response = await ApiService.put<Map<String, dynamic>>(
      '/api/auth/profile',
      body: body,
      fromJson: (data) => Map<String, dynamic>.from(data),
    );

    return response;
  }

  // Refresh authentication token
  static Future<ApiResponse<Map<String, dynamic>>> refreshToken() async {
    try {
      // Get stored refresh token (for now we'll use the access token since your backend doesn't return refresh token)
      final storedRefreshToken = await _storage.read(key: _refreshTokenKey);
      if (storedRefreshToken == null) {
        return ApiResponse.error('No refresh token available');
      }

      final response = await ApiService.post<Map<String, dynamic>>(
        '/api/auth/refresh',
        body: {
          'refreshToken': storedRefreshToken,
        },
        fromJson: (data) => Map<String, dynamic>.from(data),
        requiresAuth: false, // This is an auth endpoint
      );

      // Update stored token if refresh successful
      if (response.success && response.data != null) {
        // ApiService already extracted the 'data' field from the backend response
        final authData = response.data!;

        // Apply same business rule as login - force all users to be GROOMER
        debugPrint('🔍 Token refresh - Backend returned user role: ${authData['userRole']}');
        if (authData['userRole'] == 'USER' || authData['userRole'] == 'ADMIN' || authData['userRole'] == null) {
          debugPrint('🔧 Token refresh - Converting ${authData['userRole']} to GROOMER');
          authData['userRole'] = 'GROOMER';
        }

        // Update stored tokens
        await _storeTokens(
          accessToken: authData['accessToken'] as String,
          refreshToken: authData['accessToken'] as String, // Your backend doesn't return refreshToken
          userId: authData['userId'] as String,
          userPhone: authData['userPhone'] as String?,
          userRole: authData['userRole'] as String?,
          salonId: authData['salonId'] as String?,
        );

        ApiService.setAuthToken(authData['accessToken'] as String);

        // Register FCM token after successful token refresh
        try {
          await NotificationHandler.registerTokenAfterLogin();
        } catch (e) {
          if (ApiConfig.enableLogging) {
            debugPrint('⚠️ Failed to register FCM token after token refresh: $e');
          }
        }

        return ApiResponse.success(authData);
      }

      return response;
    } catch (e) {
      return ApiResponse.error('Token refresh failed: $e');
    }
  }

  // Check if user is authenticated
  static bool isAuthenticated() {
    return ApiService.authToken != null;
  }

  // Get stored auth token
  static String? getAuthToken() {
    return ApiService.authToken;
  }


  static String? validateName(String? name) {
    if (name == null || name.trim().isEmpty) {
      return 'Numele este obligatoriu';
    }

    if (name.trim().length < 2) {
      return 'Numele trebuie să aibă cel puțin 2 caractere';
    }

    return null; // Valid name
  }

  static String? validateEmail(String? email) {
    if (email == null || email.trim().isEmpty) {
      return 'Email-ul este obligatoriu';
    }

    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!emailRegex.hasMatch(email.trim())) {
      return 'Email-ul nu este valid';
    }

    return null;
  }

  static String? validatePassword(String? password) {
    if (password == null || password.trim().isEmpty) {
      return 'Parola este obligatorie';
    }
    if (password.length < 8) {
      return 'Parola trebuie să aibă cel puțin 8 caractere';
    }
    return null;
  }

  static String? validatePhone(String? phone) {
    if (phone == null || phone.trim().isEmpty) {
      return 'Numărul de telefon este obligatoriu';
    }

    // Use PhoneNumberUtils for validation
    final normalizedPhone = phone.trim();

    // Basic validation - check if it looks like a phone number
    final phoneRegex = RegExp(r'^[\+]?[0-9\s\-\(\)]{8,}$');
    if (!phoneRegex.hasMatch(normalizedPhone)) {
      return 'Numărul de telefon nu este valid';
    }

    return null;
  }

  /// Initialize authentication state on app startup
  static Future<void> initializeAuth() async {
    try {
      if (ApiConfig.enableLogging) {
        debugPrint('🔄 === AUTH INITIALIZATION START ===');
      }

      final accessToken = await _storage.read(key: _accessTokenKey);

      if (ApiConfig.enableLogging) {
        debugPrint('🔍 Stored access token found: ${accessToken != null}');
        if (accessToken != null) {
          debugPrint('🔍 Token length: ${accessToken.length}');
          debugPrint('🔍 Token preview: ${accessToken.length > 20 ? '${accessToken.substring(0, 20)}...' : accessToken}');
        }
      }

      if (accessToken != null && accessToken.isNotEmpty) {
        // Set the token in ApiService
        ApiService.setAuthToken(accessToken);
        if (ApiConfig.enableLogging) {
          debugPrint('✅ Auth token set in ApiService');
        }

        // Verify token validity using ApiService validation
        final isValid = await ApiService.validateAuthState();
        if (ApiConfig.enableLogging) {
          debugPrint('🔍 Token validation result: $isValid');
        }

        if (!isValid) {
          if (ApiConfig.enableLogging) {
            debugPrint('🔄 Token invalid, attempting refresh...');
          }
          // Try to refresh token
          final refreshResult = await refreshToken();
          if (!refreshResult.success) {
            if (ApiConfig.enableLogging) {
              debugPrint('❌ Token refresh failed, clearing auth data');
            }
            // If refresh fails, clear auth data
            await _clearStoredData();
            ApiService.clearAuthToken();
          } else {
            if (ApiConfig.enableLogging) {
              debugPrint('✅ Token refreshed successfully');
            }
          }
        } else {
          if (ApiConfig.enableLogging) {
            debugPrint('✅ Token is valid');
          }
        }
      } else {
        // No stored token
        ApiService.clearAuthToken();
        if (ApiConfig.enableLogging) {
          debugPrint('🔑 No stored auth token found during initialization');
        }
      }

      if (ApiConfig.enableLogging) {
        debugPrint('🔄 === AUTH INITIALIZATION END ===');
      }
    } catch (e) {
      if (ApiConfig.enableLogging) {
        debugPrint('❌ Error during auth initialization: $e');
      }
      // Clear everything on error
      await _clearStoredData();
      ApiService.clearAuthToken();
    }
  }

  /// Get current access token
  static Future<String?> getAccessToken() async {
    return await _storage.read(key: _accessTokenKey);
  }

  /// Get current user ID
  static Future<String?> getCurrentUserId() async {
    return await _storage.read(key: _userIdKey);
  }

  /// Get current user phone
  static Future<String?> getCurrentUserPhone() async {
    return await _storage.read(key: _userPhoneKey);
  }

  /// Get current user role
  static Future<String?> getCurrentUserRole() async {
    return await _storage.read(key: _userRoleKey);
  }

  /// Get current salon ID
  static Future<String?> getCurrentSalonId() async {
    final salonId = await _storage.read(key: _salonIdKey);
    debugPrint('🏢 AuthService: getCurrentSalonId() returning: $salonId');
    return salonId;
  }

  /// Get current user profile from API
  static Future<ApiResponse<Map<String, dynamic>>> getCurrentUser() async {
    try {
      final response = await ApiService.get<Map<String, dynamic>>(
        '/api/auth/profile',
        fromJson: (data) => Map<String, dynamic>.from(data),
      );
      return response;
    } catch (e) {
      debugPrint('❌ Error getting current user: $e');
      return ApiResponse.error('Failed to get current user: $e');
    }
  }

  /// Get current user name
  static Future<String?> getCurrentUserName() async {
    try {
      final response = await getCurrentUser();
      if (response.success && response.data != null) {
        return response.data!['name'] as String?;
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error getting current user name: $e');
      return null;
    }
  }

  /// Update user name using PATCH /api/auth/profile/name
  static Future<ApiResponse<Map<String, dynamic>>> updateUserName(String name) async {
    try {
      debugPrint('🔄 Updating user name to: $name');

      final response = await ApiService.patch<Map<String, dynamic>>(
        '/api/auth/profile/name',
        body: {'name': name},
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success) {
        debugPrint('✅ User name updated successfully');
      } else {
        debugPrint('❌ Failed to update user name: ${response.error}');
      }

      return response;
    } catch (e) {
      debugPrint('❌ Error updating user name: $e');
      return ApiResponse.error('Eroare la actualizarea numelui: $e');
    }
  }

  /// Update user phone number with SMS confirmation
  static Future<ApiResponse<Map<String, dynamic>>> updateUserPhone(String phoneNumber, String confirmationCode) async {
    try {
      debugPrint('🔄 === UPDATE USER PHONE START ===');
      debugPrint('🔄 Updating user phone to: $phoneNumber');
      debugPrint('🔍 Current auth state: ${isAuthenticated()}');
      debugPrint('🔍 ApiService has token: ${ApiService.hasAuthToken}');
      debugPrint('🔍 ApiService token: ${ApiService.authToken != null ? '${ApiService.authToken!.substring(0, 20)}...' : 'NULL'}');

      // Ensure we have a valid token before making the request
      final storedToken = await getAccessToken();
      if (storedToken != null && !ApiService.hasAuthToken) {
        debugPrint('🔄 Setting auth token from storage before phone update');
        ApiService.setAuthToken(storedToken);
      }

      final response = await ApiService.patch<Map<String, dynamic>>(
        '/api/auth/profile/phone',
        body: {
          'phoneNumber': phoneNumber,
          'confirmationCode': confirmationCode,
        },
        fromJson: (data) => Map<String, dynamic>.from(data),
      );

      if (response.success) {
        debugPrint('✅ User phone updated successfully');
        // Update stored phone number
        await _storage.write(key: _userPhoneKey, value: phoneNumber);
      } else {
        debugPrint('❌ Failed to update user phone: ${response.error}');
      }

      debugPrint('🔄 === UPDATE USER PHONE END ===');

      return response;
    } catch (e) {
      debugPrint('❌ Error updating user phone: $e');
      return ApiResponse.error('Eroare la actualizarea telefonului: $e');
    }
  }

  /// Resend phone confirmation code
  static Future<ApiResponse<PhoneVerificationInfo>> resendPhoneConfirmationCode(
      String phoneNumber) async {
    try {
      debugPrint('🚀 === AUTH SERVICE SMS API CALL START ===');
      debugPrint('📱 Phone number: $phoneNumber');
      debugPrint('🔗 Endpoint: /api/auth/phone/send-verification');
      debugPrint('📦 Request body: {"phoneNumber": "$phoneNumber"}');

      final response = await ApiService.post<PhoneVerificationInfo>(
        '/api/auth/phone/send-verification',
        body: {'phoneNumber': phoneNumber},
        fromJson: (data) {
          debugPrint('🔄 Parsing response data: $data');
          debugPrint('🔄 Data type: ${data.runtimeType}');
          return PhoneVerificationInfo.fromJson(data as Map<String, dynamic>);
        },
      );

      debugPrint('📡 API response received');
      debugPrint('✅ Response success: ${response.success}');
      debugPrint('📄 Response data: ${response.data}');
      debugPrint('❌ Response error: ${response.error}');
      debugPrint('🔢 Response status code: ${response.statusCode}');

      if (response.success) {
        debugPrint('🎉 Phone confirmation code sent successfully');
        if (response.data != null) {
          debugPrint('📱 Message: ${response.data!.message}');
          debugPrint('📱 Phone: ${response.data!.phoneNumber}');
          debugPrint('📱 Expires in: ${response.data!.expiresIn}');
          debugPrint('📱 Can resend after: ${response.data!.canResendAfter}');
          debugPrint('📱 Remaining attempts: ${response.data!.remainingAttempts}');
        }
      } else {
        debugPrint('💥 Failed to send phone confirmation code');
        debugPrint('❌ Error details: ${response.error}');
      }

      debugPrint('🏁 === AUTH SERVICE SMS API CALL END ===');
      return response;
    } catch (e) {
      debugPrint('💥 === AUTH SERVICE SMS API EXCEPTION ===');
      debugPrint('❌ Exception: $e');
      debugPrint('❌ Exception type: ${e.runtimeType}');
      debugPrint('❌ Stack trace: ${StackTrace.current}');
      debugPrint('🏁 === AUTH SERVICE SMS API EXCEPTION END ===');
      return ApiResponse.error('Eroare la retrimirea codului: $e');
    }
  }

  /// Get groomer permissions
  static Future<String?> getGroomerPermissions() async {
    return await _storage.read(key: _groomerPermissionsKey);
  }

  /// Update current salon ID (for salon switching)
  static Future<void> updateCurrentSalonId(String? salonId) async {
    if (salonId != null) {
      await _storage.write(key: _salonIdKey, value: salonId);
    } else {
      await _storage.delete(key: _salonIdKey);
    }

    if (ApiConfig.enableLogging) {
      debugPrint('🔄 Updated current salon ID: $salonId');
    }
  }


  /// Store authentication tokens securely
  static Future<void> _storeTokens({
    required String accessToken,
    required String refreshToken,
    required String userId,
    String? userPhone,
    String? userRole,
    String? salonId,
  }) async {
    // Debug logging
    debugPrint('🔍 Storing tokens:');
    debugPrint('  User ID: $userId');
    debugPrint('  User Phone: $userPhone');
    debugPrint('  User Role: $userRole');
    debugPrint('  Salon ID: $salonId');

    await Future.wait([
      _storage.write(key: _accessTokenKey, value: accessToken),
      _storage.write(key: _refreshTokenKey, value: refreshToken),
      _storage.write(key: _userIdKey, value: userId),
      if (userPhone != null) _storage.write(key: _userPhoneKey, value: userPhone),
      if (userRole != null) _storage.write(key: _userRoleKey, value: userRole),
      if (salonId != null) _storage.write(key: _salonIdKey, value: salonId),
    ]);

    debugPrint('✅ Tokens stored successfully');
  }

  /// Clear all stored authentication data
  static Future<void> _clearStoredData() async {
    await Future.wait([
      _storage.delete(key: _accessTokenKey),
      _storage.delete(key: _refreshTokenKey),
      _storage.delete(key: _userIdKey),
      _storage.delete(key: _userPhoneKey),
      _storage.delete(key: _userRoleKey),
      _storage.delete(key: _salonIdKey),
      _storage.delete(key: _groomerPermissionsKey),
      _storage.delete(key: _firebaseTokenKey),
    ]);
  }

}
